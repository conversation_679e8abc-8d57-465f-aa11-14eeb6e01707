[1, ["87KJdfUjNA1pqHCGJqumKH", "2b7rASqEdNKp69oQ9/zA7S", "0be81vzH1FoK7mBxvGGC28@f9941", "60TZW9Q6ZNUZQf73SDw4Vs@f9941", "6aoXk+gYtHg57/JehAnxyU@f9941", "bdG8q6vX1KcbFDmXyII4Pk@f9941", "cbtfJNj/1HWLtsVyJSDiln", "6cKz+ZUM9C+rpahRX/14HR@f9941", "69PPT9dqZBgo8/SaF99eoS@f9941", "fcB51r3FJHYJznkoAKIYK1@f9941", "c5+SZSSGNMoKp6H5lnpasw@f9941", "e2tftiePdHiZlUJSKDsoae@f9941", "dcUKZqxppBPJgr55VZ2X2Q@f9941", "02hWyS6StHRIhijeRUzOmi@f9941", "92Gu9k1TNLgZMPf3ezlcAT@f9941"], ["node", "targetInfo", "_spriteFrame", "root", "asset", "_target", "_parent", "data", "inpuBox", "_backgroundImage"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_children", "_parent", "_lpos"], -2, 4, 9, 2, 1, 5], ["cc.Widget", ["_alignFlags", "_bottom", "_originalWidth", "_originalHeight", "_top", "_right", "_left", "_verticalCenter", "_horizontalCenter", "node", "__prefab"], -6, 1, 4], ["cc.Sprite", ["_type", "_sizeMode", "_isTrimmedMode", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_lpos", "_parent", "_children"], 0, 12, 4, 5, 1, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["47052uw/Y5O1LXaLObj4ARx", ["watchPath", "condition", "_enabled", "node", "__prefab", "watchNodes"], 0, 1, 4, 2], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_target", "_normalColor"], 2, 1, 4, 1, 5], ["cc.Label", ["_string", "_actualFontSize", "_lineHeight", "_horizontalAlign", "_overflow", "_enableWrapText", "node", "__prefab", "_color"], -3, 1, 4, 5], ["cc.Layout", ["_layoutType", "_spacingY", "node", "__prefab"], 1, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedComponents"], 2, 1, 9, 9], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["4c088mplRRK5o6eY0e1zc45", ["node", "__prefab"], 3, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetInfo", ["localID"], 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["405ecLg31JDyb8hWju4bULw", ["node", "__prefab", "inpuBox"], 3, 1, 4, 1], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 9], ["cc.EditBox", ["_inputMode", "node", "__prefab", "_textLabel", "_placeholder<PERSON><PERSON><PERSON>"], 2, 1, 4, 1, 1]], [[11, 0, 2], [16, 0, 2], [17, 0, 1, 2, 3], [18, 0, 1, 2, 2], [14, 0, 1, 2, 3, 4, 5, 5], [4, 0, 1, 2, 1], [0, 3, 4, 8, 5, 3], [15, 0, 1, 2, 3, 4, 5, 4], [19, 0, 1, 2, 3], [9, 0, 1, 2, 2], [20, 0, 1, 2, 2], [0, 0, 1, 8, 7, 6, 5, 9, 3], [4, 0, 1, 1], [2, 0, 3, 4, 5, 2], [0, 0, 1, 8, 6, 5, 3], [2, 3, 4, 5, 1], [6, 0, 1, 2, 4, 3, 2], [0, 0, 1, 7, 6, 5, 3], [0, 0, 1, 8, 7, 6, 5, 3], [10, 0, 2], [0, 0, 2, 1, 7, 6, 5, 9, 4], [0, 0, 1, 7, 6, 5, 9, 3], [0, 0, 1, 8, 6, 5, 9, 3], [4, 0, 1, 2, 3, 1], [13, 0, 1, 2, 3, 4, 5, 4], [8, 0, 1, 2, 3, 3], [5, 0, 1, 3, 4, 5, 3], [1, 0, 6, 5, 4, 1, 8, 7, 2, 3, 9, 10, 10], [1, 0, 4, 1, 3, 9, 10, 5], [9, 0, 1, 3, 2, 2], [2, 0, 1, 3, 4, 5, 3], [22, 0, 1, 1], [0, 0, 2, 1, 8, 7, 6, 5, 9, 4], [3, 0, 1, 7, 3, 4, 5, 3], [3, 0, 2, 1, 6, 3, 4, 5, 4], [3, 0, 1, 6, 3, 4, 5, 3], [12, 0, 1, 1], [8, 0, 2, 3, 2], [5, 2, 0, 3, 4, 5, 3], [5, 0, 3, 4, 5, 2], [1, 0, 6, 5, 4, 1, 7, 2, 3, 9, 10, 9], [1, 0, 2, 3, 9, 10, 4], [1, 0, 6, 5, 4, 1, 2, 3, 9, 10, 8], [1, 0, 5, 1, 9, 10, 4], [1, 0, 6, 5, 1, 2, 9, 10, 6], [1, 4, 1, 2, 3, 9, 10, 5], [1, 0, 4, 1, 2, 3, 9, 10, 6], [21, 0, 1, 2, 1], [2, 3, 4, 1], [2, 0, 1, 2, 3, 4, 5, 4], [6, 0, 1, 2, 3, 2], [6, 0, 1, 2, 2], [7, 0, 1, 2, 6, 7, 8, 4], [7, 0, 3, 1, 2, 4, 5, 6, 7, 7], [7, 0, 3, 1, 2, 4, 5, 6, 7, 8, 7], [23, 0, 1, 2, 3, 4, 2]], [[[[19, "hallVmRankCell"], [17, "hallVmRankCell", 33554432, [-7], [[5, -5, [0, "1ehE0yjgpOdpGShi2Ty+Al"], [5, 260.15625, 50.4]], [36, -6, [0, "03iYLSvBVIBqw4wSv7GMSF"]]], [24, "26iaepJgFCqKV3l5WUmOuO", null, null, -4, 0, [-1, -2, -3]]], [11, "centerNode", 33554432, 1, [-13, -14, -15], [[12, -8, [0, "fft5apgMpHxagaJ1pBxC86"]], [37, 2, -9, [0, "93oBg+vsxKg6F6FCmgN86P"]], [26, "*.times", 2, -11, [0, "73+99BOgdGvI5ITjxxfnh1"], [-10]], [40, 2, 80.078125, 80.078125, -24.8, -24.8, 400, 100, 100, -12, [0, "04OHEtIjFMXIgN519oY9pR"]]], [4, "e6nDtHVlNF4qOQl/P3kQ1A", null, null, null, 1, 0], [1, 0, 400, 0]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, ["09i06HqvBP2ZaQ4Pk+p3Mt"]], [6, 0, {}, 2, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -20, [9, "f2rmwxfZhAOYpRh0djJser", 1, [[2, "vmTimesLabel", ["_name"], -16], [3, ["_lpos"], -17, [1, 0, -76, 0]], [3, ["_lrot"], -18, [3, 0, 0, 0, 1]], [3, ["_euler"], -19, [1, 0, 0, 0]], [8, "vmTimesLabel", ["_dataID"], [1, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]], [10, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 245.625, 50.4]]]], 2]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [6, 0, {}, 2, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -21, [9, "8eCNJYQF5Ck6oLAgJvxVQU", 1, [[2, "vmRankLabel", ["_name"], 3], [3, ["_lpos"], 3, [1, 0, 24.8, 0]], [3, ["_lrot"], 3, [3, 0, 0, 0, 1]], [3, ["_euler"], 3, [1, 0, 0, 0]], [2, true, ["templateMode"], 4], [2, 1, ["watchPathArr", "length"], 4], [2, "*.rank", ["watchPathArr", "0"], 4], [2, "vmRankLabel", ["_dataID"], 4], [10, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 248.4375, 50.4]], [2, true, ["_active"], 3]]], 0]], [6, 0, {}, 2, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -22, [9, "59oamas7BK6IOgDZ/Oxeew", 1, [[8, "vmCountryLabel", ["_name"], [1, ["f05XX5jrpEOYwv6lCoUIav"]]], [10, ["_lpos"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, -25.599999999999998, 0]], [10, ["_lrot"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [3, 0, 0, 0, 1]], [10, ["_euler"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 0, 0]], [8, false, ["templateMode"], [1, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]], [8, 0, ["watchPathArr", "length"], [1, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]], [8, "hgcrvm.vmRank1", ["watchPathArr", "0"], [1, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]], [8, "UnKnow", ["_dataID"], [1, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]], [10, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 248.4375, 50.4]], [8, true, ["_active"], [1, ["f05XX5jrpEOYwv6lCoUIav"]]]]], 1]]], 0, [0, -1, 5, 0, -2, 8, 0, -3, 7, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, -2, 8, 0, -3, 5, 0, 1, 6, 0, 1, 6, 0, 1, 6, 0, 1, 6, 0, 3, 5, 0, 3, 7, 0, 3, 8, 0, 7, 1, 22], [0, 0, 0], [4, 4, 4], [0, 0, 0]], [[[19, "hallUIView"], [17, "hallUIView", 33554432, [-15, -16, -17, -18, -19], [[5, -10, [0, "77N2cid5pKDpXplRH/AWEU"], [5, 750, 1334]], [41, 5, 750, 2, -11, [0, "63zNQq8NlBQ5QWzOJ4Kgjs"]], [47, -13, [0, "7enO7d9+1NMLRxmsXeVho4"], -12], [48, -14, [0, "542RguQ8FIF44y7flPpMpo"]]], [24, "a0daVw8DRLi6ToMaTA0VS2", null, null, -9, 0, [-1, -2, -3, -4, -5, -6, -7, -8]]], [11, "topBtnNode", 33554432, 1, [-23, -24, -25], [[12, -20, [0, "4ekgjMKlZELabbCLVwFc7A"]], [42, 9, 7, 643.2579999999998, 148.15100000000007, 1085.849, 100, 100, -21, [0, "fad9jliGtI/r7qMu29ltVe"]], [25, 2, 15, -22, [0, "a2uyvexHVDQZCMxTqjN98G"]]], [4, "1eDfCkRjdBzobevFmzIHJ/", null, null, null, 1, 0], [1, -318, 468.84899999999993, 0]], [20, "btnTeam", false, 33554432, [-31], [[5, -26, [0, "61EcVdnpNLkKFtCqEgegCr"], [5, 101, 81]], [13, 1, -27, [0, "43Pe/B3wNPBrcIEcLdoRDj"], 15], [16, 3, -29, [0, "beL729iVNII4RMpG2K7Xz/"], [4, 4292269782], -28], [27, 12, -300.5, 482.53800000000007, 154.60700000000003, 9.5, -486, 2.45799999999997, 105, 97, -30, [0, "342QkrDeFF1ZQ5WL7dDdpg"]]], [4, "2aGlkENwhC+r6kkxwGg0T4", null, null, null, 1, 0], [1, -300, 0, 0]], [21, "btnShare", 33554432, [-37], [[5, -32, [0, "c8hvp9mLlEMq85rOqAhZK3"], [5, 101, 81]], [13, 1, -33, [0, "36Rkycmt1HAYMIB2+BRPHV"], 17], [16, 3, -35, [0, "b8klwxogZLJIBOs/+GZX8i"], [4, 4292269782], -34], [27, 36, -300, -300.5, 154.60700000000003, 9.5, 280.097, 2.45799999999997, 105, 97, -36, [0, "c1Da0DvdlI2b+i9ZWwVPyF"]]], [4, "f9t236LAlADbrpziyxQG3q", null, null, null, 1, 0], [1, 300, 0, 0]], [21, "btnRank", 33554432, [-43], [[5, -38, [0, "ec1n0iUBpCc79bRUkeU5jm"], [5, 101, 81]], [13, 1, -39, [0, "25d4GgHF1FJohAaVcjTpG7"], 19], [16, 3, -41, [0, "97JruPFsVCWqiVHpuYA88Y"], [4, 4292269782], -40], [43, 36, -300.5, 9.5, -42, [0, "bcdFfJbTBNMIQK5L9GlIqz"]]], [4, "05toTTMr9CYa1LP4f2ycE5", null, null, null, 1, 0], [1, 300, 0, 0]], [20, "btnCollect", false, 33554432, [-49], [[5, -44, [0, "35UcVVXClJqpDHWWm3KWci"], [5, 101, 81]], [13, 1, -45, [0, "390WQ9No9Fh64UX/aoJ9Nw"], 21], [16, 3, -47, [0, "d88JLvfkVO1LuXyow2ETUJ"], [4, 4292269782], -46], [44, 12, -300.5, -300.5, 9.5, 101, -48, [0, "27C7/ArpVBqJ4zxchIAEpt"]]], [4, "c5GL0DfBdL6LpYznWT2f9x", null, null, null, 1, 0], [1, -300, 0, 0]], [33, "inputBox", 33554432, [-54, -55], [[[5, -50, [0, "2cTXrk5FxNcrcIVc+ePkxd"], [5, 200, 82]], [30, 1, 0, -51, [0, "548JONfDRHrJpMucOyMoC5"], 22], -52, [45, 559.41, 692.59, 750, 82, -53, [0, "07xCu8LDlLjJxy2kxFaY25"]]], 4, 4, 1, 4], [4, "19IwcJUVNAxK/Xe8kkKr2s", null, null, null, 1, 0], [1, 66.003, 59.563, 0]], [18, "rankLabelNode", 33554432, 1, [-57, -58, -59, -60], [[12, -56, [0, "c6ZS87fXxJU4IE9qT5nedU"]]], [4, "33BB6JlD9KKJBrwNb7mOm8", null, null, null, 1, 0]], [32, "welfareBtn", false, 33554432, 2, [-65], [[5, -61, [0, "08A6T7g1BM0pZAxo1d4k/7"], [5, 72, 72]], [13, 1, -62, [0, "6fYbzZr+VLMZzaE6yQoM3P"], 8], [50, 3, -64, [0, "d2RgRgzoFHr55bATyPgTRL"], -63]], [4, "58BmiPi8ZHE4E9KAXHeaD/", null, null, null, 1, 0], [1, 0, -160, 0]], [17, "btnStart", 33554432, [-70], [[5, -66, [0, "aaT+LtUNtDg4sl74TKP17o"], [5, 376, 129]], [49, 1, 0, false, -67, [0, "4aA7PM4D1GOaYsU1k4ABtL"], 10], [16, 3, -69, [0, "7fklaqWxlJCbk4taXUqaMK"], [4, 4292269782], -68]], [4, "8aWo3OHKJImbSZMLFESzpO", null, null, null, 1, 0]], [11, "midPanel", 33554432, 1, [-74, -75], [[5, -71, [0, "de8uH9h5xOh6AxOgp1jApy"], [5, 750, 100]], [46, 44, 1421.184, 330, 100, 100, -72, [0, "368YgPiJ5GGIA8GBOmSMOH"]], [25, 2, 50, -73, [0, "3cz/yQxbNLNprkZ2cxoREK"]]], [4, "bdi/3HT69MRZOP/C9GlzL9", null, null, null, 1, 0], [1, 0, -287, 0]], [18, "debugNode", 33554432, 1, [7, -79], [[12, -76, [0, "e5gwNNfU1IVIC4jdrZFx3R"]], [38, false, "*.showDebugNode", -78, [0, "dfyl0KfPBKdJSV1KOV0YPb"], [-77]]], [4, "65ciAtLpdHm5toOgNm+LmM", null, null, null, 1, 0]], [1, ["26iaepJgFCqKV3l5WUmOuO"]], [1, ["26iaepJgFCqKV3l5WUmOuO"]], [1, ["26iaepJgFCqKV3l5WUmOuO"]], [1, ["26iaepJgFCqKV3l5WUmOuO"]], [1, ["7eFCmK8SNKy6KspRqyadU8"]], [11, "personBtn", 33554432, 2, [-83], [[5, -80, [0, "62f0fI9bNAQ5yWUxobMJHV"], [5, 72, 72]], [13, 1, -81, [0, "d13QbkX8RL7orgyjq4e1CM"], 6], [51, 3, -82, [0, "40LWxW3npP/JjR9yz3eoWi"]]], [4, "bawerE46dMeot8vYD919P+", null, null, null, 1, 0], [1, 0, -73, 0]], [11, "startNode", 33554432, 1, [10, -86], [[12, -84, [0, "9dgOfkZmdMeKzTM4qijWCF"]], [28, 20, 938.78, 255.019, 129, -85, [0, "655tzG2yNF1oT526l/6Hqm"]]], [4, "6d0j3qraxDVIHg4guofonx", null, null, null, 1, 0], [1, 0, -361.981, 0]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [11, "黑底", 33554432, 19, [-89, -90], [[5, -87, [0, "f3AEfUf4BPRalcZGv5W3xg"], [5, 376, 38]], [30, 1, 0, -88, [0, "eepMhfFL9C77jtjCw+T0Tg"], 13]], [4, "2aAeqcfplPF6pfUSnmUGK2", null, null, null, 1, 0], [1, 0, -91.58699999999999, 0]], [6, 0, {}, 21, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -106, [29, "e4qJFBLwBIPpGSmni858Aq", 1, [[31, [1, ["f05XX5jrpEOYwv6lCoUIav"]], [[26, "role.selfCountryRank", 2, -105, [0, "02IanLFLZH8pxAyRW7aDaq"], [-104]]]]], [[2, "areaRank", ["_name"], -91], [3, ["_lpos"], -92, [1, 0, 0, 0]], [3, ["_lrot"], -93, [3, 0, 0, 0, 1]], [3, ["_euler"], -94, [1, 0, 0, 0]], [2, 28, ["_fontSize"], -95], [10, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 42.21875, 50.4]], [2, 28, ["_actualFontSize"], -96], [2, false, ["_isBold"], -97], [2, "123", ["_string"], -98], [2, "MyAreaRank", ["_dataID"], -99], [2, true, ["templateMode"], -100], [2, 1, ["watchPathArr", "length"], -101], [2, "role.selfCountryRank", ["watchPathArr", "0"], -102], [2, true, ["_active"], -103]]], 11]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [6, 0, {}, 21, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -118, [29, "cdRFWBtiNLsqwrLpdNHRG7", 1, [[31, [1, ["f05XX5jrpEOYwv6lCoUIav"]], [[39, "role.selfCountryRank", -117, [0, "16GtJmfJdFGbkByE8VCsw3"], [-116]]]]], [[2, "noRank", ["_name"], -107], [3, ["_lpos"], -108, [1, 0, 0, 0]], [3, ["_lrot"], -109, [3, 0, 0, 0, 1]], [3, ["_euler"], -110, [1, 0, 0, 0]], [2, 28, ["_fontSize"], -111], [10, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 42.21875, 50.4]], [2, 28, ["_actualFontSize"], -112], [2, false, ["_isBold"], -113], [2, "123", ["_string"], -114], [8, "NotCountryListed", ["_dataID"], [1, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]], [2, true, ["_active"], -115]]], 12]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, ["4a5atXBglJxJGAlAL90RE0"]], [1, ["09i06HqvBP2ZaQ4Pk+p3Mt"]], [1, ["4a5atXBglJxJGAlAL90RE0"]], [18, "mid1", 33554432, 11, [3, 4], [[12, -119, [0, "67AkS0sZ5LJr4/qsSrjaXz"]]], [4, "0f3h5eQalIoo2cku1EZ/AK", null, null, null, 1, 0]], [11, "mid2", 33554432, 11, [5, 6], [[12, -120, [0, "0b5G7brcVF4J/A/vrvGmeo"]]], [4, "458OAGCjJAloIZNS1FW+wJ", null, null, null, 1, 0], [1, 0, -150, 0]], [22, "Label", 33554432, 12, [[5, -121, [0, "3begBk7xpJVYMwkfEhSdXS"], [5, 480, 52.92]], [52, "游戏测试，可输入测试关卡", 40, 42, -122, [0, "beIopfR49G2LUg8XC8hHK9"], [4, 3338731305]], [28, 16, 933.54, 933.54, 52.92, -123, [0, "edInj/NTZHPJTwdhVFY1Db"]]], [4, "14TKt+NFRJpr5JzfnOrjr3", null, null, null, 1, 0], [1, 0, -37.52, 0]], [6, 0, {}, 8, [7, "26iaepJgFCqKV3l5WUmOuO", null, null, -124, [9, "589xwVMjdCYJoykCsV1Vc0", 1, [[2, "hallVmRankCell1", ["_name"], 13], [3, ["_lpos"], 13, [1, 0, 0, 0]], [3, ["_lrot"], 13, [3, 0, 0, 0, 1]], [3, ["_euler"], 13, [1, 0, 0, 0]], [2, false, ["_active"], 13], [8, "*.times", ["watchPathArr", "0"], [1, ["f2rmwxfZhAOYpRh0djJser", "09i06HqvBP2ZaQ4Pk+p3Mt"]]]]], 0]], [6, 0, {}, 8, [7, "26iaepJgFCqKV3l5WUmOuO", null, null, -125, [9, "efJQxWkMBJBLx5kdSzWciO", 1, [[2, "hallVmRankCell2", ["_name"], 14], [3, ["_lpos"], 14, [1, 0, 0, 0]], [3, ["_lrot"], 14, [3, 0, 0, 0, 1]], [3, ["_euler"], 14, [1, 0, 0, 0]], [2, false, ["_active"], 14]]], 1]], [6, 0, {}, 8, [7, "26iaepJgFCqKV3l5WUmOuO", null, null, -126, [9, "df046UYXdLbY4SFqEQ9VJ4", 1, [[2, "hallVmRankCell3", ["_name"], 15], [3, ["_lpos"], 15, [1, 0, 0, 0]], [3, ["_lrot"], 15, [3, 0, 0, 0, 1]], [3, ["_euler"], 15, [1, 0, 0, 0]], [2, false, ["_active"], 15]]], 2]], [6, 0, {}, 8, [7, "26iaepJgFCqKV3l5WUmOuO", null, null, -127, [9, "90/Me5PXRORY41YxuCRuf6", 1, [[2, "hallVmRankCell4", ["_name"], 16], [3, ["_lpos"], 16, [1, 0, 0, 0]], [3, ["_lrot"], 16, [3, 0, 0, 0, 1]], [3, ["_euler"], 16, [1, 0, 0, 0]], [2, false, ["_active"], 16]]], 3]], [6, 0, {}, 2, [7, "7eFCmK8SNKy6KspRqyadU8", null, null, -128, [9, "e7E/zCSRBFyIFDlFFALGPs", 1, [[2, "settingBtn", ["_name"], 17], [3, ["_lpos"], 17, [1, 0, 14, 0]], [3, ["_lrot"], 17, [3, 0, 0, 0, 1]], [3, ["_euler"], 17, [1, 0, 0, 0]], [2, true, ["_active"], 17], [8, true, ["_active"], [1, ["c4f0hiv0FKX7tm6MnzVPoc"]]]]], 4]], [14, "ti探险家", 33554432, 18, [[5, -129, [0, "5aEgSCGgZCQoJhsiLicFFK"], [5, 47, 53]], [13, 1, -130, [0, "58yOS7/itHppizyGlGRdX+"], 5]], [4, "2fwj4aW9NM5bH4vNUN5Mdq", null, null, null, 1, 0]], [22, "t1福利", 33554432, 9, [[5, -131, [0, "5cQ9SItzhCz5bjkF/MVNy6"], [5, 56, 49]], [15, -132, [0, "24uPAh1/xPYKqpRvNouSBk"], 7]], [4, "a4FVjvUGxO44VipQMe51jR", null, null, null, 1, 0], [1, -7.815970093361102e-14, 1.8330000000000837, 0]], [6, 0, {}, 10, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -135, [9, "0fiHlC5mRIi5/mmXusqS4x", 1, [[2, "startGame", ["_name"], 20], [3, ["_lpos"], 20, [1, 0, 11.884000000000015, 0]], [3, ["_lrot"], 20, [3, 0, 0, 0, 1]], [3, ["_euler"], 20, [1, 0, 0, 0]], [8, "<PERSON><PERSON><PERSON>", ["_string"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [10, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 100.46875, 50.4]], [2, "<PERSON><PERSON><PERSON>", ["_dataID"], -133], [2, true, ["_active"], 20], [2, false, ["templateMode"], -134]]], 9]], [14, "t2团队赛", 33554432, 3, [[5, -136, [0, "de9OLROu5Bv5Mfp9rJyexh"], [5, 57, 60]], [15, -137, [0, "01QhixjzBF0JDcP1XUkdqm"], 14]], [4, "39r+GsDihN2JHVVm24WzaR", null, null, null, 1, 0]], [14, "t2分享", 33554432, 4, [[5, -138, [0, "bf572QBW1A06mYpMx/mWbw"], [5, 57, 54]], [15, -139, [0, "2eLUwHPpZPB4N9uWGymRyN"], 16]], [4, "d6EwFGJwxNMZK28OGSTzcY", null, null, null, 1, 0]], [14, "t3排行榜", 33554432, 5, [[5, -140, [0, "edAvKB36VEDLnU0pNQFcXY"], [5, 78, 80]], [15, -141, [0, "2cVC9w5TxDLpgcKbP+0I6F"], 18]], [4, "23FfWC9W9Ps4+c/3cgzJcc", null, null, null, 1, 0]], [14, "t3图鉴", 33554432, 6, [[5, -142, [0, "47lTUzm3dGEZO7DqkftdmB"], [5, 81, 81]], [15, -143, [0, "afuppcJWhO0oD/nEW/z258"], 20]], [4, "0fY1D0GfxHi4gzBpI5fCU2", null, null, null, 1, 0]], [34, "TEXT_LABEL", false, 33554432, 7, [[[23, -144, [0, "6c3GFcDZxKubSUFPsNtHSy"], [5, 198, 82], [0, 0, 1]], -145], 4, 1], [4, "03kQsYjS1DI498aCXslZlI", null, null, null, 1, 0], [1, -98, 41, 0]], [35, "PLACEHOLDER_LABEL", 33554432, 7, [[[23, -146, [0, "2flZLV9VFOd5J5kpdbXnIk"], [5, 198, 82], [0, 0, 1]], -147], 4, 1], [4, "81gCinbDNFzIUDskHIX6/I", null, null, null, 1, 0], [1, -98, 41, 0]], [1, ["09i06HqvBP2ZaQ4Pk+p3Mt"]], [53, "", 0, 40, 60, 1, false, 44, [0, "4cQCFiYRJN4LMDh9VuSuOe"]], [54, "0", 0, 40, 60, 1, false, 45, [0, "f5YHPhNvFLd4gFs+YuF1GE"], [4, 4290493371]], [55, 6, 7, [0, "79G/tFKxxJUJ97MHJPmyJb"], 47, 48]], 0, [0, -1, 24, 0, -2, 22, 0, -3, 39, 0, -4, 36, 0, -5, 35, 0, -6, 34, 0, -7, 33, 0, -8, 32, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 8, 49, 0, 0, 1, 0, 0, 1, 0, -1, 8, 0, -2, 2, 0, -3, 19, 0, -4, 11, 0, -5, 12, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 36, 0, -2, 18, 0, -3, 9, 0, 0, 3, 0, 0, 3, 0, 5, 3, 0, 0, 3, 0, 0, 3, 0, -1, 40, 0, 0, 4, 0, 0, 4, 0, 5, 4, 0, 0, 4, 0, 0, 4, 0, -1, 41, 0, 0, 5, 0, 0, 5, 0, 5, 5, 0, 0, 5, 0, 0, 5, 0, -1, 42, 0, 0, 6, 0, 0, 6, 0, 5, 6, 0, 0, 6, 0, 0, 6, 0, -1, 43, 0, 0, 7, 0, 0, 7, 0, -3, 49, 0, 0, 7, 0, -1, 44, 0, -2, 45, 0, 0, 8, 0, -1, 32, 0, -2, 33, 0, -3, 34, 0, -4, 35, 0, 0, 9, 0, 0, 9, 0, 5, 9, 0, 0, 9, 0, -1, 38, 0, 0, 10, 0, 0, 10, 0, 5, 10, 0, 0, 10, 0, -1, 39, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, -1, 29, 0, -2, 30, 0, 0, 12, 0, -1, 12, 0, 0, 12, 0, -2, 31, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, -1, 37, 0, 0, 19, 0, 0, 19, 0, -2, 21, 0, 0, 21, 0, 0, 21, 0, -1, 22, 0, -2, 24, 0, 1, 23, 0, 1, 23, 0, 1, 23, 0, 1, 23, 0, 1, 26, 0, 1, 26, 0, 1, 26, 0, 1, 26, 0, 1, 27, 0, 1, 27, 0, 1, 27, 0, 1, 27, 0, 1, 23, 0, -1, 22, 0, 0, 22, 0, 3, 22, 0, 1, 25, 0, 1, 25, 0, 1, 25, 0, 1, 25, 0, 1, 28, 0, 1, 28, 0, 1, 28, 0, 1, 28, 0, 1, 25, 0, -1, 24, 0, 0, 24, 0, 3, 24, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 31, 0, 3, 32, 0, 3, 33, 0, 3, 34, 0, 3, 35, 0, 3, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 1, 46, 0, 1, 46, 0, 3, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, -2, 47, 0, 0, 45, 0, -2, 48, 0, 7, 1, 3, 6, 29, 4, 6, 29, 5, 6, 30, 6, 6, 30, 7, 6, 12, 10, 6, 19, 147], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49], [4, 4, 4, 4, 4, 2, 2, 2, 2, 4, 2, 4, 4, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 9], [1, 1, 1, 1, 6, 7, 2, 8, 2, 0, 9, 0, 0, 10, 11, 3, 12, 3, 13, 4, 14, 4, 5, 5]]]]