[1, ["80pt/43AZPnaLIAxcNqh3d", "87KJdfUjNA1pqHCGJqumKH", "c1CT58ndZMeKnGKKaoK8m+", "d0FmJQ79tA4IthGAk9i2K6@f9941", "14I5OoJkFHdaecy/4b2eXK", "80OqXHI3BB0Kfw4LZ0sXr0", "e52WjWPthMfaIAVaBGR0gr", "2etrlQE9RP4YFE0X54+JYV@f9941", "03PF2TGrlGZLRts5PeD6Yi@f9941", "622je7I3dHiIx9vsWb3KE2", "208m2EU+lBRpwKCxPxLV1h@f9941", "2av/+acrlCNq680y8eaX2f@f9941", "e0sLVzT0RKqJuKRmjkou3I@f9941", "3bNGi1wppDeaOf7RqIm9Sm@f9941", "51lyu5+GtKAKvqrcIDW40V@f9941", "f3r81OnwxOW7GyQOcsDXFp", "38aXRfwBtGJ4AdL6LSBawu@f9941", "a1IOC+dm1EAo+x7tFQuRBY@f9941", "26qSDlZtJO+JKe1m5B/Eag@f9941", "6bAK8rf8pPE54WiDqq1t7L@f9941", "b4Kl7ucq1AyZ84IiEUyK6K@f9941", "12CGLetYxEn5JY6OsQY9mT@f9941"], ["node", "targetInfo", "root", "asset", "_spriteFrame", "_parent", "value", "_normalSprite", "_target", "data"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_children", "_parent", "_lpos"], -1, 4, 9, 2, 1, 5], ["cc.Widget", ["_alignFlags", "_top", "_left", "_right", "_originalWidth", "_originalHeight", "node", "__prefab"], -3, 1, 4], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedComponents"], 2, 1, 9, 9], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Layout", ["_layoutType", "_paddingTop", "_resizeMode", "_paddingLeft", "_paddingRight", "_paddingBottom", "_spacingY", "_constraint", "_constraintNum", "node", "__prefab"], -6, 1, 4], ["47052uw/Y5O1LXaLObj4ARx", ["watchPath", "valueA", "node", "__prefab", "watchNodes"], 1, 1, 4, 2], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 6], ["cc.TargetInfo", ["localID"], 2], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 9], ["cc.CompPrefabInfo", ["fileId"], 2], ["4bff6xrIC5LtYScAfbMmEdR", ["_dataID", "node", "__prefab"], 2, 4, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_target", "_normalSprite"], 2, 1, 4, 1, 6], ["57053nawwtNYJmviHWY0LN+", ["node", "__prefab"], 3, 1, 4]], [[11, 0, 1, 2, 3], [16, 0, 2], [18, 0, 2], [12, 0, 1, 2, 2], [9, 0, 1, 2, 3, 4, 5, 5], [13, 0, 1, 2, 2], [4, 0, 1, 2, 1], [0, 2, 3, 7, 4, 3], [8, 0, 1, 2, 3, 4, 5, 4], [3, 0, 1, 2, 2], [14, 0, 1, 2, 3], [0, 0, 1, 7, 6, 5, 4, 8, 3], [2, 0, 1, 2, 3, 4, 3], [2, 2, 3, 4, 1], [0, 0, 1, 6, 5, 4, 3], [3, 0, 1, 3, 2, 2], [17, 0, 1, 1], [0, 0, 1, 7, 5, 4, 8, 3], [1, 0, 4, 5, 6, 7, 4], [0, 0, 1, 6, 5, 4, 8, 3], [1, 0, 6, 7, 2], [1, 0, 2, 3, 1, 4, 6, 7, 6], [1, 0, 2, 6, 7, 3], [7, 0, 2], [0, 0, 1, 7, 6, 5, 4, 3], [0, 0, 2], [10, 0, 1, 2, 3, 4, 5, 4], [15, 0, 1, 2, 2], [1, 0, 1, 6, 7, 3], [1, 0, 3, 1, 6, 7, 4], [4, 0, 1, 1], [2, 0, 2, 3, 4, 2], [5, 0, 1, 9, 10, 3], [5, 2, 0, 3, 4, 1, 5, 6, 7, 8, 9, 10, 10], [19, 0, 1, 2, 2], [6, 0, 1, 2, 3, 4, 3], [6, 0, 2, 3, 4, 2], [20, 0, 1, 2, 3, 4, 2], [21, 0, 1, 1]], [[23, "PersonInfo"], [14, "PersonInfo", 33554432, [-18, -19], [[6, -15, [2, "77N2cid5pKDpXplRH/AWEU"], [5, 749.9999999999999, 1334]], [18, 45, 2, 2, -16, [2, "63zNQq8NlBQ5QWzOJ4Kgjs"]], [38, -17, [2, "27PEsFUVpOZql3uqKN41v2"]]], [26, "a0daVw8DRLi6ToMaTA0VS2", null, null, -14, 0, [-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13]]], [14, "content", 33554432, [-23, -24, -25], [[6, -20, [2, "4aZQCb2DdDqYTM4z0iAuRw"], [5, 590, 820]], [12, 1, 0, -21, [2, "53SRtbqZNDTKbIxpjbBlwM"], 22], [33, 1, 3, 50, 50, 50, 50, 20, 2, 1, -22, [2, "620O/rm/ZEb6YX0ZxV4CmW"]]], [4, "c9Cl0DW4JF4ZSXUsxlcb3i", null, null, null, 1, 0]], [19, "join", 33554432, [-30, -31], [[6, -26, [2, "fbdDrP3+BNPaLmCmEc2jkr"], [5, 345, 95]], [12, 1, 0, -27, [2, "c8Nw4kUMNIPLgo/6Ajof6t"], 5], [20, 4, -28, [2, "47MP5bSsxPOJKM+X3+xWYi"]], [32, 2, 6, -29, [2, "b28kM0w7tKwKKPNOq4K9QK"]]], [4, "f65/bqkwNLVIskojn8Ti5Q", null, null, null, 1, 0], [1, 0, -32.5, 0]], [11, "down", 33554432, 2, [-34, -35, -36, -37], [[6, -32, [2, "66gb+tcChBxZMhS3cuW/nP"], [5, 500, 410]], [12, 1, 0, -33, [2, "33vNnFHLRCJb6/EmobjXyY"], 21]], [4, "96amr5mSNDCo+oOae2VM0H", null, null, null, 1, 0], [1, 5, -155, 0]], [14, "小底已通过", 33554432, [-43], [[6, -38, [2, "2cpnW6KvRK56C2UvOh9yxf"], [5, 300, 38]], [12, 1, 0, -39, [2, "8bIK7k8uxFmqXU7WvIE713"], 17], [18, 45, 300, 38, -40, [2, "78k2w39AlA0asGnslbmfoL"]], [35, "*.<PERSON><PERSON><PERSON>", 1, -42, [2, "56XK969spOf4RHIoNeAAeE"], [-41]]], [4, "10hmGWNTBCZ5qRITZT+zWL", null, null, null, 1, 0]], [14, "小底未通过", 33554432, [-49], [[6, -44, [2, "f1b+uySAFEmpPvojvF3FdH"], [5, 300, 38]], [12, 1, 0, -45, [2, "23WTB1GRBPLaESNnPdIJ9B"], 19], [18, 45, 32, 38, -46, [2, "1a9SmY+o9BXprTOz1Vyk1j"]], [36, "*.<PERSON><PERSON><PERSON>", -48, [2, "19f0QA3YpDtL6ff/DMh3f/"], [-47]]], [4, "a7ql+zBbxJEaP2muLfuAza", null, null, null, 1, 0]], [1, ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]], [11, "邀请有礼 底", 33554432, 4, [-53, -54], [[6, -50, [2, "27J/V91WJJZaeiNoUnKsgd"], [5, 327, 55]], [13, -51, [2, "30CSRXu/dAHaZ+7OPhYFHO"], 13], [28, 17, 28, -52, [2, "a2tVklQ1dNZoubUoPinmnF"]]], [4, "60BAj5EelHGLzQQXXr73JZ", null, null, null, 1, 0], [1, 0, 149.5, 0]], [19, "title", 33554432, [-58, -59], [[6, -55, [2, "943ZT+JMpIg4H+057tLZ+K"], [5, 590, 73]], [12, 1, 0, -56, [2, "89STazpY1N5Z9KKWJdZi0U"], 26], [21, 17, 318.5, 315.5, -384.586, 116, -57, [2, "22b7FbYP9OSKfswX02ulXr"]]], [4, "62ST9SqUdAKYKJDa7YLhvi", null, null, null, 1, 0], [1, 0, 398.086, 0]], [1, ["4a5atXBglJxJGAlAL90RE0"]], [17, "closeBtn", 33554432, 9, [[6, -60, [2, "feQxtseBBAmr5IgkGIjIrP"], [5, 68, 69]], [31, 1, -61, [2, "83jvKbhlhGpJxzBhUNwwr0"], 24], [37, 3, -63, [2, "2czDRzWc9BT5qW4SCj8uky"], -62, 25], [29, 33, -10.663000000000011, -12.970000000000027, -64, [2, "86W4DjDxNJaIYMfh3omAbf"]]], [4, "a95kLmHpBByqsklSf271iu", null, null, null, 1, 0], [1, 271.663, 14.970000000000027, 0]], [24, "info", 33554432, 1, [2, 9, -66], [[30, -65, [2, "01JOSufuxD2b+12LNVCnrm"]]], [4, "87st9QOS1Ma5hMOrGh/5kp", null, null, null, 1, 0]], [11, "mid", 33554432, 2, [-69, -70], [[6, -67, [2, "36uIH+R5RGP6yZRmG8OehG"], [5, 500, 110]], [12, 1, 0, -68, [2, "53/LurpxFPHJ5UaYdSVyxT"], 10]], [4, "a5esKyNJxFsb5jp2zpqT6r", null, null, null, 1, 0], [1, 5, 125, 0]], [11, "标记", 33554432, 13, [-74], [[6, -71, [2, "88fSHmLulN6qDIdbfdiWpO"], [5, 25, 31]], [13, -72, [2, "b5cRc9BGZOcrHRSuk2H3sK"], 7], [22, 10, 20, -73, [2, "f7/pEsM/dFBKSm7ip6aWQ6"]]], [4, "94Sj+zPKtOypR1qQt+DEwQ", null, null, null, 1, 0], [1, -217.5, 0, 0]], [1, ["4a5atXBglJxJGAlAL90RE0"]], [1, ["4a5atXBglJxJGAlAL90RE0"]], [1, ["a0daVw8DRLi6ToMaTA0VS2"]], [11, "top", 33554432, 2, [-76, -77], [[6, -75, [2, "86mtK/Vs5CZps9k5bgBstg"], [5, 500, 160]]], [4, "edMTQvghtPnoKNMcTpRxmG", null, null, null, 1, 0], [1, 5, 280, 0]], [7, 0, {}, 18, [8, "b2LyNAFEdCsIkChnpFdNTi", null, null, -85, [15, "e0AXo1n4ZDzYNNXbObynR3", 1, [[16, [1, ["b2LyNAFEdCsIkChnpFdNTi"]], [[20, 8, -84, [2, "83qKcxJGJM5Zib7HLNtNBK"]]]]], [[0, "avatar", ["_name"], -78], [3, ["_lpos"], -79, [1, -181, 0, 0]], [3, ["_lrot"], -80, [3, 0, 0, 0, 1]], [3, ["_euler"], -81, [1, 0, 0, 0]], [0, "avatarImg", ["_name"], -82], [5, ["_contentSize"], [1, ["fagAZlPmdJDYrk089Nl/sl"]], [5, 138, 151]], [0, true, ["_active"], -83]]], 1]], [1, ["b2LyNAFEdCsIkChnpFdNTi"]], [11, "info", 33554432, 18, [-87, 3], [[6, -86, [2, "00Lkeap5NJYpHr3Ccb/28l"], [5, 355, 160]]], [4, "e3zKlICv1PFZRiwZ+JZi99", null, null, null, 1, 0], [1, 72.5, 0, 0]], [7, 0, {}, 21, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -99, [15, "7e2xCz43xI/IVXLckctAnt", 1, [[16, [1, ["f05XX5jrpEOYwv6lCoUIav"]], [[21, 17, 94.41552734375, 94.41552734375, 9.883000000000017, 166.1689453125, -98, [2, "b4VR1gISJLmabF6uR0oAc+"]]]]], [[0, "name", ["_name"], -88], [3, ["_lpos"], -89, [1, 0, 51.216999999999985, 0]], [3, ["_lrot"], -90, [3, 0, 0, 0, 1]], [3, ["_euler"], -91, [1, 0, 0, 0]], [5, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 163.3125, 37.8]], [0, "nickname", ["_string"], -92], [0, 26, ["_fontSize"], -93], [0, 26, ["_actualFontSize"], -94], [3, ["_color"], -95, [4, 4282481837]], [0, "role.nick<PERSON>ame", ["watchPath"], -96], [0, "role.nick<PERSON>ame", ["_dataID"], -97]]], 2]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, ["4a5atXBglJxJGAlAL90RE0"]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, ["4a5atXBglJxJGAlAL90RE0"]], [1, ["48BGi+JnJOKpaEdqvfVVS1"]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, ["47zQiH5ZpHGZOMEAJOGjcx"]], [7, 0, {}, 14, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -107, [15, "c27DPVRahFsqVQ1hmgVTZH", 1, [[16, [1, ["f05XX5jrpEOYwv6lCoUIav"]], [[22, 10, 13.43225000000001, -106, [2, "9bpmiMr8NKTKnyblxdtlmm"]]]]], [[0, "countryCode", ["_name"], -100], [3, ["_lpos"], -101, [1, 82.40100000000001, 0, 0]], [3, ["_lrot"], -102, [3, 0, 0, 0, 1]], [3, ["_euler"], -103, [1, 0, 0, 0]], [0, "countryCode", ["_string"], 15], [5, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 162.9375, 37.8]], [3, ["_color"], 15, [4, 4282481837]], [0, 22, ["_fontSize"], 15], [0, 22, ["_actualFontSize"], 15], [0, 0, ["_horizontalAlign"], 15], [0, "role.countryCode", ["watchPath"], -104], [0, "role.countryCode", ["_dataID"], -105]]], 6]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, ["30Ahna/hFADJL5JEyiP8Kc"]], [1, ["4a5atXBglJxJGAlAL90RE0"]], [11, "投影", 33554432, 4, [-110], [[6, -108, [2, "5b4xd7MAlBIbZyf6EO2873"], [5, 149, 39]], [13, -109, [2, "580PDwJfdI64wNlmy/zCPI"], 15]], [4, "7cgovbbPxAxbC8De7PBg2n", null, null, null, 1, 0], [1, 0, -57.166, 0]], [11, "Node", 33554432, 4, [5, 6], [[6, -111, [2, "abvgvIPItHpb5AjxLBLEyP"], [5, 300, 38]]], [4, "76wnxMBcNMzJNmaNK24Y71", null, null, null, 1, 0], [1, 0, -121.469, 0]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, ["47zQiH5ZpHGZOMEAJOGjcx"]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, ["48BGi+JnJOKpaEdqvfVVS1"]], [7, 0, {}, 1, [8, "a0daVw8DRLi6ToMaTA0VS2", null, null, -112, [9, "4bd7+FLBlMqJQRLHhjl3Px", 1, [[0, "mask", ["_name"], 17], [3, ["_lpos"], 17, [1, 0, 0, 0]], [3, ["_lrot"], 17, [3, 0, 0, 0, 1]], [3, ["_euler"], 17, [1, 0, 0, 0]]]], 0]], [7, 0, {}, 3, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -113, [9, "c4deIIQWNOX7mqSyAFsBs1", 1, [[0, "jonInTimeLabel", ["_name"], 25], [3, ["_lpos"], 25, [1, 0, 22.6, 0]], [3, ["_lrot"], 25, [3, 0, 0, 0, 1]], [3, ["_euler"], 25, [1, 0, 0, 0]], [0, "2024/11/11", ["_string"], 26], [5, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 133.125, 37.8]], [3, ["_color"], 26, [4, 4282481837]], [0, 24, ["_fontSize"], 26], [0, 24, ["_actualFontSize"], 26], [0, true, ["templateMode"], 27], [0, 1, ["watchPathArr", "length"], 27], [0, "*.createtime", ["watchPathArr", "0"], 27], [0, "RegisterTime", ["_dataID"], 27]]], 3]], [7, 0, {}, 3, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -114, [9, "0a5s5qHllMQrs3SqwJwp6p", 1, [[0, "passTimeLabel", ["_name"], 28], [3, ["_lpos"], 28, [1, 0, -12.679999999999996, 0]], [3, ["_lrot"], 28, [3, 0, 0, 0, 1]], [3, ["_euler"], 28, [1, 0, 0, 0]], [10, "pass", ["_string"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [5, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 107.625, 32.76]], [0, "PassTimes", ["_dataID"], 29], [0, true, ["templateMode"], 29], [0, 1, ["watchPathArr", "length"], 29], [0, "role.passTimes", ["watchPathArr", "0"], 29]]], 4]], [7, 0, {}, 13, [8, "30Ahna/hFADJL5JEyiP8Kc", null, null, -115, [15, "f7T3myHJpJCqEGNB/Fl+6F", 1, [[16, [1, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [[34, "ModifyArea", [25, "New Node"], [2, "03cZZBD5BE04qK/8Iudydn"]]]]], [[0, "vmButton", ["_name"], 32], [3, ["_lpos"], 32, [1, 141.58000000000004, 0, 0]], [3, ["_lrot"], 32, [3, 0, 0, 0, 1]], [3, ["_euler"], 32, [1, 0, 0, 0]], [27, ["_spriteFrame"], [1, ["daO3pUYmlKVIgsdc588k+9"]], 9], [0, "ModifyArea", ["_string"], 7], [5, ["_contentSize"], [1, ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 135.3203125, 54.4]], [5, ["_contentSize"], [1, ["b2FpfgEc1Py4FLQrPbDjfF"]], [5, 158, 68]], [3, ["_color"], 7, [4, 4294967295]], [0, 26, ["_fontSize"], 7], [0, 26, ["_actualFontSize"], 7], [0, true, ["_enableOutline"], 7], [3, ["_outlineColor"], 7, [4, 4279916581]], [5, ["_lpos"], [1, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 2.1580000000000155, 0]]]], 8]], [17, "灯光", 33554432, 8, [[6, -116, [2, "daYxKWDhNLAblCCkk2LSVO"], [5, 285, 222]], [13, -117, [2, "a12P0/ER5FO60a0pXDnIgD"], 11]], [4, "9dyzOj/eRLMYLs1SO86ziD", null, null, null, 1, 0], [1, 0, -85.8889999999999, 0]], [7, 0, {}, 8, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -118, [9, "ffX0o8tORMfb04qPjoTsoF", 1, [[10, "VMLabelLanguage_White", ["_name"], [1, ["f05XX5jrpEOYwv6lCoUIav"]]], [5, ["_lpos"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 4.982999999999947, 0]], [5, ["_lrot"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [3, 0, 0, 0, 1]], [5, ["_euler"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 0, 0]], [10, 26, ["_fontSize"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [5, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 146.0859375, 39.28]], [10, 26, ["_actualFontSize"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [0, "InvitationGift", ["_string"], 33], [0, true, ["_enableOutline"], 33], [3, ["_outlineColor"], 33, [4, 4281221017]], [0, 28, ["_lineHeight"], 33], [10, "InvitationGift", ["_dataID"], [1, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]]]], 12]], [17, "松鼠", 33554432, 34, [[6, -119, [2, "8f0eaRR8FGYL0yQKAL3hW1"], [5, 134, 149]], [13, -120, [2, "a4+RRRgyRGJLfUIwztNbw0"], 14]], [4, "a8skFVKwRCQJukllOJ714w", null, null, null, 1, 0], [1, 0, 59.79899999999998, 0]], [7, 0, {}, 5, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -121, [9, "93gjAcl0BKNK0kEi7ruzy4", 1, [[0, "VMLabelLanguage_Yellow", ["_name"], 36], [3, ["_lpos"], 36, [1, 0, 0, 0]], [3, ["_lrot"], 36, [3, 0, 0, 0, 1]], [3, ["_euler"], 36, [1, 0, 0, 0]], [5, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 209.6875, 30.240000000000002]], [10, "CompletedChallenges", ["_dataID"], [1, ["36m7MuCL5KzbRIiB/pDpxj"]]]]], 16]], [7, 0, {}, 6, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -122, [9, "bff6xcsixPm4wvObUjhsXO", 1, [[0, "VMLabelLanguage_White", ["_name"], 37], [3, ["_lpos"], 37, [1, 0, 0, 0]], [3, ["_lrot"], 37, [3, 0, 0, 0, 1]], [3, ["_euler"], 37, [1, 0, 0, 0]], [0, 22, ["_fontSize"], 16], [5, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 127.9609375, 30.240000000000002]], [0, 22, ["_actualFontSize"], 16], [0, 24, ["_lineHeight"], 16], [0, false, ["_isBold"], 16], [0, "NoPassLevels", ["_string"], 16], [10, "NoPassLevels", ["_dataID"], [1, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]]]], 18]], [7, 0, {}, 4, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -123, [9, "f4kZnDOOBOz4W+1Fe4WDcX", 1, [[3, ["_lpos"], 38, [1, 0, -170.065, 0]], [0, "record", ["_name"], 38], [3, ["_lrot"], 38, [3, 0, 0, 0, 1]], [3, ["_euler"], 38, [1, 0, 0, 0]], [5, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 145.125, 32.76]], [0, "ContentToday", ["_dataID"], 39], [0, true, ["templateMode"], 39], [0, 1, ["watchPathArr", "length"], 39], [0, "*.lbl<PERSON><PERSON><PERSON>", ["watchPathArr", "0"], 39]]], 20]], [7, 0, {}, 9, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -124, [9, "14LBNjJuJBW7K0o2fekUeg", 1, [[0, "title", ["_name"], 40], [3, ["_lpos"], 40, [1, 0, 0, 0]], [3, ["_lrot"], 40, [3, 0, 0, 0, 1]], [3, ["_euler"], 40, [1, 0, 0, 0]], [0, true, ["_enableOutline"], 10], [5, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 266.0390625, 48.1]], [0, "Settings", ["_string"], 10], [0, 34, ["_fontSize"], 10], [0, 34, ["_actualFontSize"], 10], [3, ["_outlineColor"], 10, [4, 4285882397]], [0, 35, ["_lineHeight"], 10], [10, "PlayerInformation", ["_dataID"], [1, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]]]], 23]], [7, 0, {}, 12, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -125, [9, "d4Z9pMomhEcaGWdCOeqQ1c", 1, [[0, "uuid", ["_name"], 41], [3, ["_lpos"], 41, [1, 0, -381.57, 0]], [3, ["_lrot"], 41, [3, 0, 0, 0, 1]], [3, ["_euler"], 41, [1, 0, 0, 0]], [10, "uid:00000", ["_string"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [5, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 63.546875, 37.8]], [0, true, ["templateMode"], 42], [0, 1, ["watchPathArr", "length"], 42], [0, "role.guuid", ["watchPathArr", "0"], 42], [0, "UUID", ["_dataID"], 42]]], 27]], [1, ["5a8880C91AI6EEZ9EsW7dy"]], [1, ["4dgANpwkxN8pTx4nabOESy"]], [1, ["48BGi+JnJOKpaEdqvfVVS1"]]], 0, [0, -1, 54, 0, -2, 53, 0, -3, 52, 0, -4, 51, 0, -5, 50, 0, -6, 48, 0, -7, 46, 0, -8, 30, 0, -9, 45, 0, -10, 44, 0, -11, 22, 0, -12, 19, 0, -13, 43, 0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 43, 0, -2, 12, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 18, 0, -2, 13, 0, -3, 4, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 44, 0, -2, 45, 0, 0, 4, 0, 0, 4, 0, -1, 8, 0, -2, 34, 0, -3, 35, 0, -4, 52, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 5, 0, 0, 5, 0, -1, 50, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 6, 0, 0, 6, 0, -1, 51, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 47, 0, -2, 48, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 53, 0, -2, 11, 0, 0, 11, 0, 0, 11, 0, 8, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -3, 54, 0, 0, 13, 0, 0, 13, 0, -1, 14, 0, -2, 46, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, -1, 30, 0, 0, 18, 0, -1, 19, 0, -2, 21, 0, 1, 20, 0, 1, 20, 0, 1, 20, 0, 1, 20, 0, 1, 55, 0, 1, 55, 0, 0, 19, 0, 2, 19, 0, 0, 21, 0, -1, 22, 0, 1, 23, 0, 1, 23, 0, 1, 23, 0, 1, 23, 0, 1, 24, 0, 1, 24, 0, 1, 24, 0, 1, 24, 0, 1, 56, 0, 1, 56, 0, 0, 22, 0, 2, 22, 0, 1, 31, 0, 1, 31, 0, 1, 31, 0, 1, 31, 0, 1, 57, 0, 1, 57, 0, 0, 30, 0, 2, 30, 0, 0, 34, 0, 0, 34, 0, -1, 49, 0, 0, 35, 0, 2, 43, 0, 2, 44, 0, 2, 45, 0, 2, 46, 0, 0, 47, 0, 0, 47, 0, 2, 48, 0, 0, 49, 0, 0, 49, 0, 2, 50, 0, 2, 51, 0, 2, 52, 0, 2, 53, 0, 2, 54, 0, 9, 1, 2, 5, 12, 3, 5, 21, 5, 5, 35, 6, 5, 35, 9, 5, 12, 125], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [3, 3, 3, 3, 3, 4, 3, 4, 3, 6, 4, 4, 3, 4, 4, 4, 3, 4, 3, 4, 3, 4, 4, 3, 4, 7, 4, 3], [4, 5, 6, 0, 2, 7, 0, 8, 9, 10, 3, 11, 1, 12, 13, 14, 15, 16, 1, 17, 2, 3, 18, 1, 19, 20, 21, 0]]