[1, ["5dZJ5VKmVMn4WQldoN7+vA@f9941"], ["node", "_spriteFrame", "root", "_target", "data"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_parent", "_children", "_lpos"], 0, 9, 4, 1, 2, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_cacheMode", "_overflow", "_enableWrapText", "node", "__prefab", "_color"], -3, 1, 4, 5], ["cc.UITransform", ["_name", "node", "__prefab", "_contentSize"], 2, 1, 4, 5], ["cc.Sprite", ["_sizeMode", "_name", "_type", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["110c8vEd5NEPL/N9meGQnaX", ["_name", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON>", ["_name", "_transition", "_zoomScale", "node", "__prefab", "_normalColor", "_target"], 0, 1, 4, 5, 1], ["cc.Widget", ["_alignFlags", "_right", "_bottom", "node", "__prefab"], 0, 1, 4], ["16137VJ7E1Febf119tRFbk4", ["node", "__prefab"], 3, 1, 4]], [[5, 0, 2], [6, 0, 1, 2, 3, 4, 5, 5], [2, 1, 2, 3, 1], [0, 0, 1, 5, 3, 4, 7, 3], [4, 0, 2], [0, 0, 1, 6, 3, 4, 3], [0, 0, 2, 1, 5, 6, 3, 4, 7, 4], [0, 0, 2, 1, 5, 3, 4, 4], [2, 0, 1, 2, 3, 2], [1, 0, 1, 2, 3, 6, 7, 5], [1, 0, 1, 2, 4, 3, 6, 7, 6], [1, 0, 1, 2, 4, 5, 3, 6, 7, 8, 7], [7, 0, 1, 2, 2], [3, 1, 2, 0, 3, 4, 5, 4], [3, 0, 3, 4, 5, 2], [8, 0, 1, 2, 3, 4, 5, 6, 4], [9, 0, 1, 2, 3, 4, 4], [10, 0, 1, 1]], [[4, "Pop1"], [5, "Pop1", 33554432, [-5, -6, -7], [[2, -2, [0, "37o1ko8nFK1oK3xmkqtwMG"], [5, 478, 320]], [14, 0, -3, [0, "a0vC/W2QdFb7e69t18Gf21"], 1], [17, -4, [0, "b0ZYxCknJONKYZk4Ie5hPm"]]], [1, "c7ZN3fr+hJoZmUZs2A0Ye0", null, null, null, -1, 0]], [6, "btnCancel", 512, 33554432, 1, [-13], [[8, "btn_cancel<UITransform>", -8, [0, "79W4eqKFxMapzMreg0cRB3"], [5, 100, 40]], [13, "btn_cancel<Sprite>", 1, 0, -9, [0, "08v+UeZXZH+a5Tu0BOuubs"], 0], [15, "btn_cancel<Button>", 3, 0.9, -11, [0, "23mGTQh9JPcaNniCjfDZ/c"], [4, 4292269782], -10], [16, 36, 189, 40, -12, [0, "ffeyp+r3tEe67JwpieOoNw"]]], [1, "3aIfIm6bVFaK62tZkb2Iic", null, null, null, 1, 0], [1, 0, -100, 0]], [7, "lblBtnStr", 512, 33554432, 2, [[2, -14, [0, "73nkyWjZ9I/oxF+6QfBLqr"], [5, 100, 40]], [11, "关闭", 20, 20, 1, false, 1, -15, [0, "0eYsv4mJlIa7/bSpPQvt+7"], [4, 4278190080]], [12, "lab_ok<LanguageLabel>", -16, [0, "c5vY7CEaJOBbvqXXwmsDAU"]]], [1, "38cZMAuzVCOJPotiPg+6BM", null, null, null, 1, 0]], [3, "lblTitle", 33554432, 1, [[2, -17, [0, "1c9xohlJtL3bm53voN5DeV"], [5, 123.3544921875, 50.4]], [9, "POP窗口", 30, 30, 1, -18, [0, "26eZlgc6xJWqYRq4rBoG6R"]]], [1, "42TIPOfhhM7ra85tXw74QR", null, null, null, 1, 0], [1, 0, 114, 0]], [3, "lblContent", 33554432, 1, [[2, -19, [0, "49SU+NRCVFC7lotcT5Arsj"], [5, 420, 50.4]], [10, "1号POP窗口", 26, 26, 3, 1, -20, [0, "867mEteZhIVLc5LxzUTUW5"]]], [1, "7fk7nP/NJBI7+oI60VkgZO", null, null, null, 1, 0], [1, 0, 12, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 2, 0, 0, 2, 0, 0, 2, 0, 3, 2, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 4, 1, 20], [0, 0], [1, 1], [0, 0]]