{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/scenes/Game/GameEntity.ts"], "names": ["EcsSceneSystem", "Collider", "instantiate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Node", "Prefab", "RigidBody", "Vec3", "Vec4", "oops", "ecs", "PropType", "SceneType", "smc", "EventMessage", "ClientConst", "SceneItemType", "GameEvent", "simpleLoader", "ItemEntity", "ItemSceneViewComp", "WallSceneViewComp", "GameSceneViewComp", "BaseSceneEntity", "GameModelComp", "GameUIViewComp", "config<PERSON><PERSON><PERSON>", "GameState", "UnifiedGameManager", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UIID", "gameAudioManager", "levelProgressManager", "CollectionSlotManager", "GameResultManager", "ItemInteractionManager", "ItemInteractionManagerComp", "GameEntity", "register", "GameModel", "GameUIView", "GameSceneView", "WallSceneView", "gameManager", "interactionManager", "gameResultManager", "isDissolveAnimating", "hardModeResourcesLoaded", "init", "add", "listenEvent", "initializeGameManager", "initialize", "setupStateCallbacks", "log", "logBusiness", "error", "logError", "onStateChange", "SimpleMode", "setupSimpleMode", "updateGameStateInViewModel", "HardMode", "setupHardMode", "Win", "handleGameWin", "GameOver", "handleGameFail", "Paused", "Loading", "currentState", "getCurrentState", "vmdata", "gameState", "isSimpleMode", "isHardMode", "isGameOver", "isWin", "isPaused", "isPlaying", "levelConfig", "getCurrentLevelConfig", "setRefillStrategy", "adjustByPerformance", "adjustByTime", "minInterval", "spawnInterval", "maxInterval", "difficultyMultiplier", "playSimpleGameMusic", "startPreloadingHardModeInBackground", "getDifficultyMultiplier", "role", "getPassIndex", "playHardGameMusic", "isNewPlayer", "RoleModel", "userGameData", "completeNewPlayerGuide", "then", "success", "catch", "costChallengeAttempt", "startPreloadingEndGameResources", "ensureHardModeResourcesPreloaded", "showLoading", "hardModeItems", "length", "gui", "open", "uniqueItemNames", "Array", "from", "Set", "map", "item", "name", "itemPrefabPath", "getItemPrefabPath", "prefabPaths", "loadPrefabs", "remove", "preloadGameResultResources", "preloadHallResources", "res", "loadAsync", "log<PERSON>arn", "sceneMgr", "preloadScene", "Hall", "onHide", "setInputEnabled", "message", "on", "GAME_HIDE", "UseProp", "onUseProp", "unListenEvent", "off", "chooseItem", "inputEnabled", "isInputEnabled", "dissolveAnimating", "chooseExtraItem", "destroy", "console", "clear", "loadUIAndScene", "index", "onEssentialLoaded", "level", "waitForGameManagerReady", "guide", "load", "loadLevel", "maxWaitTime", "checkInterval", "waitedTime", "Promise", "resolve", "setTimeout", "Error", "createEmergencyFloor", "floorNode", "parent", "game", "root", "setPosition", "setScale", "rigidBody", "addComponent", "type", "Type", "STATIC", "enabled", "collider", "loadWallAndScene", "gameSceneViewPrefab", "addViewAsync", "ent", "node", "getComponent", "wallSceneNode", "getChildByName", "wallSceneComp", "loadScene", "startGame", "destroyItem", "itemEntity", "ItemModel", "pickBox", "freeItem", "itemId", "undefined", "allItemEntitys", "delete", "checkThreshold", "checkAndRefillItems", "createItemOnPos", "<PERSON><PERSON><PERSON>", "itemName", "randomRotation", "itemConfigs", "easyModeItems", "foundItem", "find", "getEntity", "itemType", "Foods", "touching", "startScale", "pickScale", "itemPath", "getItemPrefabPathFromConfig", "prefab", "get", "itemNode", "startRotation", "rotation", "clone", "randomY", "Math", "random", "setRotationFromEuler", "<PERSON><PERSON><PERSON><PERSON>", "itemSceneViewComp", "item<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "initializeComponent", "sharedMaterial", "set", "allItemsToPick", "runDissolveAnimation", "duration", "updateInterval", "elapsedTime", "lastProgress", "updateTimer", "setInterval", "setProperty", "clearInterval", "completeDissolveAndEnableInput", "progress", "easedProgress", "easeInOutCubic", "progressDiff", "abs", "t", "pow", "handleInputEnabling", "startDissolveAnimation", "dissolveStartDelay", "dissolveDuration", "dissoveCreatedDuration", "loadGameUI", "uic", "onAdded", "params", "comp", "gameUIID", "loadGameUIWithCallback", "onUIReady", "getCurrentGameState", "wasPreviouslyHardMode", "wasPreviouslySimpleMode", "getCurrentLevelIndex", "itemPrefabPaths", "configured<PERSON><PERSON>", "path", "endsWith", "touchedItem", "doGameFail", "changeState", "doGameWin", "beforeStartGame", "slotManager", "restartFromSimpleMode", "initializeEasyModeItems", "initializeHardModeItems", "createInitialItemsInScene", "checkResult", "collectItemsCount", "collectItems", "allItemsToPickCount", "size", "gameMode", "hasPossibleMerge", "itemCounts", "count", "Object", "values", "_", "args", "handleUseProp", "tryUseProp", "usePropArgs", "toastMsg", "handleSimpleModeToHardMode", "ensureGuideSystemCleanup", "GuideModel", "reset", "guideItemComponents", "getComponentsInChildren", "<PERSON><PERSON><PERSON><PERSON>", "updateProp", "PropsDayLeftCount", "System", "constructor"], "mappings": ";;;4jBAipCaA,c;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjpCJC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AACpEC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,S,iBAAAA,S;;AACVC,MAAAA,G,iBAAAA,G;;AAEAC,MAAAA,Y,iBAAAA,Y;;AAEAC,MAAAA,W,iBAAAA,W;;AAEAC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Y,kBAAAA,Y;;AACAC,MAAAA,U,kBAAAA,U;;AACAC,MAAAA,iB,kBAAAA,iB;;AACAC,MAAAA,iB,kBAAAA,iB;;AACAC,MAAAA,iB,kBAAAA,iB;;AACAC,MAAAA,e,kBAAAA,e;;AACAC,MAAAA,a,kBAAAA,a;;AACAC,MAAAA,c,kBAAAA,c;;AAGAC,MAAAA,a,kBAAAA,a;;AACAC,MAAAA,S,kBAAAA,S;AAAWC,MAAAA,kB,kBAAAA,kB;;AAGXC,MAAAA,U,kBAAAA,U;;AACAC,MAAAA,I,kBAAAA,I;;AACAC,MAAAA,gB,kBAAAA,gB;;AACAC,MAAAA,oB,kBAAAA,oB;;AAGAC,MAAAA,qB,kBAAAA,qB;;AACAC,MAAAA,iB,kBAAAA,iB;;AACAC,MAAAA,sB,kBAAAA,sB;;AAC0BC,MAAAA,0B,kBAA1BD,sB;;;;;;;qHAdT;AAIA;AAMA;;;AAMA;AACA;AACA;AACA;AACA;4BAEaE,U,WADZ;AAAA;AAAA,sBAAIC,QAAJ,c,gBAAD,MACaD,UADb;AAAA;AAAA,8CACgD;AAAA;AAAA;AAC5C;AAD4C,eAE5CE,SAF4C;AAAA,eAG5CC,UAH4C;AAAA,eAI5CC,aAJ4C;AAAA,eAK5CC,aAL4C;AAO5C;AAP4C,eAQrCC,WARqC;AAU5C;AAV4C,eAWrCC,kBAXqC;AAa5C;AAb4C,eAcpCC,iBAdoC;AAgB5C;AAhB4C,eAiBpCC,mBAjBoC,GAiBL,KAjBK;AAmB5C;AAnB4C,eAoBpCC,uBApBoC,GAoBD,KApBC;AAAA;;AAsB5C;AACJ;AACA;AACcC,QAAAA,IAAI,GAAG;AACb,eAAKC,GAAL;AAAA;AAAA;AACA,eAAKC,WAAL,GAFa,CAGb;;AACA,eAAKC,qBAAL,GAJa,CAMb;;AACA,eAAKP,kBAAL,GAA0B;AAAA;AAAA,gEAA2B,IAA3B,CAA1B,CAPa,CASb;;AACA,eAAKC,iBAAL,GAAyB;AAAA;AAAA,sDAAsB,IAAtB,CAAzB;AACH;AAED;AACJ;AACA;;;AACkBM,QAAAA,qBAAqB,GAAkB;AAAA;;AAAA;AACjD,gBAAI;AACA;AACA,oBAAM;AAAA;AAAA,kDAAcC,UAAd,EAAN,CAFA,CAIA;;AACA,cAAA,KAAI,CAACT,WAAL,GAAmB;AAAA;AAAA,4DAAuB,KAAvB,CAAnB,CALA,CAOA;;AACA,cAAA,KAAI,CAACU,mBAAL;;AAEA;AAAA;AAAA,gCAAKC,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH,aAXD,CAWE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKF,GAAL,CAASG,QAAT,CAAkB,eAAlB,EAAmCD,KAAnC;AACH;AAdgD;AAepD;AAED;AACJ;AACA;;;AACYH,QAAAA,mBAAmB,GAAS;AAChC,eAAKV,WAAL,CAAiBe,aAAjB,CAA+B;AAAA;AAAA,sCAAUC,UAAzC,EAAqD,MAAM;AACvD,iBAAKC,eAAL;AACA,iBAAKC,0BAAL,GAFuD,CAEpB;AACtC,WAHD;AAKA,eAAKlB,WAAL,CAAiBe,aAAjB,CAA+B;AAAA;AAAA,sCAAUI,QAAzC,EAAmD,MAAM;AACrD,iBAAKC,aAAL;AACA,iBAAKF,0BAAL,GAFqD,CAElB;AACtC,WAHD;AAKA,eAAKlB,WAAL,CAAiBe,aAAjB,CAA+B;AAAA;AAAA,sCAAUM,GAAzC,EAA8C,MAAM;AAChD,iBAAKC,aAAL;AACA,iBAAKJ,0BAAL,GAFgD,CAEb;AACtC,WAHD;AAKA,eAAKlB,WAAL,CAAiBe,aAAjB,CAA+B;AAAA;AAAA,sCAAUQ,QAAzC,EAAmD,MAAM;AACrD,iBAAKC,cAAL;AACA,iBAAKN,0BAAL,GAFqD,CAElB;AACtC,WAHD;AAKA,eAAKlB,WAAL,CAAiBe,aAAjB,CAA+B;AAAA;AAAA,sCAAUU,MAAzC,EAAiD,MAAM;AACnD,iBAAKP,0BAAL,GADmD,CAChB;AACtC,WAFD;AAIA,eAAKlB,WAAL,CAAiBe,aAAjB,CAA+B;AAAA;AAAA,sCAAUW,OAAzC,EAAkD,MAAM;AACpD,iBAAKR,0BAAL,GADoD,CACjB;AACtC,WAFD;AAGH;AAED;AACJ;AACA;AACA;;;AACWA,QAAAA,0BAA0B,GAAS;AACtC,cAAMS,YAAY,GAAG,KAAK3B,WAAL,CAAiB4B,eAAjB,EAArB,CADsC,CAGtC;;AACA,eAAKhC,SAAL,CAAeiC,MAAf,CAAsBC,SAAtB,GAAkCH,YAAlC;AACA,eAAK/B,SAAL,CAAeiC,MAAf,CAAsBE,YAAtB,GAAqC,KAAK/B,WAAL,CAAiB+B,YAAjB,KAAkC,CAAlC,GAAsC,CAA3E;AACA,eAAKnC,SAAL,CAAeiC,MAAf,CAAsBG,UAAtB,GAAmC,KAAKhC,WAAL,CAAiBgC,UAAjB,KAAgC,CAAhC,GAAoC,CAAvE;AACA,eAAKpC,SAAL,CAAeiC,MAAf,CAAsBI,UAAtB,GAAmC,KAAKjC,WAAL,CAAiBiC,UAAjB,KAAgC,CAAhC,GAAoC,CAAvE;AACA,eAAKrC,SAAL,CAAeiC,MAAf,CAAsBK,KAAtB,GAA8B,KAAKlC,WAAL,CAAiBkC,KAAjB,KAA2B,CAA3B,GAA+B,CAA7D;AACA,eAAKtC,SAAL,CAAeiC,MAAf,CAAsBM,QAAtB,GAAiC,KAAKnC,WAAL,CAAiBmC,QAAjB,KAA8B,CAA9B,GAAkC,CAAnE;AACA,eAAKvC,SAAL,CAAeiC,MAAf,CAAsBO,SAAtB,GACI,KAAKpC,WAAL,CAAiB+B,YAAjB,MAAmC,KAAK/B,WAAL,CAAiBgC,UAAjB,EAAnC,GAAmE,CAAnE,GAAuE,CAD3E;AAGA;AAAA;AAAA,4BAAKrB,GAAL,CAASC,WAAT,wEAA6Ce,YAA7C;AACH;AAED;AACJ;AACA;;;AACYV,QAAAA,eAAe,GAAS;AAC5B,cAAMoB,WAAW,GAAG,KAAKrC,WAAL,CAAiBsC,qBAAjB,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACb,iBAAKrC,WAAL,CAAiBuC,iBAAjB,CAAmC;AAC/BC,cAAAA,mBAAmB,EAAE,KADU;AAE/BC,cAAAA,YAAY,EAAE,KAFiB;AAG/BC,cAAAA,WAAW,EAAEL,WAAW,CAACM,aAAZ,IAA6B,IAHX;AAI/BC,cAAAA,WAAW,EAAE,CAACP,WAAW,CAACM,aAAZ,IAA6B,IAA9B,IAAsC,GAJpB;AAK/BE,cAAAA,oBAAoB,EAAE;AALS,aAAnC;AAOH;;AACD;AAAA;AAAA,oDAAiBC,mBAAjB,GAX4B,CAa5B;;AACA,eAAKC,mCAAL;AACH;AAED;AACJ;AACA;;;AACY3B,QAAAA,aAAa,GAAS;AAC1B,cAAMiB,WAAW,GAAG,KAAKrC,WAAL,CAAiBsC,qBAAjB,EAApB;;AACA,cAAID,WAAJ,EAAiB;AACb,iBAAKrC,WAAL,CAAiBuC,iBAAjB,CAAmC;AAC/BC,cAAAA,mBAAmB,EAAE,IADU;AAE/BC,cAAAA,YAAY,EAAE,IAFiB;AAG/BC,cAAAA,WAAW,EAAEL,WAAW,CAACM,aAAZ,IAA6B,IAHX;AAI/BC,cAAAA,WAAW,EAAE,CAACP,WAAW,CAACM,aAAZ,IAA6B,IAA9B,IAAsC,CAJpB;AAK/BE,cAAAA,oBAAoB,EAAE;AAAA;AAAA,gEAAqBG,uBAArB,CAClB;AAAA;AAAA,8BAAIC,IAAJ,CAASC,YAAT,EADkB;AALS,aAAnC;AASH;;AACD;AAAA;AAAA,oDAAiBC,iBAAjB,GAb0B,CAe1B;;AACA,cAAI;AAAA;AAAA,0BAAIF,IAAJ,CAASG,WAAT,EAAJ,EAA4B;AAAA;;AACxB;AAAA;AAAA,8BAAKzC,GAAL,CAASC,WAAT,CAAqB,+BAArB;AACA;AAAA;AAAA,8BAAKD,GAAL,CAASC,WAAT,qFAC+B;AAAA;AAAA,4BAAIqC,IAAJ,CAASI,SADxC,gCAC+B,gBAAoBC,YADnD,qBAC+B,gBAAkCF,WADjE;AAIA;AAAA;AAAA,4BAAIH,IAAJ,CACKM,sBADL,GAEKC,IAFL,CAEUC,OAAO,IAAI;AACb,kBAAIA,OAAJ,EAAa;AAAA;;AACT;AAAA;AAAA,kCAAK9C,GAAL,CAASC,WAAT,CAAqB,YAArB;AACA;AAAA;AAAA,kCAAKD,GAAL,CAASC,WAAT,sFAC+B;AAAA;AAAA,gCAAIqC,IAAJ,CAASI,SADxC,iCAC+B,iBAAoBC,YADnD,qBAC+B,iBAAkCF,WADjE;AAGH,eALD,MAKO;AACH;AAAA;AAAA,kCAAKzC,GAAL,CAASG,QAAT,CAAkB,YAAlB;AACH;AACJ,aAXL,EAYK4C,KAZL,CAYW7C,KAAK,IAAI;AACZ;AAAA;AAAA,gCAAKF,GAAL,CAASG,QAAT,CAAkB,aAAlB,EAAiCD,KAAjC;AACH,aAdL;AAeH,WArBD,MAqBO;AACH;AAAA;AAAA,8BAAKF,GAAL,CAASC,WAAT,CAAqB,8BAArB;AACH,WAvCyB,CAyC1B;;;AACA,eAAK+C,oBAAL,GACKH,IADL,CACUC,OAAO,IAAI;AACb,gBAAIA,OAAJ,EAAa;AACT;AAAA;AAAA,gCAAK9C,GAAL,CAASC,WAAT,CAAqB,gBAArB;AACH,aAFD,MAEO;AACH;AAAA;AAAA,gCAAKD,GAAL,CAASC,WAAT,CAAqB,gBAArB;AACH;AACJ,WAPL,EAQK8C,KARL,CAQW7C,KAAK,IAAI;AACZ;AAAA;AAAA,8BAAKF,GAAL,CAASG,QAAT,CAAkB,iBAAlB,EAAqCD,KAArC;AACH,WAVL,EA1C0B,CAsD1B;;AACA,eAAK+C,+BAAL;AACH;AAED;AACJ;AACA;;;AACkBtC,QAAAA,aAAa,GAAkB;AAAA;;AAAA;AACzC;AAAA;AAAA,8BAAKX,GAAL,CAASC,WAAT,CAAqB,aAArB,EADyC,CAGzC;;AACA,kBAAM,MAAI,CAACV,iBAAL,CAAuBoB,aAAvB,EAAN;AAJyC;AAK5C;AAED;AACJ;AACA;;;AACkBE,QAAAA,cAAc,GAAkB;AAAA;;AAAA;AAC1C;AAAA;AAAA,8BAAKb,GAAL,CAASC,WAAT,CAAqB,aAArB,EAD0C,CAG1C;;AACA,kBAAM,MAAI,CAACV,iBAAL,CAAuBsB,cAAvB,EAAN;AAJ0C;AAK7C;AAED;AACJ;AACA;;;AACYuB,QAAAA,mCAAmC,GAAS;AAChD;AACA,eAAKc,gCAAL,CAAsC,KAAtC,EAA6CH,KAA7C,CAAmD7C,KAAK,IAAI;AACxD;AAAA;AAAA,8BAAKF,GAAL,CAASG,QAAT,CAAkB,kBAAlB,EAAsCD,KAAtC,EADwD,CAExD;AACH,WAHD;AAIH;AAED;AACJ;AACA;AACA;;;AACkBgD,QAAAA,gCAAgC,CAACC,WAAD,EAA8C;AAAA;;AAAA;AAAA,gBAA7CA,WAA6C;AAA7CA,cAAAA,WAA6C,GAAtB,KAAsB;AAAA;;AACxF,gBAAMzB,WAAW,GAAG,MAAI,CAACrC,WAAL,CAAiBsC,qBAAjB,EAApB;;AACA,gBAAI,EAACD,WAAD,YAACA,WAAW,CAAE0B,aAAd,KAA+B1B,WAAW,CAAC0B,aAAZ,CAA0BC,MAA1B,KAAqC,CAAxE,EAA2E;AACvE;AAAA;AAAA,gCAAKrD,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACA,cAAA,MAAI,CAACR,uBAAL,GAA+B,IAA/B;AACA;AACH;;AAED,gBAAI,MAAI,CAACA,uBAAT,EAAkC;AAC9B;AAAA;AAAA,gCAAKO,GAAL,CAASC,WAAT,CAAqB,kBAArB;AACA;AACH;;AAED,gBAAIkD,WAAJ,EAAiB;AACb;AAAA;AAAA,gCAAKG,GAAL,CAASC,IAAT,CAAc;AAAA;AAAA,gCAAKxC,OAAnB;AACH;;AAED,gBAAI;AACA,kBAAMyC,eAAe,GAAGC,KAAK,CAACC,IAAN,CACpB,IAAIC,GAAJ,CAAQjC,WAAW,CAAC0B,aAAZ,CAA0BQ,GAA1B,CAA8BC,IAAI,IAAIA,IAAI,CAACC,IAA3C,CAAR,CADoB,CAAxB,CADA,CAIA;;AACA,kBAAMC,cAAc,GAAG,MAAI,CAACC,iBAAL,EAAvB;;AACA,kBAAMC,WAAW,GAAGT,eAAe,CAACI,GAAhB,CAAoBE,IAAI,SAAOC,cAAP,GAAwBD,IAAhD,CAApB;AAEA,oBAAM;AAAA;AAAA,gDAAaI,WAAb,CAAyBD,WAAzB,CAAN;AACA,cAAA,MAAI,CAACxE,uBAAL,GAA+B,IAA/B;AACA;AAAA;AAAA,gCAAKO,GAAL,CAASC,WAAT,iFAAuCuD,eAAe,CAACH,MAAvD;AACH,aAXD,CAWE,OAAOnD,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKF,GAAL,CAASG,QAAT,CAAkB,gBAAlB,EAAoCD,KAApC;AACA,oBAAMA,KAAN;AACH,aAdD,SAcU;AACN,kBAAIiD,WAAJ,EAAiB;AACb;AAAA;AAAA,kCAAKG,GAAL,CAASa,MAAT,CAAgB;AAAA;AAAA,kCAAKpD,OAArB;AACH;AACJ;AAnCuF;AAoC3F;AAED;AACJ;AACA;AACA;;;AACYkC,QAAAA,+BAA+B,GAAS;AAC5C;AAAA;AAAA,4BAAKjD,GAAL,CAASC,WAAT,CAAqB,uBAArB,EAD4C,CAG5C;;AACA,eAAKmE,0BAAL,GAJ4C,CAM5C;;AACA,eAAKC,oBAAL;AACH;AAED;AACJ;AACA;;;AACkBD,QAAAA,0BAA0B,GAAkB;AAAA;AACtD,gBAAI;AACA;AACA,oBAAM;AAAA;AAAA,gCAAKE,GAAL,CAASC,SAAT,CAAmB,kCAAnB,EAAuDxH,MAAvD,CAAN;AACA;AAAA;AAAA,gCAAKiD,GAAL,CAASC,WAAT,CAAqB,uBAArB;AACH,aAJD,CAIE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKF,GAAL,CAASwE,OAAT,CAAiB,yBAAjB,EAA4CtE,KAA5C;AACH;AAPqD;AAQzD;AAED;AACJ;AACA;;;AACkBmE,QAAAA,oBAAoB,GAAkB;AAAA;AAChD,gBAAI;AACA;AACA,kBAAMvB,OAAO,SAAS;AAAA;AAAA,8BAAI2B,QAAJ,CAAaC,YAAb,CAA0B;AAAA;AAAA,0CAAUC,IAApC,CAAtB;;AACA,kBAAI7B,OAAJ,EAAa;AACT;AAAA;AAAA,kCAAK9C,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH,eAFD,MAEO;AACH;AAAA;AAAA,kCAAKD,GAAL,CAASwE,OAAT,CAAiB,kBAAjB;AACH;AACJ,aARD,CAQE,OAAOtE,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKF,GAAL,CAASwE,OAAT,CAAiB,mBAAjB,EAAsCtE,KAAtC;AACH;AAX+C;AAYnD;AAED;AACJ;AACA;;;AACI0E,QAAAA,MAAM,GAAG;AAAA;;AACL,oCAAKvF,WAAL,+BAAkBwF,eAAlB,CAAkC,KAAlC;AACH;AAED;AACJ;AACA;;;AACIjF,QAAAA,WAAW,GAAG;AACV;AAAA;AAAA,4BAAKkF,OAAL,CAAaC,EAAb,CAAgB;AAAA;AAAA,4CAAaC,SAA7B,EAAwC,KAAKJ,MAA7C,EAAqD,IAArD;AACA;AAAA;AAAA,4BAAKE,OAAL,CAAaC,EAAb,CAAgB;AAAA;AAAA,sCAAUE,OAA1B,EAAmC,KAAKC,SAAxC,EAAmD,IAAnD;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,aAAa,GAAG;AACZ;AAAA;AAAA,4BAAKL,OAAL,CAAaM,GAAb,CAAiB;AAAA;AAAA,4CAAaJ,SAA9B,EAAyC,KAAKJ,MAA9C,EAAsD,IAAtD;AACA;AAAA;AAAA,4BAAKE,OAAL,CAAaM,GAAb,CAAiB;AAAA;AAAA,sCAAUH,OAA3B,EAAoC,KAAKC,SAAzC,EAAoD,IAApD;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,UAAU,CAACxB,IAAD,EAAa;AACnB,cAAMyB,YAAY,GAAG,KAAKjG,WAAL,CAAiBkG,cAAjB,EAArB;AACA,cAAMC,iBAAiB,GAAG,KAAKhG,mBAA/B,CAFmB,CAInB;;AACA;AAAA;AAAA,4BAAKQ,GAAL,CAASC,WAAT,4CACe4D,IAAI,CAACC,IADpB,mCACkCwB,YADlC,mCACwDE,iBADxD;;AAIA,cAAI,CAACF,YAAL,EAAmB;AACf;AAAA;AAAA,8BAAKtF,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACA;AACH,WAZkB,CAcnB;;;AACA,cAAIuF,iBAAJ,EAAuB;AACnB;AAAA;AAAA,8BAAKxF,GAAL,CAASC,WAAT,CAAqB,mBAArB;AACA;AACH,WAlBkB,CAoBnB;;;AACA,eAAKX,kBAAL,CAAwB+F,UAAxB,CAAmCxB,IAAnC;AACH;AAED;AACJ;AACA;;;AACI4B,QAAAA,eAAe,CAAC5B,IAAD,EAAa;AACxB,cAAI,CAAC,KAAKxE,WAAL,CAAiBkG,cAAjB,EAAL,EAAwC;AAExC,eAAKjG,kBAAL,CAAwBmG,eAAxB,CAAwC5B,IAAxC;AACH;AAED;AACJ;AACA;;;AACI6B,QAAAA,OAAO,GAAS;AAAA;;AACZC,UAAAA,OAAO,CAAC3F,GAAR,CAAY,oBAAZ,EADY,CAGZ;;AACA,cAAI,KAAKf,SAAT,EAAoB;AAChB0G,YAAAA,OAAO,CAAC3F,GAAR,CAAY,kBAAZ;AACA,iBAAKf,SAAL,CAAe2G,KAAf;AACH,WAPW,CASZ;;;AACA,cAAI,KAAKtG,kBAAT,EAA6B;AACzB,iBAAKA,kBAAL,CAAwBoG,OAAxB;AACH,WAZW,CAcZ;;;AACA,qCAAKrG,WAAL,gCAAkBqG,OAAlB,GAfY,CAiBZ;;AACA,cAAI,KAAKnG,iBAAT,EAA4B;AACxB,iBAAKA,iBAAL,CAAuBmG,OAAvB;AACH,WApBW,CAsBZ;;;AACA,eAAKP,aAAL,GAvBY,CAyBZ;;AACA,gBAAMO,OAAN;AAEAC,UAAAA,OAAO,CAAC3F,GAAR,CAAY,kBAAZ;AACH;AAED;AACJ;AACA;;;AACU6F,QAAAA,cAAc,CAACC,KAAD,EAAiBC,iBAAjB,EAAgE;AAAA;;AAAA;AAChF,gBAAMC,KAAK,GAAGF,KAAK,IAAI,CAAvB,CADgF,CACtD;;AAC1B,gBAAI;AAAA;;AACA;AAAA;AAAA,gCAAK9F,GAAL,CAASC,WAAT,uFAAwC+F,KAAxC,EADA,CAGA;;AACA,oBAAM,MAAI,CAACC,uBAAL,EAAN,CAJA,CAMA;;AACA,2BAAI;AAAA;AAAA,8BAAI3D,IAAR,aAAI,MAAUG,WAAV,EAAJ,EAA6B;AACzB,oBAAI;AACA,wBAAM;AAAA;AAAA,kCAAIyD,KAAJ,CAAUC,IAAV,EAAN;AACA;AAAA;AAAA,oCAAKnG,GAAL,CAASC,WAAT,CAAqB,cAArB;AACH,iBAHD,CAGE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,oCAAKF,GAAL,CAASG,QAAT,CAAkB,aAAlB,EAAiCD,KAAjC,EADY,CAEZ;AACH;AACJ;;AAED,oBAAM,MAAI,CAACb,WAAL,CAAiB+G,SAAjB,CAA2BJ,KAA3B,EAAkCD,iBAAlC,CAAN;AACH,aAlBD,CAkBE,OAAO7F,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKF,GAAL,CAASG,QAAT,0BAA0B6F,KAA1B,iCAAyC9F,KAAzC;AACA,oBAAMA,KAAN;AACH;AAvB+E;AAwBnF;AAED;AACJ;AACA;;;AACkB+F,QAAAA,uBAAuB,GAAkB;AAAA;;AAAA;AACnD,gBAAMI,WAAW,GAAG,IAApB,CADmD,CACzB;;AAC1B,gBAAMC,aAAa,GAAG,EAAtB,CAFmD,CAEzB;;AAC1B,gBAAIC,UAAU,GAAG,CAAjB;;AAEA,mBAAO,CAAC,MAAI,CAAClH,WAAN,IAAqBkH,UAAU,GAAGF,WAAzC,EAAsD;AAClD,oBAAM,IAAIG,OAAJ,CAAYC,OAAO,IAAIC,UAAU,CAACD,OAAD,EAAUH,aAAV,CAAjC,CAAN;AACAC,cAAAA,UAAU,IAAID,aAAd;AACH;;AAED,gBAAI,CAAC,MAAI,CAACjH,WAAV,EAAuB;AACnB,oBAAM,IAAIsH,KAAJ,CAAU,kBAAV,CAAN;AACH;AAZkD;AAatD;AAED;AACJ;AACA;;;AACYC,QAAAA,oBAAoB,GAAG;AAC3B;AAAA;AAAA,4BAAK5G,GAAL,CAASwE,OAAT,CAAiB,kBAAjB,EAD2B,CAG3B;;AACA,cAAMqC,SAAS,GAAG,IAAI/J,IAAJ,CAAS,gBAAT,CAAlB;AACA+J,UAAAA,SAAS,CAACC,MAAV,GAAmB;AAAA;AAAA,4BAAKC,IAAL,CAAUC,IAA7B;AACAH,UAAAA,SAAS,CAACI,WAAV,CAAsB,CAAtB,EAAyB,CAAC,EAA1B,EAA8B,CAA9B;AACAJ,UAAAA,SAAS,CAACK,QAAV,CAAmB,GAAnB,EAAwB,CAAxB,EAA2B,GAA3B,EAP2B,CAS3B;;AACA,cAAMC,SAAS,GAAGN,SAAS,CAACO,YAAV,CAAuBpK,SAAvB,CAAlB;AACAmK,UAAAA,SAAS,CAACE,IAAV,GAAiBrK,SAAS,CAACsK,IAAV,CAAeC,MAAhC;AACAJ,UAAAA,SAAS,CAACK,OAAV,GAAoB,IAApB,CAZ2B,CAc3B;;AACA,cAAMC,QAAQ,GAAGZ,SAAS,CAACO,YAAV,CAAuBzK,QAAvB,CAAjB;AACA8K,UAAAA,QAAQ,CAACD,OAAT,GAAmB,IAAnB;AAEA;AAAA;AAAA,4BAAKxH,GAAL,CAASC,WAAT,CAAqB,gCAArB;AACH;AAED;AACJ;AACA;;;AACUyH,QAAAA,gBAAgB,GAAG;AAAA;;AAAA;AACrB,gBAAI;AACA;AAAA;AAAA,gCAAK1H,GAAL,CAASC,WAAT,CAAqB,mBAArB,EADA,CAGA;;AACA;AAAA;AAAA,gCAAKD,GAAL,CAASC,WAAT,8CAA4C,MAAI,CAAChB,SAAL,CAAe0I,mBAA3D;AACA,oBAAM;AAAA;AAAA,4CAAWC,YAAX,CACF,MADE;AAAA;AAAA,0DAGF;AAAA;AAAA,gCAAKb,IAAL,CAAUC,IAHR,EAIF,MAAI,CAAC/H,SAAL,CAAe0I,mBAJb,CAAN;AAMA;AAAA;AAAA,gCAAK3H,GAAL,CAASC,WAAT,CAAqB,qBAArB,EAXA,CAaA;;AACA,kBAAI,MAAI,CAACd,aAAT,EAAwB;AACpB,gBAAA,MAAI,CAACA,aAAL,CAAmB0I,GAAnB,GAAyB,MAAzB;AACA;AAAA;AAAA,kCAAK7H,GAAL,CAASC,WAAT,CAAqB,0BAArB,EAFoB,CAIpB;;AACA,oBAAI,CAAC,MAAI,CAACd,aAAL,CAAmB2I,IAAnB,CAAwBC,YAAxB;AAAA;AAAA,6EAAL,EAAuE;AACnE,kBAAA,MAAI,CAAC5I,aAAL,CAAmB2I,IAAnB,CAAwBV,YAAxB;AAAA;AAAA;;AACA;AAAA;AAAA,oCAAKpH,GAAL,CAASC,WAAT,CAAqB,6CAArB;AACH;AACJ,eATD,MASO;AACH;AAAA;AAAA,kCAAKD,GAAL,CAASG,QAAT,CAAkB,wBAAlB;AACH,eAzBD,CA2BA;;;AACA;AAAA;AAAA,gCAAKH,GAAL,CAASC,WAAT,CAAqB,wCAArB;;AACA,kBAAM+H,aAAa,GAAG,MAAI,CAAC7I,aAAL,CAAmB2I,IAAnB,CAAwBG,cAAxB,CAAuC,eAAvC,CAAtB;;AACA,kBAAID,aAAJ,EAAmB;AACf,oBAAME,aAAa,GAAGF,aAAa,CAACD,YAAd;AAAA;AAAA,2DAAtB;;AACA,oBAAIG,aAAJ,EAAmB;AACf;AACA,kBAAA,MAAI,CAAC9I,aAAL,GAAqB8I,aAArB;AACA,kBAAA,MAAI,CAAC9I,aAAL,CAAmByI,GAAnB,GAAyB,MAAzB;AACA;AAAA;AAAA,oCAAK7H,GAAL,CAASC,WAAT,CAAqB,qCAArB;AACH,iBALD,MAKO;AACH;AAAA;AAAA,oCAAKD,GAAL,CAASwE,OAAT,CAAiB,2CAAjB;AACH;AACJ,eAVD,MAUO;AACH;AAAA;AAAA,kCAAKxE,GAAL,CAASwE,OAAT,CAAiB,sCAAjB;AACH;;AAED;AAAA;AAAA,gCAAKxE,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH,aA7CD,CA6CE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKF,GAAL,CAASG,QAAT,CAAkB,eAAlB,EAAmCD,KAAnC;AACA,oBAAMA,KAAN;AACH;AAjDoB;AAkDxB;AAED;AACJ;AACA;;;AACIiI,QAAAA,SAAS,GAAG;AACR,eAAKT,gBAAL,GAAwB7E,IAAxB,CAA6B,MAAM;AAC/B6D,YAAAA,UAAU,CAAC,MAAM;AACb,mBAAK0B,SAAL;AACH,aAFS,EAEP,GAFO,CAAV;AAGH,WAJD;AAKH;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,CAACC,UAAD,EAAyB;AAChC;AACA,cAAI,CAACA,UAAD,IAAe,CAACA,UAAU,CAACC,SAA/B,EAA0C;AACtC;AAAA;AAAA,8BAAKvI,GAAL,CAASwE,OAAT;AACA;AACH,WAL+B,CAOhC;;;AACA,cAAI8D,UAAU,CAACC,SAAX,CAAqBC,OAAzB,EAAkC;AAC9BF,YAAAA,UAAU,CAACC,SAAX,CAAqBC,OAArB,CAA6BC,QAA7B;AACA;AAAA;AAAA,8BAAKzI,GAAL,CAASC,WAAT,yDAAkDqI,UAAU,CAACC,SAAX,CAAqBG,MAAvE;AACH;;AAED,cAAMA,MAAM,GAAGJ,UAAU,CAACC,SAAX,CAAqBG,MAApC;;AACA,cAAIA,MAAM,KAAKC,SAAX,IAAwBD,MAAM,KAAK,IAAvC,EAA6C;AACzC,iBAAKzJ,SAAL,CAAe2J,cAAf,CAA8BC,MAA9B,CAAqCH,MAArC;AACH;;AAEDJ,UAAAA,UAAU,CAAC5C,OAAX;AACH;AAED;AACJ;AACA;;;AACUoD,QAAAA,cAAc,GAAG;AAAA;;AAAA;AACnB,YAAA,MAAI,CAACzJ,WAAL,CAAiB0J,mBAAjB;AADmB;AAEtB;AAED;AACJ;AACA;;;AACIC,QAAAA,eAAe,CACXC,OADW,EAEXC,QAFW,EAGXpD,KAHW,EAIXqD,cAJW,EAKb;AAAA,cADEA,cACF;AADEA,YAAAA,cACF,GAD4B,IAC5B;AAAA;;AACE,cAAMzH,WAAW,GAAG,KAAKrC,WAAL,CAAiBsC,qBAAjB,EAApB;;AACA,cAAI,CAACD,WAAL,EAAkB;AACd;AAAA;AAAA,8BAAK1B,GAAL,CAASG,QAAT,4FAAqC+I,QAArC;AACA;AACH;;AAED,cAAME,WAAW,GAAG,KAAK/J,WAAL,CAAiB+B,YAAjB,KACdM,WAAW,CAAC2H,aADE,GAEd3H,WAAW,CAAC0B,aAFlB;AAIA,cAAMkG,SAAS,GAAGF,WAAW,CAACG,IAAZ,CAAiB1F,IAAI,IAAIA,IAAI,CAACC,IAAL,KAAcoF,QAAvC,CAAlB;;AACA,cAAI,CAACI,SAAL,EAAgB;AACZ;AAAA;AAAA,8BAAKtJ,GAAL,CAASG,QAAT,4FAAqC+I,QAArC;AACA;AACH,WAfH,CAiBE;;;AACA,cAAMZ,UAAU,GAAG;AAAA;AAAA,0BAAIkB,SAAJ;AAAA;AAAA,uCAAnB;AACAlB,UAAAA,UAAU,CAACC,SAAX,CAAqBG,MAArB,GAA8B5C,KAA9B;AACAwC,UAAAA,UAAU,CAACC,SAAX,CAAqBkB,QAArB,GAAgC;AAAA;AAAA,8CAAcC,KAA9C;AACApB,UAAAA,UAAU,CAACC,SAAX,CAAqBoB,QAArB,GAAgC,KAAhC,CArBF,CAuBE;;AACArB,UAAAA,UAAU,CAACC,SAAX,CAAqBqB,UAArB,GAAkC,IAAI3M,IAAJ,CAC9BqM,SAAS,CAACM,UADoB,EAE9BN,SAAS,CAACM,UAFoB,EAG9BN,SAAS,CAACM,UAHoB,CAAlC;AAKAtB,UAAAA,UAAU,CAACC,SAAX,CAAqBsB,SAArB,GAAiC,IAAI5M,IAAJ,CAC7BqM,SAAS,CAACO,SADmB,EAE7BP,SAAS,CAACO,SAFmB,EAG7BP,SAAS,CAACO,SAHmB,CAAjC,CA7BF,CAmCE;;AACA,cAAMC,QAAQ,GAAG,KAAKC,2BAAL,KAAqCb,QAAtD;AACA,cAAMc,MAAM,GAAG;AAAA;AAAA,4BAAK1F,GAAL,CAAS2F,GAAT,CAAaH,QAAb,EAAuB/M,MAAvB,CAAf;;AAEA,cAAIiN,MAAJ,EAAY;AACR,gBAAME,QAAQ,GAAGtN,WAAW,CAACoN,MAAD,CAA5B;AACAE,YAAAA,QAAQ,CAACjD,WAAT,CAAqBgC,OAArB,EAFQ,CAIR;;AACAX,YAAAA,UAAU,CAACC,SAAX,CAAqB4B,aAArB,GAAqCD,QAAQ,CAACE,QAAT,CAAkBC,KAAlB,EAArC,CALQ,CAOR;;AACA,gBAAIlB,cAAJ,EAAoB;AAChB,kBAAMmB,OAAO,GAAGC,IAAI,CAACC,MAAL,KAAgB,GAAhC;AACAN,cAAAA,QAAQ,CAACO,oBAAT,CAA8B,CAA9B,EAAiCH,OAAjC,EAA0C,CAA1C;AACH,aAXO,CAaR;;;AACA,gBAAIhC,UAAU,CAACC,SAAX,CAAqBqB,UAAzB,EAAqC;AACjCM,cAAAA,QAAQ,CAAChD,QAAT,CAAkBoB,UAAU,CAACC,SAAX,CAAqBqB,UAAvC;AACH;;AAED;AAAA;AAAA,8BAAK7C,IAAL,CAAUC,IAAV,CAAe0D,QAAf,CAAwBR,QAAxB,EAlBQ,CAoBR;;AACA,gBAAIS,iBAAiB,GAAGT,QAAQ,CAACnC,YAAT;AAAA;AAAA,uDAAxB;;AACA,gBAAI,CAAC4C,iBAAL,EAAwB;AACpBA,cAAAA,iBAAiB,GAAGT,QAAQ,CAAC9C,YAAT;AAAA;AAAA,yDAApB;AACH,aAxBO,CA0BR;;;AACAuD,YAAAA,iBAAiB,CAAC9C,GAAlB,GAAwBS,UAAxB;AACAqC,YAAAA,iBAAiB,CAACpC,SAAlB,GAA8BD,UAAU,CAACC,SAAzC,CA5BQ,CA8BR;;AACA,gBAAMqC,gBAAgB,GAAGV,QAAQ,CAACnC,YAAT,CAAsBlL,YAAtB,CAAzB;;AACA,gBAAI+N,gBAAJ,EAAsB;AAClBtC,cAAAA,UAAU,CAACC,SAAX,CAAqBsC,YAArB,GAAoCD,gBAApC;AACH,aAlCO,CAoCR;;;AACA,gBAAI,OAAOD,iBAAiB,CAACG,mBAAzB,KAAiD,UAArD,EAAiE;AAC7DH,cAAAA,iBAAiB,CAACG,mBAAlB;AACH,aAvCO,CAyCR;;;AACA,gBAAMD,YAAY,GAAGX,QAAQ,CAACnC,YAAT,CAAsBlL,YAAtB,CAArB;;AAEA,gBAAI,KAAKyC,kBAAL,IAA2B,CAAC,KAAKA,kBAAL,CAAwByL,cAAxD,EAAwE;AACpE,mBAAKzL,kBAAL,CAAwByL,cAAxB,GAAyCF,YAAzC,oBAAyCA,YAAY,CAAEE,cAAvD;AACA;AAAA;AAAA,gCAAK/K,GAAL,CAASC,WAAT,CAAqB,kBAArB;AACH,aA/CO,CAiDR;;;AACA,iBAAKhB,SAAL,CAAe2J,cAAf,CAA8BoC,GAA9B,CAAkClF,KAAlC,EAAyCwC,UAAzC;AACA,iBAAKrJ,SAAL,CAAegM,cAAf,CAA8BD,GAA9B,CAAkClF,KAAlC,EAAyCoE,QAAzC;AACH;AACJ;AAED;AACJ;AACA;;;AACYgB,QAAAA,oBAAoB,CAACC,QAAD,EAAyB;AACjD;AACA,cAAMC,cAAc,GAAG,OAAO,EAA9B,CAFiD,CAEf;;AAClC,cAAIC,WAAW,GAAG,CAAlB;AACA,cAAIC,YAAY,GAAG,CAAC,CAApB,CAJiD,CAI1B;;AAEvB,cAAMC,WAAW,GAAGC,WAAW,CAAC,MAAM;AAClCH,YAAAA,WAAW,IAAID,cAAc,GAAG,IAAhC;;AAEA,gBAAIC,WAAW,IAAIF,QAAnB,EAA6B;AACzB;AACA,mBAAK7L,kBAAL,CAAwByL,cAAxB,CAAuCU,WAAvC,CACI,gBADJ,EAEI,IAAIvO,IAAJ,CAAS,GAAT,EAAc,GAAd,EAAmB,GAAnB,EAAwB,GAAxB,CAFJ,EAFyB,CAOzB;;AACAwO,cAAAA,aAAa,CAACH,WAAD,CAAb;AACA,mBAAKI,8BAAL;AACA;AACH,aAdiC,CAgBlC;;;AACA,gBAAMC,QAAQ,GAAGP,WAAW,GAAGF,QAA/B;AACA,gBAAMU,aAAa,GAAG,KAAKC,cAAL,CAAoBF,QAApB,CAAtB,CAlBkC,CAoBlC;;AACA,gBAAMG,YAAY,GAAGxB,IAAI,CAACyB,GAAL,CAASH,aAAa,GAAGP,YAAzB,CAArB;;AACA,gBAAIS,YAAY,GAAG,IAAnB,EAAyB;AACrB;AACA,mBAAKzM,kBAAL,CAAwByL,cAAxB,CAAuCU,WAAvC,CACI,gBADJ,EAEI,IAAIvO,IAAJ,CAAS2O,aAAT,EAAwB,GAAxB,EAA6B,GAA7B,EAAkC,GAAlC,CAFJ;AAIAP,cAAAA,YAAY,GAAGO,aAAf;AACH;AACJ,WA9B8B,EA8B5BT,cA9B4B,CAA/B;AA+BH;AAED;AACJ;AACA;;;AACYU,QAAAA,cAAc,CAACG,CAAD,EAAoB;AACtC,iBAAOA,CAAC,GAAG,GAAJ,GAAU,IAAIA,CAAJ,GAAQA,CAAR,GAAYA,CAAtB,GAA0B,IAAI1B,IAAI,CAAC2B,GAAL,CAAS,CAAC,CAAD,GAAKD,CAAL,GAAS,CAAlB,EAAqB,CAArB,IAA0B,CAA/D;AACH;AAED;AACJ;AACA;;;AACYN,QAAAA,8BAA8B,GAAS;AAC3C,eAAKnM,mBAAL,GAA2B,KAA3B;AACA,eAAKH,WAAL,CAAiBwF,eAAjB,CAAiC,IAAjC;AACA;AAAA;AAAA,4BAAK7E,GAAL,CAASC,WAAT,CAAqB,6BAArB;AACH;AAED;AACJ;AACA;;;AACYkM,QAAAA,mBAAmB,GAAS;AAAA;;AAChC;AACA,uCAAI,KAAK7M,kBAAT,aAAI,sBAAyByL,cAA7B,EAA6C;AACzC;AAAA;AAAA,8BAAK/K,GAAL,CAASC,WAAT,CAAqB,mBAArB;AACA,iBAAKmM,sBAAL;AACH,WAHD,MAGO;AACH;AAEA1F,YAAAA,UAAU,CAAC,MAAM;AACb,mBAAKiF,8BAAL;AACH,aAFS,EAEP,MAAM,IAFC,CAAV;AAGA;AAAA;AAAA,8BAAK3L,GAAL,CAASC,WAAT,CAAqB,kBAArB;AACH;AACJ;AAED;AACJ;AACA;;;AACYmM,QAAAA,sBAAsB,GAAS;AACnC,cAAMC,kBAAkB,GAAG,GAA3B,CADmC,CACH;;AAChC,cAAMC,gBAAgB,GAAG;AAAA;AAAA,0CAAYC,sBAAZ,IAAsC,GAA/D,CAFmC,CAEiC;AAEpE;;AACA,eAAK/M,mBAAL,GAA2B,IAA3B,CALmC,CAOnC;;AACA,eAAKF,kBAAL,CAAwByL,cAAxB,CAAuCU,WAAvC,CACI,gBADJ,EAEI,IAAIvO,IAAJ,CAAS,CAAT,EAAY,GAAZ,EAAiB,GAAjB,EAAsB,GAAtB,CAFJ,EARmC,CAanC;;AACAwJ,UAAAA,UAAU,CAAC,MAAM;AACb;AAAA;AAAA,8BAAK1G,GAAL,CAASC,WAAT,uFAAwCqM,gBAAxC;AACA,iBAAKpB,oBAAL,CAA0BoB,gBAA1B;AACH,WAHS,EAGPD,kBAAkB,GAAG,IAHd,CAAV;AAIH;AAED;AACJ;AACA;;;AACIG,QAAAA,UAAU,GAAG;AACT,cAAMC,GAAG,GAAG;AACRC,YAAAA,OAAO,EAAE,CAAC5E,IAAD,EAAa6E,MAAb,KAA6B;AAClC;AACA,kBAAMC,IAAI,GAAG9E,IAAI,CAACC,YAAL;AAAA;AAAA,mDAAb;AACA,mBAAKpI,GAAL,CAASiN,IAAT;AACH;AALO,WAAZ;AAOA;AAAA;AAAA,4BAAKtJ,GAAL,CAASC,IAAT,CAAc,KAAKtE,SAAL,CAAe4N,QAA7B,EAAuC,IAAvC,EAA6CJ,GAA7C;AACH;AAED;AACJ;AACA;;;AACIK,QAAAA,sBAAsB,CAACC,SAAD,EAAwB;AAC1C,cAAMN,GAAG,GAAG;AACRC,YAAAA,OAAO,EAAE,CAAC5E,IAAD,EAAa6E,MAAb,KAA6B;AAClC;AACA,kBAAMC,IAAI,GAAG9E,IAAI,CAACC,YAAL;AAAA;AAAA,mDAAb;AACA,mBAAKpI,GAAL,CAASiN,IAAT,EAHkC,CAKlC;;AACAG,cAAAA,SAAS;AACZ;AARO,WAAZ;AAUA;AAAA;AAAA,4BAAKzJ,GAAL,CAASC,IAAT,CAAc,KAAKtE,SAAL,CAAe4N,QAA7B,EAAuC,IAAvC,EAA6CJ,GAA7C;AACH,SAhyB2C,CAkyB5C;;;AAEOO,QAAAA,mBAAmB,GAAc;AACpC,iBAAO,KAAK3N,WAAL,CAAiB4B,eAAjB,EAAP;AACH;;AAEMG,QAAAA,YAAY,GAAY;AAC3B,iBAAO,KAAK/B,WAAL,CAAiB+B,YAAjB,EAAP;AACH;;AAEMC,QAAAA,UAAU,GAAY;AACzB,iBAAO,KAAKhC,WAAL,CAAiBgC,UAAjB,EAAP;AACH;AAED;AACJ;AACA;;;AACW4L,QAAAA,qBAAqB,GAAY;AACpC,iBAAO,KAAK5N,WAAL,CAAiB4N,qBAAjB,EAAP;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,uBAAuB,GAAY;AACtC,iBAAO,KAAK7N,WAAL,CAAiB6N,uBAAjB,EAAP;AACH;AAED;AACJ;AACA;;;AACWC,QAAAA,oBAAoB,GAAW;AAAA;;AAClC,iBAAO,4BAAK9N,WAAL,wCAAkB8N,oBAAlB,OAA4C,CAAnD;AACH;AAED;AACJ;AACA;;;AACWnJ,QAAAA,iBAAiB,GAAW;AAC/B,iBAAO,KAAK+F,2BAAL,EAAP;AACH;AAED;AACJ;AACA;;;AACYA,QAAAA,2BAA2B,GAAW;AAC1C,cAAMrI,WAAW,GAAG,KAAKrC,WAAL,CAAiBsC,qBAAjB,EAApB;;AACA,cAAI,CAACD,WAAL,EAAkB;AACd;AAAA;AAAA,8BAAK1B,GAAL,CAASwE,OAAT;AACA,mBAAO;AAAA;AAAA,4CAAY4I,eAAnB,CAFc,CAEsB;AACvC;;AAED,cAAMC,cAAc,GAAG3L,WAAW,CAAC0L,eAAnC,CAP0C,CAS1C;;AACA,cAAME,IAAI,GAAGD,cAAc,CAACE,QAAf,CAAwB,GAAxB,IAA+BF,cAA/B,GAAgDA,cAAc,GAAG,GAA9E;AACA,iBAAOC,IAAP;AACH;;AAEMvE,QAAAA,mBAAmB,CAACyE,WAAD,EAA2B;AACjD,eAAKnO,WAAL,CAAiB0J,mBAAjB,CAAqCyE,WAArC;AACH;AAED;AACJ;AACA;;;AACWjI,QAAAA,cAAc,GAAY;AAAA;;AAC7B,gEAAO,KAAKlG,WAAZ,qBAAO,mBAAkBkG,cAAlB,EAAP,oCAA6C,KAA7C;AACH;;AAEKkI,QAAAA,UAAU,GAAG;AAAA;;AAAA;AACf,YAAA,MAAI,CAACpO,WAAL,CAAiBqO,WAAjB,CAA6B;AAAA;AAAA,wCAAU9M,QAAvC;AADe;AAElB;;AAEK+M,QAAAA,SAAS,GAAG;AAAA;;AAAA;AACd,YAAA,OAAI,CAACtO,WAAL,CAAiBqO,WAAjB,CAA6B;AAAA;AAAA,wCAAUhN,GAAvC;AADc;AAEjB;;AAEDkN,QAAAA,eAAe,GAAG;AAAA;;AACd;AACA;AAAA;AAAA,4BAAK5N,GAAL,CAASC,WAAT,CAAqB,gBAArB;AACA;AAAA;AAAA,4BAAKD,GAAL,CAASC,WAAT,wBAAyC,KAAKb,aAAL,GAAqB,OAArB,GAA+B,OAAxE;AACA;AAAA;AAAA,4BAAKY,GAAL,CAASC,WAAT,wBAAyC,KAAKd,aAAL,GAAqB,OAArB,GAA+B,OAAxE;AACA;AAAA;AAAA,4BAAKa,GAAL,CAASC,WAAT,qBAAsC,KAAKf,UAAL,GAAkB,OAAlB,GAA4B,OAAlE,GALc,CAOd;;AACA,qCAAI,KAAKE,aAAT,aAAI,oBAAoBwO,eAAxB,EAAyC;AACrC;AAAA;AAAA,8BAAK5N,GAAL,CAASC,WAAT;AACA,iBAAKb,aAAL,CAAmBwO,eAAnB;AACH,WAHD,MAGO;AACH;AAAA;AAAA,8BAAK5N,GAAL,CAASwE,OAAT,CAAiB,4CAAjB,EADG,CAEH;;AACA,iBAAKoC,oBAAL;AACH;;AAED,qCAAI,KAAKzH,aAAT,aAAI,oBAAoByO,eAAxB,EAAyC;AACrC;AAAA;AAAA,8BAAK5N,GAAL,CAASC,WAAT,CAAqB,sCAArB;AACA,iBAAKd,aAAL,CAAmByO,eAAnB,GAFqC,CAIrC;;AACA,gBAAI,KAAKtO,kBAAT,EAA6B;AACzB,mBAAKA,kBAAL,CAAwBuO,WAAxB,GAAsC;AAAA;AAAA,kEAA0B,KAAK1O,aAA/B,CAAtC;AACH;AACJ,WARD,MAQO;AACH;AAAA;AAAA,8BAAKa,GAAL,CAASwE,OAAT,CAAiB,uBAAjB;AACH;;AAED,kCAAI,KAAKtF,UAAT,aAAI,iBAAiB0O,eAArB,EAAsC;AAClC;AAAA;AAAA,8BAAK5N,GAAL,CAASC,WAAT,CAAqB,oCAArB;AACA,iBAAKf,UAAL,CAAgB0O,eAAhB;AACH,WAHD,MAGO;AACH;AAAA;AAAA,8BAAK5N,GAAL,CAASwE,OAAT,CAAiB,oBAAjB;AACH;;AAED,eAAKsJ,qBAAL;AACH;AAED;AACJ;AACA;;;AACWA,QAAAA,qBAAqB,GAAS;AACjC;AACA,eAAKzO,WAAL,CAAiBqO,WAAjB,CAA6B;AAAA;AAAA,sCAAUrN,UAAvC,EAFiC,CAIjC;;AACA,eAAK+H,SAAL;AACH;;AAEKA,QAAAA,SAAS,GAAG;AAAA;;AAAA;AACd;AACA,YAAA,OAAI,CAACnJ,SAAL,CAAe2G,KAAf,GAFc,CAId;;;AACA,gBAAI,OAAI,CAACtG,kBAAT,EAA6B;AACzB,cAAA,OAAI,CAACA,kBAAL,CAAwByL,cAAxB,GAAyC,IAAzC;AACH;;AACD,YAAA,OAAI,CAACvL,mBAAL,GAA2B,KAA3B;AACA,YAAA,OAAI,CAACC,uBAAL,GAA+B,KAA/B,CATc,CAWd;;AACA,gBAAI,OAAI,CAAC2B,YAAL,EAAJ,EAAyB;AACrB,cAAA,OAAI,CAAC/B,WAAL,CAAiB0O,uBAAjB;AACH,aAFD,MAEO,IAAI,OAAI,CAAC1M,UAAL,EAAJ,EAAuB;AAC1B,cAAA,OAAI,CAAChC,WAAL,CAAiB2O,uBAAjB;AACH,aAhBa,CAkBd;;;AACA,YAAA,OAAI,CAAC3O,WAAL,CAAiB4O,yBAAjB,GAnBc,CAqBd;;;AACA,YAAA,OAAI,CAAC5O,WAAL,CAAiBwF,eAAjB,CAAiC,KAAjC,EAtBc,CAwBd;;;AACA,YAAA,OAAI,CAACsH,mBAAL;AAzBc;AA0BjB;AAED;AACJ;AACA;;;AACI+B,QAAAA,WAAW,GAAG;AACV;AACA,cAAMC,iBAAiB,GAAG,KAAKlP,SAAL,CAAemP,YAAf,CAA4B/K,MAAtD;AACA,cAAMgL,mBAAmB,GAAG,KAAKpP,SAAL,CAAegM,cAAf,CAA8BqD,IAA1D;AACA,cAAMC,QAAQ,GAAG,KAAKnN,YAAL,KAAsB,MAAtB,GAA+B,MAAhD;AAEA;AAAA;AAAA,4BAAKpB,GAAL,CAASC,WAAT,yDACkBsO,QADlB,8BACoCJ,iBADpC,mCAC+DE,mBAD/D,EANU,CAUV;;AACA,cAAIF,iBAAiB,KAAK,CAAtB,IAA2BE,mBAAmB,KAAK,CAAvD,EAA0D;AACtD;AACA;AAAA;AAAA,8BAAKrO,GAAL,CAASC,WAAT,mBAA2BsO,QAA3B;AACA,iBAAKlP,WAAL,CAAiBqO,WAAjB,CAA6B;AAAA;AAAA,wCAAUhN,GAAvC;AACA;AACH,WAhBS,CAkBV;;;AACA,cAAIyN,iBAAiB,IAAI,CAAzB,EAA4B;AACxB;AACA,gBAAI,KAAKK,gBAAL,EAAJ,EAA6B;AACzB;AAAA;AAAA,gCAAKxO,GAAL,CAASC,WAAT,CAAqB,8BAArB,EADyB,CAEzB;;AACA;AACH,aANuB,CAQxB;;;AACA;AAAA;AAAA,8BAAKD,GAAL,CAASC,WAAT,CAAqB,oBAArB;AACA,iBAAKZ,WAAL,CAAiBqO,WAAjB,CAA6B;AAAA;AAAA,wCAAU9M,QAAvC;AACH;AACJ;AAED;AACJ;AACA;;;AACY4N,QAAAA,gBAAgB,GAAY;AAAA;;AAChC,cAAI,4BAAC,KAAKlP,kBAAN,aAAC,uBAAyBuO,WAA1B,CAAJ,EAA2C;AACvC,mBAAO,KAAP;AACH,WAH+B,CAKhC;;;AACA,cAAMY,UAAqC,GAAG,EAA9C;;AACA,eAAK,IAAM5K,IAAX,IAAmB,KAAK5E,SAAL,CAAemP,YAAlC,EAAgD;AAC5C,gBAAMtK,IAAI,GAAGD,IAAI,CAACC,IAAlB;AACA2K,YAAAA,UAAU,CAAC3K,IAAD,CAAV,GAAmB,CAAC2K,UAAU,CAAC3K,IAAD,CAAV,IAAoB,CAArB,IAA0B,CAA7C;AACH,WAV+B,CAYhC;;;AACA,eAAK,IAAM4K,KAAX,IAAoBC,MAAM,CAACC,MAAP,CAAcH,UAAd,CAApB,EAA+C;AAC3C,gBAAIC,KAAK,IAAI,CAAb,EAAgB;AACZ,qBAAO,IAAP;AACH;AACJ;;AAED,iBAAO,KAAP;AACH;;AAEDxJ,QAAAA,SAAS,CAAC2J,CAAD,EAAUC,IAAV,EAA8B;AACnCnJ,UAAAA,OAAO,CAAC3F,GAAR,CAAY,OAAZ,EAAqB8O,IAArB;AACH;;AAEDC,QAAAA,aAAa,CAACF,CAAD,EAAUC,IAAV,EAA8B;AACvCnJ,UAAAA,OAAO,CAAC3F,GAAR,CAAY,OAAZ,EAAqB8O,IAArB;AACH;;AAEDE,QAAAA,UAAU,CAACC,WAAD,EAA2BC,QAA3B,EAAqD;AAAA,cAA1BA,QAA0B;AAA1BA,YAAAA,QAA0B,GAAf,IAAe;AAAA;;AAC3D,iBAAO,IAAP;AACH;AAED;AACJ;AACA;AACA;;;AACiBC,QAAAA,0BAA0B,GAAkB;AAAA;;AAAA;AACrD;AACA,YAAA,OAAI,CAAC9P,WAAL,CAAiBwF,eAAjB,CAAiC,KAAjC,EAFqD,CAIrD;;;AACA,YAAA,OAAI,CAACuK,wBAAL,GALqD,CAOrD;;;AACA,kBAAM,OAAI,CAAClM,gCAAL,CAAsC,IAAtC,CAAN,CARqD,CAUrD;;AACA,YAAA,OAAI,CAACjE,SAAL,CAAe2G,KAAf,GAXqD,CAarD;;;AACA,gBAAI,OAAI,CAACtG,kBAAT,EAA6B;AACzB,cAAA,OAAI,CAACA,kBAAL,CAAwByL,cAAxB,GAAyC,IAAzC;AACH;;AACD,YAAA,OAAI,CAACvL,mBAAL,GAA2B,KAA3B,CAjBqD,CAmBrD;;AACA,YAAA,OAAI,CAACH,WAAL,CAAiBqO,WAAjB,CAA6B;AAAA;AAAA,wCAAUlN,QAAvC,EApBqD,CAsBrD;;;AACA,YAAA,OAAI,CAACnB,WAAL,CAAiB2O,uBAAjB,GAvBqD,CAyBrD;;;AACA,YAAA,OAAI,CAAC3O,WAAL,CAAiB4O,yBAAjB,GA1BqD,CA4BrD;;;AACA,YAAA,OAAI,CAAC9B,mBAAL;;AAEA;AAAA;AAAA,8BAAKnM,GAAL,CAASC,WAAT,CAAqB,qBAArB;AA/BqD;AAgCxD;AAED;AACJ;AACA;;;AACYmP,QAAAA,wBAAwB,GAAS;AACrC,cAAI;AACA;AACA,gBAAI;AAAA;AAAA,4BAAIlJ,KAAJ,IAAa;AAAA;AAAA,4BAAIA,KAAJ,CAAUmJ,UAA3B,EAAuC;AACnC;AAAA;AAAA,gCAAKrP,GAAL,CAASC,WAAT,CAAqB,cAArB;AACA;AAAA;AAAA,8BAAIiG,KAAJ,CAAUmJ,UAAV,CAAqBC,KAArB;AACH,aALD,CAOA;;;AACA,gBAAI,KAAKnQ,aAAL,IAAsB,KAAKA,aAAL,CAAmB2I,IAA7C,EAAmD;AAC/C,kBAAMyH,mBAAmB,GAAG,KAAKpQ,aAAL,CAAmB2I,IAAnB,CAAwB0H,uBAAxB,CACxB,iBADwB,CAA5B;;AAGA,mBAAK,IAAM5C,IAAX,IAAmB2C,mBAAnB,EAAwC;AACpC,oBAAI3C,IAAI,IAAIA,IAAI,CAAC9E,IAAb,IAAqB8E,IAAI,CAAC9E,IAAL,CAAU2H,OAAnC,EAA4C;AACxC;AAAA;AAAA,oCAAKzP,GAAL,CAASC,WAAT,2DAAqC2M,IAAI,CAAC9E,IAAL,CAAUhE,IAA/C;AACA8I,kBAAAA,IAAI,CAAC9E,IAAL,CAAUpC,OAAV;AACH;AACJ;AACJ;;AAED;AAAA;AAAA,8BAAK1F,GAAL,CAASC,WAAT,CAAqB,eAArB;AACH,WArBD,CAqBE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKF,GAAL,CAASwE,OAAT,CAAiB,kBAAjB,EAAqCtE,KAArC;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACkB8C,QAAAA,oBAAoB,GAAqB;AAAA;AACnD,gBAAI;AACA;AACA,kBAAMF,OAAO,SAAS;AAAA;AAAA,8BAAIR,IAAJ,CAASoN,UAAT,CAClB;AAAA;AAAA,wCAASC,iBADS,EAElB,CAAC,CAFiB,EAGlB,iBAHkB,CAAtB;;AAMA,kBAAI7M,OAAJ,EAAa;AACT;AAAA;AAAA,kCAAK9C,GAAL,CAASC,WAAT,CAAqB,uBAArB;AACA,uBAAO,IAAP;AACH,eAHD,MAGO;AACH;AAAA;AAAA,kCAAKD,GAAL,CAASC,WAAT,CAAqB,YAArB;AACA,uBAAO,KAAP;AACH;AACJ,aAfD,CAeE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKF,GAAL,CAASG,QAAT,CAAkB,aAAlB,EAAiCD,KAAjC;AACA,qBAAO,KAAP;AACH;AAnBkD;AAoBtD;;AAnmC2C,O;;gCAsmCnCxD,c,GAAN,MAAMA,cAAN,SAA6B;AAAA;AAAA,sBAAIkT,MAAjC,CAAwC;AAC3CC,QAAAA,WAAW,GAAG;AACV;AACH;;AAH0C,O", "sourcesContent": ["import { Collider, instantiate, Mesh<PERSON><PERSON>er, Node, Prefab, RigidBody, Vec3, Vec4 } from 'cc';\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { ecs } from '../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';\nimport { PropType, SceneType, UsePropArgs } from '../../../tsrpc/protocols/base';\nimport { smc } from '../../common/SingletonModuleComp';\n\nimport { EventMessage } from '../../../../../extensions/oops-plugin-framework/assets/core/common/event/EventMessage';\n\nimport { ClientConst } from '../../common/ClientConst';\n\nimport { SceneItemType } from '../../../tsrpc/protocols/base';\nimport { GameEvent } from '../../common/Enum';\nimport { simpleLoader } from '../../common/loader/SimpleLoadingManager';\nimport { ItemEntity } from '../../item/ItemEntity';\nimport { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';\nimport { WallSceneViewComp } from '../../prefab/WallSceneViewComp';\nimport { GameSceneViewComp } from '../../sceneMgr/vIew/GameSceneViewComp';\nimport { BaseSceneEntity } from '../BaseSceneEntity';\nimport { GameModelComp } from './model/GameModelComp';\nimport { GameUIViewComp } from './view/GameUIViewComp';\n\n// 🏗️ 统一管理器导入\nimport { configManager } from '../../managers/ConfigManager';\nimport { GameState, UnifiedGameManager } from '../../managers/UnifiedGameManager';\n\n// 🎮 业务逻辑管理器导入\nimport { ModuleUtil } from '../../../../../extensions/oops-plugin-framework/assets/module/common/ModuleUtil';\nimport { UIID } from '../../common/config/GameUIConfig';\nimport { gameAudioManager } from '../../managers/AudioManager';\nimport { levelProgressManager } from '../../managers/LevelProgressManager';\n\n// 🎯 导入触摸交互管理器\nimport { CollectionSlotManager } from '../../managers/CollectionSlotManager';\nimport { GameResultManager } from '../../managers/GameResultManager';\nimport { ItemInteractionManager } from '../../managers/ItemInteractionManager';\nimport { ItemInteractionManager as ItemInteractionManagerComp } from './ItemInteractionManager';\n\n/**\n * 游戏实体 - 简化版\n * <AUTHOR>\n * @version 3.0.0 - 统一管理器架构 + 消融生成效果 + 完整触摸系统\n */\**************(`GameEntity`)\nexport class GameEntity extends BaseSceneEntity {\n    // 🎯 核心组件\n    GameModel!: GameModelComp;\n    GameUIView!: GameUIViewComp;\n    GameSceneView!: GameSceneViewComp;\n    WallSceneView!: WallSceneViewComp;\n\n    // 🏗️ 统一管理器\n    public gameManager!: UnifiedGameManager;\n\n    // 🎯 触摸交互管理器\n    public interactionManager!: ItemInteractionManager;\n\n    // 🎯 游戏结果管理器\n    private gameResultManager!: GameResultManager;\n\n    // 🎭 简化版消融系统 - 只用一个标志位\n    private isDissolveAnimating: boolean = false;\n\n    // 🎯 预加载状态管理\n    private hardModeResourcesLoaded: boolean = false;\n\n    /**\n     * 🚀 实体初始化\n     */\n    protected init() {\n        this.add(GameModelComp);\n        this.listenEvent();\n        // 延迟初始化gameManager，确保异步完成\n        this.initializeGameManager();\n\n        // 🎯 初始化触摸交互管理器\n        this.interactionManager = new ItemInteractionManager(this);\n\n        // 🎯 初始化游戏结果管理器\n        this.gameResultManager = new GameResultManager(this);\n    }\n\n    /**\n     * 🎮 初始化统一游戏管理器\n     */\n    private async initializeGameManager(): Promise<void> {\n        try {\n            // 确保ConfigManager已初始化\n            await configManager.initialize();\n\n            // 创建统一管理器\n            this.gameManager = new UnifiedGameManager(this);\n\n            // 设置状态回调\n            this.setupStateCallbacks();\n\n            oops.log.logBusiness('🎮 统一游戏管理器初始化完成');\n        } catch (error) {\n            oops.log.logError('❌ 游戏管理器初始化失败:', error);\n        }\n    }\n\n    /**\n     * 🎯 设置状态回调\n     */\n    private setupStateCallbacks(): void {\n        this.gameManager.onStateChange(GameState.SimpleMode, () => {\n            this.setupSimpleMode();\n            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态\n        });\n\n        this.gameManager.onStateChange(GameState.HardMode, () => {\n            this.setupHardMode();\n            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态\n        });\n\n        this.gameManager.onStateChange(GameState.Win, () => {\n            this.handleGameWin();\n            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态\n        });\n\n        this.gameManager.onStateChange(GameState.GameOver, () => {\n            this.handleGameFail();\n            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态\n        });\n\n        this.gameManager.onStateChange(GameState.Paused, () => {\n            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态\n        });\n\n        this.gameManager.onStateChange(GameState.Loading, () => {\n            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态\n        });\n    }\n\n    /**\n     * 🎯 更新ViewModel中的游戏状态数据\n     * 供VMState组件使用\n     */\n    public updateGameStateInViewModel(): void {\n        const currentState = this.gameManager.getCurrentState();\n\n        // 更新vmdata中的游戏状态\n        this.GameModel.vmdata.gameState = currentState;\n        this.GameModel.vmdata.isSimpleMode = this.gameManager.isSimpleMode() ? 1 : 0;\n        this.GameModel.vmdata.isHardMode = this.gameManager.isHardMode() ? 1 : 0;\n        this.GameModel.vmdata.isGameOver = this.gameManager.isGameOver() ? 1 : 0;\n        this.GameModel.vmdata.isWin = this.gameManager.isWin() ? 1 : 0;\n        this.GameModel.vmdata.isPaused = this.gameManager.isPaused() ? 1 : 0;\n        this.GameModel.vmdata.isPlaying =\n            this.gameManager.isSimpleMode() || this.gameManager.isHardMode() ? 1 : 0;\n\n        oops.log.logBusiness(`🎯 ViewModel游戏状态已更新: ${currentState}`);\n    }\n\n    /**\n     * ⚡ 设置简单模式\n     */\n    private setupSimpleMode(): void {\n        const levelConfig = this.gameManager.getCurrentLevelConfig();\n        if (levelConfig) {\n            this.gameManager.setRefillStrategy({\n                adjustByPerformance: false,\n                adjustByTime: false,\n                minInterval: levelConfig.spawnInterval || 3000,\n                maxInterval: (levelConfig.spawnInterval || 3000) * 1.5,\n                difficultyMultiplier: 1.0,\n            });\n        }\n        gameAudioManager.playSimpleGameMusic();\n\n        // 🎯 简单模式开始时，后台预加载困难模式资源\n        this.startPreloadingHardModeInBackground();\n    }\n\n    /**\n     * 🔥 设置困难模式\n     */\n    private setupHardMode(): void {\n        const levelConfig = this.gameManager.getCurrentLevelConfig();\n        if (levelConfig) {\n            this.gameManager.setRefillStrategy({\n                adjustByPerformance: true,\n                adjustByTime: true,\n                minInterval: levelConfig.spawnInterval || 2000,\n                maxInterval: (levelConfig.spawnInterval || 2000) * 2,\n                difficultyMultiplier: levelProgressManager.getDifficultyMultiplier(\n                    smc.role.getPassIndex()\n                ),\n            });\n        }\n        gameAudioManager.playHardGameMusic();\n\n        // 🎯 进入困难模式时完成新手引导（统一新手状态管理）\n        if (smc.role.isNewPlayer()) {\n            oops.log.logBusiness('🎓 进入困难模式，检测到新手玩家，开始完成新手引导...');\n            oops.log.logBusiness(\n                `🔍 引导前状态: isNewPlayer = ${smc.role.RoleModel?.userGameData?.isNewPlayer}`\n            );\n\n            smc.role\n                .completeNewPlayerGuide()\n                .then(success => {\n                    if (success) {\n                        oops.log.logBusiness('✅ 新手引导完成成功');\n                        oops.log.logBusiness(\n                            `🔍 引导后状态: isNewPlayer = ${smc.role.RoleModel?.userGameData?.isNewPlayer}`\n                        );\n                    } else {\n                        oops.log.logError('❌ 新手引导完成失败');\n                    }\n                })\n                .catch(error => {\n                    oops.log.logError('❌ 完成新手引导异常:', error);\n                });\n        } else {\n            oops.log.logBusiness('ℹ️ 进入困难模式，玩家已完成新手引导，跳过新手状态更新');\n        }\n\n        // 💰 困难模式开始时扣除挑战次数并记录挑战数据\n        this.costChallengeAttempt()\n            .then(success => {\n                if (success) {\n                    oops.log.logBusiness('✅ 困难模式挑战次数扣除成功');\n                } else {\n                    oops.log.logBusiness('❌ 困难模式挑战次数扣除失败');\n                }\n            })\n            .catch(error => {\n                oops.log.logError('❌ 困难模式挑战次数扣除异常:', error);\n            });\n\n        // 🎯 困难模式开始后开始预加载后续需要的资源\n        this.startPreloadingEndGameResources();\n    }\n\n    /**\n     * 🏆 处理游戏胜利\n     */\n    private async handleGameWin(): Promise<void> {\n        oops.log.logBusiness('🏆 游戏胜利处理开始');\n\n        // 🎯 委托给GameResultManager统一处理\n        await this.gameResultManager.handleGameWin();\n    }\n\n    /**\n     * 💥 处理游戏失败\n     */\n    private async handleGameFail(): Promise<void> {\n        oops.log.logBusiness('💥 游戏失败处理开始');\n\n        // 🎯 委托给GameResultManager统一处理\n        await this.gameResultManager.handleGameFail();\n    }\n\n    /**\n     * 🎯 简单模式开始时后台预加载困难模式资源\n     */\n    private startPreloadingHardModeInBackground(): void {\n        // 🎯 异步后台预加载，不阻塞游戏进行，不显示Loading UI\n        this.ensureHardModeResourcesPreloaded(false).catch(error => {\n            oops.log.logError('❌ 困难模式资源后台预加载失败:', error);\n            // 预加载失败时不设置标记，让胜利时再次尝试\n        });\n    }\n\n    /**\n     * 🎯 统一的困难模式资源预加载方法\n     * @param showLoading 是否显示Loading界面\n     */\n    private async ensureHardModeResourcesPreloaded(showLoading: boolean = false): Promise<void> {\n        const levelConfig = this.gameManager.getCurrentLevelConfig();\n        if (!levelConfig?.hardModeItems || levelConfig.hardModeItems.length === 0) {\n            oops.log.logBusiness('🎯 无困难模式道具需要预加载');\n            this.hardModeResourcesLoaded = true;\n            return;\n        }\n\n        if (this.hardModeResourcesLoaded) {\n            oops.log.logBusiness('🎯 困难模式资源已预加载，跳过');\n            return;\n        }\n\n        if (showLoading) {\n            oops.gui.open(UIID.Loading);\n        }\n\n        try {\n            const uniqueItemNames = Array.from(\n                new Set(levelConfig.hardModeItems.map(item => item.name))\n            );\n            // 🎯 使用配置化的物品路径\n            const itemPrefabPath = this.getItemPrefabPath();\n            const prefabPaths = uniqueItemNames.map(name => `${itemPrefabPath}${name}`);\n\n            await simpleLoader.loadPrefabs(prefabPaths);\n            this.hardModeResourcesLoaded = true;\n            oops.log.logBusiness(`✅ 困难模式道具预加载完成: ${uniqueItemNames.length} 种道具`);\n        } catch (error) {\n            oops.log.logError('❌ 困难模式资源预加载失败:', error);\n            throw error;\n        } finally {\n            if (showLoading) {\n                oops.gui.remove(UIID.Loading);\n            }\n        }\n    }\n\n    /**\n     * 🎯 开始预加载游戏结束相关资源\n     * 在困难模式开始时调用，后台预加载GameResult界面和Hall资源\n     */\n    private startPreloadingEndGameResources(): void {\n        oops.log.logBusiness('🎯 困难模式开始，后台预加载结束游戏资源');\n\n        // 预加载GameResult界面资源\n        this.preloadGameResultResources();\n\n        // 预加载Hall大厅资源\n        this.preloadHallResources();\n    }\n\n    /**\n     * 🎮 预加载GameResult界面资源\n     */\n    private async preloadGameResultResources(): Promise<void> {\n        try {\n            // 预加载GameResult预制体\n            await oops.res.loadAsync('prefabs/commonPrefabs/GameResult', Prefab);\n            oops.log.logBusiness('✅ GameResult界面资源预加载完成');\n        } catch (error) {\n            oops.log.logWarn('⚠️ GameResult界面资源预加载失败:', error);\n        }\n    }\n\n    /**\n     * 🏛️ 预加载Hall大厅资源\n     */\n    private async preloadHallResources(): Promise<void> {\n        try {\n            // 使用SimpleSceneManager的预加载方法\n            const success = await smc.sceneMgr.preloadScene(SceneType.Hall);\n            if (success) {\n                oops.log.logBusiness('✅ Hall大厅资源预加载完成');\n            } else {\n                oops.log.logWarn('⚠️ Hall大厅资源预加载失败');\n            }\n        } catch (error) {\n            oops.log.logWarn('⚠️ Hall大厅资源预加载失败:', error);\n        }\n    }\n\n    /**\n     * 🙈 游戏隐藏事件处理\n     */\n    onHide() {\n        this.gameManager?.setInputEnabled(false);\n    }\n\n    /**\n     * 👂 注册事件监听\n     */\n    listenEvent() {\n        oops.message.on(EventMessage.GAME_HIDE, this.onHide, this);\n        oops.message.on(GameEvent.UseProp, this.onUseProp, this);\n    }\n\n    /**\n     * 🔇 注销事件监听\n     */\n    unListenEvent() {\n        oops.message.off(EventMessage.GAME_HIDE, this.onHide, this);\n        oops.message.off(GameEvent.UseProp, this.onUseProp, this);\n    }\n\n    /**\n     * 👆 选择物品 - 恢复完整逻辑\n     */\n    chooseItem(item: Node) {\n        const inputEnabled = this.gameManager.isInputEnabled();\n        const dissolveAnimating = this.isDissolveAnimating;\n\n        // 🎯 详细状态检查日志\n        oops.log.logBusiness(\n            `🎯 选择物品 ${item.name}: 输入启用=${inputEnabled}, 消融动画=${dissolveAnimating}`\n        );\n\n        if (!inputEnabled) {\n            oops.log.logBusiness('🚫 输入被禁用，无法选择物品');\n            return;\n        }\n\n        // 检查消融动画是否完成\n        if (dissolveAnimating) {\n            oops.log.logBusiness('🎭 消融动画进行中，无法选择物品');\n            return;\n        }\n\n        // 委托给触摸交互管理器处理\n        this.interactionManager.chooseItem(item);\n    }\n\n    /**\n     * 🎯 选择额外区物品\n     */\n    chooseExtraItem(item: Node) {\n        if (!this.gameManager.isInputEnabled()) return;\n\n        this.interactionManager.chooseExtraItem(item);\n    }\n\n    /**\n     * 💥 销毁实体\n     */\n    destroy(): void {\n        console.log('🗑️ GameEntity销毁开始');\n\n        // 🧹 清理收集槽和所有物品（防止场景切换时物品残留）\n        if (this.GameModel) {\n            console.log('🧹 场景切换时清理收集槽和物品');\n            this.GameModel.clear();\n        }\n\n        // 🎯 清理交互管理器\n        if (this.interactionManager) {\n            this.interactionManager.destroy();\n        }\n\n        // 🎮 清理游戏管理器\n        this.gameManager?.destroy();\n\n        // 🎯 清理游戏结果管理器\n        if (this.gameResultManager) {\n            this.gameResultManager.destroy();\n        }\n\n        // 🔇 取消事件监听\n        this.unListenEvent();\n\n        // 🧹 调用父类销毁\n        super.destroy();\n\n        console.log('✅ GameEntity销毁完成');\n    }\n\n    /**\n     * 🏗️ 加载UI和场景\n     */\n    async loadUIAndScene(index?: number, onEssentialLoaded?: () => void): Promise<void> {\n        const level = index || 1; // 默认关卡1\n        try {\n            oops.log.logBusiness(`🎮 开始加载游戏场景，关卡: ${level}`);\n\n            // 🔧 等待gameManager初始化完成\n            await this.waitForGameManagerReady();\n\n            // 🎓 如果是新手玩家，引导\n            if (smc.role?.isNewPlayer()) {\n                try {\n                    await smc.guide.load();\n                    oops.log.logBusiness('✅ 新手引导系统加载完成');\n                } catch (error) {\n                    oops.log.logError('❌ 引导系统加载失败:', error);\n                    // 即使引导系统加载失败，也继续游戏流程\n                }\n            }\n\n            await this.gameManager.loadLevel(level, onEssentialLoaded);\n        } catch (error) {\n            oops.log.logError(`❌ 关卡 ${level} 加载失败:`, error);\n            throw error;\n        }\n    }\n\n    /**\n     * 🔧 等待gameManager准备就绪\n     */\n    private async waitForGameManagerReady(): Promise<void> {\n        const maxWaitTime = 5000; // 最多等待5秒\n        const checkInterval = 50; // 每50ms检查一次\n        let waitedTime = 0;\n\n        while (!this.gameManager && waitedTime < maxWaitTime) {\n            await new Promise(resolve => setTimeout(resolve, checkInterval));\n            waitedTime += checkInterval;\n        }\n\n        if (!this.gameManager) {\n            throw new Error('GameManager初始化超时');\n        }\n    }\n\n    /**\n     * 🚨 紧急创建地板（当WallSceneView未加载时的备用方案）\n     */\n    private createEmergencyFloor() {\n        oops.log.logWarn('🚨 启动紧急地板创建程序...');\n\n        // 创建一个简单的地板节点\n        const floorNode = new Node('EmergencyFloor');\n        floorNode.parent = oops.game.root;\n        floorNode.setPosition(0, -10, 0);\n        floorNode.setScale(100, 1, 100);\n\n        // 添加刚体组件\n        const rigidBody = floorNode.addComponent(RigidBody);\n        rigidBody.type = RigidBody.Type.STATIC;\n        rigidBody.enabled = true;\n\n        // 添加碰撞体组件\n        const collider = floorNode.addComponent(Collider);\n        collider.enabled = true;\n\n        oops.log.logBusiness('✅ 紧急地板创建完成: 位置Y=-10, 尺寸100x100');\n    }\n\n    /**\n     * 🏗️ 异步加载游戏场景（wallSceneView已包含在gameSceneView中）\n     */\n    async loadWallAndScene() {\n        try {\n            oops.log.logBusiness('🏗️ 开始加载游戏场景组件...');\n\n            // 加载游戏场景视图（现在包含了wallSceneView子节点）\n            oops.log.logBusiness(`📦 加载GameSceneView: ${this.GameModel.gameSceneViewPrefab}`);\n            await ModuleUtil.addViewAsync(\n                this,\n                GameSceneViewComp,\n                oops.game.root,\n                this.GameModel.gameSceneViewPrefab\n            );\n            oops.log.logBusiness('✅ GameSceneView加载完成');\n\n            // 🔧 确保GameSceneView的ent属性正确设置\n            if (this.GameSceneView) {\n                this.GameSceneView.ent = this;\n                oops.log.logBusiness('✅ GameSceneView.ent属性已设置');\n\n                // 🎯 在GameSceneView节点上添加触摸交互管理器组件\n                if (!this.GameSceneView.node.getComponent(ItemInteractionManagerComp)) {\n                    this.GameSceneView.node.addComponent(ItemInteractionManagerComp);\n                    oops.log.logBusiness('✅ ItemInteractionManager组件已添加到GameSceneView');\n                }\n            } else {\n                oops.log.logError('❌ GameSceneView组件未正确加载');\n            }\n\n            // 🔧 优化：从GameSceneView中获取WallSceneView子组件\n            oops.log.logBusiness('🔍 从GameSceneView中查找WallSceneView组件...');\n            const wallSceneNode = this.GameSceneView.node.getChildByName('wallSceneView');\n            if (wallSceneNode) {\n                const wallSceneComp = wallSceneNode.getComponent(WallSceneViewComp);\n                if (wallSceneComp) {\n                    // 手动注册WallSceneView组件到实体\n                    this.WallSceneView = wallSceneComp;\n                    this.WallSceneView.ent = this;\n                    oops.log.logBusiness('✅ WallSceneView组件已从GameSceneView中获取');\n                } else {\n                    oops.log.logWarn('⚠️ wallSceneView节点中未找到WallSceneViewComp组件');\n                }\n            } else {\n                oops.log.logWarn('⚠️ GameSceneView中未找到wallSceneView子节点');\n            }\n\n            oops.log.logBusiness('🎉 所有游戏场景组件加载完成');\n        } catch (error) {\n            oops.log.logError('❌ 游戏场景组件加载失败:', error);\n            throw error;\n        }\n    }\n\n    /**\n     * 🎬 加载场景\n     */\n    loadScene() {\n        this.loadWallAndScene().then(() => {\n            setTimeout(() => {\n                this.startGame();\n            }, 300);\n        });\n    }\n\n    /**\n     * 🗑️ 销毁物品\n     */\n    destroyItem(itemEntity: ItemEntity) {\n        // 🎯 安全检查：确保itemEntity和ItemModel存在\n        if (!itemEntity || !itemEntity.ItemModel) {\n            oops.log.logWarn(`⚠️ destroyItem: itemEntity或ItemModel为空`);\n            return;\n        }\n\n        // 🎯 在销毁前释放PickBox（双重保险）\n        if (itemEntity.ItemModel.pickBox) {\n            itemEntity.ItemModel.pickBox.freeItem();\n            oops.log.logBusiness(`📤 destroyItem时释放PickBox: ${itemEntity.ItemModel.itemId}`);\n        }\n\n        const itemId = itemEntity.ItemModel.itemId;\n        if (itemId !== undefined && itemId !== null) {\n            this.GameModel.allItemEntitys.delete(itemId);\n        }\n\n        itemEntity.destroy();\n    }\n\n    /**\n     * 🔍 检查阈值触发\n     */\n    async checkThreshold() {\n        this.gameManager.checkAndRefillItems();\n    }\n\n    /**\n     * 🎯 在指定位置创建物品 - 简化版，适配重新设计的预制体\n     */\n    createItemOnPos(\n        bornPos: Vec3,\n        itemName: string,\n        index: number,\n        randomRotation: Boolean = true\n    ) {\n        const levelConfig = this.gameManager.getCurrentLevelConfig();\n        if (!levelConfig) {\n            oops.log.logError(`关卡配置未找到，无法创建物品: ${itemName}`);\n            return;\n        }\n\n        const itemConfigs = this.gameManager.isSimpleMode()\n            ? levelConfig.easyModeItems\n            : levelConfig.hardModeItems;\n\n        const foundItem = itemConfigs.find(item => item.name === itemName);\n        if (!foundItem) {\n            oops.log.logError(`物品配置未找到，无法创建物品: ${itemName}`);\n            return;\n        }\n\n        // 创建ECS实体并预先初始化\n        const itemEntity = ecs.getEntity<ItemEntity>(ItemEntity);\n        itemEntity.ItemModel.itemId = index;\n        itemEntity.ItemModel.itemType = SceneItemType.Foods;\n        itemEntity.ItemModel.touching = false;\n\n        // 🎯 设置缩放参数\n        itemEntity.ItemModel.startScale = new Vec3(\n            foundItem.startScale,\n            foundItem.startScale,\n            foundItem.startScale\n        );\n        itemEntity.ItemModel.pickScale = new Vec3(\n            foundItem.pickScale,\n            foundItem.pickScale,\n            foundItem.pickScale\n        );\n\n        // 创建物品节点 - 支持从配置获取路径\n        const itemPath = this.getItemPrefabPathFromConfig() + itemName;\n        const prefab = oops.res.get(itemPath, Prefab);\n\n        if (prefab) {\n            const itemNode = instantiate(prefab);\n            itemNode.setPosition(bornPos);\n\n            // 🎯 保存预制体的原始旋转方向\n            itemEntity.ItemModel.startRotation = itemNode.rotation.clone();\n\n            // 然后才设置随机旋转（用于掉落效果）\n            if (randomRotation) {\n                const randomY = Math.random() * 360;\n                itemNode.setRotationFromEuler(0, randomY, 0);\n            }\n\n            // 🎯 应用初始缩放到节点\n            if (itemEntity.ItemModel.startScale) {\n                itemNode.setScale(itemEntity.ItemModel.startScale);\n            }\n\n            oops.game.root.addChild(itemNode);\n\n            // 获取或添加ItemSceneViewComp组件\n            let itemSceneViewComp = itemNode.getComponent(ItemSceneViewComp);\n            if (!itemSceneViewComp) {\n                itemSceneViewComp = itemNode.addComponent(ItemSceneViewComp);\n            }\n\n            // 建立ECS关联\n            itemSceneViewComp.ent = itemEntity;\n            itemSceneViewComp.ItemModel = itemEntity.ItemModel;\n\n            // 设置渲染器\n            const itemMeshRenderer = itemNode.getComponent(MeshRenderer);\n            if (itemMeshRenderer) {\n                itemEntity.ItemModel.meshRenderer = itemMeshRenderer;\n            }\n\n            // 初始化组件\n            if (typeof itemSceneViewComp.initializeComponent === 'function') {\n                itemSceneViewComp.initializeComponent();\n            }\n\n            // 检查消融效果\n            const meshRenderer = itemNode.getComponent(MeshRenderer);\n\n            if (this.interactionManager && !this.interactionManager.sharedMaterial) {\n                this.interactionManager.sharedMaterial = meshRenderer?.sharedMaterial;\n                oops.log.logBusiness('✅ 消融材质已设置，等待统一启动');\n            }\n\n            // 添加到管理器\n            this.GameModel.allItemEntitys.set(index, itemEntity);\n            this.GameModel.allItemsToPick.set(index, itemNode);\n        }\n    }\n\n    /**\n     * 🎨 执行消融动画 - 性能优化版本\n     */\n    private runDissolveAnimation(duration: number): void {\n        // 🚀 性能优化：降低更新频率到30fps，减少GPU状态切换\n        const updateInterval = 1000 / 30; // 30fps足够流畅\n        let elapsedTime = 0;\n        let lastProgress = -1; // 避免重复设置相同值\n\n        const updateTimer = setInterval(() => {\n            elapsedTime += updateInterval / 1000;\n\n            if (elapsedTime >= duration) {\n                // 动画完成，设置最终状态\n                this.interactionManager.sharedMaterial.setProperty(\n                    'dissolveParams',\n                    new Vec4(1.0, 0.1, 0.0, 0.0)\n                );\n\n                // 清理定时器并完成动画\n                clearInterval(updateTimer);\n                this.completeDissolveAndEnableInput();\n                return;\n            }\n\n            // 计算当前进度并应用缓动\n            const progress = elapsedTime / duration;\n            const easedProgress = this.easeInOutCubic(progress);\n\n            // 🚀 性能优化：只在进度有明显变化时才更新材质\n            const progressDiff = Math.abs(easedProgress - lastProgress);\n            if (progressDiff > 0.01) {\n                // 1%的变化阈值\n                this.interactionManager.sharedMaterial.setProperty(\n                    'dissolveParams',\n                    new Vec4(easedProgress, 0.1, 0.0, 0.0)\n                );\n                lastProgress = easedProgress;\n            }\n        }, updateInterval);\n    }\n\n    /**\n     * 🎨 三次方缓动函数\n     */\n    private easeInOutCubic(t: number): number {\n        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;\n    }\n\n    /**\n     * 🎉 完成消融动画并启用输入 - 统一入口\n     */\n    private completeDissolveAndEnableInput(): void {\n        this.isDissolveAnimating = false;\n        this.gameManager.setInputEnabled(true);\n        oops.log.logBusiness('🎉 消融动画完成！游戏正式开始，可以触摸选择物品了！');\n    }\n\n    /**\n     * 🎯 统一处理输入启用 - 检查是否需要消融动画\n     */\n    private handleInputEnabling(): void {\n        // 🔍 检查是否有共享材质（通过材质属性判断是否需要消融动画）\n        if (this.interactionManager?.sharedMaterial) {\n            oops.log.logBusiness('🎬 检测到消融材质，启动消融动画');\n            this.startDissolveAnimation();\n        } else {\n            // 🎯 没有消融动画，延迟一下直接启用输入（\n\n            setTimeout(() => {\n                this.completeDissolveAndEnableInput();\n            }, 0.5 * 1000);\n            oops.log.logBusiness('🎯 无消融材质，将延迟启用输入');\n        }\n    }\n\n    /**\n     * 🎬 启动消融动画 - 统一入口\n     */\n    private startDissolveAnimation(): void {\n        const dissolveStartDelay = 0.2; // 延迟0.2秒开始消融\n        const dissolveDuration = ClientConst.dissoveCreatedDuration || 1.5; // 消融总时长\n\n        // 设置标志位\n        this.isDissolveAnimating = true;\n\n        // 设置初始消融参数：完全透明\n        this.interactionManager.sharedMaterial.setProperty(\n            'dissolveParams',\n            new Vec4(0, 0.1, 0.0, 0.0)\n        );\n\n        // 延迟启动消融动画\n        setTimeout(() => {\n            oops.log.logBusiness(`🎬 开始消融动画，持续时间: ${dissolveDuration}秒`);\n            this.runDissolveAnimation(dissolveDuration);\n        }, dissolveStartDelay * 1000);\n    }\n\n    /**\n     * 🎮 加载游戏UI\n     */\n    loadGameUI() {\n        const uic = {\n            onAdded: (node: Node, params: any) => {\n                // 🔗 绑定UI组件到实体\n                const comp = node.getComponent(GameUIViewComp)!;\n                this.add(comp);\n            },\n        };\n        oops.gui.open(this.GameModel.gameUIID, null, uic);\n    }\n\n    /**\n     * 🎮 加载游戏UI并执行回调\n     */\n    loadGameUIWithCallback(onUIReady: () => void) {\n        const uic = {\n            onAdded: (node: Node, params: any) => {\n                // 🔗 绑定UI组件到实体\n                const comp = node.getComponent(GameUIViewComp)!;\n                this.add(comp);\n\n                // 执行回调\n                onUIReady();\n            },\n        };\n        oops.gui.open(this.GameModel.gameUIID, null, uic);\n    }\n\n    // =================== 公共API ===================\n\n    public getCurrentGameState(): GameState {\n        return this.gameManager.getCurrentState();\n    }\n\n    public isSimpleMode(): boolean {\n        return this.gameManager.isSimpleMode();\n    }\n\n    public isHardMode(): boolean {\n        return this.gameManager.isHardMode();\n    }\n\n    /**\n     * 🎯 判断之前是否为困难模式（用于Win/GameOver状态时的模式判断）\n     */\n    public wasPreviouslyHardMode(): boolean {\n        return this.gameManager.wasPreviouslyHardMode();\n    }\n\n    /**\n     * 🎯 判断之前是否为简单模式（用于Win/GameOver状态时的模式判断）\n     */\n    public wasPreviouslySimpleMode(): boolean {\n        return this.gameManager.wasPreviouslySimpleMode();\n    }\n\n    /**\n     * 🎯 获取当前关卡索引（支持测试关卡）\n     */\n    public getCurrentLevelIndex(): number {\n        return this.gameManager?.getCurrentLevelIndex() || 1;\n    }\n\n    /**\n     * 🎯 获取当前关卡的物品预制体路径（公共方法）\n     */\n    public getItemPrefabPath(): string {\n        return this.getItemPrefabPathFromConfig();\n    }\n\n    /**\n     * 🎯 获取当前关卡的物品预制体路径\n     */\n    private getItemPrefabPathFromConfig(): string {\n        const levelConfig = this.gameManager.getCurrentLevelConfig();\n        if (!levelConfig) {\n            oops.log.logWarn(`⚠️ 关卡配置不存在，使用默认物品路径`);\n            return ClientConst.itemPrefabPaths; // 兜底默认路径\n        }\n\n        const configuredPath = levelConfig.itemPrefabPaths;\n\n        // 确保路径以斜杠结尾\n        const path = configuredPath.endsWith('/') ? configuredPath : configuredPath + '/';\n        return path;\n    }\n\n    public checkAndRefillItems(touchedItem?: Node): void {\n        this.gameManager.checkAndRefillItems(touchedItem);\n    }\n\n    /**\n     * 🎯 检查输入是否启用（供引导系统使用）\n     */\n    public isInputEnabled(): boolean {\n        return this.gameManager?.isInputEnabled() ?? false;\n    }\n\n    async doGameFail() {\n        this.gameManager.changeState(GameState.GameOver);\n    }\n\n    async doGameWin() {\n        this.gameManager.changeState(GameState.Win);\n    }\n\n    beforeStartGame() {\n        // 🔧 调试：检查所有组件的加载状态\n        oops.log.logBusiness('🔍 检查游戏组件加载状态:');\n        oops.log.logBusiness(`- WallSceneView: ${this.WallSceneView ? '✅ 已加载' : '❌ 未加载'}`);\n        oops.log.logBusiness(`- GameSceneView: ${this.GameSceneView ? '✅ 已加载' : '❌ 未加载'}`);\n        oops.log.logBusiness(`- GameUIView: ${this.GameUIView ? '✅ 已加载' : '❌ 未加载'}`);\n\n        // 🔧 强制调用墙体创建，即使组件未正确加载\n        if (this.WallSceneView?.beforeStartGame) {\n            oops.log.logBusiness(`🏗️ 调用WallSceneView.beforeStartGame()`);\n            this.WallSceneView.beforeStartGame();\n        } else {\n            oops.log.logWarn('⚠️ WallSceneView组件未加载或beforeStartGame方法不存在');\n            // 🔧 尝试手动创建基础地板\n            this.createEmergencyFloor();\n        }\n\n        if (this.GameSceneView?.beforeStartGame) {\n            oops.log.logBusiness('🎮 调用GameSceneView.beforeStartGame()');\n            this.GameSceneView.beforeStartGame();\n\n            // 🎯 直接初始化收集槽管理器\n            if (this.interactionManager) {\n                this.interactionManager.slotManager = new CollectionSlotManager(this.GameSceneView);\n            }\n        } else {\n            oops.log.logWarn('⚠️ GameSceneView组件未加载');\n        }\n\n        if (this.GameUIView?.beforeStartGame) {\n            oops.log.logBusiness('🖥️ 调用GameUIView.beforeStartGame()');\n            this.GameUIView.beforeStartGame();\n        } else {\n            oops.log.logWarn('⚠️ GameUIView组件未加载');\n        }\n\n        this.restartFromSimpleMode();\n    }\n\n    /**\n     * 🔄 从简单模式重新开始游戏\n     */\n    public restartFromSimpleMode(): void {\n        // 🎯 强制设置为简单模式（状态回调会自动调用setupSimpleMode）\n        this.gameManager.changeState(GameState.SimpleMode);\n\n        // 🎮 开始游戏\n        this.startGame();\n    }\n\n    async startGame() {\n        // 🧹 统一清理游戏状态（避免重复清理）\n        this.GameModel.clear();\n\n        // 🧹 清理其他状态\n        if (this.interactionManager) {\n            this.interactionManager.sharedMaterial = null!;\n        }\n        this.isDissolveAnimating = false;\n        this.hardModeResourcesLoaded = false;\n\n        // 🎯 根据当前游戏状态初始化对应的道具（状态由外部设置，不在这里改变）\n        if (this.isSimpleMode()) {\n            this.gameManager.initializeEasyModeItems();\n        } else if (this.isHardMode()) {\n            this.gameManager.initializeHardModeItems();\n        }\n\n        // 🎯 统一创建道具（UnifiedGameManager会根据模式和新手状态选择策略）\n        this.gameManager.createInitialItemsInScene();\n\n        // 🎯 初始时禁用输入，然后统一处理输入启用\n        this.gameManager.setInputEnabled(false);\n\n        // 🎯 统一处理输入启用：检查是否需要消融动画\n        this.handleInputEnabling();\n    }\n\n    /**\n     * 🎯 检查游戏结果\n     */\n    checkResult() {\n        // 🔍 输出当前状态调试信息\n        const collectItemsCount = this.GameModel.collectItems.length;\n        const allItemsToPickCount = this.GameModel.allItemsToPick.size;\n        const gameMode = this.isSimpleMode() ? '简单模式' : '困难模式';\n\n        oops.log.logBusiness(\n            `🔍 检查游戏结果 [${gameMode}]: 收集槽=${collectItemsCount}, 场景物品=${allItemsToPickCount}`\n        );\n\n        // 检查是否所有物品都被收集\n        if (collectItemsCount === 0 && allItemsToPickCount === 0) {\n            // 游戏胜利\n            oops.log.logBusiness(`🎉 ${gameMode}胜利！所有物品已收集`);\n            this.gameManager.changeState(GameState.Win);\n            return;\n        }\n\n        // 检查是否收集区已满 - 但要考虑三消可能性\n        if (collectItemsCount >= 7) {\n            // 🔍 检查是否有三消的可能性\n            if (this.hasPossibleMerge()) {\n                oops.log.logBusiness('🎯 收集区已满，但检测到可能的三消，等待三消完成...');\n                // 有三消可能性，不立即结束游戏，等待三消逻辑处理\n                return;\n            }\n\n            // 没有三消可能性，游戏失败\n            oops.log.logBusiness('💥 游戏失败！收集区已满且无法三消');\n            this.gameManager.changeState(GameState.GameOver);\n        }\n    }\n\n    /**\n     * 🔍 检查是否有可能的三消\n     */\n    private hasPossibleMerge(): boolean {\n        if (!this.interactionManager?.slotManager) {\n            return false;\n        }\n\n        // 统计每种物品的数量\n        const itemCounts: { [key: string]: number } = {};\n        for (const item of this.GameModel.collectItems) {\n            const name = item.name;\n            itemCounts[name] = (itemCounts[name] || 0) + 1;\n        }\n\n        // 检查是否有任何物品数量>=3\n        for (const count of Object.values(itemCounts)) {\n            if (count >= 3) {\n                return true;\n            }\n        }\n\n        return false;\n    }\n\n    onUseProp(_?: any, args?: UsePropArgs) {\n        console.log('使用道具:', args);\n    }\n\n    handleUseProp(_?: any, args?: UsePropArgs) {\n        console.log('使用道具:', args);\n    }\n\n    tryUseProp(usePropArgs: UsePropArgs, toastMsg = true): boolean {\n        return true;\n    }\n\n    /**\n     * 🔄 处理简单模式到困难模式的切换\n     * 由GameResultManager调用，处理模式切换的完整流程\n     */\n    public async handleSimpleModeToHardMode(): Promise<void> {\n        // 🎯 禁用输入，防止玩家操作\n        this.gameManager.setInputEnabled(false);\n\n        // 🧹 确保Guide系统被完全清理\n        this.ensureGuideSystemCleanup();\n\n        // 🎬 确保困难模式资源已预加载（显示Loading UI如果需要）\n        await this.ensureHardModeResourcesPreloaded(true);\n\n        // 🧹 统一清理游戏状态（避免重复清理）\n        this.GameModel.clear();\n\n        // 🧹 清理其他状态\n        if (this.interactionManager) {\n            this.interactionManager.sharedMaterial = null!;\n        }\n        this.isDissolveAnimating = false;\n\n        // 🔄 切换到困难模式（状态回调会自动调用setupHardMode）\n        this.gameManager.changeState(GameState.HardMode);\n\n        // 🎯 初始化困难模式道具数组\n        this.gameManager.initializeHardModeItems();\n\n        // 🎯 创建困难模式道具\n        this.gameManager.createInitialItemsInScene();\n\n        // 🎯 处理输入启用\n        this.handleInputEnabling();\n\n        oops.log.logBusiness('✅ 困难模式切换完成，重复游戏流程开始');\n    }\n\n    /**\n     * 🧹 确保Guide系统被完全清理\n     */\n    private ensureGuideSystemCleanup(): void {\n        try {\n            // 🎯 清理Guide系统\n            if (smc.guide && smc.guide.GuideModel) {\n                oops.log.logBusiness('🧹 清理Guide系统');\n                smc.guide.GuideModel.reset();\n            }\n\n            // 🎯 补充清理GuideView3DItemComp（Guide系统没有清理的部分）\n            if (this.GameSceneView && this.GameSceneView.node) {\n                const guideItemComponents = this.GameSceneView.node.getComponentsInChildren(\n                    'GuideView3DItem' as any\n                );\n                for (const comp of guideItemComponents) {\n                    if (comp && comp.node && comp.node.isValid) {\n                        oops.log.logBusiness(`🧹 清理3D引导组件: ${comp.node.name}`);\n                        comp.node.destroy();\n                    }\n                }\n            }\n\n            oops.log.logBusiness('✅ Guide系统清理完成');\n        } catch (error) {\n            oops.log.logWarn('⚠️ 清理Guide系统时出错:', error);\n        }\n    }\n\n    /**\n     * 💰 扣除挑战次数并记录挑战数据\n     * @returns Promise<boolean> 是否成功扣除\n     */\n    private async costChallengeAttempt(): Promise<boolean> {\n        try {\n            // 🔄 使用Role的updateProp方法扣除挑战次数（会自动调用服务端API记录数据）\n            const success = await smc.role.updateProp(\n                PropType.PropsDayLeftCount,\n                -1,\n                'start_hard_mode'\n            );\n\n            if (success) {\n                oops.log.logBusiness('✅ 困难模式挑战次数已扣除，挑战数据已记录');\n                return true;\n            } else {\n                oops.log.logBusiness('❌ 挑战次数扣除失败');\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 扣除挑战次数异常:', error);\n            return false;\n        }\n    }\n}\n\nexport class EcsSceneSystem extends ecs.System {\n    constructor() {\n        super();\n    }\n}\n"]}