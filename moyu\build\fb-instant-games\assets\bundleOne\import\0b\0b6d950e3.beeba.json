[1, ["d2lw3KtrZG9aD626X+kq2a", "bdc/VAaXlMH57gR5XTjf+v@dbca1"], ["node", "root", "data", "_mesh"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11]], [[3, 0, 2], [0, 0, 2], [1, 0, 1, 2, 2], [2, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 3, 4, 3], [7, 0, 1, 2, 3, 4, 5, 5], [8, 0, 1, 2, 3]], [[[[1, "面包_11"], [2, "面包_11", [[3, 4, -2, [0, "68xT5AbRtGwY/hsC41cKrB"]], [4, 1, -3, [0, "12j0xPavRMEbiTZzsPP317"], [0], [5, true, true], 1], [6, 0.5104589760303497, 0, -4, [0, "46klTxS1lEBI7StlDa1s8z"], [1, 0.002244323492050171, 0.007360674440860748, 0.046015314757823944]]], [7, "73U8rB9rlMbIZTVq5FA7VW", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]], [[[8, ".bin", 780985417, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 8016, "length": 1188, "count": 594, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 8016, "count": 167, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.22286997735500336, -0.4133371412754059, -0.1926344931125641], "maxPosition", 8, [1, 0.3365774154663086, 0.6564556956291199, 0.5393534302711487]]], -1], 0, 0, [], [], []]]]