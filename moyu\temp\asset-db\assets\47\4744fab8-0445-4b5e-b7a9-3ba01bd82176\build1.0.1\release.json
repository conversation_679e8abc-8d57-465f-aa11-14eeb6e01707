[1, ["bdG8q6vX1KcbFDmXyII4Pk@f9941", "c5bVW/H3hBT6UAof9xFQx+", "622je7I3dHiIx9vsWb3KE2", "22uVqlFnJGNpLDdM1Z+uwi@f9941", "80pt/43AZPnaLIAxcNqh3d", "bb/05UoiVKaY+5lJTDYZFT@f9941", "87KJdfUjNA1pqHCGJqumKH", "12CGLetYxEn5JY6OsQY9mT@f9941", "1eWFsMVo9G2Iy50BMhdO0J@f9941", "14I5OoJkFHdaecy/4b2eXK"], ["targetInfo", "node", "root", "asset", "target", "_spriteFrame", "_parent", "source", "value", "_backgroundImage", "_placeholder<PERSON><PERSON><PERSON>", "_textLabel", "_target", "data", "regPasswordAgainEBox", "regPasswordEBox", "regAccountEBox", "loginPasswordEBox", "loginAccountEBox"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_active", "_prefab", "_components", "_children", "_parent", "_lpos"], -2, 4, 12, 2, 1, 5], ["cc.Widget", ["_alignFlags", "_top", "_originalHeight", "_left", "_right", "_originalWidth", "_bottom", "_horizontalCenter", "_verticalCenter", "node", "__prefab"], -6, 1, 4], ["cc.Node", ["_name", "_layer", "_components", "_prefab", "_children", "_lpos", "_parent"], 1, 9, 4, 2, 5, 1], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedComponents", "mountedChil<PERSON>n"], 2, 1, 9, 9, 9], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["47052uw/Y5O1LXaLObj4ARx", ["watchPath", "valueAction", "valueA", "node", "__prefab", "watchNodes"], 0, 1, 4, 2], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Label", ["_string", "_horizontalAlign", "_actualFontSize", "_fontSize", "_overflow", "_enableWrapText", "node", "__prefab", "_color"], -3, 1, 4, 5], ["cc.TargetOverrideInfo", ["propertyPath", "target", "targetInfo", "source"], 2, 1, 4, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_children", "_components", "_prefab", "_lpos"], 1, 1, 12, 9, 4, 5], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 6], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 1], ["cc.TargetInfo", ["localID"], 2], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 9], ["4bff6xrIC5LtYScAfbMmEdR", ["_dataID", "node", "__prefab"], 2, 4, 4], ["4bff6xrIC5LtYScAfbMmEdR", ["_dataID", "node", "__prefab"], 2, 1, 4], ["cc.CompPrefabInfo", ["fileId"], 2], ["7d2a4voaOJJGJZRWFPG6Bk7", ["watchPath", "valueClamp", "node", "__prefab"], 1, 1, 4], ["cc.ClickEvent", ["_componentId", "handler", "customEventData", "target"], 0, 1], ["cc.MountedChildrenInfo", ["targetInfo", "nodes"], 3, 4, 2], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target"], 2, 1, 4, 5, 1], ["cc.Layout", ["_layoutType", "_paddingLeft", "_spacingX", "node", "__prefab"], 0, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["4e091gilDVKO7MagP5GRpoa", ["node", "__prefab", "loginNode", "registerNode"], 3, 1, 4, 1, 1], ["cc.EditBox", ["_inputMode", "_maxLength", "node", "__prefab"], 1, 1, 4]], [[21, 0, 2], [25, 0, 2], [14, 0, 1, 2, 3], [15, 0, 1, 2, 2], [16, 0, 1, 2, 2], [12, 0, 1, 2, 3, 4, 5, 5], [17, 0, 1, 2, 3], [4, 0, 1, 2, 1], [11, 0, 1, 2, 3, 4, 5, 4], [0, 2, 3, 8, 5, 3], [0, 0, 2], [22, 0, 1, 1], [4, 0, 1, 2, 3, 1], [2, 0, 1, 6, 4, 2, 3, 5, 3], [6, 0, 1, 2, 3, 4, 3], [3, 0, 1, 2, 2], [8, 0, 3, 1, 2, 2], [0, 0, 1, 7, 6, 5, 9, 3], [0, 0, 4, 1, 8, 6, 5, 9, 4], [0, 0, 1, 8, 6, 5, 9, 3], [3, 0, 1, 3, 2, 2], [18, 0, 1, 2, 2], [23, 0, 1, 2, 2], [24, 0, 1, 2, 2], [33, 0, 1, 2, 3, 3], [7, 0, 1, 2, 3, 4, 5, 6, 7, 7], [7, 0, 1, 2, 3, 4, 5, 6, 7, 8, 7], [5, 0, 1, 3, 4, 5, 3], [0, 2, 3, 5, 3], [2, 0, 1, 2, 3, 5, 3], [10, 0, 1, 2, 3, 4, 5, 6, 3], [3, 0, 1, 4, 2, 2], [19, 0, 1, 2, 2], [20, 0, 1, 2, 2], [1, 0, 3, 9, 10, 3], [1, 0, 4, 9, 10, 3], [1, 0, 1, 9, 10, 3], [1, 0, 3, 4, 5, 9, 10, 5], [26, 0, 1, 2, 3, 3], [27, 0, 1, 2, 3, 4], [28, 0, 1, 1], [6, 0, 2, 3, 4, 2], [29, 0, 1, 2, 3, 4, 2], [8, 0, 1, 2, 2], [9, 0, 2], [0, 0, 1, 7, 6, 5, 3], [0, 0, 4, 1, 7, 6, 5, 4], [2, 0, 1, 6, 4, 2, 3, 3], [2, 0, 1, 4, 2, 3, 5, 3], [13, 0, 1, 2, 3, 4, 5, 3], [1, 0, 1, 6, 2, 9, 10, 5], [1, 0, 7, 9, 10, 3], [1, 0, 1, 6, 8, 2, 9, 10, 6], [1, 0, 5, 2, 9, 10, 4], [4, 0, 1, 1], [5, 0, 2, 3, 4, 5, 3], [5, 0, 3, 4, 5, 2], [30, 0, 1, 2, 3, 4, 4], [31, 0, 1, 1], [32, 0, 1, 2, 3, 1]], [[44, "login"], [45, "login", ********, [-39, -40], [[[7, -35, [1, "c1hVvKAKJAsY8YKb40A5oY"], [5, 750, 1334]], [58, -36, [1, "48O7mvCMxPQ4BoitAgcnAc"]], [53, 45, 1080, 1920, -37, [1, "c9y1hDPe5P24tzvKVZtU1V"]], -38], 4, 4, 4, 1], [49, "22ahoiOOVHK7EfzrB8wn/z", null, -34, 0, [[43, ["loginAccountEBox"], -16, [0, ["916eopZOJCC6M9n2sVZLmV"]]], [43, ["loginPasswordEBox"], -17, [0, ["916eopZOJCC6M9n2sVZLmV"]]], [16, ["regPasswordEBox"], -19, -18, [0, ["916eopZOJCC6M9n2sVZLmV"]]], [16, ["regAccountEBox"], -21, -20, [0, ["916eopZOJCC6M9n2sVZLmV"]]], [16, ["regPasswordAgainEBox"], -23, -22, [0, ["916eopZOJCC6M9n2sVZLmV"]]], [16, ["loginAccountEBox"], -25, -24, [0, ["916eopZOJCC6M9n2sVZLmV"]]], [16, ["loginPasswordEBox"], -27, -26, [0, ["916eopZOJCC6M9n2sVZLmV"]]], [16, ["watchNodes", "0"], -29, -28, [0, ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]]], [16, ["watchNodes", "0"], -31, -30, [0, ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]]], [16, ["watchNodes", "0"], -33, -32, [0, ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]]]], [-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15]]], [46, "registerNode", false, ********, [-47, -48], [[[7, -41, [1, "53ALStLT1DtLO+NxNvn/jJ"], [5, 450, 430]], [50, 18, 788.*************, 445.4595, 100, -42, [1, "55DySH7ulNiJV2zNa9Cjqb"]], [14, 1, 0, -43, [1, "c3Hb3cXxVE9br/v3IR03nK"], 12], -44, -45, -46], 4, 4, 4, 1, 1, 1], [5, "26Qy8kmPtNCLF1vyA1FKNw", null, null, null, 1, 0]], [13, "content", ********, 2, [-51, -52, -53, -54, -55], [[7, -49, [1, "91jAhidJNMIo6xClycKcfL"], [5, 450, 297.8]], [36, 1, 80, -50, [1, "a0ZJjEDO1IPKL2XAxy/LPH"]]], [5, "1dFKxTwv5P9oI3z7DvI1lp", null, null, null, 1, 0], [1, 0, -13.***************, 0]], [48, "loginNode", ********, [-59, -60], [[7, -56, [1, "d4HrnCI3JCpJLrBrM0xHMm"], [5, 450, 380]], [52, 18, 788.*************, 445.4595, 24.283999999999992, 100, -57, [1, "e3nU+me0hFl4bVYXAaltmW"]], [14, 1, 0, -58, [1, "34e4QdZBRNprNvke31ZIZJ"], 25]], [5, "73Tql2xnNJ8KUCDd54Drkg", null, null, null, 1, 0], [1, 0, 24.283999999999992, 0]], [28, 0, {}, [8, "30Ahna/hFADJL5JEyiP8Kc", null, null, -78, [20, "ba8btBVWlN8rtzdW1lzRh/", 1, [[11, [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [[22, "back_login", [10, "New Node"], [1, "a7G/+Sb19JFpwDqpkEyJXY"]]]], [11, [0, ["30Ahna/hFADJL5JEyiP8Kc"]], [[35, 32, 60, -76, [1, "ffT/MdSLFBTrVPl7VC2TE7"]], [38, "*.is<PERSON><PERSON><PERSON>", true, -77, [1, "86fT4q6yJIwI2OxNSqxphW"]]]]], [[2, "back<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", ["_name"], -61], [3, ["_lpos"], -62, [1, 105, 0, 0]], [3, ["_lrot"], -63, [3, 0, 0, 0, 1]], [3, ["_euler"], -64, [1, 0, 0, 0]], [4, ["_lpos"], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 2.725999999999999, 0]], [2, 22, ["_fontSize"], -65], [4, ["_contentSize"], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 103.8125, 50.4]], [2, 22, ["_actualFontSize"], -66], [3, ["_color"], -67, [4, 4293362287]], [4, ["_contentSize"], [0, ["b2FpfgEc1Py4FLQrPbDjfF"]], [5, 120, 50]], [21, ["_spriteFrame"], -68, 7], [2, "register", ["_string"], -69], [2, 0, ["_sizeMode"], -70], [2, 1, ["clickEvents", "length"], -71], [32, ["clickEvents", "0"], -73, [39, "7d2a4voaOJJGJZRWFPG6Bk7", "vSub", "1", -72]], [33, ["_target"], -75, -74]]], 6]], [13, "content", ********, 4, [-81, -82, -83, -84], [[7, -79, [1, "8dmNebGE9B/YKhU7D46R5G"], [5, 450, 242.25]], [36, 1, 76.06200000000001, -80, [1, "f1aTjdyWBGmpHwRvBsbWlZ"]]], [5, "894SERsltI45hByEmMa91b", null, null, null, 1, 0], [1, 0, -7.187000000000012, 0]], [28, 0, {}, [8, "30Ahna/hFADJL5JEyiP8Kc", null, null, -102, [20, "a1moknUJlGnL65AOZvhQB0", 1, [[11, [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [[22, "register", [10, "New Node"], [1, "a7G/+Sb19JFpwDqpkEyJXY"]]]], [11, [0, ["30Ahna/hFADJL5JEyiP8Kc"]], [[51, 16, 3, -100, [1, "73Hkkg3JRKrLRVnGMqxRWn"]], [38, "*.is<PERSON><PERSON><PERSON>", true, -101, [1, "ferJPr/xtMNYKv/BqVZFmM"]]]]], [[2, "registerButton", ["_name"], -85], [3, ["_lpos"], -86, [1, 3, 0, 0]], [3, ["_lrot"], -87, [3, 0, 0, 0, 1]], [3, ["_euler"], -88, [1, 0, 0, 0]], [4, ["_lpos"], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 2.725999999999999, 0]], [2, 22, ["_fontSize"], -89], [4, ["_contentSize"], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 71.328125, 50.4]], [2, 22, ["_actualFontSize"], -90], [3, ["_color"], -91, [4, 4293362287]], [4, ["_contentSize"], [0, ["b2FpfgEc1Py4FLQrPbDjfF"]], [5, 120, 50]], [21, ["_spriteFrame"], -92, 18], [2, "register", ["_string"], -93], [2, 0, ["_sizeMode"], -94], [2, 1, ["clickEvents", "length"], -95], [32, ["clickEvents", "0"], -97, [39, "7d2a4voaOJJGJZRWFPG6Bk7", "vAdd", "1", -96]], [33, ["_target"], -99, -98]]], 17]], [47, "Node", ********, 1, [2, 4], [[54, -103, [1, "46BH2zQaZDHqZUETi982R9"]], [55, "*.is<PERSON><PERSON><PERSON>", 1, -104, [1, "1eFFnrDp1I/4XMtg46FfzV"], [2]], [56, "*.is<PERSON><PERSON><PERSON>", -105, [1, "7altCVp2hEc6fF0FD/mPhl"], [4]]], [5, "40U5xWXqpAaI7r+VZtuTbs", null, null, null, 1, 0]], [9, 0, {}, 3, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -110, [15, "5b9lrIiFJI5ZotGpBAGSqP", 1, [[2, "regAccountEditBoxNode", ["_name"], -106], [3, ["_lpos"], -107, [1, -16.7109375, 118.9, 0]], [3, ["_lrot"], -108, [3, 0, 0, 0, 1]], [3, ["_euler"], -109, [1, 0, 0, 0]]]], 1]], [9, 0, {}, 3, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -115, [15, "85wpl8wmhPPYgmJpsTUOv5", 1, [[2, "regPassEditBoxNode", ["_name"], -111], [3, ["_lpos"], -112, [1, -16.7109375, 58.***************, 0]], [3, ["_lrot"], -113, [3, 0, 0, 0, 1]], [3, ["_euler"], -114, [1, 0, 0, 0]], [6, "password", ["_dataID"], [0, ["eaXK79jL1DLI2yR06q+Pwy", "807dKXf5tHrJtEdIFnHMo0"]]], [4, ["_contentSize"], [0, ["eaXK79jL1DLI2yR06q+Pwy", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 118.34375, 37.8]], [6, "password_tips", ["_dataID"], [0, ["e34fdXI8pErIudzokGEhEC"]]], [6, "password_tips", ["_dataID"], [0, ["09sQr/0RFDc7MTGVUn+MyB", "800tdHtlJM2IVdpueUwzRq"]]], [4, ["_contentSize"], [0, ["09sQr/0RFDc7MTGVUn+MyB", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 93.515625, 27.72]], [6, "passwordMsg", ["_name"], [0, ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]]]]], 2]], [9, 0, {}, 3, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -120, [15, "74lYjpGkJIwIxH5cnZRGy/", 1, [[2, "regPassEditBoxNode2", ["_name"], -116], [3, ["_lpos"], -117, [1, -16.7109375, -1.0999999999999943, 0]], [3, ["_lrot"], -118, [3, 0, 0, 0, 1]], [3, ["_euler"], -119, [1, 0, 0, 0]], [6, "password_again", ["_dataID"], [0, ["e34fdXI8pErIudzokGEhEC"]]], [6, "repeat_password", ["_dataID"], [0, ["09sQr/0RFDc7MTGVUn+MyB", "800tdHtlJM2IVdpueUwzRq"]]], [4, ["_contentSize"], [0, ["09sQr/0RFDc7MTGVUn+MyB", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 113.4375, 27.72]], [6, "repeat_password", ["_dataID"], [0, ["eaXK79jL1DLI2yR06q+Pwy", "807dKXf5tHrJtEdIFnHMo0"]]], [4, ["_contentSize"], [0, ["eaXK79jL1DLI2yR06q+Pwy", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 211.75, 37.8]], [6, "passwordMsg2", ["_name"], [0, ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]]]]], 3]], [9, 0, {}, 6, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -123, [15, "d0Iji3M5BAbLz5cdn3Y8A8", 1, [[6, "loginAccount", ["_name"], [0, ["f05XX5jrpEOYwv6lCoUIav"]]], [4, ["_lpos"], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 91.125, 0]], [4, ["_lrot"], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [3, 0, 0, 0, 1]], [4, ["_euler"], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 0, 0]], [2, "loginErrMsg", ["_name"], -121], [6, "login_fail", ["_dataID"], [0, ["09sQr/0RFDc7MTGVUn+MyB", "800tdHtlJM2IVdpueUwzRq"]]], [4, ["_contentSize"], [0, ["09sQr/0RFDc7MTGVUn+MyB", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 58.********, 27.72]], [6, "account_placeholder", ["_dataID"], [0, ["e34fdXI8pErIudzokGEhEC"]]], [2, false, ["_active"], -122]]], 13]], [9, 0, {}, 6, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -130, [15, "634xbzy75PRJnOkPOSxX3u", 1, [[2, "loginPassword", ["_name"], -124], [3, ["_lpos"], -125, [1, 0, 28.9, 0]], [3, ["_lrot"], -126, [3, 0, 0, 0, 1]], [3, ["_euler"], -127, [1, 0, 0, 0]], [6, "password", ["_dataID"], [0, ["eaXK79jL1DLI2yR06q+Pwy", "807dKXf5tHrJtEdIFnHMo0"]]], [4, ["_contentSize"], [0, ["eaXK79jL1DLI2yR06q+Pwy", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 118.34375, 37.8]], [6, "password_placeholder", ["_dataID"], [0, ["e34fdXI8pErIudzokGEhEC"]]], [6, "password_tips", ["_dataID"], [0, ["09sQr/0RFDc7MTGVUn+MyB", "800tdHtlJM2IVdpueUwzRq"]]], [4, ["_contentSize"], [0, ["09sQr/0RFDc7MTGVUn+MyB", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 93.515625, 27.72]], [2, "accountsg", ["_name"], -128], [4, ["_contentSize"], [0, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 376.578125, 64.45]], [4, ["_lpos"], [0, ["eaXK79jL1DLI2yR06q+Pwy", "f05XX5jrpEOYwv6lCoUIav"]], [1, -140, -13.***************, 0]], [2, false, ["_active"], -129], [4, ["_lpos"], [0, ["ddOfelTBZE1rggF7WlbAIs"]], [1, 0, -15.36, 0]], [4, ["_contentSize"], [0, ["b8YzmdduxAio6kjkLI8vm8"]], [5, 250, 59.***************]]]], 14]], [13, "buttonNode", ********, 6, [-133, 7, -134], [[7, -131, [1, "974ighhdRF4bpCSC8J7wEd"], [5, 450, 80]], [57, 1, 38, 10, -132, [1, "1fTrxsgG1FWJ4zdjStClba"]]], [5, "70BT4StMlEsoDheY+TOddu", null, null, null, 1, 0], [1, 0, -43.**************, 0]], [59, 1, [1, "212kEVGDJGvbTS/ufre90c"], 4, 2], [17, "EditBox", ********, [-138, -139], [[[7, -135, [1, "7ae2ep09FLHoObQPI3gzpL"], [5, 250, 29.255]], [14, 1, 0, -136, [1, "8dD7VWwtxHhbP6eNxeH7O8"], 26], -137], 4, 4, 1], [5, "ddOfelTBZE1rggF7WlbAIs", null, null, null, 1, 0], [1, 0, -15.36, 0]], [17, "EditBox", ********, [-143, -144], [[[7, -140, [1, "7ae2ep09FLHoObQPI3gzpL"], [5, 250, 29.255]], [14, 1, 0, -141, [1, "8dD7VWwtxHhbP6eNxeH7O8"], 27], -142], 4, 4, 1], [5, "ddOfelTBZE1rggF7WlbAIs", null, null, null, 1, 0], [1, 0, -15.36, 0]], [17, "EditBox", ********, [-148, -149], [[[7, -145, [1, "7ae2ep09FLHoObQPI3gzpL"], [5, 250, 29.255]], [14, 1, 0, -146, [1, "8dD7VWwtxHhbP6eNxeH7O8"], 28], -147], 4, 4, 1], [5, "ddOfelTBZE1rggF7WlbAIs", null, null, null, 1, 0], [1, 0, -15.36, 0]], [17, "EditBox", ********, [-153, -154], [[[7, -150, [1, "7ae2ep09FLHoObQPI3gzpL"], [5, 250, 29.255]], [14, 1, 0, -151, [1, "8dD7VWwtxHhbP6eNxeH7O8"], 29], -152], 4, 4, 1], [5, "ddOfelTBZE1rggF7WlbAIs", null, null, null, 1, 0], [1, 0, -15.36, 0]], [17, "EditBox", ********, [-158, -159], [[[7, -155, [1, "7ae2ep09FLHoObQPI3gzpL"], [5, 250, 29.255]], [14, 1, 0, -156, [1, "8dD7VWwtxHhbP6eNxeH7O8"], 30], -157], 4, 4, 1], [5, "ddOfelTBZE1rggF7WlbAIs", null, null, null, 1, 0], [1, 0, -15.36, 0]], [29, "googleButton1", ********, [[7, -160, [1, "a7IniUOrJMn4OVhP+zXD/h"], [5, 29, 29]], [41, 1, -161, [1, "3aEXlNDuBHi6RspGfG9A9K"], 9], [42, 3, -163, [1, "54AXdvYh5IBZHP2nTJTrtv"], [4, 4292269782], -162]], [5, "eberLAwtlAiYb1q0OLYgTd", null, null, null, 1, 0], [1, 0, -35, 0]], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [13, "title", ********, 2, [-167], [[7, -164, [1, "62Qj49XppOXJIqSfy8Q0m2"], [5, 450, 73]], [14, 1, 0, -165, [1, "91M27zGNdDwbtFVhycwtUp"], 11], [37, 17, 318.5, 315.5, 116, -166, [1, "0d60y6oRNJRalPCLkC95ok"]]], [5, "abejEqSatFoIWRKtvApQuu", null, null, null, 1, 0], [1, 0, 178.5, 0]], [29, "googleButton2", ********, [[7, -168, [1, "a7IniUOrJMn4OVhP+zXD/h"], [5, 29, 29]], [41, 1, -169, [1, "3aEXlNDuBHi6RspGfG9A9K"], 22], [42, 3, -171, [1, "54AXdvYh5IBZHP2nTJTrtv"], [4, 4292269782], -170]], [5, "eberLAwtlAiYb1q0OLYgTd", null, null, null, 1, 0], [1, 0, -45, 0]], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [13, "title", ********, 4, [-175], [[7, -172, [1, "3eVXTyo71M4aXvsA1Zi316"], [5, 450, 73]], [14, 1, 0, -173, [1, "7fWpa8ylpGzIeCj1XkLZje"], 24], [37, 17, 318.5, 315.5, 116, -174, [1, "88zq088pJDaLMmhcvBSOQ9"]]], [5, "9eZwfu8E9Da4BWz79aiIJP", null, null, null, 1, 0], [1, 0, 153.5, 0]], [0, ["a0daVw8DRLi6ToMaTA0VS2"]], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [13, "buttonNode", ********, 3, [-177, 5], [[7, -176, [1, "73DPhbu79G7rYCIgWu4Klz"], [5, 450, 80]]], [5, "04hwQPIxVPTrcSZzjXTOg2", null, null, null, 1, 0], [1, 0, -71.1, 0]], [9, 0, {}, 31, [8, "30Ahna/hFADJL5JEyiP8Kc", null, null, -189, [20, "c3BaD5fEJPwKTCDGqayVk2", 1, [[11, [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [[22, "confirm_register", [10, "New Node"], [1, "91YLLzSvhHr5ZfxoEExPFG"]]]], [11, [0, ["30Ahna/hFADJL5JEyiP8Kc"]], [[34, 8, 60, -188, [1, "07TjJyIEBLLbpR42DbmG5Z"]]]]], [[2, "confirmRegButton", ["_name"], -178], [3, ["_lpos"], -179, [1, -105, 0, 0]], [3, ["_lrot"], -180, [3, 0, 0, 0, 1]], [3, ["_euler"], -181, [1, 0, 0, 0]], [4, ["_lpos"], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 2.725999999999999, 0]], [2, 22, ["_fontSize"], -182], [4, ["_contentSize"], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 154.171875, 50.4]], [2, 22, ["_actualFontSize"], -183], [3, ["_color"], -184, [4, 4293362287]], [4, ["_contentSize"], [0, ["b2FpfgEc1Py4FLQrPbDjfF"]], [5, 120, 50]], [21, ["_spriteFrame"], -185, 5], [2, 0, ["_sizeMode"], -186], [2, "login_button", ["_string"], -187]]], 4]], [0, ["30Ahna/hFADJL5JEyiP8Kc"]], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]], [0, ["30Ahna/hFADJL5JEyiP8Kc"]], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]], [9, 0, {}, 3, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -193, [31, "96r+Y8PRBOia4qSHCljRm5", 1, [[40, [0, ["f05XX5jrpEOYwv6lCoUIav"]], [21]]], [[2, "otherway", ["_name"], 22], [3, ["_lpos"], 22, [1, 0, -130, 0]], [3, ["_lrot"], 22, [3, 0, 0, 0, 1]], [3, ["_euler"], 22, [1, 0, 0, 0]], [6, "other_login_way", ["_dataID"], [0, ["48BGi+JnJOKpaEdqvfVVS1"]]], [4, ["_contentSize"], [0, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 129.8671875, 37.8]], [2, "other_login_way", ["_string"], -190], [2, 18, ["_fontSize"], -191], [2, 18, ["_actualFontSize"], -192], [2, true, ["_active"], 22]]], 8]], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [9, 0, {}, 14, [8, "30Ahna/hFADJL5JEyiP8Kc", null, null, -205, [20, "ac+bRd6uxGJoe+IpsGyyaQ", 1, [[11, [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [[22, "login_button", [10, "New Node"], [1, "91YLLzSvhHr5ZfxoEExPFG"]]]], [11, [0, ["30Ahna/hFADJL5JEyiP8Kc"]], [[34, 8, 38, -204, [1, "eeapftaz5BgI1+fsgRuNiC"]]]]], [[2, "loginButton", ["_name"], -194], [3, ["_lpos"], -195, [1, -127, 0, 0]], [3, ["_lrot"], -196, [3, 0, 0, 0, 1]], [3, ["_euler"], -197, [1, 0, 0, 0]], [4, ["_lpos"], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 2.725999999999999, 0]], [2, 22, ["_fontSize"], -198], [4, ["_contentSize"], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 119.625, 50.4]], [2, 22, ["_actualFontSize"], -199], [3, ["_color"], -200, [4, 4293362287]], [4, ["_contentSize"], [0, ["b2FpfgEc1Py4FLQrPbDjfF"]], [5, 120, 50]], [21, ["_spriteFrame"], -201, 16], [2, 0, ["_sizeMode"], -202], [2, "login_button", ["_string"], -203]]], 15]], [0, ["30Ahna/hFADJL5JEyiP8Kc"]], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]], [0, ["30Ahna/hFADJL5JEyiP8Kc"]], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]], [9, 0, {}, 14, [8, "30Ahna/hFADJL5JEyiP8Kc", null, null, -217, [20, "2c/mYp1c1JqoZcuObrurp9", 1, [[11, [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [[22, "login_guest", [10, "New Node"], [1, "a7G/+Sb19JFpwDqpkEyJXY"]]]], [11, [0, ["30Ahna/hFADJL5JEyiP8Kc"]], [[35, 32, 32, -216, [1, "d3XhKv58VOi4vrQptxjRba"]]]]], [[2, "loginGuestButton", ["_name"], -206], [3, ["_lpos"], -207, [1, 133, 0, 0]], [3, ["_lrot"], -208, [3, 0, 0, 0, 1]], [3, ["_euler"], -209, [1, 0, 0, 0]], [4, ["_lpos"], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 2.725999999999999, 0]], [2, 22, ["_fontSize"], -210], [4, ["_contentSize"], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 109.9140625, 50.4]], [2, 22, ["_actualFontSize"], -211], [3, ["_color"], -212, [4, 4285219288]], [4, ["_contentSize"], [0, ["b2FpfgEc1Py4FLQrPbDjfF"]], [5, 120, 50]], [21, ["_spriteFrame"], -213, 20], [2, "login_guest", ["_string"], -214], [2, 0, ["_sizeMode"], -215]]], 19]], [0, ["30Ahna/hFADJL5JEyiP8Kc"]], [0, ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]], [9, 0, {}, 6, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -221, [31, "e0YF1EG7VIn6/1FWZrzM9Z", 1, [[40, [0, ["f05XX5jrpEOYwv6lCoUIav"]], [24]]], [[2, "otherway", ["_name"], 25], [3, ["_lpos"], 25, [1, 0, -102.225, 0]], [3, ["_lrot"], 25, [3, 0, 0, 0, 1]], [3, ["_euler"], 25, [1, 0, 0, 0]], [6, "other_login_way", ["_dataID"], [0, ["48BGi+JnJOKpaEdqvfVVS1"]]], [4, ["_contentSize"], [0, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 129.8671875, 37.8]], [2, "other_login_way", ["_string"], -218], [2, 18, ["_fontSize"], -219], [2, 18, ["_actualFontSize"], -220], [2, true, ["_active"], 25]]], 21]], [19, "PLACEHOLDER_LABEL", ********, 16, [[[12, -222, [1, "65PABYbBFD+oKiMMB1/8CQ"], [5, 248, 29.255], [0, 0, 1]], -223, [23, "account_placeholder", -224, [1, "e34fdXI8pErIudzokGEhEC"]]], 4, 1, 4], [5, "afI3gpV49F66OArF8TDqq2", null, null, null, 1, 0], [1, -123, 14.6275, 0]], [19, "PLACEHOLDER_LABEL", ********, 17, [[[12, -225, [1, "65PABYbBFD+oKiMMB1/8CQ"], [5, 248, 29.255], [0, 0, 1]], -226, [23, "password_placeholder", -227, [1, "e34fdXI8pErIudzokGEhEC"]]], 4, 1, 4], [5, "afI3gpV49F66OArF8TDqq2", null, null, null, 1, 0], [1, -123, 14.6275, 0]], [19, "PLACEHOLDER_LABEL", ********, 18, [[[12, -228, [1, "65PABYbBFD+oKiMMB1/8CQ"], [5, 248, 29.255], [0, 0, 1]], -229, [23, "username_tips", -230, [1, "e34fdXI8pErIudzokGEhEC"]]], 4, 1, 4], [5, "afI3gpV49F66OArF8TDqq2", null, null, null, 1, 0], [1, -123, 14.6275, 0]], [19, "PLACEHOLDER_LABEL", ********, 19, [[[12, -231, [1, "65PABYbBFD+oKiMMB1/8CQ"], [5, 248, 29.255], [0, 0, 1]], -232, [23, "password_tips", -233, [1, "e34fdXI8pErIudzokGEhEC"]]], 4, 1, 4], [5, "afI3gpV49F66OArF8TDqq2", null, null, null, 1, 0], [1, -123, 14.6275, 0]], [19, "PLACEHOLDER_LABEL", ********, 20, [[[12, -234, [1, "65PABYbBFD+oKiMMB1/8CQ"], [5, 248, 29.255], [0, 0, 1]], -235, [23, "password_again", -236, [1, "e34fdXI8pErIudzokGEhEC"]]], 4, 1, 4], [5, "afI3gpV49F66OArF8TDqq2", null, null, null, 1, 0], [1, -123, 14.6275, 0]], [9, 0, {}, 1, [8, "a0daVw8DRLi6ToMaTA0VS2", null, null, -237, [15, "6cH5XhLHlJb5HbIIaNX3/i", 1, [[2, "mask", ["_name"], 27], [3, ["_lpos"], 27, [1, 0, 0, 0]], [3, ["_lrot"], 27, [3, 0, 0, 0, 1]], [3, ["_euler"], 27, [1, 0, 0, 0]], [4, ["_contentSize"], [0, ["77N2cid5pKDpXplRH/AWEU"]], [5, 750, 1334]]]], 0]], [0, ["43UvszQV9LRKWvhBXHpprD"]], [0, ["4a5atXBglJxJGAlAL90RE0"]], [9, 0, {}, 23, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -240, [15, "44CyLpHnhFKIxOby/HO+WK", 1, [[2, "title", ["_name"], 38], [3, ["_lpos"], 38, [1, 0, 0, 0]], [3, ["_lrot"], 38, [3, 0, 0, 0, 1]], [3, ["_euler"], 38, [1, 0, 0, 0]], [2, true, ["_enableOutline"], -238], [4, ["_contentSize"], [0, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 133.6875, 54.4]], [2, "Settings", ["_string"], -239], [6, "register", ["_dataID"], [0, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]]]], 10]], [0, ["43UvszQV9LRKWvhBXHpprD"]], [0, ["4a5atXBglJxJGAlAL90RE0"]], [9, 0, {}, 26, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -241, [15, "b6EypWCVlMda/goZdetqsG", 1, [[6, "title", ["_name"], [0, ["f05XX5jrpEOYwv6lCoUIav"]]], [4, ["_lpos"], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 0, 0]], [4, ["_lrot"], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [3, 0, 0, 0, 1]], [4, ["_euler"], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 0, 0]], [6, true, ["_enableOutline"], [0, ["4a5atXBglJxJGAlAL90RE0"]]], [4, ["_contentSize"], [0, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 209.78125, 54.4]], [6, "Settings", ["_string"], [0, ["4a5atXBglJxJGAlAL90RE0"]]], [6, "login_game", ["_dataID"], [0, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]]]], 23]], [18, "TEXT_LABEL", false, ********, 16, [[[12, -242, [1, "0bEfqhR8lOTZtnQhHBoMMX"], [5, 248, 29.255], [0, 0, 1]], -243], 4, 1], [5, "2bXdpKVOFLVZlwE9ooS0gn", null, null, null, 1, 0], [1, -123, 14.6275, 0]], [18, "TEXT_LABEL", false, ********, 17, [[[12, -244, [1, "0bEfqhR8lOTZtnQhHBoMMX"], [5, 248, 29.255], [0, 0, 1]], -245], 4, 1], [5, "2bXdpKVOFLVZlwE9ooS0gn", null, null, null, 1, 0], [1, -123, 14.6275, 0]], [18, "TEXT_LABEL", false, ********, 18, [[[12, -246, [1, "0bEfqhR8lOTZtnQhHBoMMX"], [5, 248, 29.255], [0, 0, 1]], -247], 4, 1], [5, "2bXdpKVOFLVZlwE9ooS0gn", null, null, null, 1, 0], [1, -123, 14.6275, 0]], [18, "TEXT_LABEL", false, ********, 19, [[[12, -248, [1, "0bEfqhR8lOTZtnQhHBoMMX"], [5, 248, 29.255], [0, 0, 1]], -249], 4, 1], [5, "2bXdpKVOFLVZlwE9ooS0gn", null, null, null, 1, 0], [1, -123, 14.6275, 0]], [18, "TEXT_LABEL", false, ********, 20, [[[12, -250, [1, "0bEfqhR8lOTZtnQhHBoMMX"], [5, 248, 29.255], [0, 0, 1]], -251], 4, 1], [5, "2bXdpKVOFLVZlwE9ooS0gn", null, null, null, 1, 0], [1, -123, 14.6275, 0]], [0, ["daO3pUYmlKVIgsdc588k+9"]], [0, ["daO3pUYmlKVIgsdc588k+9"]], [0, ["4a5atXBglJxJGAlAL90RE0"]], [27, "*.isSafeAccount", 1, 2, [1, "089bzZzS5Agq5DkwyVm0pA"], [-252]], [10, "New Node"], [27, "*.isSafePassword", 1, 2, [1, "b2eYvABXlLNbL1WUAkUwMW"], [-253]], [10, "New Node"], [27, "*.isPasswordMatch", 1, 2, [1, "15yqvxD+xA9boemiPclS70"], [-254]], [10, "New Node"], [0, ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]], [0, ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]], [0, ["daO3pUYmlKVIgsdc588k+9"]], [0, ["daO3pUYmlKVIgsdc588k+9"]], [0, ["daO3pUYmlKVIgsdc588k+9"]], [24, 6, 8, 16, [1, "916eopZOJCC6M9n2sVZLmV"]], [30, "node", ********, 12, [[[10, "New Node"], 16], 4, 1], [[7, -255, [1, "b8YzmdduxAio6kjkLI8vm8"], [5, 250, 60]]], [5, "69q9sWWqJA97+Y5p9TX9uk", null, null, null, 1, 0], [1, 63.2890625, 0, 0]], [25, "", 0, 40, 20, 1, false, 61, [1, "71wbpeDSRJJIR6SalXggaP"]], [26, "account_placeholder", 0, 20, 20, 1, false, 49, [1, "28jx8A8HJIlpFtWwyDq5Bf"], [4, **********]], [24, 6, 8, 17, [1, "916eopZOJCC6M9n2sVZLmV"]], [30, "node", ********, 13, [[[10, "New Node"], 17], 4, 1], [[7, -256, [1, "b8YzmdduxAio6kjkLI8vm8"], [5, 250, 59.***************]]], [5, "69q9sWWqJA97+Y5p9TX9uk", null, null, null, 1, 0], [1, 63.2890625, 0, 0]], [25, "", 0, 40, 20, 1, false, 62, [1, "71wbpeDSRJJIR6SalXggaP"]], [26, "password_placeholder", 0, 20, 20, 1, false, 50, [1, "28jx8A8HJIlpFtWwyDq5Bf"], [4, **********]], [24, 6, 8, 18, [1, "916eopZOJCC6M9n2sVZLmV"]], [13, "node", ********, 9, [70, 18], [[7, -257, [1, "b8YzmdduxAio6kjkLI8vm8"], [5, 250, 60]]], [5, "69q9sWWqJA97+Y5p9TX9uk", null, null, null, 1, 0], [1, 63.2890625, 0, 0]], [25, "", 0, 40, 20, 1, false, 63, [1, "71wbpeDSRJJIR6SalXggaP"]], [26, "username_tips", 0, 20, 20, 1, false, 51, [1, "28jx8A8HJIlpFtWwyDq5Bf"], [4, **********]], [24, 6, 8, 19, [1, "916eopZOJCC6M9n2sVZLmV"]], [13, "node", ********, 10, [72, 19], [[7, -258, [1, "b8YzmdduxAio6kjkLI8vm8"], [5, 250, 60]]], [5, "69q9sWWqJA97+Y5p9TX9uk", null, null, null, 1, 0], [1, 63.2890625, 0, 0]], [25, "", 0, 40, 20, 1, false, 64, [1, "71wbpeDSRJJIR6SalXggaP"]], [26, "username_tips", 0, 20, 20, 1, false, 52, [1, "28jx8A8HJIlpFtWwyDq5Bf"], [4, **********]], [24, 6, 8, 20, [1, "916eopZOJCC6M9n2sVZLmV"]], [13, "node", ********, 11, [74, 20], [[7, -259, [1, "b8YzmdduxAio6kjkLI8vm8"], [5, 250, 60]]], [5, "69q9sWWqJA97+Y5p9TX9uk", null, null, null, 1, 0], [1, 63.2890625, 0, 0]], [25, "", 0, 40, 20, 1, false, 65, [1, "71wbpeDSRJJIR6SalXggaP"]], [26, "username_tips", 0, 20, 20, 1, false, 53, [1, "28jx8A8HJIlpFtWwyDq5Bf"], [4, **********]]], 0, [0, -1, 60, 0, -2, 48, 0, -3, 45, 0, -4, 7, 0, -5, 40, 0, -6, 13, 0, -7, 12, 0, -8, 57, 0, -9, 37, 0, -10, 5, 0, -11, 32, 0, -12, 11, 0, -13, 10, 0, -14, 9, 0, -15, 54, 0, 4, 12, 0, 4, 13, 0, 4, 10, 0, 7, 15, 0, 4, 9, 0, 7, 15, 0, 4, 11, 0, 7, 15, 0, 4, 12, 0, 7, 15, 0, 4, 13, 0, 7, 15, 0, 4, 9, 0, 7, 69, 0, 4, 10, 0, 7, 71, 0, 4, 11, 0, 7, 73, 0, 2, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, -4, 15, 0, -1, 54, 0, -2, 8, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, -4, 69, 0, -5, 71, 0, -6, 73, 0, -1, 3, 0, -2, 23, 0, 1, 3, 0, 1, 3, 0, -1, 9, 0, -2, 10, 0, -3, 11, 0, -4, 31, 0, -5, 37, 0, 1, 4, 0, 1, 4, 0, 1, 4, 0, -1, 6, 0, -2, 26, 0, 0, 35, 0, 0, 35, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 36, 0, 0, 67, 0, 0, 36, 0, 0, 67, 0, 0, 55, 0, 4, 5, 0, 0, 55, 0, 8, 5, 0, 0, 55, 0, 1, 5, 0, 1, 5, 0, 2, 5, 0, 1, 6, 0, 1, 6, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, -4, 48, 0, 0, 43, 0, 0, 43, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 44, 0, 0, 78, 0, 0, 44, 0, 0, 78, 0, 0, 58, 0, 4, 7, 0, 0, 58, 0, 8, 7, 0, 0, 58, 0, 1, 7, 0, 1, 7, 0, 2, 7, 0, 1, 8, 0, 1, 8, 0, 1, 8, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, 0, 28, 0, 2, 9, 0, 0, 29, 0, 0, 29, 0, 0, 29, 0, 0, 29, 0, 2, 10, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 2, 11, 0, 0, 75, 0, 0, 75, 0, 2, 12, 0, 0, 39, 0, 0, 39, 0, 0, 39, 0, 0, 39, 0, 0, 76, 0, 0, 76, 0, 2, 13, 0, 1, 14, 0, 1, 14, 0, -1, 40, 0, -3, 45, 0, 1, 16, 0, 1, 16, 0, -3, 80, 0, -1, 61, 0, -2, 49, 0, 1, 17, 0, 1, 17, 0, -3, 84, 0, -1, 62, 0, -2, 50, 0, 1, 18, 0, 1, 18, 0, -3, 88, 0, -1, 63, 0, -2, 51, 0, 1, 19, 0, 1, 19, 0, -3, 92, 0, -1, 64, 0, -2, 52, 0, 1, 20, 0, 1, 20, 0, -3, 96, 0, -1, 65, 0, -2, 53, 0, 1, 21, 0, 1, 21, 0, 12, 21, 0, 1, 21, 0, 1, 23, 0, 1, 23, 0, 1, 23, 0, -1, 57, 0, 1, 24, 0, 1, 24, 0, 12, 24, 0, 1, 24, 0, 1, 26, 0, 1, 26, 0, 1, 26, 0, -1, 60, 0, 1, 31, 0, -1, 32, 0, 0, 33, 0, 0, 33, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 34, 0, 0, 66, 0, 0, 66, 0, 0, 34, 0, 1, 32, 0, 2, 32, 0, 0, 56, 0, 0, 56, 0, 0, 56, 0, 2, 37, 0, 0, 41, 0, 0, 41, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 42, 0, 0, 77, 0, 0, 77, 0, 0, 42, 0, 1, 40, 0, 2, 40, 0, 0, 46, 0, 0, 46, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 47, 0, 0, 79, 0, 0, 47, 0, 0, 79, 0, 1, 45, 0, 2, 45, 0, 0, 59, 0, 0, 59, 0, 0, 59, 0, 2, 48, 0, 1, 49, 0, -2, 83, 0, 1, 49, 0, 1, 50, 0, -2, 87, 0, 1, 50, 0, 1, 51, 0, -2, 91, 0, 1, 51, 0, 1, 52, 0, -2, 95, 0, 1, 52, 0, 1, 53, 0, -2, 99, 0, 1, 53, 0, 2, 54, 0, 0, 68, 0, 0, 68, 0, 2, 57, 0, 2, 60, 0, 1, 61, 0, -2, 82, 0, 1, 62, 0, -2, 86, 0, 1, 63, 0, -2, 90, 0, 1, 64, 0, -2, 94, 0, 1, 65, 0, -2, 98, 0, -1, 70, 0, -1, 72, 0, -1, 74, 0, 1, 81, 0, 1, 85, 0, 1, 89, 0, 1, 93, 0, 1, 97, 0, 13, 1, 2, 6, 8, 4, 6, 8, 5, 6, 31, 7, 6, 14, 15, 14, 96, 15, 15, 92, 15, 16, 88, 15, 17, 84, 15, 18, 80, 16, 6, 81, 17, 6, 85, 18, 6, 89, 19, 6, 93, 20, 6, 97, 21, 6, 37, 24, 6, 48, 80, 10, 83, 80, 11, 82, 84, 10, 87, 84, 11, 86, 88, 10, 91, 88, 11, 90, 92, 10, 95, 92, 11, 94, 96, 10, 99, 96, 11, 98, 259], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 80, 84, 88, 92, 96], [3, 3, 3, 3, 3, 8, 3, 8, 3, 5, 3, 5, 5, 3, 3, 3, 8, 3, 8, 3, 8, 3, 5, 3, 5, 5, 5, 5, 5, 5, 5, 9, 9, 9, 9, 9], [9, 1, 1, 1, 2, 3, 2, 3, 4, 5, 6, 7, 8, 1, 1, 2, 3, 2, 3, 2, 3, 4, 5, 6, 7, 8, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]]