System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, Vec3, oops, Utils, simpleLoader, smc, GuideView3DItemComp, configManager, UnifiedGameManager, _crd, GameState;

  function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUtils(extras) {
    _reporterNs.report("Utils", "../../utils/Utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsimpleLoader(extras) {
    _reporterNs.report("simpleLoader", "../common/loader/SimpleLoadingManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "../common/SingletonModuleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelConfig(extras) {
    _reporterNs.report("LevelConfig", "../common/table/LevelConfigTypes", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGuideView3DItemComp(extras) {
    _reporterNs.report("GuideView3DItemComp", "../guide/view/GuideView3DItemComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfconfigManager(extras) {
    _reporterNs.report("configManager", "./ConfigManager", _context.meta, extras);
  }

  _export("UnifiedGameManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      Utils = _unresolved_3.Utils;
    }, function (_unresolved_4) {
      simpleLoader = _unresolved_4.simpleLoader;
    }, function (_unresolved_5) {
      smc = _unresolved_5.smc;
    }, function (_unresolved_6) {
      GuideView3DItemComp = _unresolved_6.GuideView3DItemComp;
    }, function (_unresolved_7) {
      configManager = _unresolved_7.configManager;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5881bsQgYBHSaIbQhBQC6vy", "UnifiedGameManager", undefined);

      __checkObsolete__(['Node', 'Vec3']);

      // 引入 GameEntity，但避免循环引用

      /** 游戏状态枚举 */
      _export("GameState", GameState = /*#__PURE__*/function (GameState) {
        GameState["Loading"] = "Loading";
        GameState["SimpleMode"] = "SimpleMode";
        GameState["HardMode"] = "HardMode";
        GameState["Paused"] = "Paused";
        GameState["GameOver"] = "GameOver";
        GameState["Win"] = "Win";
        return GameState;
      }({}));
      /** 统一游戏管理器 - 合并多个管理器功能 */


      _export("UnifiedGameManager", UnifiedGameManager = class UnifiedGameManager {
        constructor(gameEntity) {
          this.gameEntity = void 0;
          // 状态管理
          this.currentState = GameState.Loading;
          this.previousGameState = GameState.Loading;
          // 🎯 记录前一个游戏状态
          this.stateCallbacks = new Map();
          // 关卡管理
          this.currentLevelConfig = null;
          this.currentLevelIndex = 1;
          // 🎯 记录当前关卡索引
          // 输入管理
          this.inputEnabled = false;
          // 物品生成管理
          this.refillStrategy = {
            adjustByPerformance: false,
            adjustByTime: false,
            minInterval: 3000,
            maxInterval: 4500,
            difficultyMultiplier: 1.0
          };
          this.gameEntity = gameEntity;
          this.initializeStateCallbacks();
        } // =================== 状态管理 ===================


        initializeStateCallbacks() {
          this.stateCallbacks.set(GameState.SimpleMode, []);
          this.stateCallbacks.set(GameState.HardMode, []);
          this.stateCallbacks.set(GameState.Loading, []);
          this.stateCallbacks.set(GameState.GameOver, []);
          this.stateCallbacks.set(GameState.Win, []);
          this.stateCallbacks.set(GameState.Paused, []);
        }

        onStateChange(state, callback) {
          var callbacks = this.stateCallbacks.get(state);

          if (callbacks) {
            callbacks.push(callback);
          }
        }

        changeState(newState) {
          if (this.currentState === newState) return;
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness("\uD83C\uDFAE \u72B6\u6001\u5207\u6362: " + this.currentState + " \u2192 " + newState); // 🎯 记录前一个状态（仅在游戏进行状态时记录）

          if (this.currentState === GameState.SimpleMode || this.currentState === GameState.HardMode) {
            this.previousGameState = this.currentState;
          }

          this.currentState = newState; // 执行状态回调

          var callbacks = this.stateCallbacks.get(newState);

          if (callbacks) {
            callbacks.forEach(callback => callback());
          }
        }

        getCurrentState() {
          return this.currentState;
        }

        isSimpleMode() {
          return this.currentState === GameState.SimpleMode;
        }

        isHardMode() {
          return this.currentState === GameState.HardMode;
        }
        /**
         * 🎯 判断之前是否为困难模式（用于Win/GameOver状态时的模式判断）
         */


        wasPreviouslyHardMode() {
          return this.previousGameState === GameState.HardMode;
        }
        /**
         * 🎯 判断之前是否为简单模式（用于Win/GameOver状态时的模式判断）
         */


        wasPreviouslySimpleMode() {
          return this.previousGameState === GameState.SimpleMode;
        }

        isPaused() {
          return this.currentState === GameState.Paused || this.gameEntity.GameModel.pasue;
        }

        isGameOver() {
          return this.currentState === GameState.GameOver;
        }

        isWin() {
          return this.currentState === GameState.Win;
        } // =================== 关卡加载管理 ===================


        loadLevel(levelIndex, onEssentialLoaded) {
          var _this = this;

          return _asyncToGenerator(function* () {
            try {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness("\uD83C\uDFAE \u5F00\u59CB\u52A0\u8F7D\u5173\u5361 " + levelIndex); // 🎯 记录当前关卡索引

              _this.currentLevelIndex = levelIndex; // 加载关卡配置

              _this.currentLevelConfig = yield (_crd && configManager === void 0 ? (_reportPossibleCrUseOfconfigManager({
                error: Error()
              }), configManager) : configManager).getLevelConfig(levelIndex);

              if (!_this.currentLevelConfig) {
                throw new Error("\u5173\u5361\u914D\u7F6E\u4E0D\u5B58\u5728: " + levelIndex);
              } // 加载简单模式物品资源


              yield _this.loadEasyModeItems(); // 🔧 设置游戏模式（但输入仍禁用，等待物品生成完成）

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🎯 设置游戏状态为简单模式，但输入仍禁用');

              _this.changeState(GameState.SimpleMode); // 加载场景和UI（wallSceneView现在包含在gameSceneView中）


              yield _this.gameEntity.loadWallAndScene();
              yield _this.loadGameUI(onEssentialLoaded); // 🔧 调用游戏实体的beforeStartGame方法（此时状态已经是SimpleMode）

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🎮 调用GameEntity.beforeStartGame()');

              _this.gameEntity.beforeStartGame(); // 🎯 准备配置，但不自动创建道具（由startGame调用）


              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness("\u2705 \u5173\u5361 " + levelIndex + " \u52A0\u8F7D\u5B8C\u6210");
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError("\u274C \u5173\u5361 " + levelIndex + " \u52A0\u8F7D\u5931\u8D25:", error);
              throw error;
            }
          })();
        }

        loadEasyModeItems() {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            if (!_this2.currentLevelConfig) return;
            var easyItems = _this2.currentLevelConfig.easyModeItems;
            if (easyItems.length === 0) return; // 🎯 获取关卡配置的物品路径

            var itemPrefabPath = _this2.gameEntity.getItemPrefabPath(); // 获取去重的物品名称并加载


            var uniqueItemNames = Array.from(new Set(easyItems.map(item => item.name)));
            var prefabPaths = uniqueItemNames.map(name => "" + itemPrefabPath + name);
            yield (_crd && simpleLoader === void 0 ? (_reportPossibleCrUseOfsimpleLoader({
              error: Error()
            }), simpleLoader) : simpleLoader).loadPrefabs(prefabPaths);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\u2705 \u7B80\u5355\u6A21\u5F0F\u7269\u54C1\u8D44\u6E90\u52A0\u8F7D\u5B8C\u6210");
          })();
        }

        loadGameUI(onUIReady) {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            return new Promise(resolve => {
              _this3.gameEntity.loadGameUIWithCallback(() => {
                if (onUIReady) {
                  onUIReady();
                }

                resolve();
              });
            });
          })();
        }

        getCurrentLevelConfig() {
          return this.currentLevelConfig;
        }

        getCurrentLevelIndex() {
          return this.currentLevelIndex;
        } // =================== 输入管理 ===================


        setInputEnabled(enabled) {
          this.inputEnabled = enabled;
        }

        isInputEnabled() {
          return this.inputEnabled;
        } // =================== 物品生成管理 ===================


        setRefillStrategy(strategy) {
          this.refillStrategy = _extends({}, this.refillStrategy, strategy);
        }

        checkAndRefillItems(touchedItem) {
          // 🎯 使用统一的状态检查
          if (this.isSimpleMode() || this.isGameOver() || this.isWin()) {
            return;
          } // 简化的补充逻辑


          var currentItems = this.gameEntity.GameModel.allItemsToPick.size;
          var shouldRefill = currentItems < 5; // 简单阈值

          if (shouldRefill && this.gameEntity.GameModel.allItemsToCreate.length > 0) {
            var refillCount = Math.min(3, this.gameEntity.GameModel.allItemsToCreate.length);

            for (var i = 0; i < refillCount; i++) {
              var itemName = this.gameEntity.GameModel.allItemsToCreate.pop();

              if (itemName) {
                var bornPos = new Vec3(0, 5, 0);
                this.gameEntity.createItemOnPos(bornPos, itemName, i + 1, true);
              }
            }
          }
        } // =================== 游戏控制 ===================


        initializeEasyModeItems() {
          if (!this.currentLevelConfig) return;
          this.gameEntity.GameModel.allItemsToCreate = [];
          var easyModeItems = this.currentLevelConfig.easyModeItems;

          for (var item of easyModeItems) {
            for (var i = 0; i < item.count; i++) {
              this.gameEntity.GameModel.allItemsToCreate.push(item.name);
            }
          }

          this.gameEntity.GameModel.allItemsToCreate = (_crd && Utils === void 0 ? (_reportPossibleCrUseOfUtils({
            error: Error()
          }), Utils) : Utils).arrRandomly(this.gameEntity.GameModel.allItemsToCreate);
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness("\uD83C\uDFAE \u7B80\u5355\u6A21\u5F0F\u7269\u54C1\u521D\u59CB\u5316\u5B8C\u6210: " + this.gameEntity.GameModel.allItemsToCreate.length + " \u4E2A\u7269\u54C1");
        }

        initializeHardModeItems() {
          if (!this.currentLevelConfig) return;
          this.gameEntity.GameModel.allItemsToCreate = [];
          var hardModeItems = this.currentLevelConfig.hardModeItems;

          for (var item of hardModeItems) {
            for (var i = 0; i < item.count; i++) {
              this.gameEntity.GameModel.allItemsToCreate.push(item.name);
            }
          }

          this.gameEntity.GameModel.allItemsToCreate = (_crd && Utils === void 0 ? (_reportPossibleCrUseOfUtils({
            error: Error()
          }), Utils) : Utils).arrRandomly(this.gameEntity.GameModel.allItemsToCreate);
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness("\uD83D\uDD25 \u56F0\u96BE\u6A21\u5F0F\u7269\u54C1\u521D\u59CB\u5316\u5B8C\u6210: " + this.gameEntity.GameModel.allItemsToCreate.length + " \u4E2A\u7269\u54C1");
        }
        /**
         * 🚀 分批创建困难模式道具 - 性能优化版本
         */


        createHardModeItemsBatched() {
          var batchSize = 20; // 每批创建20个道具

          var batchDelay = 50; // 每批间隔50ms

          var allItems = this.gameEntity.GameModel.allItemsToCreate;
          var currentBatch = 0;
          var totalBatches = Math.ceil(allItems.length / batchSize);
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness("\uD83D\uDE80 \u5F00\u59CB\u5206\u6279\u521B\u5EFA " + allItems.length + " \u4E2A\u9053\u5177\uFF0C\u5171 " + totalBatches + " \u6279");

          var createBatch = () => {
            var startIndex = currentBatch * batchSize;
            var endIndex = Math.min(startIndex + batchSize, allItems.length); // 创建当前批次的道具

            for (var i = startIndex; i < endIndex; i++) {
              var itemName = allItems[i];
              var bornPos = this.gameEntity.getRandomBornPos();
              this.gameEntity.createItemOnPos(bornPos, itemName, i);
            }

            currentBatch++;

            if (currentBatch < totalBatches) {
              // 继续下一批
              setTimeout(createBatch, batchDelay);
            } else {
              // 所有批次完成
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness("\uD83C\uDF89 \u6240\u6709\u9053\u5177\u521B\u5EFA\u5B8C\u6210\uFF0C\u603B\u8BA1: " + allItems.length + " \u4E2A");
              this.gameEntity.handleInputEnabling();
            }
          }; // 开始第一批


          createBatch();
        }

        createInitialItemsInScene() {
          var _role;

          if (this.gameEntity.GameModel.allItemsToCreate.length === 0) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 没有道具需要创建');
            return;
          }

          var itemCount = this.gameEntity.GameModel.allItemsToCreate.length;
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness("\uD83C\uDFAF \u5F00\u59CB\u521B\u5EFA " + itemCount + " \u4E2A\u9053\u5177"); // 🚀 性能优化：困难模式使用分批创建

          if (this.isHardMode() && itemCount > 50) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🚀 困难模式大量道具，使用分批创建优化性能');
            this.createHardModeItemsBatched();
            return;
          }

          var itemIndex = 0;
          var isNewPlayer = this.gameEntity.isSimpleMode() && ((_role = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).role) == null ? void 0 : _role.isNewPlayer());
          var isSimpleMode = this.gameEntity.isSimpleMode(); // 🎯 新手玩家：使用固定九宫格位置

          var fixedPositions = [];

          if (isNewPlayer) {
            fixedPositions = [new Vec3(-2, 8, -2), // 1. 左上
            new Vec3(0, 8, -2), // 2. 中上
            new Vec3(2, 8, -2), // 3. 右上
            new Vec3(-2, 8, 0), // 4. 左中
            new Vec3(0, 8, 0), // 5. 中心
            new Vec3(2, 8, 0), // 6. 右中
            new Vec3(-2, 8, 2), // 7. 左下
            new Vec3(0, 8, 2), // 8. 中下
            new Vec3(2, 8, 2) // 9. 右下
            ];
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🎓 新手玩家：使用固定九宫格位置创建道具');
          }

          while (this.gameEntity.GameModel.allItemsToCreate.length > 0) {
            var itemName = this.gameEntity.GameModel.allItemsToCreate.pop();

            if (itemName) {
              var bornPos = void 0;

              if (isNewPlayer && itemIndex < fixedPositions.length) {
                // 🎯 新手玩家简单模式：使用固定位置
                bornPos = fixedPositions[itemIndex];
              } else {
                // 🎲 其他情况：使用随机位置（老玩家简单模式 + 所有困难模式）
                bornPos = this.generateRandomPosition(isSimpleMode);
              } // 创建道具（新手不随机旋转）


              this.gameEntity.createItemOnPos(bornPos, itemName, itemIndex++, !isNewPlayer);
            }
          }

          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness("\uD83C\uDF89 \u6240\u6709\u9053\u5177\u521B\u5EFA\u5B8C\u6210\uFF0C\u603B\u8BA1: " + itemIndex + " \u4E2A"); // 🎯 新手玩家：启动引导系统

          if (isNewPlayer) {
            this.startNewPlayerGuide(itemIndex);
          }
        }
        /**
         * 🎲 生成随机位置
         */


        generateRandomPosition(isSimpleMode) {
          if (isSimpleMode) {
            // 简单模式：较小的随机范围
            var radius = 3;
            var angle = Math.random() * Math.PI * 2;
            var distance = Math.random() * radius;
            return new Vec3(Math.cos(angle) * distance, 8 + Math.random() * 2, // 高度在8-10之间
            Math.sin(angle) * distance);
          } else {
            // 困难模式：较大的随机范围
            return new Vec3((Math.random() - 0.5) * 10, // x: -5 到 5
            Math.random() * 3 + 2, // y: 2 到 5
            (Math.random() - 0.5) * 10 // z: -5 到 5
            );
          }
        }
        /**
         * 🎯 启动新手引导系统
         */


        startNewPlayerGuide(itemCount) {
          setTimeout(() => {
            var _guide;

            var guideSteps = Math.min(itemCount, 9); // 🎯 设置引导的最后一步

            if ((_guide = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).guide) != null && _guide.GuideModel) {
              (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).guide.GuideModel.last = guideSteps;
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness("\uD83C\uDF93 \u8BBE\u7F6E\u5F15\u5BFC\u6700\u540E\u4E00\u6B65: " + guideSteps);
            } // 为前9个道具添加引导组件


            for (var i = 0; i < guideSteps; i++) {
              this.addGuideComponentToItem(i, i + 1);
            }
          }, 100); // 延迟确保道具创建完成
        }
        /**
         * 🎯 为道具添加引导组件
         */


        addGuideComponentToItem(itemIndex, guideStep) {
          var itemNode = this.gameEntity.GameModel.allItemsToPick.get(itemIndex);

          if (!itemNode) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn("\u26A0\uFE0F \u9053\u5177\u8282\u70B9\u672A\u627E\u5230: " + itemIndex);
            return;
          }

          var guideComp = itemNode.getComponent(_crd && GuideView3DItemComp === void 0 ? (_reportPossibleCrUseOfGuideView3DItemComp({
            error: Error()
          }), GuideView3DItemComp) : GuideView3DItemComp);

          if (!guideComp) {
            guideComp = itemNode.addComponent(_crd && GuideView3DItemComp === void 0 ? (_reportPossibleCrUseOfGuideView3DItemComp({
              error: Error()
            }), GuideView3DItemComp) : GuideView3DItemComp);
          }

          if (guideComp && guideComp.setStep) {
            guideComp.setStep(guideStep);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\uD83C\uDF93 \u4E3A\u9053\u5177 " + itemIndex + " \u6DFB\u52A0\u5F15\u5BFC\u6B65\u9AA4: " + guideStep);
          }
        } // =================== 清理 ===================


        destroy() {
          this.currentLevelConfig = null;
          this.stateCallbacks.clear();
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9d8a2ff0be1d1ab901c3a8a340022c6dbb12c7d0.js.map