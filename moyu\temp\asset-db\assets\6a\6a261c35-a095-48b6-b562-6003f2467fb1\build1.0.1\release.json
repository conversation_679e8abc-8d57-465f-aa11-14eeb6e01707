[1, ["14I5OoJkFHdaecy/4b2eXK", "26qSDlZtJO+JKe1m5B/Eag@f9941", "87KJdfUjNA1pqHCGJqumKH", "6bAK8rf8pPE54WiDqq1t7L@f9941", "b4Kl7ucq1AyZ84IiEUyK6K@f9941", "12CGLetYxEn5JY6OsQY9mT@f9941"], ["node", "_spriteFrame", "root", "asset", "targetInfo", "_normalSprite", "_target", "data", "_parent"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_children", "_parent", "_lpos"], -1, 4, 9, 2, 1, 5], ["cc.Widget", ["_alignFlags", "_right", "_top", "_originalWidth", "_originalHeight", "_left", "_bottom", "_verticalCenter", "node", "__prefab"], -5, 1, 4], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Layout", ["_resizeMode", "_layoutType", "_startAxis", "_paddingLeft", "_paddingRight", "_constraint", "_constraintNum", "node", "__prefab"], -4, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_target", "_normalSprite"], 2, 1, 4, 1, 6], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4]], [[14, 0, 2], [9, 0, 1, 2, 2], [12, 0, 2], [13, 0, 1, 2, 1], [5, 0, 1, 2, 3, 4, 5, 5], [8, 0, 1, 2, 3], [0, 2, 3, 7, 4, 3], [0, 0, 1, 7, 5, 4, 8, 3], [4, 0, 1, 2, 3, 4, 5, 4], [7, 0, 1, 2, 2], [10, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 3], [3, 0, 2], [0, 0, 1, 6, 5, 4, 3], [0, 0, 1, 7, 6, 5, 4, 8, 3], [0, 0, 1, 6, 5, 4, 8, 3], [6, 0, 1, 2, 3, 4, 5, 4], [11, 0, 1, 2, 3], [15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 8], [2, 0, 2, 3, 4, 2], [1, 0, 8, 9, 2], [1, 0, 1, 2, 8, 9, 4], [1, 0, 5, 1, 3, 8, 9, 5], [1, 0, 2, 6, 7, 4, 8, 9, 6], [1, 0, 3, 4, 8, 9, 4], [16, 0, 1, 2, 3, 4, 2], [17, 0, 1, 1]], [[12, "2dExamplePanel"], [13, "2dExamplePanel", 33554432, [-7, -8], [[3, -4, [0, "c1hVvKAKJAsY8YKb40A5oY"], [5, 750, 1334]], [26, -5, [0, "48O7mvCMxPQ4BoitAgcnAc"]], [24, 45, 1080, 1920, -6, [0, "c9y1hDPe5P24tzvKVZtU1V"]]], [16, "22ahoiOOVHK7EfzrB8wn/z", null, null, -3, 0, [-1, -2]]], [15, "title", 33554432, [-12, -13], [[3, -9, [0, "d3vqG+P8ZBZJgOR3jR563o"], [5, 450, 73]], [11, 1, 0, -10, [0, "8ahGh33ilD37Dyij/F8bZ5"], 5], [22, 17, 318.5, 315.5, 116, -11, [0, "aed2MGKixBELk0E11tkhTi"]]], [4, "cfRQ8WNUpGJKz/AG3J2FJ+", null, null, null, 1, 0], [1, 0, 13.5, 0]], [7, "closeBtn", 33554432, 2, [[3, -14, [0, "c8o+0e8ZBBB5tKZRRjepwm"], [5, 68, 69]], [19, 1, -15, [0, "7ej9l2JnZKEIDfWjwsCWy/"], 3], [25, 3, -17, [0, "e3+yP0BjZDC46nMpyM4yJy"], -16, 4], [21, 33, -10.663000000000011, -12.970000000000027, -18, [0, "749N42OW1MqKbnhc6ESuBy"]]], [4, "2ddhaPEdNDaYuysgtvhIDZ", null, null, null, 1, 0], [1, 201.663, 14.970000000000027, 0]], [14, "Node", 33554432, 1, [-21, 2], [[3, -19, [0, "6c6B9m0E1PErzxhPnWEKGj"], [5, 450, 100]], [23, 18, 788.5405000000001, 445.4595, 273.919, 100, -20, [0, "01Ko6y26tKQrBV+zKgAext"]]], [4, "1cweqzvJFKxLQ5MJ7c8/HQ", null, null, null, 1, 0], [1, 0, 273.919, 0]], [7, "content", 33554432, 4, [[3, -22, [0, "83tQuHYDRBJZzxnNgkMSLu"], [5, 40, 561]], [18, 1, 3, 1, 20, 20, 2, 1, -23, [0, "9cBURkj1NCYLhkBHxOaebF"]], [11, 1, 0, -24, [0, "b7TWmCW09L/6FTSljPy5Ch"], 1], [20, 1, -25, [0, "80GeuzJsNOCqdsZRlAxYQZ"]]], [4, "d7XphpDLtKq6lRFVYM1inb", null, null, null, 1, 0], [1, 0, -230.5, 0]], [2, ["a0daVw8DRLi6ToMaTA0VS2"]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [6, 0, {}, 1, [8, "a0daVw8DRLi6ToMaTA0VS2", null, null, -26, [9, "6cH5XhLHlJb5HbIIaNX3/i", 1, [[5, "mask", ["_name"], 6], [1, ["_lpos"], 6, [1, 0, 0, 0]], [1, ["_lrot"], 6, [3, 0, 0, 0, 1]], [1, ["_euler"], 6, [1, 0, 0, 0]], [10, ["_contentSize"], [2, ["77N2cid5pKDpXplRH/AWEU"]], [5, 1080, 1920]]]], 0]], [6, 0, {}, 2, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -29, [9, "ebgik2GuFIgqaLPAUIt14M", 1, [[5, "title", ["_name"], 7], [1, ["_lpos"], 7, [1, 0, 0, 0]], [1, ["_lrot"], 7, [3, 0, 0, 0, 1]], [1, ["_euler"], 7, [1, 0, 0, 0]], [5, true, ["_enableOutline"], -27], [10, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 161.79296875, 54.4]], [17, "Settings", ["_dataID"], [2, ["64LRaaBbNLDa+7mRmUDjg9"]]], [5, "Settings", ["_string"], -28]]], 2]], [2, ["4a5atXBglJxJGAlAL90RE0"]]], 0, [0, -1, 9, 0, -2, 8, 0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 8, 0, -2, 4, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 9, 0, -2, 3, 0, 0, 3, 0, 0, 3, 0, 6, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, -1, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 2, 8, 0, 4, 10, 0, 4, 10, 0, 2, 9, 0, 7, 1, 2, 8, 4, 29], [0, 0, 0, 0, 0, 0], [3, 1, 3, 1, 5, 1], [0, 1, 2, 3, 4, 5]]