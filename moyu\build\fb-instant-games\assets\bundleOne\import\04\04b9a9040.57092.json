[1, ["e5LUoqx3RAr41dA5QrbKMj", "578gOhVG5B/oPzgRxEZ/pK@fbb46"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 2778041786, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 44352, "length": 9060, "count": 4530, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 44352, "count": 924, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.8223974704742432, -0.28186824917793274, -0.39529967308044434], "maxPosition", 8, [1, 0.8622209429740906, 0.39920783042907715, 0.3920905888080597]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_19"], [3, "日式寿司_19", [[4, 1, -2, [0, "bf/ksb0ZpHWZHWKZL5GkCw"], [0], [5, true, true], 1], [6, 4, -3, [0, "785S1ubipFGqxvp3kIP1G4"]], [7, 0.7444825172424316, 0, -4, [0, "95OhJdryZNp6HXOFsuKSEo"], [1, -0.059935033321380615, 0.11511364579200745, -0.00043658167123794556]]], [8, "1d2aFMNSFNIb5uJMsHYTeC", null, null, null, -1, 0], [1, 2.577, 0, 6.415]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]