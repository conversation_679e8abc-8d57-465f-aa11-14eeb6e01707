System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, Environment, Platform, PRODUCTION_FACEBOOK_CONFIG, CONFIG_MAP, ShareConfig;

  // 根据当前分支确定环境
  function getCurrentEnvironment() {
    // master-facebook 分支固定返回 PRODUCTION_FACEBOOK 环境
    return Environment.PRODUCTION_FACEBOOK;
  } // 🔧 master-facebook分支专用：Facebook生产环境配置


  // 配置验证
  function validateConfig() {
    var config = ShareConfig;

    if (!config.serverUrl || !config.clientUrl) {
      console.error('Missing required URL configuration');
      return false;
    }

    if (!config.mongoUrl || !config.mongoDbName) {
      console.error('Missing required MongoDB configuration');
      return false;
    }

    if (config.enableFacebookSDK && !config.facebookAppId) {
      console.error('Facebook SDK enabled but no App ID provided');
      return false;
    }

    return true;
  } // 打印当前配置信息


  function printConfigInfo() {
    console.log('=== ShareConfig Information (master-facebook) ===');
    console.log("Environment: " + ShareConfig.environment);
    console.log("Platform: " + ShareConfig.platform);
    console.log("Server URL: " + ShareConfig.serverUrl);
    console.log("Client URL: " + ShareConfig.clientUrl);
    console.log("Database: " + ShareConfig.mongoDbName);
    console.log("Port: " + ShareConfig.port);
    console.log("Production: " + ShareConfig.isProduction);
    console.log("Facebook SDK: " + ShareConfig.enableFacebookSDK);
    console.log("Facebook App ID: " + ShareConfig.facebookAppId);
    console.log('==================================================');
  }

  _export({
    validateConfig: validateConfig,
    printConfigInfo: printConfigInfo
  });

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "74cfdUc6HZLNJCAYnQxXpa+", "ShareConfig", undefined);

      // ShareConfig.ts - master-facebook分支配置 (Facebook生产环境)
      // 根据分支自动确定环境配置，简化部署流程
      _export("Environment", Environment = /*#__PURE__*/function (Environment) {
        Environment["DEVELOPMENT"] = "development";
        Environment["FACEBOOK_MOCK"] = "facebook_mock";
        Environment["PRODUCTION_PERSONAL"] = "production_personal";
        Environment["PRODUCTION_FACEBOOK"] = "production_facebook";
        return Environment;
      }({}));

      _export("Platform", Platform = /*#__PURE__*/function (Platform) {
        Platform["PERSONAL"] = "personal";
        Platform["FACEBOOK"] = "facebook";
        return Platform;
      }({}));

      PRODUCTION_FACEBOOK_CONFIG = {
        environment: Environment.PRODUCTION_FACEBOOK,
        platform: Platform.FACEBOOK,
        serverUrl: 'https://idlefun.press/api/facebook',
        clientUrl: 'https://idlefun.press/facebook',
        mongoUrl: 'mongodb://mongodb-facebook:27017',
        mongoDbName: 'moyu_facebook',
        port: 3002,
        gamePort: 3003,
        gameServerUrl: 'https://idlefun.press/api/facebook/game',
        isProduction: true,
        enableCors: true,
        corsOrigins: ['https://idlefun.press', 'https://www.facebook.com', 'https://developers.facebook.com'],
        logLevel: 'warn',
        enableFacebookSDK: true,
        enableAnalytics: true,
        maxPlayersPerRoom: 8,
        gameSettings: {
          enableRanking: true,
          enableRewards: true,
          enableSocialFeatures: true
        },
        // 兼容属性
        https: true,
        gate: 'idlefun.press:3002',
        httpPort: 3002,
        json: true,
        security: true
      }; // 环境配置映射

      CONFIG_MAP = {
        [Environment.DEVELOPMENT]: PRODUCTION_FACEBOOK_CONFIG,
        // 简化
        [Environment.FACEBOOK_MOCK]: PRODUCTION_FACEBOOK_CONFIG,
        // 简化
        [Environment.PRODUCTION_PERSONAL]: PRODUCTION_FACEBOOK_CONFIG,
        // 简化
        [Environment.PRODUCTION_FACEBOOK]: PRODUCTION_FACEBOOK_CONFIG
      }; // 获取当前配置 - master-facebook分支专用Facebook生产配置

      _export("ShareConfig", ShareConfig = PRODUCTION_FACEBOOK_CONFIG);

      _export("default", ShareConfig);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fd93af24472005c2ce5a8e9c23602f4330dc09a4.js.map