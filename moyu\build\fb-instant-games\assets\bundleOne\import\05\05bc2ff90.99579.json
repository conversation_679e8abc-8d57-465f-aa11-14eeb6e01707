[1, ["e5LUoqx3RAr41dA5QrbKMj", "116txOA6VEoqBuINvZMIYt@4aabd"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 1966876795, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 29136, "length": 5808, "count": 2904, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 29136, "count": 607, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.7844734191894531, -0.5926149487495422, -0.304598867893219], "maxPosition", 8, [1, 0.7973909974098206, 0.6548352837562561, 0.3718078136444092]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_23"], [3, "日式寿司_23", [[4, 1, -2, [0, "23/RTbrXRKG4eZZxwcLd79"], [0], [5, true, true], 1], [6, 4, -3, [0, "48qNHZR+ZOF7ZRbf05V5SX"]], [7, 0.7909322082996368, 0, -4, [0, "cdgkRVN3tCHI/Qo861GKYh"], [1, 0.006458789110183716, 0.031110167503356934, 0.03360447287559509]]], [8, "d9x3dkYbJP4bX+i9Hj5xCH", null, null, null, -1, 0], [1, 4.454, 0, 6.282]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]