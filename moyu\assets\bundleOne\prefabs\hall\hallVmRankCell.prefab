[{"__type__": "cc.Prefab", "_name": "hallVmRankCell", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "hallVmRankCell", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 63}, {"__id__": 65}], "_prefab": {"__id__": 67}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "centerNode", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 19}, {"__id__": 42}], "_active": true, "_components": [{"__id__": 54}, {"__id__": 56}, {"__id__": 58}, {"__id__": 60}], "_prefab": {"__id__": 62}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 400, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 4}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 3}, "asset": {"__uuid__": "8728975f-5233-40d6-9a87-08626aba6287", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 5}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "8eCNJYQF5Ck6oLAgJvxVQU", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 6}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 13}, {"__id__": 14}, {"__id__": 15}, {"__id__": 16}, {"__id__": 18}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 7}, "propertyPath": ["_name"], "value": "vmRankLabel"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 7}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 24.8, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 7}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 7}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 12}, "propertyPath": ["templateMode"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["09i06HqvBP2ZaQ4Pk+p3Mt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 12}, "propertyPath": ["watchPathArr", "length"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 12}, "propertyPath": ["watchPathArr", "0"], "value": "*.rank"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 12}, "propertyPath": ["_dataID"], "value": "vmRankLabel"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 248.4375, "height": 50.4}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 7}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 20}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 19}, "asset": {"__uuid__": "8728975f-5233-40d6-9a87-08626aba6287", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 21}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "59oamas7BK6IOgDZ/Oxeew", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 22}, {"__id__": 24}, {"__id__": 26}, {"__id__": 28}, {"__id__": 30}, {"__id__": 32}, {"__id__": 34}, {"__id__": 36}, {"__id__": 38}, {"__id__": 40}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 23}, "propertyPath": ["_name"], "value": "vmCountryLabel"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 25}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -25.599999999999998, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 29}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 31}, "propertyPath": ["templateMode"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["09i06HqvBP2ZaQ4Pk+p3Mt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 33}, "propertyPath": ["watchPathArr", "length"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["09i06HqvBP2ZaQ4Pk+p3Mt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 35}, "propertyPath": ["watchPathArr", "0"], "value": "hgcrvm.vmRank1"}, {"__type__": "cc.TargetInfo", "localID": ["09i06HqvBP2ZaQ4Pk+p3Mt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 37}, "propertyPath": ["_dataID"], "value": "UnKnow"}, {"__type__": "cc.TargetInfo", "localID": ["09i06HqvBP2ZaQ4Pk+p3Mt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 248.4375, "height": 50.4}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 41}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 2}, "_prefab": {"__id__": 43}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 42}, "asset": {"__uuid__": "8728975f-5233-40d6-9a87-08626aba6287", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 44}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "f2rmwxfZhAOYpRh0djJser", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 45}, {"__id__": 47}, {"__id__": 48}, {"__id__": 49}, {"__id__": 50}, {"__id__": 52}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_name"], "value": "vmTimesLabel"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -76, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_dataID"], "value": "vmTimesLabel"}, {"__type__": "cc.TargetInfo", "localID": ["09i06HqvBP2ZaQ4Pk+p3Mt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 53}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 245.625, "height": 50.4}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 55}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fft5apgMpHxagaJ1pBxC86"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 57}, "_resizeMode": 0, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93oBg+vsxKg6F6FCmgN86P"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 59}, "watchPath": "*.times", "foreachChildMode": false, "condition": 2, "foreachChildType": 0, "valueA": 0, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 42}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73+99BOgdGvI5ITjxxfnh1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 61}, "_alignFlags": 2, "_target": null, "_left": 80.078125, "_right": 80.078125, "_top": -24.8, "_bottom": -24.8, "_horizontalCenter": 0, "_verticalCenter": 400, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04OHEtIjFMXIgN519oY9pR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e6nDtHVlNF4qOQl/P3kQ1A", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 64}, "_contentSize": {"__type__": "cc.Size", "width": 260.15625, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ehE0yjgpOdpGShi2Ty+Al"}, {"__type__": "4c088mplRRK5o6eY0e1zc45", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 66}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03iYLSvBVIBqw4wSv7GMSF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26iaepJgFCqKV3l5WUmOuO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 42}, {"__id__": 19}, {"__id__": 3}]}]