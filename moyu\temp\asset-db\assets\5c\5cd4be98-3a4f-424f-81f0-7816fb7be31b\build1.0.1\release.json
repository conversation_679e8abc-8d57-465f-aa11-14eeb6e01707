[1, ["a2wVh2WahPGrkdpCJuKFxG@f9941", "eeXHdJTsxBgKoBm1aIQDLe@f9941", "87KJdfUjNA1pqHCGJqumKH", "1awEnXyJlJKomnTo2Fzkyx@f9941"], ["node", "targetInfo", "_spriteFrame", "root", "asset", "target", "source", "data", "_parent"], [["cc.Sprite", ["_sizeMode", "_type", "_name", "_fillRange", "node", "__prefab", "_spriteFrame"], -1, 1, 4, 6], ["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_children", "_components", "_lpos", "_parent"], -1, 4, 2, 12, 5, 1], ["cc.UITransform", ["_name", "node", "__prefab", "_contentSize"], 2, 1, 4, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_children", "_lpos"], 1, 1, 9, 4, 2, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_alignMode", "node", "__prefab"], -1, 1, 4], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "root", "asset", "targetOverrides", "nestedPrefabInstanceRoots"], 1, 1, 1, 9, 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["cc.TargetInfo", ["localID"], 2], ["8be380XepJLdJhkffMjph3L", ["node", "__prefab", "progressBarFill", "catIcon"], 3, 1, 4, 1, 1], ["cc.TargetOverrideInfo", ["propertyPath", "source", "target", "targetInfo"], 2, 1, 1, 4]], [[6, 0, 2], [7, 0, 1, 2, 3, 4, 5, 5], [2, 1, 2, 3, 1], [14, 0, 2], [11, 0, 1, 2, 3], [12, 0, 1, 2, 2], [3, 0, 1, 2, 5, 3, 4, 3], [3, 0, 1, 2, 3, 4, 6, 3], [5, 0, 2], [1, 0, 1, 5, 6, 4, 3], [1, 0, 1, 5, 6, 4, 7, 3], [1, 2, 3, 8, 4, 3], [2, 1, 2, 1], [2, 0, 1, 2, 3, 2], [0, 0, 4, 5, 6, 2], [0, 1, 0, 4, 5, 3], [0, 4, 5, 6, 1], [0, 2, 1, 0, 3, 4, 5, 5], [8, 0, 1, 2, 3, 4, 5, 4], [9, 0, 1, 2, 3, 4, 5, 3], [4, 0, 1, 2, 3, 4, 5, 5], [4, 0, 1, 2, 4, 5, 4], [10, 0, 1, 2, 2], [13, 0, 1, 2, 2], [15, 0, 1, 2, 3, 1], [16, 0, 1, 2, 3, 2]], [[8, "SimpleLoading"], [9, "SimpleLoading", 33554432, [-8, -9, -10], [[[13, "loading<UITransform>", -5, [0, "dcwRyrrAFJIZu8cg5N9Q7q"], [5, 750, 1334]], [21, 45, 1280, 720, -6, [0, "26ZokSfFxPFr+5cT88/8nR"]], -7], 4, 4, 1], [19, "65Pe/9AIJMgIjcSkJv5QHh", null, -4, 0, [[25, ["loadingText"], -3, -2, [3, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]]], [-1]]], [6, "Background", 33554432, 1, [-14], [[2, -11, [0, "246k7cFLRFjZJKidQNC3Id"], [5, 750, 1334]], [15, 1, 0, -12, [0, "92mR0scQ1L5651+SmTaN0p"]], [20, 45, 1080, 1920, 1, -13, [0, "85527XgEpDlp06U7yfUkd1"]]], [1, "89hGKfYKtKZqaicj3ZFt/Q", null, null, null, 1, 0]], [10, "ProgressBarFill", 33554432, [-17], [[[2, -15, [0, "de29cYKcdJaovLykkaxjZs"], [5, 549, 47]], -16], 4, 1], [1, "d6gYKUHPRMt5BfHReLB3m4", null, null, null, 1, 0], [1, 0, -566.688, 0]], [7, "catIcon", 33554432, 3, [[2, -18, [0, "cc/KiBWuBEr6bteZLaJDz7"], [5, 90, 69]], [16, -19, [0, "a8MVa8U/dArrFktayvn0o+"], 1]], [1, "78o0Qys2tH+JgAyorNu/MP", null, null, null, 1, 0], [1, 255.053, 0, 0]], [11, 0, {}, 1, [18, "f05XX5jrpEOYwv6lCoUIav", null, null, -26, [22, "bfO8mJmytH7raI7Jg9n9rI", 1, [[4, "vmLoadingText", ["_name"], -20], [5, ["_lpos"], -21, [1, 0, -506.402, 0]], [5, ["_lrot"], -22, [3, 0, 0, 0, 1]], [5, ["_euler"], -23, [1, 0, 0, 0]], [4, "update_tips_load_game", ["_dataID"], -24], [23, ["_contentSize"], [3, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 248.4375, 50.4]], [4, false, ["templateMode"], -25]]], 2]], [3, ["f05XX5jrpEOYwv6lCoUIav"]], [7, "logoImg", 33554432, 2, [[2, -27, [0, "c8PFKJPDNL47Fd1N9WI6pE"], [5, 536, 246]], [14, 0, -28, [0, "c4jJha6FhHF7CS/izMgTMY"], 0]], [1, "723EvY8CpCX63uw/REp6Px", null, null, null, 1, 0], [1, 0, 315, 0]], [6, "ProgressContainer", 33554432, 1, [3], [[12, -29, [0, "3cPCr6Aa1PmqxTHY6toels"]]], [1, "d5mclU4vpFhZztFxqBA0fG", null, null, null, 1, 0]], [17, "Sprite<Sprite>", 3, 0, 1, 3, [0, "dcGM5HCyhJc6xINqNVm8Rh"]], [3, ["09i06HqvBP2ZaQ4Pk+p3Mt"]], [24, 1, [0, "2cdD5Sl5ZG2oCISNAwbbC2"], 9, 4]], 0, [0, -1, 5, 0, 5, 5, 0, 6, 11, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -3, 11, 0, -1, 2, 0, -2, 8, 0, -3, 5, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, 0, 3, 0, -2, 9, 0, -1, 4, 0, 0, 4, 0, 0, 4, 0, 1, 6, 0, 1, 6, 0, 1, 6, 0, 1, 6, 0, 1, 10, 0, 1, 10, 0, 3, 5, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 7, 1, 3, 8, 8, 29], [0, 0, 0, 9], [2, 2, 4, 2], [0, 1, 2, 3]]