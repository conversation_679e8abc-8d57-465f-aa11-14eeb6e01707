[1, ["56oY0PFUVKRJA95WSWzdXD", "e28dJLVXJC3YFObxU5gPQx@d053a"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", [], 3], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.S<PERSON>ider", ["_radius", "node", "__prefab", "_center"], 2, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 1], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 2], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 1980525964, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 43776, "length": 5880, "count": 2940, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 43776, "count": 684, "stride": 64}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}, {"name": "a_texCoord1", "format": 21, "isNormalized": false}, {"name": "a_texCoord2", "format": 21, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.4471590220928192, -0.36962199211120605, -0.5358822345733643], "maxPosition", 8, [1, 0.431759774684906, 0.4572232961654663, 0.34934332966804504]]], -1], 0, 0, [], [], []], [[[2, "番茄"], [3, "番茄", [[4, 1, -2, [0, "6b2rDVS3BJmrRlHcOoF58+"], [0], [5], 1], [6, 4, -3, [0, "e9Iqlp6sRAG4sdCHEt41bL"]], [7, 0.428, -4, [0, "54NBdXr+5MGpJAv/B/OmfP"], [1, -0.007699623703956604, 0.04380065202713013, -0.0932694524526596]]], [8, "8fgdpaSmZKxIOLO6SuWECm", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]