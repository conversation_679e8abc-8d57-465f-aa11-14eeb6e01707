[1, 0, 0, [["cc.Json<PERSON>set", ["_name", "json"], 1]], [[0, 0, 1, 3]], [[0, "en", {"common_prompt_ok": "ok", "common_prompt_cancal": "cancal", "common_prompt_title": "System Prompt", "common_server_maintenance": "Server is under maintenance, please wait!", "common_server_error": "Server error, please try again later", "Update_tips_force": "The current version is too old, do you want to update and restart?", "Update_tips_reload_config": "Loading configuration", "Update_tips_load_game": "Loading game", "Update_tips_check_update": "Checking for updates", "Update_tips_new_version": "New version resource discovered", "Update_tips_success": "Update completed, click OK to restart the game", "Update_tips_update": "Updating:", "Update_tips_update_fail": "Update failed", "Update_tips_update_fail_click_retry": "Update failed, click retry", "Update_tips_update_success": "Update successful", "login_game": "Game Login", "login_guest": "Guest Login", "account": "Account", "account_placeholder": "Please enter account", "password": "Password", "password_placeholder": "Please enter password", "login_button": "<PERSON><PERSON>", "register": "Register", "confirm_register": "Confirm Register", "back_login": "Back to Login", "forget_password": "Forget Password?", "other_login_way": "Other Login Way", "username_tips": "Username: 6-20 characters, letters, numbers, or underscores", "password_tips": "Password: 6-20 characters, letters, numbers, and underscores, and must contain at least two of the following combinations: letters, numbers, and underscores", "login_fail": "<PERSON><PERSON> failed, please check your account and password", "login_empty": "Account/Password cannot be empty", "register_same": "Registration failed, account already exists", "register_err": "Registration failed, please try again later", "login_error_relogin": "<PERSON><PERSON> failed, please re-login", "Exist_No_Exist": "Account does not exist", "register_success": "Registration successful", "register_enter_login": "Whether to enter the game directly", "password_not_match": "Password does not match", "password_again": "Please enter the password again", "repeat_password": "Repeat password", "_comment_daily_challenge": "=== Daily Challenge System ===", "daily_challenge_no_attempts": "Daily attempts exhausted, try again tomorrow!", "daily_challenge_completed": "Daily challenge completed! Max level: {0}", "daily_challenge_remaining": "Daily attempts remaining: {0}", "daily_challenge_start_fail": "Daily attempts exhausted", "daily_challenge_insufficient": "Insufficient attempts, cannot start game", "daily_challenge_win_title": "Victory!", "daily_challenge_auto_exit": "Auto exit to hall in 5 seconds", "daily_challenge_first_win": "🎉 First victory today!", "daily_challenge_level_completed": "Level completed: {0}", "daily_challenge_time_used": "Time used: {0}s", "daily_challenge_total_completions": "Total completions: {0}", "daily_challenge_best_record": "Best record: {0} levels", "daily_challenge_continue": "Continue Challenge", "daily_challenge_exit": "Exit to Hall", "_comment_game_system": "=== Game System ===", "game_load_failed": "Game loading failed, please retry", "game_restart_failed": "Game restart failed", "game_network_error": "Network error, please retry", "game_saving_data": "Saving game data...", "game_data_saved": "Game data saved", "game_save_failed": "Data save failed", "_comment_debug": "=== Debug Info ===", "debug_manager_exposed": "Manager exposed to global debug", "debug_show_status": "Show status info", "debug_reset_data": "Reset data", "_comment_ranking": "=== Ranking System ===", "rank_no": "No.{0}", "rank_times": "{0} Times", "rank_vacant": "Vacant", "cn": "CN", "us": "US", "jp": "JP", "kr": "KR", "gb": "GB", "de": "DE", "fr": "FR", "it": "IT", "es": "ES", "br": "BR", "in": "IN", "ru": "RU", "au": "AU", "ca": "CA", "un": "UN"}]], 0, 0, [], [], []]