[1, ["e5LUoqx3RAr41dA5QrbKMj", "58gKWse3JDS7e7YHyKtRjg@4184f"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 137643536, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 56256, "length": 7710, "count": 3855, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 56256, "count": 1172, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.23495127260684967, -0.9018680453300476, -0.172916442155838], "maxPosition", 8, [1, 0.18939775228500366, 0.6588811278343201, 0.18695150315761566]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_10"], [3, "日式寿司_10", [[4, 1, -2, [0, "d1N8D/exFGcqJg1lU9zbDm"], [0], [5, true, true], 1], [6, 4, -3, [0, "adXEEzi0pJToeTQZ6z261B"]], [7, 0.21217451244592667, 1.1364001482725143, -4, [0, "a5yUiFcJhCCYm7qx6qbPKI"], [1, -0.022776760160923004, -0.12149345874786377, 0.0070175305008888245]]], [8, "46q2ZT75dKUZyDFGKCYXIa", null, null, null, -1, 0], [1, 0.045, 0, 3.064]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]