<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>IdleFun - Facebook Instant Game</title>
  
  <!-- 🔒 Facebook权限策略配置 - 禁用Facebook不允许的浏览器API -->
  <meta http-equiv="Permissions-Policy" content="
    gamepad=(),                    /* 🎮 禁用游戏手柄API - 防止Cocos引擎检测手柄导致权限错误 */
    geolocation=(),                /* 🌍 禁用地理位置API - Facebook不允许小游戏获取用户位置 */
    camera=(),                     /* 📷 禁用摄像头API - 保护用户隐私，小游戏无需摄像头 */
    microphone=(),                 /* 🎤 禁用麦克风API - 保护用户隐私，小游戏无需录音 */
    payment=(),                    /* 💳 禁用支付API - 必须使用Facebook的支付系统 */
    web-share=(),                  /* 📤 禁用原生分享API - 必须使用Facebook的分享功能 */
    ch-ua-arch=(),                 /* 🏗️ 禁用设备架构信息(x86/ARM) - 防止设备指纹追踪 */
    ch-viewport-width=(),          /* 📐 禁用视口宽度信息 - 减少可获取的屏幕信息 */
    ch-width=(),                   /* 📏 禁用屏幕宽度信息 - 减少设备识别特征 */
    ch-ua-bitness=(),              /* 🔢 禁用系统位数信息(32/64位) - 防止系统指纹 */
    ch-viewport-height=(),         /* 📏 禁用视口高度信息 - 减少屏幕特征泄露 */
    ch-save-data=(),               /* 📱 禁用省流量模式检测 - 保护用户网络偏好隐私 */
    ch-ua-full-version=(),         /* 🔍 禁用浏览器完整版本 - 减少浏览器指纹信息 */
    ch-ua-mobile=(),               /* 📱 禁用移动设备检测 - 防止设备类型识别 */
    ch-ua-model=(),                /* 📲 禁用设备型号信息 - 保护设备隐私 */
    ch-ua-platform=(),             /* 💻 禁用操作系统信息 - 减少系统指纹 */
    ch-ua-platform-version=(),     /* 🔢 禁用系统版本信息 - 防止详细系统识别 */
    ch-dpr=(),                     /* 🖥️ 禁用设备像素比 - 减少显示设备特征 */
    ch-device-memory=(),           /* 🧠 禁用设备内存信息 - 保护硬件配置隐私 */
    ch-downlink=(),                /* 🌐 禁用网络下行速度 - 保护网络状况隐私 */
    ch-ect=(),                     /* 📶 禁用网络连接类型 - 防止网络环境识别 */
    ch-rtt=()                      /* ⏱️ 禁用网络延迟信息 - 保护网络性能隐私 */
  ">
  
  <!--http://www.html5rocks.com/en/mobile/mobifying/-->
  <meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1,minimum-scale=1,maximum-scale=1,minimal-ui=true"/>
  
  <!--https://developer.apple.com/library/safari/documentation/AppleApplications/Reference/SafariHTMLRef/Articles/MetaTags.html-->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="format-detection" content="telephone=no">
  
  <!-- force webkit on 360 -->
  <meta name="renderer" content="webkit"/>
  <meta name="force-rendering" content="webkit"/>
  <!-- force edge on IE -->
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
  <meta name="msapplication-tap-highlight" content="no">
  
  <!-- force full screen on some browser -->
  <meta name="full-screen" content="yes"/>
  <meta name="x5-fullscreen" content="true"/>
  <meta name="360-fullscreen" content="true"/>
  
  <!--fix fireball/issues/3568 -->
  <meta name="x5-page-mode" content="app">
  
  <link rel="stylesheet" type="text/css" href="style.f76d1.css"/>
  
  <style>
    /* 🎯 Canvas性能优化：解决频繁图像读取警告 */
    /* 问题：Canvas2D频繁调用getImageData导致性能警告 */
    /* 解决：优化图像渲染方式，减少像素级读取操作 */
    canvas {
      image-rendering: -webkit-optimize-contrast; /* Webkit浏览器优化对比度渲染 */
      image-rendering: -moz-crisp-edges;         /* Firefox清晰边缘渲染 */
      image-rendering: crisp-edges;              /* 标准清晰边缘渲染 */
      image-rendering: pixelated;                /* 像素化渲染，适合像素游戏 */
    }
    
    /* 🎯 自定义加载界面样式 */
    #customLoader {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      font-family: 'Arial', sans-serif;
    }
    
    .loader-logo {
      font-size: 48px;
      font-weight: bold;
      margin-bottom: 30px;
      color: #fff;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    }
    
    .progress-container {
      width: 350px;
      margin-bottom: 20px;
    }
    
    .progress-label {
      font-size: 14px;
      color: rgba(255,255,255,0.9);
      margin-bottom: 8px;
      display: flex;
      justify-content: space-between;
    }
    
    .progress-bar {
      width: 100%;
      height: 20px;
      background: rgba(255,255,255,0.2);
      border-radius: 10px;
      overflow: hidden;
      box-shadow: inset 0 2px 4px rgba(0,0,0,0.3);
    }
    
    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #FF6B6B, #4ECDC4);
      width: 0%;
      transition: width 0.3s ease;
      border-radius: 10px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    
    .status-text {
      font-size: 16px;
      color: #fff;
      text-align: center;
      margin-bottom: 20px;
    }
    
    @keyframes pulse {
      0% { opacity: 1; }
      50% { opacity: 0.7; }
      100% { opacity: 1; }
    }
    
    .loading-text {
      animation: pulse 2s infinite;
    }
  </style>
</head>
<body>
  <!-- 🎯 自定义加载界面 -->
  <div id="customLoader">
    <div class="loader-logo">
      <img src="logo_dog.62845.jpg" alt="IdleFun" style="width: 80px; height: 80px; border-radius: 50%; margin-bottom: 10px;">
      <div style="font-size: 24px;">IdleFun</div>
    </div>
    
    <div class="progress-container">
      <div class="progress-label">
        <span id="statusText">Initializing Facebook SDK...</span>
        <span id="progressPercent">0%</span>
      </div>
      <div class="progress-bar">
        <div class="progress-fill" id="progressBar"></div>
      </div>
    </div>
    
    <div class="status-text loading-text" id="detailText">Preparing game...</div>
  </div>

  <!-- 🎮 游戏容器 -->
  <div id="GameDiv" cc_exact_fit_screen="true" style="display: none;">
    <div id="Cocos3dGameContainer">
      <!-- 🎨 游戏画布 - willReadFrequently="true"告诉浏览器这个Canvas会频繁读取像素数据 -->
      <!-- 作用：优化getImageData等操作的性能，减少"Multiple readback operations"警告 -->
      <!-- 适用：游戏引擎需要频繁读取画布内容进行渲染优化时使用 -->
      <canvas id="GameCanvas" 
              oncontextmenu="event.preventDefault()" 
              tabindex="99" 
              willReadFrequently="true">
      </canvas>
    </div>
  </div>

  <!-- Facebook SDK -->
  <script src="https://connect.facebook.net/en_US/fbinstant.latest.js"></script>
  <!-- <script src="http://localhost:8081/fbinstant.8.0.mock.js"></script> -->

  <!-- 🎯 自定义加载器脚本 -->
  <script>
    // 全局进度更新函数
    window.updateProgress = function(percent, text, detail) {
      const progressBar = document.getElementById('progressBar');
      const statusText = document.getElementById('statusText');
      const progressPercent = document.getElementById('progressPercent');
      const detailText = document.getElementById('detailText');
      
      if (progressBar) progressBar.style.width = percent + '%';
      if (statusText) statusText.textContent = text || 'Loading...';
      if (progressPercent) progressPercent.textContent = Math.round(percent) + '%';
      if (detailText) detailText.textContent = detail || text || 'Loading...';
      
      console.log(`🎯 Loading Progress: ${percent}% - ${text}`);
    };
    
    // 隐藏加载界面
    window.hideLoader = function() {
      const loader = document.getElementById('customLoader');
      const gameDiv = document.getElementById('GameDiv');
      
      if (loader) loader.style.display = 'none';
      if (gameDiv) gameDiv.style.display = 'block';
      
      console.log('✅ Custom loader hidden, game started');
    };
    
    // 初始化进度
    window.updateProgress(0, 'Initializing...', 'Starting Facebook SDK...');
  </script>

  <!-- Polyfills bundle. -->
  <script src="src/polyfills.bundle.43263.js" charset="utf-8"> </script>

  <!-- SystemJS support. -->
  <script src="src/system.bundle.543e6.js" charset="utf-8"> </script>

  <!-- Import map -->
  <script src="src/import-map.15999.json" type="systemjs-importmap" charset="utf-8"> </script>

  <!-- 🚀 官方启动流程 -->
  <script>
    System.import('./index.00e67.js').catch(function(err) { 
      console.error('Game loading failed:', err); 
      window.updateProgress(0, 'Loading Failed', 'Please refresh the page');
    });
  </script>

</body>
</html> 
