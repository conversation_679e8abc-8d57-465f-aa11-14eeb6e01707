[1, ["87KJdfUjNA1pqHCGJqumKH", "47io4JbX5NFK16QQdvTpAe@f9941"], ["node", "root", "asset", "_spriteFrame", "data"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_children", "_components", "_parent"], -1, 4, 2, 9, 1], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Widget", ["_alignFlags", "_left", "_right", "_bottom", "_originalWidth", "node", "__prefab"], -2, 1, 4], ["cc.Sprite", ["node", "__prefab", "_spriteFrame"], 3, 1, 4, 6], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab"], 2, 1, 4]], [[5, 0, 1, 2, 3], [6, 0, 1, 2, 2], [9, 0, 2], [11, 0, 2], [1, 0, 2], [0, 0, 1, 5, 6, 4, 3], [0, 2, 3, 7, 4, 3], [2, 0, 1, 2, 3, 4, 5, 4], [3, 0, 1, 2, 3, 4, 5, 4], [4, 0, 1, 2, 2], [7, 0, 1, 2, 2], [8, 0, 1, 2, 3], [10, 0, 1, 2, 1], [12, 0, 1, 2, 3, 4, 5, 6, 6], [13, 0, 1, 2, 1], [14, 0, 1, 2, 2]], [[4, "button1"], [5, "button1", 33554432, [-7], [[12, -3, [3, "cdKt0uXpFGNbiqFioh9jcQ"], [5, 263, 113]], [13, 44, 243.5, 243.5, 610.5, 100, -4, [3, "54mK9AptNNwaiUV05x/xQl"]], [14, -5, [3, "d6DPSk5Y1K8pnS8DeaPIj+"], 1], [15, 3, -6, [3, "73oePQhnBLeJdDGgimYmm9"]]], [8, "87kTBNQWJI3pftCW6wRRdv", null, null, -2, 0, [-1]]], [2, ["4a5atXBglJxJGAlAL90RE0"]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [6, 0, {}, 1, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -8, [9, "9fJkiblSpOQbUJPdFapuF8", 1, [[0, "lblName1", ["_name"], 3], [1, ["_lpos"], 3, [1, 0, 0, 0]], [1, ["_lrot"], 3, [3, 0, 0, 0, 1]], [1, ["_euler"], 3, [1, 0, 0, 0]], [0, 32, ["_fontSize"], 2], [10, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 143.125, 46.84]], [0, 32, ["_actualFontSize"], 2], [0, true, ["_enableOutline"], 2], [1, ["_outlineColor"], 2, [4, 4280629146]], [11, "BtnReplay", ["_dataID"], [2, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]], [0, "BtnReplay", ["_string"], 2], [0, 34, ["_lineHeight"], 2]]], 0]]], 0, [0, -1, 4, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, 1, 4, 0, 4, 1, 8], [0, 0], [2, 3], [0, 1]]