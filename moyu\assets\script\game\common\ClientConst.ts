import { Enum } from 'cc';

export class ClientConst {
    //重力相关
    public static Gravity = 30; //  数值越大。下落越快
    public static LinearDamping = 0.1; //控制物体线性速度衰减的程度。值越大，物体的速度衰减越快 如果希望物品下落过程中减速不那么明显，可以减小该值
    public static AngularDamping = 0.5; //如果希望物品在下落过程中旋转得更稳定或者减少旋转速度的衰减，可以减小该值
    public static pickBoxScale = 8; //盒子大小

    public static eazyModeItemKinds = 3; // 简单模式下的道具种类
    public static eazyModeItemNums = 15; // 简单模式下的道具数量
    public static eazyModeItemArr = [6, 6, 3];
    public static eazyModeItemArrNew = [3, 3, 3];

    public static initialRadius = 4; // 圆锥的底部半径 [3-6] 为佳
    public static initConeAngle = 80; // 圆锥的底部角度(锥形) [60~80] 为佳
    public static wallCellNum = 3; // wallCell的个数。个数越多。底部越近似一个圆 [3-5]为佳。
    public static hallGroundBgSize = 1.6; // wallCell的大小。越大。越接近一个圆 [16-20]为佳。
    public static hallGroundDistance = 16; // wallCell的距离。
    public static hallGroundNum = 4; //初始地面的个数。
    public static hallMaxRank = 50; // 排行榜最大排名限制
    public static increaseVertexCount = 5000; // 原固定顶点数（保留，若不需要可删除）
    public static increaseVertexCountPct = 0.02; // 新增：百分比权重（例如0.1表示10%）
    public static commonPrefabs: string = 'prefabs/commonPrefabs/';
    public static itemPrefabPaths: string = 'prefabs/game/'; // 默认物品预制体路径
    public static defaultBgPath: string = 'img/bg/bg_1'; // 默认背景图片路径
    public static dissoveCreatedDuration: number = 1.5; // 消融生成动画时长
    public static alwaysNewPlayerTest: boolean = false; // 是否总是新手玩家,测试用，别删
}

export class MusicConf {
    public static btnclick = 'boot/audios/btnclick'; // 按钮点击音效

    public static freeze = 'common/audios/freeze'; //  消除音效
    public static pass = 'common/audios/pass'; //  过关音效
    public static softFail = 'common/audios/softFail'; //  失败音效
    public static tap = 'common/audios/tap'; //  轻触音效
    public static gameMusic1 = 'common/audios/game';
    public static gameMusic2 = 'common/audios/game_2';
    public static hallmusic = 'common/audios/hall_1';
    public static commonPickEffect = 'common/audios/pick'; // 闲置音效
    public static commonClearEffect = 'common/audios/clear'; // 删除音效
    public static gameFailEffect = 'common/audios/fail_1'; // 游戏失败音效
}

export const PickBoxCommonIndexLenth = [0, 6]; // 普通格子索引
export const PickBoxCommonExtraIndexLenth = [7, 9]; // 额外格子索引

//在PhysicsSystem 中设置 用来控制碰撞组
export const PHY_GROUP = {
    DEFAULT: 1 << 0,
    WALL: 1 << 1, // 墙
    ITEM: 1 << 2, // 道具
    ITEM_BOX: 1 << 3, // 普通格子中的道具
    ITEM_BOX_EXTRA: 1 << 4, // 额外格子中的道具
    GROUND: 1 << 5, // 地面
};
//在Layers 中设置  用来控制摄像机可见的层级
export const LAYER_GROUP = {
    DEFAULT: 1 << 30,
    RimLight: 1 << 18, //
    UI_3D: 1 << 23, //
    WALL: 1 << 19, //
    GROUND: 1 << 17, // 地面
};

Enum(PHY_GROUP);
Enum(LAYER_GROUP);
