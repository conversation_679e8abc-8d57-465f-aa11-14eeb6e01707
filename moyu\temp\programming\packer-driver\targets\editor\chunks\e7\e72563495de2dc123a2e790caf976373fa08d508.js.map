{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/tsrpc/models/GameConst.ts"], "names": ["GameConst", "dayFreeLimts", "dayFreeLimtsUse", "usePropCd", "pickIntervalClick", "defaultCountryCode", "newPlayerDefaultProps", "moveOut", "tips", "reShuffle", "revive", "TTL"], "mappings": ";;;iBAGaA,S;;;;;;;;;;;;;AAHb;AACA;AACA;2BACaA,S,GAAN,MAAMA,SAAN,CAAgB,E;;AAAVA,MAAAA,S,CACKC,Y,GAAe,I;AAAM;AAD1BD,MAAAA,S,CAEKE,e,GAAkB,G;AAAK;AAF5BF,MAAAA,S,CAGKG,S,GAAY,I;AAAM;AAHvBH,MAAAA,S,CAIKI,iB,GAAoB,C;AAAG;AAJ5BJ,MAAAA,S,CAKKK,kB,GAAqB,O;AAAS;AALnCL,MAAAA,S,CAOKM,qB,GAAwB;AAClCC,QAAAA,OAAO,EAAE,CADyB;AACtB;AACZC,QAAAA,IAAI,EAAE,CAF4B;AAEzB;AACTC,QAAAA,SAAS,EAAE,CAHuB;AAGpB;AACdC,QAAAA,MAAM,EAAE,CAJ0B,CAIvB;;AAJuB,O;;qBAO9BC,G,0BAAAA,G;AAAAA,QAAAA,G,CAAAA,G;AAAAA,QAAAA,G,CAAAA,G;AAAAA,QAAAA,G,CAAAA,G;AAAAA,QAAAA,G,CAAAA,G;AAAAA,QAAAA,G,CAAAA,G;AAAAA,QAAAA,G,CAAAA,G;AAAAA,QAAAA,G,CAAAA,G;AAAAA,QAAAA,G,CAAAA,G;eAAAA,G", "sourcesContent": ["/**\n * 客户端和游戏服务器共用的常量\n */\nexport class GameConst {\n    public static dayFreeLimts = 9999; // 每天可以玩多少次\n    public static dayFreeLimtsUse = 500; // 每天可以用多少次道具（测试值：5个）\n    public static usePropCd = 2000; //道具冷却时间(毫秒)\n    public static pickIntervalClick = 0; //点击物品的间隔 毫秒\n    public static defaultCountryCode = 'Other'; // 国家代码\n\n    public static newPlayerDefaultProps = {\n        moveOut: 1, // 移出道具默认数量\n        tips: 1, // 提示道具默认数量\n        reShuffle: 1, // 洗牌道具默认数量\n        revive: 1, // 复活道具默认数量\n    };\n}\nexport enum TTL {\n    None = 0,\n    OneHour = 3600, // 1小时\n    OneDay = 86400, // 1天\n    OneDayAndHalf = 129600, // 1天半\n    OneWeek = 604800, // 1周\n    OneWeekHalf = 302400, // 半周\n    OneMonth = 2592000, // 1月\n    OneMonthHalf = 1296000, // 半月\n}\n"]}