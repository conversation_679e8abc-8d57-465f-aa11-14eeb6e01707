[1, ["14I5OoJkFHdaecy/4b2eXK", "5dZJ5VKmVMn4WQldoN7+vA@f9941", "0fJ5VVbx1E4IlMUiRX0Fbb", "38EwnAhRxPKoeJSmk/h+84"], ["node", "root", "asset", "_spriteFrame", "data"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos"], -1, 4, 9, 1, 2, 5], ["cc.Sprite", ["node", "__prefab", "_spriteFrame"], 3, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["cc.TargetInfo", ["localID"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isBold", "node", "__prefab", "_color"], -1, 1, 4, 5], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab"], 2, 1, 4], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["e0958oXX3VN7IhkdBJWFRYF", ["node", "__prefab"], 3, 1, 4]], [[11, 0, 2], [8, 0, 1, 2, 2], [10, 0, 1, 2, 1], [4, 0, 1, 2, 3, 4, 5, 5], [0, 2, 3, 6, 4, 3], [3, 0, 1, 2, 3, 4, 5, 4], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3], [9, 0, 2], [0, 0, 1, 6, 7, 5, 4, 8, 3], [0, 0, 1, 6, 5, 4, 8, 3], [1, 0, 1, 1], [2, 0, 2], [0, 0, 1, 7, 5, 4, 3], [0, 0, 1, 6, 5, 4, 3], [5, 0, 1, 2, 3, 4, 5, 4], [1, 0, 1, 2, 1], [12, 0, 1, 2, 3, 4, 5, 6, 5], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3, 4, 4], [15, 0, 1, 1]], [[12, "revive"], [13, "revive", 33554432, [-8, -9], [[2, -5, [0, "c0cp8XlKBJOILLPAv66S6x"], [5, 750, 1334]], [19, 45, 100, 100, -6, [0, "6ekLu4c1FA0bUftvRoWF0+"]], [20, -7, [0, "eb6khvnudLVJI2etW53i3H"]]], [15, "cerIilZb1NcrnFzz7o2GEh", null, null, -4, 0, [-1, -2, -3]]], [9, "contentArea", 33554432, 1, [-11, -12, -13, -14], [[2, -10, [0, "692Y5rkj9PPanWAmVlp0s5"], [5, 558, 565]]], [3, "c6YUzDtZBCVpPFmbBRNnyd", null, null, null, 1, 0], [1, 0, 95.054, 0]], [9, "videoBtn", 33554432, 2, [-18, -19], [[2, -15, [0, "0aW35um+ZB8bIDCIvn+QC9"], [5, 173, 118]], [11, -16, [0, "0eSJhw2XROmYHwggTXI91L"]], [18, 3, -17, [0, "70PJ+xpbdPKYj57pSoAZ1k"]]], [3, "2dyVCQQrlGCbF/cM1vrkRU", null, null, null, 1, 0], [1, 3.662, -248.01, 0]], [8, ["a0daVw8DRLi6ToMaTA0VS2"]], [8, ["afQdJYzHlJS4e09arjozPb"]], [8, ["46L362HwxAyYxM0TWgugL+"]], [4, 0, {}, 1, [5, "a0daVw8DRLi6ToMaTA0VS2", null, null, -20, [6, "131+Y0z11M5Y54N99JB0X6", 1, [[7, "mask", ["_name"], 4], [1, ["_lpos"], 4, [1, 0, 0, 0]], [1, ["_lrot"], 4, [3, 0, 0, 0, 1]], [1, ["_euler"], 4, [1, 0, 0, 0]]]], 0]], [14, "bg", 33554432, 2, [[2, -21, [0, "0bSrWSLwBOvqIIHkguBJ/a"], [5, 735, 737]], [16, -22, [0, "d7XuP27wFA2ZKYBN+XPpPp"], 1]], [3, "9dLEhw+RtLLYrdo62R9mkQ", null, null, null, 1, 0]], [10, "img_heart", 33554432, 2, [[2, -23, [0, "8dMTGIInZHEodOHEGgaBYf"], [5, 304, 304]], [11, -24, [0, "baZpevnERKJbtDgMYEwOvR"]]], [3, "451XfakNRKupx5OzRUiwAE", null, null, null, 1, 0], [1, -4, 74.413, 0]], [10, "btnTips", 33554432, 3, [[2, -25, [0, "1awULQqohBxKBz2uHCLOEF"], [5, 241.796875, 50.4]], [17, "免费获得1次复活", 32, 32, true, -26, [0, "bay/TvVRtF34eViSnBptxY"], [4, 4286219621]]], [3, "24dyIPKvFGT7giUjUmudVl", null, null, null, 1, 0], [1, 0, 87.446, 0]], [4, 0, {}, 3, [5, "afQdJYzHlJS4e09arjozPb", null, null, -27, [6, "cfZeuPnqpLYpE6Dtzl2qcp", 1, [[7, "create<PERSON><PERSON>er", ["_name"], 5], [1, ["_lpos"], 5, [1, 0, 0, 0]], [1, ["_lrot"], 5, [3, 0, 0, 0, 1]], [1, ["_euler"], 5, [1, 0, 0, 0]]]], 2]], [4, 0, {}, 2, [5, "46L362HwxAyYxM0TWgugL+", null, null, -28, [6, "1fppMbMAROeprvTKNSYN7y", 1, [[7, "closeBtn", ["_name"], 6], [1, ["_lpos"], 6, [1, 322.556, 330.842, 0]], [1, ["_lrot"], 6, [3, 0, 0, 0, 1]], [1, ["_euler"], 6, [1, 0, 0, 0]]]], 3]]], 0, [0, -1, 12, 0, -2, 11, 0, -3, 7, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 7, 0, -2, 2, 0, 0, 2, 0, -1, 8, 0, -2, 9, 0, -3, 3, 0, -4, 12, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 10, 0, -2, 11, 0, 1, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 1, 11, 0, 1, 12, 0, 4, 1, 28], [0, 0, 0, 0], [2, 3, 2, 2], [0, 1, 2, 3]]