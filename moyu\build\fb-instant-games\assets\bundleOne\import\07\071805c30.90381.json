[1, ["e5LUoqx3RAr41dA5QrbKMj", "bcW+gf8OpJ+qq/mCqc0yae@3fd04"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 2849176125, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 33312, "length": 5286, "count": 2643, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 33312, "count": 694, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.43158799409866333, -0.3921351432800293, -0.37554123997688293], "maxPosition", 8, [1, 0.4195547103881836, 0.3267558515071869, 0.37554123997688293]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_05"], [3, "日式寿司_05", [[4, 1, -2, [0, "04PYRuqlhAzJITfW5L2GlQ"], [0], [5, true, true], 1], [6, 4, -3, [0, "6bDAU6SxBGhbqHSv6/HiwF"]], [7, 0.558, 0, -4, [0, "4eMgSnkKBGzYgKmaMNIti1"], [1, 0.01433485746383667, -0.025503411889076233, 5.37186861038208e-05]]], [8, "edVFf9TQdF876Kul5ZB77n", null, null, null, -1, 0], [1, 1.58, 0, -0.846]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]