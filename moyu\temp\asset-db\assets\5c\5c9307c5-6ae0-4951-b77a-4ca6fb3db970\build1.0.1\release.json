[1, ["87KJdfUjNA1pqHCGJqumKH"], ["root", "asset", "node", "data"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_children", "_components", "_lpos", "_parent"], -1, 4, 2, 9, 5, 1], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["cc.TargetInfo", ["localID"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2]], [[5, 0, 1, 2, 3], [6, 0, 1, 2, 2], [8, 0, 2], [1, 0, 2], [0, 0, 1, 5, 6, 4, 7, 3], [0, 2, 3, 8, 4, 3], [2, 0, 1, 2, 3, 4, 5, 4], [3, 0, 1, 2, 3, 4, 5, 4], [4, 0, 1, 2, 2], [7, 0, 1, 2, 2], [9, 0, 1, 2, 1], [10, 0, 2]], [[3, "hallRankUICell"], [4, "hallRankUICell", 33554432, [-4], [[10, -3, [11, "1ehE0yjgpOdpGShi2Ty+Al"], [5, 260.15625, 50.4]]], [7, "26iaepJgFCqKV3l5WUmOuO", null, null, -2, 0, [-1]], [1, 0, 116.562, 0]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [2, ["09i06HqvBP2ZaQ4Pk+p3Mt"]], [5, 0, {}, 1, [6, "f05XX5jrpEOYwv6lCoUIav", null, null, -5, [8, "8eCNJYQF5Ck6oLAgJvxVQU", 1, [[0, "VMLabelLanguage_White", ["_name"], 2], [1, ["_lpos"], 2, [1, 0, 0, 0]], [1, ["_lrot"], 2, [3, 0, 0, 0, 1]], [1, ["_euler"], 2, [1, 0, 0, 0]], [0, true, ["templateMode"], 3], [0, 1, ["watchPathArr", "length"], 3], [0, "", ["watchPathArr", "0"], 3], [0, "HallRankLabel", ["_dataID"], 3], [9, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 248.4375, 50.4]]]], 0]]], 0, [0, -1, 4, 0, 0, 1, 0, 2, 1, 0, -1, 4, 0, 0, 4, 0, 3, 1, 5], [0], [1], [0]]