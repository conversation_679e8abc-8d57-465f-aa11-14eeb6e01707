[1, ["786QW1LRpNOasfEX/o0Nvb", "80pt/43AZPnaLIAxcNqh3d", "87KJdfUjNA1pqHCGJqumKH", "c1CT58ndZMeKnGKKaoK8m+", "d0FmJQ79tA4IthGAk9i2K6@f9941", "14I5OoJkFHdaecy/4b2eXK", "80OqXHI3BB0Kfw4LZ0sXr0", "e52WjWPthMfaIAVaBGR0gr", "2etrlQE9RP4YFE0X54+JYV@f9941", "03PF2TGrlGZLRts5PeD6Yi@f9941", "622je7I3dHiIx9vsWb3KE2", "208m2EU+lBRpwKCxPxLV1h@f9941", "2av/+acrlCNq680y8eaX2f@f9941", "e0sLVzT0RKqJuKRmjkou3I@f9941", "3bNGi1wppDeaOf7RqIm9Sm@f9941", "51lyu5+GtKAKvqrcIDW40V@f9941", "f3r81OnwxOW7GyQOcsDXFp", "38aXRfwBtGJ4AdL6LSBawu@f9941", "a1IOC+dm1EAo+x7tFQuRBY@f9941", "26qSDlZtJO+JKe1m5B/Eag@f9941", "6bAK8rf8pPE54WiDqq1t7L@f9941", "b4Kl7ucq1AyZ84IiEUyK6K@f9941", "12CGLetYxEn5JY6OsQY9mT@f9941", "7dj5uJT9FMn6OrOOx83tfK@f9941", "bd/PXSJtBHnoJk6YxTC+vP", "7dj5uJT9FMn6OrOOx83tfK@6c48a", "9dK6itBNdGWLoRwPrEsxja@f9941", "bb7Zmqlf5Jobp/GFB57EqN@f9941", "9dK6itBNdGWLoRwPrEsxja@6c48a", "bb7Zmqlf5Jobp/GFB57EqN@6c48a"], ["node", "root", "targetInfo", "_spriteFrame", "asset", "data", "_font", "_parent", "_textureSource", "_target", "value", "_normalSprite"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_children", "_lpos", "_parent"], -1, 4, 9, 2, 5, 1], ["cc.Widget", ["_alignFlags", "_top", "_originalWidth", "_left", "_right", "_originalHeight", "node", "__prefab"], -3, 1, 4], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame", "_color"], 1, 1, 4, 6, 5], "cc.SpriteFrame", ["cc.Label", ["_string", "_actualFontSize", "_enableWrapText", "_isSystemFontUsed", "_isBold", "_fontSize", "_lineHeight", "node", "__prefab", "_font", "_color"], -4, 1, 4, 6, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Layout", ["_layoutType", "_paddingTop", "_resizeMode", "_paddingLeft", "_paddingRight", "_paddingBottom", "_spacingY", "_constraint", "_constraintNum", "node", "__prefab"], -6, 1, 4], ["47052uw/Y5O1LXaLObj4ARx", ["watchPath", "valueA", "node", "__prefab", "watchNodes"], 1, 1, 4, 2], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_target", "_normalSprite", "_normalColor"], 2, 1, 4, 1, 6, 5], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedComponents"], 2, 1, 9, 9], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["57053nawwtNYJmviHWY0LN+", ["node", "__prefab"], 3, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.TargetInfo", ["localID"], 2], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 6], ["4bff6xrIC5LtYScAfbMmEdR", ["_dataID", "node", "__prefab"], 2, 4, 4], ["4bff6xrIC5LtYScAfbMmEdR", ["_dataID", "node", "__prefab"], 2, 1, 4], ["cc.TTFFont", ["_name", "_native"], 1]], [[11, 0, 2], [18, 0, 1, 2, 3], [16, 0, 2], [19, 0, 1, 2, 2], [5, 0, 1, 2, 1], [14, 0, 1, 2, 3, 4, 5, 5], [20, 0, 1, 2, 2], [0, 2, 3, 8, 4, 3], [15, 0, 1, 2, 3, 4, 5, 4], [10, 0, 2], [9, 0, 1, 2, 2], [21, 0, 1, 2, 3], [0, 0, 1, 8, 6, 5, 4, 7, 3], [2, 0, 1, 2, 3, 4, 3], [0, 0, 1, 6, 5, 4, 3], [0, 0, 1, 5, 4, 3], [2, 2, 3, 4, 1], [24, 0, 1, 2, 2], [0, 0, 1, 8, 5, 4, 7, 3], [1, 0, 2, 5, 6, 7, 4], [9, 0, 1, 3, 2, 2], [17, 0, 1, 1], [4, 0, 1, 5, 6, 2, 3, 7, 8, 10, 9, 7], [2, 0, 2, 3, 4, 2], [0, 0, 1, 6, 5, 4, 7, 3], [5, 0, 1, 1], [1, 0, 6, 7, 2], [1, 0, 3, 4, 1, 2, 6, 7, 6], [1, 0, 3, 6, 7, 3], [13, 0, 1, 2, 3, 4, 5, 4], [0, 0, 1, 8, 6, 5, 4, 3], [0, 0, 2], [0, 0, 1, 5, 4, 7, 3], [1, 0, 1, 6, 7, 3], [1, 0, 4, 1, 6, 7, 4], [12, 0, 1, 1], [2, 0, 1, 2, 3, 5, 4, 3], [2, 0, 2, 3, 2], [6, 2, 0, 3, 4, 1, 5, 6, 7, 8, 9, 10, 10], [6, 0, 1, 9, 10, 3], [7, 0, 1, 2, 3, 4, 3], [7, 0, 2, 3, 4, 2], [8, 0, 1, 2, 3, 4, 2], [8, 0, 1, 2, 5, 3, 2], [22, 0, 1, 2, 2], [23, 0, 1, 2, 2], [25, 0, 1, 3], [4, 0, 1, 2, 3, 4, 7, 8, 9, 6], [4, 0, 1, 2, 3, 4, 7, 8, 10, 9, 6]], [[[[9, "PersonInfo"], [14, "PersonInfo", 33554432, [-18, -19], [[4, -15, [0, "77N2cid5pKDpXplRH/AWEU"], [5, 749.9999999999999, 1334]], [19, 45, 2, 2, -16, [0, "63zNQq8NlBQ5QWzOJ4Kgjs"]], [35, -17, [0, "27PEsFUVpOZql3uqKN41v2"]]], [29, "a0daVw8DRLi6ToMaTA0VS2", null, null, -14, 0, [-1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13]]], [14, "content", 33554432, [-23, -24, -25], [[4, -20, [0, "4aZQCb2DdDqYTM4z0iAuRw"], [5, 590, 820]], [13, 1, 0, -21, [0, "53SRtbqZNDTKbIxpjbBlwM"], 22], [38, 1, 3, 50, 50, 50, 50, 20, 2, 1, -22, [0, "620O/rm/ZEb6YX0ZxV4CmW"]]], [5, "c9Cl0DW4JF4ZSXUsxlcb3i", null, null, null, 1, 0]], [24, "join", 33554432, [-30, -31], [[4, -26, [0, "fbdDrP3+BNPaLmCmEc2jkr"], [5, 345, 95]], [13, 1, 0, -27, [0, "c8Nw4kUMNIPLgo/6Ajof6t"], 5], [26, 4, -28, [0, "47MP5bSsxPOJKM+X3+xWYi"]], [39, 2, 6, -29, [0, "b28kM0w7tKwKKPNOq4K9QK"]]], [5, "f65/bqkwNLVIskojn8Ti5Q", null, null, null, 1, 0], [1, 0, -32.5, 0]], [12, "down", 33554432, 2, [-34, -35, -36, -37], [[4, -32, [0, "66gb+tcChBxZMhS3cuW/nP"], [5, 500, 410]], [13, 1, 0, -33, [0, "33vNnFHLRCJb6/EmobjXyY"], 21]], [5, "96amr5mSNDCo+oOae2VM0H", null, null, null, 1, 0], [1, 5, -155, 0]], [14, "小底已通过", 33554432, [-43], [[4, -38, [0, "2cpnW6KvRK56C2UvOh9yxf"], [5, 300, 38]], [13, 1, 0, -39, [0, "8bIK7k8uxFmqXU7WvIE713"], 17], [19, 45, 300, 38, -40, [0, "78k2w39AlA0asGnslbmfoL"]], [40, "*.<PERSON><PERSON><PERSON>", 1, -42, [0, "56XK969spOf4RHIoNeAAeE"], [-41]]], [5, "10hmGWNTBCZ5qRITZT+zWL", null, null, null, 1, 0]], [14, "小底未通过", 33554432, [-49], [[4, -44, [0, "f1b+uySAFEmpPvojvF3FdH"], [5, 300, 38]], [13, 1, 0, -45, [0, "23WTB1GRBPLaESNnPdIJ9B"], 19], [19, 45, 32, 38, -46, [0, "1a9SmY+o9BXprTOz1Vyk1j"]], [41, "*.<PERSON><PERSON><PERSON>", -48, [0, "19f0QA3YpDtL6ff/DMh3f/"], [-47]]], [5, "a7ql+zBbxJEaP2muLfuAza", null, null, null, 1, 0]], [2, ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]], [12, "邀请有礼 底", 33554432, 4, [-53, -54], [[4, -50, [0, "27J/V91WJJZaeiNoUnKsgd"], [5, 327, 55]], [16, -51, [0, "30CSRXu/dAHaZ+7OPhYFHO"], 13], [33, 17, 28, -52, [0, "a2tVklQ1dNZoubUoPinmnF"]]], [5, "60BAj5EelHGLzQQXXr73JZ", null, null, null, 1, 0], [1, 0, 149.5, 0]], [24, "title", 33554432, [-58, -59], [[4, -55, [0, "943ZT+JMpIg4H+057tLZ+K"], [5, 590, 73]], [13, 1, 0, -56, [0, "89STazpY1N5Z9KKWJdZi0U"], 26], [27, 17, 318.5, 315.5, -384.586, 116, -57, [0, "22b7FbYP9OSKfswX02ulXr"]]], [5, "62ST9SqUdAKYKJDa7YLhvi", null, null, null, 1, 0], [1, 0, 398.086, 0]], [2, ["4a5atXBglJxJGAlAL90RE0"]], [18, "closeBtn", 33554432, 9, [[4, -60, [0, "feQxtseBBAmr5IgkGIjIrP"], [5, 68, 69]], [23, 1, -61, [0, "83jvKbhlhGpJxzBhUNwwr0"], 24], [42, 3, -63, [0, "2czDRzWc9BT5qW4SCj8uky"], -62, 25], [34, 33, -10.663000000000011, -12.970000000000027, -64, [0, "86W4DjDxNJaIYMfh3omAbf"]]], [5, "a95kLmHpBByqsklSf271iu", null, null, null, 1, 0], [1, 271.663, 14.970000000000027, 0]], [30, "info", 33554432, 1, [2, 9, -66], [[25, -65, [0, "01JOSufuxD2b+12LNVCnrm"]]], [5, "87st9QOS1Ma5hMOrGh/5kp", null, null, null, 1, 0]], [12, "mid", 33554432, 2, [-69, -70], [[4, -67, [0, "36uIH+R5RGP6yZRmG8OehG"], [5, 500, 110]], [13, 1, 0, -68, [0, "53/LurpxFPHJ5UaYdSVyxT"], 10]], [5, "a5esKyNJxFsb5jp2zpqT6r", null, null, null, 1, 0], [1, 5, 125, 0]], [12, "标记", 33554432, 13, [-74], [[4, -71, [0, "88fSHmLulN6qDIdbfdiWpO"], [5, 25, 31]], [16, -72, [0, "b5cRc9BGZOcrHRSuk2H3sK"], 7], [28, 10, 20, -73, [0, "f7/pEsM/dFBKSm7ip6aWQ6"]]], [5, "94Sj+zPKtOypR1qQt+DEwQ", null, null, null, 1, 0], [1, -217.5, 0, 0]], [2, ["4a5atXBglJxJGAlAL90RE0"]], [2, ["4a5atXBglJxJGAlAL90RE0"]], [2, ["a0daVw8DRLi6ToMaTA0VS2"]], [12, "top", 33554432, 2, [-76, -77], [[4, -75, [0, "86mtK/Vs5CZps9k5bgBstg"], [5, 500, 160]]], [5, "edMTQvghtPnoKNMcTpRxmG", null, null, null, 1, 0], [1, 5, 280, 0]], [7, 0, {}, 18, [8, "b2LyNAFEdCsIkChnpFdNTi", null, null, -85, [20, "e0AXo1n4ZDzYNNXbObynR3", 1, [[21, [2, ["b2LyNAFEdCsIkChnpFdNTi"]], [[26, 8, -84, [0, "83qKcxJGJM5Zib7HLNtNBK"]]]]], [[1, "avatar", ["_name"], -78], [3, ["_lpos"], -79, [1, -181, 0, 0]], [3, ["_lrot"], -80, [3, 0, 0, 0, 1]], [3, ["_euler"], -81, [1, 0, 0, 0]], [1, "avatarImg", ["_name"], -82], [6, ["_contentSize"], [2, ["fagAZlPmdJDYrk089Nl/sl"]], [5, 138, 151]], [1, true, ["_active"], -83]]], 1]], [2, ["b2LyNAFEdCsIkChnpFdNTi"]], [12, "info", 33554432, 18, [-87, 3], [[4, -86, [0, "00Lkeap5NJYpHr3Ccb/28l"], [5, 355, 160]]], [5, "e3zKlICv1PFZRiwZ+JZi99", null, null, null, 1, 0], [1, 72.5, 0, 0]], [7, 0, {}, 21, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -99, [20, "7e2xCz43xI/IVXLckctAnt", 1, [[21, [2, ["f05XX5jrpEOYwv6lCoUIav"]], [[27, 17, 94.41552734375, 94.41552734375, 9.883000000000017, 166.1689453125, -98, [0, "b4VR1gISJLmabF6uR0oAc+"]]]]], [[1, "name", ["_name"], -88], [3, ["_lpos"], -89, [1, 0, 51.216999999999985, 0]], [3, ["_lrot"], -90, [3, 0, 0, 0, 1]], [3, ["_euler"], -91, [1, 0, 0, 0]], [6, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 163.3125, 37.8]], [1, "nickname", ["_string"], -92], [1, 26, ["_fontSize"], -93], [1, 26, ["_actualFontSize"], -94], [3, ["_color"], -95, [4, 4282481837]], [1, "role.nick<PERSON>ame", ["watchPath"], -96], [1, "role.nick<PERSON>ame", ["_dataID"], -97]]], 2]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [2, ["4a5atXBglJxJGAlAL90RE0"]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [2, ["4a5atXBglJxJGAlAL90RE0"]], [2, ["48BGi+JnJOKpaEdqvfVVS1"]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [2, ["47zQiH5ZpHGZOMEAJOGjcx"]], [7, 0, {}, 14, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -107, [20, "c27DPVRahFsqVQ1hmgVTZH", 1, [[21, [2, ["f05XX5jrpEOYwv6lCoUIav"]], [[28, 10, 13.43225000000001, -106, [0, "9bpmiMr8NKTKnyblxdtlmm"]]]]], [[1, "countryCode", ["_name"], -100], [3, ["_lpos"], -101, [1, 82.40100000000001, 0, 0]], [3, ["_lrot"], -102, [3, 0, 0, 0, 1]], [3, ["_euler"], -103, [1, 0, 0, 0]], [1, "countryCode", ["_string"], 15], [6, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 162.9375, 37.8]], [3, ["_color"], 15, [4, 4282481837]], [1, 22, ["_fontSize"], 15], [1, 22, ["_actualFontSize"], 15], [1, 0, ["_horizontalAlign"], 15], [1, "role.countryCode", ["watchPath"], -104], [1, "role.countryCode", ["_dataID"], -105]]], 6]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [2, ["30Ahna/hFADJL5JEyiP8Kc"]], [2, ["4a5atXBglJxJGAlAL90RE0"]], [12, "投影", 33554432, 4, [-110], [[4, -108, [0, "5b4xd7MAlBIbZyf6EO2873"], [5, 149, 39]], [16, -109, [0, "580PDwJfdI64wNlmy/zCPI"], 15]], [5, "7cgovbbPxAxbC8De7PBg2n", null, null, null, 1, 0], [1, 0, -57.166, 0]], [12, "Node", 33554432, 4, [5, 6], [[4, -111, [0, "abvgvIPItHpb5AjxLBLEyP"], [5, 300, 38]]], [5, "76wnxMBcNMzJNmaNK24Y71", null, null, null, 1, 0], [1, 0, -121.469, 0]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [2, ["47zQiH5ZpHGZOMEAJOGjcx"]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [2, ["48BGi+JnJOKpaEdqvfVVS1"]], [7, 0, {}, 1, [8, "a0daVw8DRLi6ToMaTA0VS2", null, null, -112, [10, "4bd7+FLBlMqJQRLHhjl3Px", 1, [[1, "mask", ["_name"], 17], [3, ["_lpos"], 17, [1, 0, 0, 0]], [3, ["_lrot"], 17, [3, 0, 0, 0, 1]], [3, ["_euler"], 17, [1, 0, 0, 0]]]], 0]], [7, 0, {}, 3, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -113, [10, "c4deIIQWNOX7mqSyAFsBs1", 1, [[1, "jonInTimeLabel", ["_name"], 25], [3, ["_lpos"], 25, [1, 0, 22.6, 0]], [3, ["_lrot"], 25, [3, 0, 0, 0, 1]], [3, ["_euler"], 25, [1, 0, 0, 0]], [1, "2024/11/11", ["_string"], 26], [6, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 133.125, 37.8]], [3, ["_color"], 26, [4, 4282481837]], [1, 24, ["_fontSize"], 26], [1, 24, ["_actualFontSize"], 26], [1, true, ["templateMode"], 27], [1, 1, ["watchPathArr", "length"], 27], [1, "*.createtime", ["watchPathArr", "0"], 27], [1, "RegisterTime", ["_dataID"], 27]]], 3]], [7, 0, {}, 3, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -114, [10, "0a5s5qHllMQrs3SqwJwp6p", 1, [[1, "passTimeLabel", ["_name"], 28], [3, ["_lpos"], 28, [1, 0, -12.679999999999996, 0]], [3, ["_lrot"], 28, [3, 0, 0, 0, 1]], [3, ["_euler"], 28, [1, 0, 0, 0]], [11, "pass", ["_string"], [2, ["4a5atXBglJxJGAlAL90RE0"]]], [6, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 107.625, 32.76]], [1, "PassTimes", ["_dataID"], 29], [1, true, ["templateMode"], 29], [1, 1, ["watchPathArr", "length"], 29], [1, "role.passTimes", ["watchPathArr", "0"], 29]]], 4]], [7, 0, {}, 13, [8, "30Ahna/hFADJL5JEyiP8Kc", null, null, -115, [20, "f7T3myHJpJCqEGNB/Fl+6F", 1, [[21, [2, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [[45, "ModifyArea", [31, "New Node"], [0, "03cZZBD5BE04qK/8Iudydn"]]]]], [[1, "vmButton", ["_name"], 32], [3, ["_lpos"], 32, [1, 141.58000000000004, 0, 0]], [3, ["_lrot"], 32, [3, 0, 0, 0, 1]], [3, ["_euler"], 32, [1, 0, 0, 0]], [44, ["_spriteFrame"], [2, ["daO3pUYmlKVIgsdc588k+9"]], 9], [1, "ModifyArea", ["_string"], 7], [6, ["_contentSize"], [2, ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]], [5, 135.3203125, 54.4]], [6, ["_contentSize"], [2, ["b2FpfgEc1Py4FLQrPbDjfF"]], [5, 158, 68]], [3, ["_color"], 7, [4, 4294967295]], [1, 26, ["_fontSize"], 7], [1, 26, ["_actualFontSize"], 7], [1, true, ["_enableOutline"], 7], [3, ["_outlineColor"], 7, [4, 4279916581]], [6, ["_lpos"], [2, ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 2.1580000000000155, 0]]]], 8]], [18, "灯光", 33554432, 8, [[4, -116, [0, "daYxKWDhNLAblCCkk2LSVO"], [5, 285, 222]], [16, -117, [0, "a12P0/ER5FO60a0pXDnIgD"], 11]], [5, "9dyzOj/eRLMYLs1SO86ziD", null, null, null, 1, 0], [1, 0, -85.8889999999999, 0]], [7, 0, {}, 8, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -118, [10, "ffX0o8tORMfb04qPjoTsoF", 1, [[11, "VMLabelLanguage_White", ["_name"], [2, ["f05XX5jrpEOYwv6lCoUIav"]]], [6, ["_lpos"], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 4.982999999999947, 0]], [6, ["_lrot"], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [3, 0, 0, 0, 1]], [6, ["_euler"], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 0, 0]], [11, 26, ["_fontSize"], [2, ["4a5atXBglJxJGAlAL90RE0"]]], [6, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 146.0859375, 39.28]], [11, 26, ["_actualFontSize"], [2, ["4a5atXBglJxJGAlAL90RE0"]]], [1, "InvitationGift", ["_string"], 33], [1, true, ["_enableOutline"], 33], [3, ["_outlineColor"], 33, [4, 4281221017]], [1, 28, ["_lineHeight"], 33], [11, "InvitationGift", ["_dataID"], [2, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]]]], 12]], [18, "松鼠", 33554432, 34, [[4, -119, [0, "8f0eaRR8FGYL0yQKAL3hW1"], [5, 134, 149]], [16, -120, [0, "a4+RRRgyRGJLfUIwztNbw0"], 14]], [5, "a8skFVKwRCQJukllOJ714w", null, null, null, 1, 0], [1, 0, 59.79899999999998, 0]], [7, 0, {}, 5, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -121, [10, "93gjAcl0BKNK0kEi7ruzy4", 1, [[1, "VMLabelLanguage_Yellow", ["_name"], 36], [3, ["_lpos"], 36, [1, 0, 0, 0]], [3, ["_lrot"], 36, [3, 0, 0, 0, 1]], [3, ["_euler"], 36, [1, 0, 0, 0]], [6, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 209.6875, 30.240000000000002]], [11, "CompletedChallenges", ["_dataID"], [2, ["36m7MuCL5KzbRIiB/pDpxj"]]]]], 16]], [7, 0, {}, 6, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -122, [10, "bff6xcsixPm4wvObUjhsXO", 1, [[1, "VMLabelLanguage_White", ["_name"], 37], [3, ["_lpos"], 37, [1, 0, 0, 0]], [3, ["_lrot"], 37, [3, 0, 0, 0, 1]], [3, ["_euler"], 37, [1, 0, 0, 0]], [1, 22, ["_fontSize"], 16], [6, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 127.9609375, 30.240000000000002]], [1, 22, ["_actualFontSize"], 16], [1, 24, ["_lineHeight"], 16], [1, false, ["_isBold"], 16], [1, "NoPassLevels", ["_string"], 16], [11, "NoPassLevels", ["_dataID"], [2, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]]]], 18]], [7, 0, {}, 4, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -123, [10, "f4kZnDOOBOz4W+1Fe4WDcX", 1, [[3, ["_lpos"], 38, [1, 0, -170.065, 0]], [1, "record", ["_name"], 38], [3, ["_lrot"], 38, [3, 0, 0, 0, 1]], [3, ["_euler"], 38, [1, 0, 0, 0]], [6, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 145.125, 32.76]], [1, "ContentToday", ["_dataID"], 39], [1, true, ["templateMode"], 39], [1, 1, ["watchPathArr", "length"], 39], [1, "*.lbl<PERSON><PERSON><PERSON>", ["watchPathArr", "0"], 39]]], 20]], [7, 0, {}, 9, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -124, [10, "14LBNjJuJBW7K0o2fekUeg", 1, [[1, "title", ["_name"], 40], [3, ["_lpos"], 40, [1, 0, 0, 0]], [3, ["_lrot"], 40, [3, 0, 0, 0, 1]], [3, ["_euler"], 40, [1, 0, 0, 0]], [1, true, ["_enableOutline"], 10], [6, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 266.0390625, 48.1]], [1, "Settings", ["_string"], 10], [1, 34, ["_fontSize"], 10], [1, 34, ["_actualFontSize"], 10], [3, ["_outlineColor"], 10, [4, 4285882397]], [1, 35, ["_lineHeight"], 10], [11, "PlayerInformation", ["_dataID"], [2, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]]]], 23]], [7, 0, {}, 12, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -125, [10, "d4Z9pMomhEcaGWdCOeqQ1c", 1, [[1, "uuid", ["_name"], 41], [3, ["_lpos"], 41, [1, 0, -381.57, 0]], [3, ["_lrot"], 41, [3, 0, 0, 0, 1]], [3, ["_euler"], 41, [1, 0, 0, 0]], [11, "uid:00000", ["_string"], [2, ["4a5atXBglJxJGAlAL90RE0"]]], [6, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 63.546875, 37.8]], [1, true, ["templateMode"], 42], [1, 1, ["watchPathArr", "length"], 42], [1, "role.guuid", ["watchPathArr", "0"], 42], [1, "UUID", ["_dataID"], 42]]], 27]], [2, ["5a8880C91AI6EEZ9EsW7dy"]], [2, ["4dgANpwkxN8pTx4nabOESy"]], [2, ["48BGi+JnJOKpaEdqvfVVS1"]]], 0, [0, -1, 54, 0, -2, 53, 0, -3, 52, 0, -4, 51, 0, -5, 50, 0, -6, 48, 0, -7, 46, 0, -8, 30, 0, -9, 45, 0, -10, 44, 0, -11, 22, 0, -12, 19, 0, -13, 43, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 43, 0, -2, 12, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 18, 0, -2, 13, 0, -3, 4, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 44, 0, -2, 45, 0, 0, 4, 0, 0, 4, 0, -1, 8, 0, -2, 34, 0, -3, 35, 0, -4, 52, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 5, 0, 0, 5, 0, -1, 50, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 6, 0, 0, 6, 0, -1, 51, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 47, 0, -2, 48, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, -1, 53, 0, -2, 11, 0, 0, 11, 0, 0, 11, 0, 9, 11, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, -3, 54, 0, 0, 13, 0, 0, 13, 0, -1, 14, 0, -2, 46, 0, 0, 14, 0, 0, 14, 0, 0, 14, 0, -1, 30, 0, 0, 18, 0, -1, 19, 0, -2, 21, 0, 2, 20, 0, 2, 20, 0, 2, 20, 0, 2, 20, 0, 2, 55, 0, 2, 55, 0, 0, 19, 0, 1, 19, 0, 0, 21, 0, -1, 22, 0, 2, 23, 0, 2, 23, 0, 2, 23, 0, 2, 23, 0, 2, 24, 0, 2, 24, 0, 2, 24, 0, 2, 24, 0, 2, 56, 0, 2, 56, 0, 0, 22, 0, 1, 22, 0, 2, 31, 0, 2, 31, 0, 2, 31, 0, 2, 31, 0, 2, 57, 0, 2, 57, 0, 0, 30, 0, 1, 30, 0, 0, 34, 0, 0, 34, 0, -1, 49, 0, 0, 35, 0, 1, 43, 0, 1, 44, 0, 1, 45, 0, 1, 46, 0, 0, 47, 0, 0, 47, 0, 1, 48, 0, 0, 49, 0, 0, 49, 0, 1, 50, 0, 1, 51, 0, 1, 52, 0, 1, 53, 0, 1, 54, 0, 5, 1, 2, 7, 12, 3, 7, 21, 5, 7, 35, 6, 7, 35, 9, 7, 12, 125], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 4, 4, 4, 4, 3, 4, 3, 4, 10, 3, 3, 4, 3, 3, 3, 4, 3, 4, 3, 4, 3, 3, 4, 3, 11, 3, 4], [5, 6, 7, 1, 3, 8, 1, 9, 10, 11, 4, 12, 2, 13, 14, 15, 16, 17, 2, 18, 3, 4, 19, 2, 20, 21, 22, 1]], [[[9, "mask"], [15, "mask", 33554432, [[4, -2, [0, "77N2cid5pKDpXplRH/AWEU"], [5, 1080, 1920]], [19, 45, 2, 2, -3, [0, "63zNQq8NlBQ5QWzOJ4Kgjs"]], [36, 1, 0, -4, [0, "08QaVy/ehM0oBtHFRmM2vy"], [4, 1677721600], 0]], [5, "a0daVw8DRLi6ToMaTA0VS2", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 4], [0], [3], [23]], [[[9, "vmButton"], [14, "vmButton", 33554432, [-7], [[4, -3, [0, "b2FpfgEc1Py4FLQrPbDjfF"], [5, 314, 133]], [37, 1, -4, [0, "daO3pUYmlKVIgsdc588k+9"]], [43, 3, -6, [0, "43UvszQV9LRKWvhBXHpprD"], [4, 4292269782], -5]], [29, "30Ahna/hFADJL5JEyiP8Kc", null, null, -2, 0, [-1]]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [7, 0, {}, 1, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -8, [10, "8eWUAc4O9I9LUoIPJa7aJ0", 1, [[1, "VMLabelLanguage", ["_name"], 2], [3, ["_lpos"], 2, [1, 0, 0, 0]], [3, ["_lrot"], 2, [3, 0, 0, 0, 1]], [3, ["_euler"], 2, [1, 0, 0, 0]]]], 0]]], 0, [0, -1, 3, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 9, 1, 0, 0, 1, 0, -1, 3, 0, 1, 3, 0, 5, 1, 8], [0], [4], [24]], [[[46, "fzyc_4082", "fzyc_4082.ttf"], -1], 0, 0, [], [], []], [[{"name": "default_sprite_splash", "rect": {"x": 0, "y": 0, "width": 2, "height": 2}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 2, "height": 2}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-1, -1, 0, 1, -1, 0, -1, 1, 0, 1, 1, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 2, 2, 2, 0, 0, 2, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -1, "y": -1, "z": 0}, "maxPos": {"x": 1, "y": 1, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [8], [25]], [[[9, "avatar"], [14, "avatar", 33554432, [-4], [[4, -2, [0, "fagAZlPmdJDYrk089Nl/sl"], [5, 138, 151]], [23, 1, -3, [0, "aaomhQzE9C7YadIzCrbfzf"], 1]], [5, "b2LyNAFEdCsIkChnpFdNTi", null, null, null, -1, 0]], [18, "moyu", 33554432, 1, [[25, -5, [0, "abuOsxNH1HCZbbiHPsSO8b"]], [23, 1, -6, [0, "9coK2X/ZdO4rW4FPDd5YL9"], 0]], [5, "5a8880C91AI6EEZ9EsW7dy", null, null, null, 1, 0], [1, 0, 7.105000000000018, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 5, 1, 6], [0, 0], [3, 3], [26, 27]], [[[9, "VMLabelLanguagel_Gray"], [15, "VMLabelLanguagel_Gray", 33554432, [[4, -2, [0, "6cPaWujd9BCJYdUAjIP0JG"], [5, 58.40625, 37.8]], [22, "<PERSON>", 28, 28, 30, false, false, -3, [0, "4a5atXBglJxJGAlAL90RE0"], [4, 4281033624], 0], [17, "<PERSON>", -4, [0, "48BGi+JnJOKpaEdqvfVVS1"]]], [5, "f05XX5jrpEOYwv6lCoUIav", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 4], [0], [6], [0]], [[[9, "VMLabelLanguage_White"], [32, "VMLabelLanguage_White", 33554432, [[4, -2, [0, "6cPaWujd9BCJYdUAjIP0JG"], [5, 248.4375, 50.4]], [47, "HallRankLabel", 40, false, false, true, -3, [0, "4a5atXBglJxJGAlAL90RE0"], 0], [17, "HallRankLabel", -4, [0, "09i06HqvBP2ZaQ4Pk+p3Mt"]]], [5, "f05XX5jrpEOYwv6lCoUIav", null, null, null, -1, 0], [1, 0, -76, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 4], [0], [6], [0]], [[{"name": "头像", "rect": {"x": 0, "y": 0, "width": 100, "height": 100}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 100, "height": 100}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-50, -50, 0, 50, -50, 0, -50, 50, 0, 50, 50, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 100, 100, 100, 0, 0, 100, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -50, "y": -50, "z": 0}, "maxPos": {"x": 50, "y": 50, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [8], [28]], [[{"name": "头像框", "rect": {"x": 0, "y": 0, "width": 138, "height": 151}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 138, "height": 151}, "rotated": false, "capInsets": [0, 0, 0, 0], "vertices": {"rawPosition": [-69, -75.5, 0, 69, -75.5, 0, -69, 75.5, 0, 69, 75.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 151, 138, 151, 0, 0, 138, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": {"x": -69, "y": -75.5, "z": 0}, "maxPos": {"x": 69, "y": 75.5, "z": 0}}, "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [3], 0, [0], [8], [29]], [[[9, "VMLabelLanguage"], [15, "VMLabelLanguage", 33554432, [[4, -2, [0, "6cPaWujd9BCJYdUAjIP0JG"], [5, 160, 50.4]], [48, "系统字体", 40, false, false, true, -3, [0, "4a5atXBglJxJGAlAL90RE0"], [4, 4294376188], 0]], [5, "f05XX5jrpEOYwv6lCoUIav", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 3], [0], [6], [0]], [[[9, "VMLabelLanguage_Orange"], [15, "VMLabelLanguage_Orange", 33554432, [[4, -2, [0, "6cPaWujd9BCJYdUAjIP0JG"], [5, 78.75, 32.76]], [22, "Orange", 24, 24, 26, false, false, -3, [0, "4a5atXBglJxJGAlAL90RE0"], [4, 4278216447], 0], [17, "Orange", -4, [0, "47zQiH5ZpHGZOMEAJOGjcx"]]], [5, "f05XX5jrpEOYwv6lCoUIav", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 4], [0], [6], [0]], [[[9, "VMLabelLanguage_EarthyBrown"], [15, "VMLabelLanguage_EarthyBrown", 33554432, [[4, -2, [0, "6cPaWujd9BCJYdUAjIP0JG"], [5, 153.453125, 37.8]], [22, "EarthyBrown", 28, 28, 30, false, false, -3, [0, "4a5atXBglJxJGAlAL90RE0"], [4, 4282481837], 0], [17, "EarthyBrown", -4, [0, "4dgANpwkxN8pTx4nabOESy"]]], [5, "f05XX5jrpEOYwv6lCoUIav", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 4], [0], [6], [0]], [[[9, "VMLabelLanguage_Yellow"], [15, "VMLabelLanguage_Yellow", 33554432, [[4, -2, [0, "6cPaWujd9BCJYdUAjIP0JG"], [5, 252.3125, 30.240000000000002]], [22, "VMLabelLanguage_Yellow", 22, 22, 24, false, false, -3, [0, "4a5atXBglJxJGAlAL90RE0"], [4, 4283760625], 0], [17, "VMLabelLanguage_Yellow", -4, [0, "36m7MuCL5KzbRIiB/pDpxj"]]], [5, "f05XX5jrpEOYwv6lCoUIav", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 5, 1, 4], [0], [6], [0]]]]