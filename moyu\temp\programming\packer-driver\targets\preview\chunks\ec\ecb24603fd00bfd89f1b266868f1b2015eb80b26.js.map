{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/common/CommonNet.ts"], "names": ["CommonNet", "WECHAT", "HttpClient_Browser", "HttpClient", "Ws<PERSON><PERSON>_<PERSON>rowser", "WsClient", "HttpClient_Miniapp", "WsClient_Miniapp", "oops", "Security", "ShareConfig", "DataUpdateType", "ServiceProtoGame", "serviceProto", "ServiceProtoGate", "ClientConfig", "GameServerConfig", "LocalConfig", "DataManager", "smc", "constructor", "hcGate", "hcGame", "wcGame", "wcGate", "createHcGate", "createHcGame", "serverUrl", "gateUrl", "console", "log", "server", "json", "logger", "timeout", "flowClientApi", "flowAuth", "createWcGate", "isConnected", "disconnect", "wsc", "heartbeat", "interval", "heartbeat_interval", "heartbeat_timeout", "net", "flowClientMsg", "flowUserGameData", "gameUrl", "httpUrl", "createWscGame", "logNet", "client", "flows", "postApiReturnFlow", "push", "v", "return", "isSucc", "res", "updateType", "Object", "values", "includes", "dataManager", "getInstance", "processOptimizedResponse", "userGameData", "warn", "hc", "security", "preSendDataFlow", "data", "Uint8Array", "encrypt", "preRecvDataFlow", "decrypt", "preSendMsgFlow", "preRecvMsgFlow", "preCallApiFlow", "ssoToken", "storage", "get", "req", "__ssoToken", "undefined", "tokenData", "token", "expiredTime", "Date", "now", "createdTime", "set", "JSON", "stringify", "err", "code", "remove"], "mappings": ";;;kQA8BaA,S;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAvBJC,MAAAA,M,UAAAA,M;;AACcC,MAAAA,kB,iBAAdC,U;AAA8CC,MAAAA,gB,iBAAZC,Q;;AACpBC,MAAAA,kB,iBAAdH,U;AAA8CI,MAAAA,gB,iBAAZF,Q;;AAClCG,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;AACcC,MAAAA,c,iBAAAA,c;;AAEHC,MAAAA,gB,iBAAhBC,Y;;AAIgBC,MAAAA,gB,iBAAhBD,Y;;AAGKE,MAAAA,Y,iBAAAA,Y;;AAEAC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,W,kBAAAA,W;;AACAC,MAAAA,W,kBAAAA,W;;AACAC,MAAAA,G,kBAAAA,G;;;;;;AA3BT;AACA;AACA;AACA;AACA;AACA;;;AAwBA;2BACanB,S,GAAN,MAAMA,SAAN,CAAgB;AAanBoB,QAAAA,WAAW,GAAG;AAZd;AAYc,eAXdC,MAWc,GAXsE,IAWtE;;AATd;AASc,eARdC,MAQc,GARsE,IAQtE;;AANd;AAMc,eALdC,MAKc,GALkE,IAKlE;;AAHd;AAGc,eAFdC,MAEc,GAFkE,IAElE;;AACV;AACA,eAAKC,YAAL;AAEA;;AACA,eAAKC,YAAL;AACH;;AAEDD,QAAAA,YAAY,GAAG;AACX;AACA,cAAME,SAAS,GAAG;AAAA;AAAA,4CAAaC,OAA/B;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCH,SAAhC;AAEA,eAAKN,MAAL,GAAc,KAAKpB,MAAM;AAAA;AAAA;AAAA;AAAA,sDAAX;AAAA;AAAA,oDAAyE;AACnF8B,YAAAA,MAAM,EAAEJ,SAD2E;AAEnFK,YAAAA,IAAI,EAAE;AAAA;AAAA,4CAAYA,IAFiE;AAGnFC,YAAAA,MAAM,EAAEJ,OAH2E;AAInF;AACAK,YAAAA,OAAO,EAAE,IAL0E,CAKpE;;AALoE,WAAzE,CAAd;AAOA,eAAKC,aAAL,CAAmB,KAAKd,MAAxB;AACA,eAAKe,QAAL,CAAc,KAAKf,MAAnB;AACH;;AACDgB,QAAAA,YAAY,CAACV,SAAD,EAAoB;AAC5B,cAAI,KAAKH,MAAL,IAAe,KAAKA,MAAL,CAAYc,WAA/B,EAA4C;AACxC,iBAAKd,MAAL,CAAYe,UAAZ;AACA,mBAAO,KAAKf,MAAZ;AACH,WAJ2B,CAK5B;;;AACA,cAAIgB,GAAG,GAAG,KAAKvC,MAAM;AAAA;AAAA;AAAA;AAAA,kDAAX;AAAA;AAAA,oDAAqE;AAC3E8B,YAAAA,MAAM,EAAEJ,SADmE;AAE3Ec,YAAAA,SAAS,EAAE;AACPC,cAAAA,QAAQ,EAAE;AAAA;AAAA,8CAAYC,kBADf;AAEPT,cAAAA,OAAO,EAAE;AAAA;AAAA,8CAAYU;AAFd,aAFgE;AAM3EZ,YAAAA,IAAI,EAAE;AAAA;AAAA,4CAAYA,IANyD,CAO3E;AACA;;AAR2E,WAArE,CAAV;AAUA;AAAA;AAAA,0BAAIa,GAAJ,CAAQrB,MAAR,GAAiBgB,GAAjB;AACA,eAAKM,aAAL,CAAmBN,GAAnB;AACA,eAAKJ,QAAL,CAAcI,GAAd;AACA,eAAKO,gBAAL,CAAsBP,GAAtB;AACA,iBAAOA,GAAP;AACH;AAED;;;AACAd,QAAAA,YAAY,GAAG;AACX;AACA,cAAMC,SAAS,GAAG;AAAA;AAAA,4CAAaqB,OAA/B;AAEAnB,UAAAA,OAAO,CAACC,GAAR,CAAY,kBAAZ,EAAgCH,SAAhC;AAEA,eAAKL,MAAL,GAAc,KAAKrB,MAAM;AAAA;AAAA;AAAA;AAAA,sDAAX;AAAA;AAAA,oDAAyE;AACnF8B,YAAAA,MAAM,EAAEJ,SAD2E;AAEnFK,YAAAA,IAAI,EAAE;AAAA;AAAA,4CAAYA,IAFiE;AAGnFC,YAAAA,MAAM,EAAEJ,OAH2E;AAInF;AACAK,YAAAA,OAAO,EAAE,IAL0E,CAKpE;;AALoE,WAAzE,CAAd,CANW,CAcX;;AACA;AAAA;AAAA,oDAAiBe,OAAjB,GAA2BtB,SAA3B;AAEA,eAAKQ,aAAL,CAAmB,KAAKb,MAAxB;AACA,eAAKc,QAAL,CAAc,KAAKd,MAAnB;AACA,eAAKyB,gBAAL,CAAsB,KAAKzB,MAA3B;AACH;AACD;AACJ;AACA;AACA;;;AACI4B,QAAAA,aAAa,GAAG;AACZ;AACA;AAAA;AAAA,4BAAKpB,GAAL,CAASqB,MAAT,CAAgB,+BAAhB,EAFY,CAIZ;AACA;;AAEA;AAAA;AAAA,4BAAKrB,GAAL,CAASqB,MAAT,CAAgB,6BAAhB;AACH;;AAEOJ,QAAAA,gBAAgB,CAACK,MAAD,EAAc;AAClC;AACAA,UAAAA,MAAM,CAACC,KAAP,CAAaC,iBAAb,CAA+BC,IAA/B,CAAoCC,CAAC,IAAI;AACrC,gBAAIA,CAAC,CAACC,MAAF,CAASC,MAAT,IAAmBF,CAAC,CAACC,MAAF,CAASE,GAAhC,EAAqC;AACjC,kBAAMA,GAAG,GAAGH,CAAC,CAACC,MAAF,CAASE,GAArB,CADiC,CAGjC;;AACA,kBAAIA,GAAG,CAACC,UAAJ,IAAkBC,MAAM,CAACC,MAAP;AAAA;AAAA,oDAA8BC,QAA9B,CAAuCJ,GAAG,CAACC,UAA3C,CAAtB,EAA8E;AAC1E,oBAAMI,WAAW,GAAG;AAAA;AAAA,gDAAYC,WAAZ,EAApB;AACAD,gBAAAA,WAAW,CAACE,wBAAZ,CAAqCP,GAArC;AACH,eAHD,CAIA;AACA;AALA,mBAMK,IAAIA,GAAG,CAACQ,YAAR,EAAsB;AACvBtC,gBAAAA,OAAO,CAACuC,IAAR,CAAa,qCAAb,EAAoDT,GAApD;AACH;AACJ;;AACD,mBAAOH,CAAP;AACH,WAhBD;AAiBH;AAED;;;AACQrB,QAAAA,aAAa,CAACkC,EAAD,EAAU;AAC3B,cAAI,CAAC;AAAA;AAAA,0CAAYC,QAAjB,EAA2B;AAE3BD,UAAAA,EAAE,CAAChB,KAAH,CAASkB,eAAT,CAAyBhB,IAAzB,CAA8BC,CAAC,IAAI;AAC/B,gBAAIA,CAAC,CAACgB,IAAF,YAAkBC,UAAtB,EAAkC;AAC9BjB,cAAAA,CAAC,CAACgB,IAAF,GAAS;AAAA;AAAA,wCAASE,OAAT,CAAiBlB,CAAC,CAACgB,IAAnB,CAAT;AACH;;AACD,mBAAOhB,CAAP;AACH,WALD,EAH2B,CAU3B;;AACAa,UAAAA,EAAE,CAAChB,KAAH,CAASsB,eAAT,CAAyBpB,IAAzB,CAA8BC,CAAC,IAAI;AAC/B,gBAAIA,CAAC,CAACgB,IAAF,YAAkBC,UAAtB,EAAkC;AAC9BjB,cAAAA,CAAC,CAACgB,IAAF,GAAS;AAAA;AAAA,wCAASI,OAAT,CAAiBpB,CAAC,CAACgB,IAAnB,CAAT;AACH;;AACD,mBAAOhB,CAAP;AACH,WALD;AAMH;AAED;;;AACQV,QAAAA,aAAa,CAACN,GAAD,EAAW;AAC5B,cAAI,CAAC;AAAA;AAAA,0CAAY8B,QAAjB,EAA2B,OADC,CAG5B;;AACA9B,UAAAA,GAAG,CAACa,KAAJ,CAAUwB,cAAV,CAAyBtB,IAAzB,CAA8BC,CAAC,IAAI;AAC/B,gBAAIA,CAAC,CAACgB,IAAF,YAAkBC,UAAtB,EAAkC;AAC9BjB,cAAAA,CAAC,CAACgB,IAAF,GAAS;AAAA;AAAA,wCAASE,OAAT,CAAiBlB,CAAC,CAACgB,IAAnB,CAAT;AACH;;AACD,mBAAOhB,CAAP;AACH,WALD,EAJ4B,CAW5B;;AACAhB,UAAAA,GAAG,CAACa,KAAJ,CAAUyB,cAAV,CAAyBvB,IAAzB,CAA8BC,CAAC,IAAI;AAC/B,gBAAIA,CAAC,CAACgB,IAAF,YAAkBC,UAAtB,EAAkC;AAC9BjB,cAAAA,CAAC,CAACgB,IAAF,GAAS;AAAA;AAAA,wCAASI,OAAT,CAAiBpB,CAAC,CAACgB,IAAnB,CAAT;AACH;;AACD,mBAAOhB,CAAP;AACH,WALD;AAMH;AAED;;;AACQpB,QAAAA,QAAQ,CAACgB,MAAD,EAAc;AAC1B;AACA;AACAA,UAAAA,MAAM,CAACC,KAAP,CAAa0B,cAAb,CAA4BxB,IAA5B,CAAiCC,CAAC,IAAI;AAClC;AACA,gBAAMwB,QAAQ,GAAG;AAAA;AAAA,8BAAKC,OAAL,CAAaC,GAAb,CAAiB,WAAjB,CAAjB;;AACA,gBAAIF,QAAJ,EAAc;AACVxB,cAAAA,CAAC,CAAC2B,GAAF,CAAMC,UAAN,GAAmBJ,QAAnB;AACH;;AACD,mBAAOxB,CAAP;AACH,WAPD,EAH0B,CAY1B;;AACAJ,UAAAA,MAAM,CAACC,KAAP,CAAaC,iBAAb,CAA+BC,IAA/B,CAAoCC,CAAC,IAAI;AACrC,gBAAIA,CAAC,CAACC,MAAF,CAASC,MAAb,EAAqB;AACjB,kBAAMC,GAAG,GAAGH,CAAC,CAACC,MAAF,CAASE,GAArB,CADiB,CAGjB;;AACA,kBAAIA,GAAG,CAACyB,UAAJ,KAAmBC,SAAvB,EAAkC;AAC9B;AACA,oBAAMC,SAAS,GAAG;AACdC,kBAAAA,KAAK,EAAE5B,GAAG,CAACyB,UADG;AAEdI,kBAAAA,WAAW,EAAEC,IAAI,CAACC,GAAL,KAAa,WAAW,CAAxB,GAA4B,KAAK,EAAL,GAAU,IAFrC;AAE2C;AACzDC,kBAAAA,WAAW,EAAEF,IAAI,CAACC,GAAL;AAHC,iBAAlB;AAKA;AAAA;AAAA,kCAAKT,OAAL,CAAaW,GAAb,CAAiB,WAAjB,EAA8BjC,GAAG,CAACyB,UAAlC;AACA;AAAA;AAAA,kCAAKH,OAAL,CAAaW,GAAb,CAAiB,gBAAjB,EAAmCC,IAAI,CAACC,SAAL,CAAeR,SAAf,CAAnC;AACH;AACJ,aAdD,CAeA;AAfA,iBAgBK,IAAI9B,CAAC,CAACC,MAAF,CAASsC,GAAT,CAAaC,IAAb,KAAsB,YAA1B,EAAwC;AACzC;AAAA;AAAA,gCAAKf,OAAL,CAAagB,MAAb,CAAoB,WAApB;AACA;AAAA;AAAA,gCAAKhB,OAAL,CAAagB,MAAb,CAAoB,gBAApB;AACH;;AACD,mBAAOzC,CAAP;AACH,WAtBD;AAuBH;;AAlMkB,O", "sourcesContent": ["/*\n * @Author: dgflash\n * @Date: 2022-06-28 19:10:14\n * @LastEditors: dgflash\n * @LastEditTime: 2022-09-20 10:38:39\n */\n\nimport { WECHAT } from 'cc/env';\nimport { HttpClient as HttpClient_Browser, WsClient as WsClient_Browser } from 'tsrpc-browser';\nimport { HttpClient as HttpClient_Miniapp, WsClient as WsClient_Miniapp } from 'tsrpc-miniapp';\nimport { oops } from '../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { Security } from '../../tsrpc/models/Security';\nimport { ShareConfig } from '../../tsrpc/models/ShareConfig';\nimport { BaseResponse, DataUpdateType, OptimizedDataResponse } from '../../tsrpc/protocols/base';\nimport {\n    serviceProto as ServiceProtoGame,\n    ServiceType as ServiceTypeGame,\n} from '../../tsrpc/protocols/ServiceProtoGame';\nimport {\n    serviceProto as ServiceProtoGate,\n    ServiceType as ServiceTypeGate,\n} from '../../tsrpc/protocols/ServiceProtoGate';\nimport { ClientConfig } from './ClientConfig';\n\nimport { GameServerConfig } from './config/GameServerConfig';\nimport { LocalConfig } from './config/LocalConfig';\nimport { DataManager } from './DataManager';\nimport { smc } from './SingletonModuleComp';\n\n/** TSRPC网络模块 */\nexport class CommonNet {\n    /** 连接网关服务器 Http 客户端 */\n    hcGate: HttpClient_Miniapp<ServiceTypeGate> | HttpClient_Browser<ServiceTypeGate> = null!;\n\n    /** 连接数据服务器 http 客户端 ，不用了*/\n    hcGame: HttpClient_Miniapp<ServiceTypeGame> | HttpClient_Browser<ServiceTypeGame> = null!;\n\n    /** 连接数据服务器 WebSocket 客户端 */\n    wcGame: WsClient_Miniapp<ServiceTypeGame> | WsClient_Browser<ServiceTypeGame> = null!;\n\n    /** 连接数据服务器 WebSocket 客户端 */\n    wcGate: WsClient_Miniapp<ServiceTypeGate> | WsClient_Browser<ServiceTypeGate> = null!;\n\n    constructor() {\n        /** 创建连接网关服务器 Http 客户端 */\n        this.createHcGate();\n\n        /** 🎯 纯HTTP架构：同时初始化游戏HTTP客户端 */\n        this.createHcGame();\n    }\n\n    createHcGate() {\n        // 🚪 网关客户端：连接端口5000，处理登录注册\n        const serverUrl = ClientConfig.gateUrl;\n        console.log('🚪 网关HTTP客户端初始化:', serverUrl);\n\n        this.hcGate = new (WECHAT ? HttpClient_Miniapp : HttpClient_Browser)(ServiceProtoGate, {\n            server: serverUrl,\n            json: ShareConfig.json,\n            logger: console,\n            // 🚀 优化：减少超时时间，加快失败响应\n            timeout: 5000, // 从默认10秒减少到5秒\n        });\n        this.flowClientApi(this.hcGate);\n        this.flowAuth(this.hcGate);\n    }\n    createWcGate(serverUrl: string) {\n        if (this.wcGate && this.wcGate.isConnected) {\n            this.wcGate.disconnect();\n            return this.wcGate;\n        }\n        // 创建客户端与游戏服务器的 WebSocket 连接\n        let wsc = new (WECHAT ? WsClient_Miniapp : WsClient_Browser)(ServiceProtoGate, {\n            server: serverUrl,\n            heartbeat: {\n                interval: LocalConfig.heartbeat_interval,\n                timeout: LocalConfig.heartbeat_timeout,\n            },\n            json: ShareConfig.json,\n            // logger: console,\n            // logMsg: true,\n        });\n        smc.net.wcGate = wsc;\n        this.flowClientMsg(wsc);\n        this.flowAuth(wsc);\n        this.flowUserGameData(wsc);\n        return wsc;\n    }\n\n    /** 创建连游戏服务器 Http 客户端 */\n    createHcGame() {\n        // 🎮 游戏客户端：连接端口5001，处理游戏逻辑\n        const serverUrl = ClientConfig.gameUrl;\n\n        console.log('🎮 游戏HTTP客户端初始化:', serverUrl);\n\n        this.hcGame = new (WECHAT ? HttpClient_Miniapp : HttpClient_Browser)(ServiceProtoGame, {\n            server: serverUrl,\n            json: ShareConfig.json,\n            logger: console,\n            // 🚀 优化：减少超时时间，加快失败响应\n            timeout: 5000, // 从默认10秒减少到5秒\n        });\n\n        // 🔧 同步更新GameServerConfig，保持一致性\n        GameServerConfig.httpUrl = serverUrl;\n\n        this.flowClientApi(this.hcGame);\n        this.flowAuth(this.hcGame);\n        this.flowUserGameData(this.hcGame);\n    }\n    /**\n     *  创建连接游戏服务器 Websocket 客户端\n     *  🎯 纯HTTP架构：此方法已弃用，保留用于向后兼容\n     */\n    createWscGame() {\n        // 🎯 纯HTTP架构：跳过WebSocket客户端创建\n        oops.log.logNet('🎯 纯HTTP架构：跳过WebSocket游戏客户端创建');\n\n        // 不设置wcGame，避免混淆\n        // smc.net.wcGame = null;\n\n        oops.log.logNet('✅ 纯HTTP架构：WebSocket游戏客户端已跳过');\n    }\n\n    private flowUserGameData(client: any) {\n        // 将 callApi 的结果返回给调用方之后，如果有携带用户数据，直接覆盖\n        client.flows.postApiReturnFlow.push(v => {\n            if (v.return.isSucc && v.return.res) {\n                const res = v.return.res;\n\n                // 使用新的数据管理器处理优化响应\n                if (res.updateType && Object.values(DataUpdateType).includes(res.updateType)) {\n                    const dataManager = DataManager.getInstance();\n                    dataManager.processOptimizedResponse(res as OptimizedDataResponse);\n                }\n                // 🗑️ 旧的全量数据响应已弃用，所有API已迁移到新格式\n                // 如果遇到没有updateType的响应，记录警告\n                else if (res.userGameData) {\n                    console.warn('[CommonNet] 检测到旧格式API响应，请升级API到新格式:', res);\n                }\n            }\n            return v;\n        });\n    }\n\n    /** HTTP 客户端协议数据加密、解密 */\n    private flowClientApi(hc: any) {\n        if (!ShareConfig.security) return;\n\n        hc.flows.preSendDataFlow.push(v => {\n            if (v.data instanceof Uint8Array) {\n                v.data = Security.encrypt(v.data);\n            }\n            return v;\n        });\n\n        // 在处理接收到的数据之前，通常要进行加密/解密\n        hc.flows.preRecvDataFlow.push(v => {\n            if (v.data instanceof Uint8Array) {\n                v.data = Security.decrypt(v.data);\n            }\n            return v;\n        });\n    }\n\n    /** WebSocket 客户端协议数据加密、解密 */\n    private flowClientMsg(wsc: any) {\n        if (!ShareConfig.security) return;\n\n        // 发送 Message 之前\n        wsc.flows.preSendMsgFlow.push(v => {\n            if (v.data instanceof Uint8Array) {\n                v.data = Security.encrypt(v.data);\n            }\n            return v;\n        });\n\n        // 触发 Message 监听事件之前\n        wsc.flows.preRecvMsgFlow.push(v => {\n            if (v.data instanceof Uint8Array) {\n                v.data = Security.decrypt(v.data);\n            }\n            return v;\n        });\n    }\n\n    /** 帐号登录令牌验证是否逻辑（帐号中加入登录令牌，服务器通过令牌解析玩家数据，如果存在就是已登录） */\n    private flowAuth(client: any) {\n        // HttpClient WsClient\n        // 执行 callApi 之前协议中插入登录令牌\n        client.flows.preCallApiFlow.push(v => {\n            // 请求前插入登录令牌\n            const ssoToken = oops.storage.get('SSO_TOKEN');\n            if (ssoToken) {\n                v.req.__ssoToken = ssoToken;\n            }\n            return v;\n        });\n\n        // 将 callApi 的结果返回给调用方之后将登录令牌存到本地（收到协议时将登录令牌存到本地）\n        client.flows.postApiReturnFlow.push(v => {\n            if (v.return.isSucc) {\n                const res = v.return.res as BaseResponse;\n\n                // 请求成功后刷新登录令牌\n                if (res.__ssoToken !== undefined) {\n                    // 存储token和预估过期时间（7天，但提前1小时过期以确保安全）\n                    const tokenData = {\n                        token: res.__ssoToken,\n                        expiredTime: Date.now() + 86400000 * 7 - 60 * 60 * 1000, // 7天 - 1小时\n                        createdTime: Date.now(),\n                    };\n                    oops.storage.set('SSO_TOKEN', res.__ssoToken);\n                    oops.storage.set('SSO_TOKEN_INFO', JSON.stringify(tokenData));\n                }\n            }\n            // 登录令牌过期时删除客户端登录令牌（可跳转到登录界面）\n            else if (v.return.err.code === 'NEED_LOGIN') {\n                oops.storage.remove('SSO_TOKEN');\n                oops.storage.remove('SSO_TOKEN_INFO');\n            }\n            return v;\n        });\n    }\n}\n"]}