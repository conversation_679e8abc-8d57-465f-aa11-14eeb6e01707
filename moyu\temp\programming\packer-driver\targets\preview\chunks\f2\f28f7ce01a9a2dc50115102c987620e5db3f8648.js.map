{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/role/Role.ts"], "names": ["oops", "ecs", "LanguageData", "VM", "GameConst", "Platform", "ShareConfig", "PropType", "RecordType", "SceneItemType", "GameStorageConfig", "GameEvent", "smc", "RoleModelComp", "Role", "register", "Entity", "RoleModel", "pendingPropUpdates", "init", "add", "destroy", "removeFromViewModel", "remove", "quickInitialize", "isNewPlayer", "log", "logBusiness", "initializeNewPlayerData", "loadDataInBackground", "loadData", "error", "log<PERSON>arn", "validateNetworkConnection", "response", "net", "hcGame", "callApi", "isSucc", "handleAPIError", "err", "logError", "updateUserData", "newData", "isFirstInitialization", "hasUserData", "ensureDataIntegrity", "userGameData", "mergeUserData", "updateViewModel", "message", "dispatchEvent", "UserDataInitialized", "getPropData", "propType", "createDefaultPropData", "propData", "propUseData", "defaultData", "getPropsDataByType", "tryCostProp", "args", "showToast", "failureMessageKey", "gui", "toast", "absAmount", "Math", "abs", "amount", "canUse", "canUseProp", "getLangByID", "updatePropData", "updateProp", "reason", "userData", "isTemporaryData", "lastUpdateTime", "Date", "UseProp", "queuePropUpdateForSync", "batchUpdateProps", "updates", "update", "success", "getGameProgress", "index", "getPassIndex", "getNextLevelIndex", "currentProgress", "progress", "nextLevel", "Max", "updateGameProgress", "newIndex", "isGM", "targetIndex", "isGm", "GamePass", "oldIndex", "hasLoginRecord", "storage", "get", "SSOToken", "userDumpKey", "get<PERSON>son", "UserDumpKey", "String", "completeNewPlayerGuide", "once", "syncNewPlayerStatusToServer", "BasicInfoUpdate", "push", "syncPendingPropUpdates", "length", "currentTime", "basicUserData", "key", "guuid", "googleUuid", "facebookId", "userName", "nick<PERSON><PERSON>", "sex", "createtime", "openid", "platform", "platformType", "avatar", "avatarId", "countryCode", "passTimes", "currCountryPassTimes", "lastChangeCountryTime", "selfCountryRank", "createDefaultProps", "recordData", "isGuest", "lastStep", "newPlayerProps", "propTypes", "PropsMoveOut", "PropsTips", "PropsReShuffle", "PropsDayLeftCount", "PropsRevive", "PropsExp", "PropsCoin", "for<PERSON>ach", "getNewPlayerDefaultAmount", "propCount", "Object", "keys", "setTimeout", "forceCompleteUserDataLoad", "tempUserData", "performCompleteLogin", "LoginViewComp", "FACEBOOK", "loginSuccess", "doFacebookLogin", "login<PERSON>iewComp", "loginGuestButton", "getRecordData", "recordType", "dateString", "targetDate", "toDateString", "getLevelChallengeCount", "levelId", "Level", "levelDetails", "level<PERSON><PERSON>", "attempts", "getCurrentLevelChallengeCount", "currentLevelId", "getUserGameData", "Error", "data", "target", "source", "sourceValue", "targetValue", "Array", "isArray", "defaultAmount", "propId", "desc", "getTime", "lastResetTime", "newPlayerDefaultProps", "moveOut", "tips", "reShuffle", "dayFreeLimts", "revive", "viewModelData", "userId", "level", "apiName", "errorMessage", "code", "toString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,E,iBAAAA,E;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;;AAEfC,MAAAA,Q,iBAAAA,Q;AACAC,MAAAA,U,iBAAAA,U;AAEAC,MAAAA,a,iBAAAA,a;;AAGKC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,G,kBAAAA,G;;AACAC,MAAAA,a,kBAAAA,a;;;;;;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;sBAEaC,I,WADZ;AAAA;AAAA,sBAAIC,QAAJ,CAAa,MAAb,C,gBAAD,MACaD,IADb,SAC0B;AAAA;AAAA,sBAAIE,MAD9B,CACqC;AAAA;AAAA;AAAA,eACjCC,SADiC;AAGjC;AAHiC,eAIzBC,kBAJyB,GAQ5B,EAR4B;AAAA;;AAUjC;AAEUC,QAAAA,IAAI,GAAG;AACb,eAAKC,GAAL;AAAA;AAAA;AACH;;AAEDC,QAAAA,OAAO,GAAS;AACZ,eAAKC,mBAAL;AACA,eAAKC,MAAL;AAAA;AAAA;AACH,SAnBgC,CAqBjC;;AAEA;AACJ;AACA;;;AACUC,QAAAA,eAAe,GAAkB;AAAA;;AAAA;AACnC,gBAAI;AACA,kBAAI,KAAI,CAACC,WAAL,EAAJ,EAAwB;AACpB;AAAA;AAAA,kCAAKC,GAAL,CAASC,WAAT,CAAqB,eAArB;AACA,sBAAM,KAAI,CAACC,uBAAL,EAAN,CAFoB,CAIpB;;AACA,gBAAA,KAAI,CAACC,oBAAL;AACH,eAND,MAMO;AACH;AAAA;AAAA,kCAAKH,GAAL,CAASC,WAAT,CAAqB,YAArB,EADG,CAEH;;AACA,sBAAM,KAAI,CAACG,QAAL,EAAN;AACH;AACJ,aAZD,CAYE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKL,GAAL,CAASM,OAAT,CAAiB,iBAAjB,EAAoCD,KAApC,EADY,CAEZ;;AACA,oBAAM,KAAI,CAACH,uBAAL,EAAN;AACH;AAjBkC;AAkBtC;AAED;AACJ;AACA;AACA;;;AACUE,QAAAA,QAAQ,GAAqB;AAAA;;AAAA;AAC/B;AAAA;AAAA,8BAAKJ,GAAL,CAASC,WAAT,CAAqB,gBAArB;;AAEA,gBAAI,CAAC,MAAI,CAACM,yBAAL,EAAL,EAAuC;AACnC,qBAAO,KAAP;AACH;;AAED,gBAAI;AACA,kBAAMC,QAAQ,SAAS;AAAA;AAAA,8BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,EAAnC,CAAvB;;AAEA,kBAAIH,QAAQ,CAACI,MAAb,EAAqB;AACjB;AACA;AAAA;AAAA,kCAAKZ,GAAL,CAASC,WAAT,CAAqB,YAArB;AACA,uBAAO,IAAP;AACH,eAJD,MAIO;AACH,gBAAA,MAAI,CAACY,cAAL,CAAoB,UAApB,EAAgCL,QAAQ,CAACM,GAAzC;;AACA,uBAAO,KAAP;AACH;AACJ,aAXD,CAWE,OAAOT,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKL,GAAL,CAASe,QAAT,CAAkB,aAAlB,EAAiCV,KAAjC;AACA,qBAAO,KAAP;AACH;AArB8B;AAsBlC;AAED;AACJ;AACA;;;AACIW,QAAAA,cAAc,CAACC,OAAD,EAA8B;AACxC,cAAMC,qBAAqB,GAAG,CAAC,KAAKC,WAAL,EAA/B,CADwC,CAGxC;;AACA,eAAKC,mBAAL,CAAyBH,OAAzB,EAJwC,CAMxC;;AACA,cAAI,KAAK1B,SAAL,CAAe8B,YAAnB,EAAiC;AAC7B,iBAAKC,aAAL,CAAmB,KAAK/B,SAAL,CAAe8B,YAAlC,EAAgDJ,OAAhD;AACH,WAFD,MAEO;AACH,iBAAK1B,SAAL,CAAe8B,YAAf,GAA8BJ,OAA9B;AACH,WAXuC,CAaxC;;;AACA,eAAKM,eAAL,GAdwC,CAgBxC;;AACA,cAAIL,qBAAJ,EAA2B;AACvB;AAAA;AAAA,8BAAKlB,GAAL,CAASC,WAAT,CAAqB,gBAArB;AACA;AAAA;AAAA,8BAAKuB,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,wCAAUC,mBAArC,EAA0D,KAAKnC,SAAL,CAAe8B,YAAzE;AACH;AACJ,SAlGgC,CAoGjC;;AAEA;AACJ;AACA;;;AACIM,QAAAA,WAAW,CAACC,QAAD,EAA0B;AAAA;;AACjC,cAAI,CAAC,KAAKT,WAAL,EAAL,EAAyB;AACrB,mBAAO,KAAKU,qBAAL,CAA2BD,QAA3B,CAAP;AACH;;AAED,cAAME,QAAQ,4BAAG,KAAKvC,SAAL,CAAe8B,YAAf,CAA4BU,WAA/B,qBAAG,sBAA0CH,QAA1C,CAAjB;;AACA,cAAI,CAACE,QAAL,EAAe;AACX;AACA,gBAAME,WAAW,GAAG,KAAKH,qBAAL,CAA2BD,QAA3B,CAApB;AACA,iBAAKrC,SAAL,CAAe8B,YAAf,CAA4BU,WAA5B,GAA0C,KAAKxC,SAAL,CAAe8B,YAAf,CAA4BU,WAA5B,IAA2C,EAArF;AACA,iBAAKxC,SAAL,CAAe8B,YAAf,CAA4BU,WAA5B,CAAwCH,QAAxC,IAAoDI,WAApD;AACA,mBAAOA,WAAP;AACH;;AAED,iBAAOF,QAAP;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,kBAAkB,CAACL,QAAD,EAA0B;AACxC,iBAAO,KAAKD,WAAL,CAAiBC,QAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIM,QAAAA,WAAW,CACPC,IADO,EAEPC,SAFO,EAGPC,iBAHO,EAIA;AAAA,cAFPD,SAEO;AAFPA,YAAAA,SAEO,GAFc,IAEd;AAAA;;AACP,cAAI,CAAC,KAAKjB,WAAL,EAAL,EAAyB;AACrB,gBAAIiB,SAAJ,EAAe;AACX;AAAA;AAAA,gCAAKE,GAAL,CAASC,KAAT,CAAe,UAAf;AACH;;AACD,mBAAO,KAAP;AACH;;AAED,cAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAASP,IAAI,CAACQ,MAAd,CAAlB;AACA,cAAMC,MAAM,GAAG,KAAKC,UAAL,CAAgBV,IAAI,CAACP,QAArB,EAA+BY,SAA/B,CAAf;;AAEA,cAAI,CAACI,MAAD,IAAWR,SAAX,IAAwBC,iBAA5B,EAA+C;AAC3C;AAAA;AAAA,8BAAKC,GAAL,CAASC,KAAT,CAAe;AAAA;AAAA,8CAAaO,WAAb,CAAyBT,iBAAzB,KAA+CA,iBAA9D;AACH;;AAED,iBAAOO,MAAP;AACH;AAED;AACJ;AACA;;;AACUG,QAAAA,cAAc,CAACZ,IAAD,EAIC;AAAA;;AAAA;AACjB,yBAAa,MAAI,CAACa,UAAL,CAAgBb,IAAI,CAACP,QAArB,EAA+BO,IAAI,CAACQ,MAApC,EAA4CR,IAAI,CAACc,MAAjD,CAAb;AADiB;AAEpB;AAED;AACJ;AACA;;;AACIJ,QAAAA,UAAU,CAACjB,QAAD,EAAqBe,MAArB,EAA8C;AACpD,cAAMb,QAAQ,GAAG,KAAKH,WAAL,CAAiBC,QAAjB,CAAjB;AACA,iBAAOE,QAAQ,CAACa,MAAT,IAAmBF,IAAI,CAACC,GAAL,CAASC,MAAT,CAA1B;AACH;AAED;AACJ;AACA;;;AACUK,QAAAA,UAAU,CAACpB,QAAD,EAAqBe,MAArB,EAAqCM,MAArC,EAAwE;AAAA;;AAAA;AAAA;;AACpF;AACA,gBAAIN,MAAM,GAAG,CAAT,IAAc,CAAC,MAAI,CAACE,UAAL,CAAgBjB,QAAhB,EAA0Ba,IAAI,CAACC,GAAL,CAASC,MAAT,CAA1B,CAAnB,EAAgE;AAC5D;AAAA;AAAA,gCAAKL,GAAL,CAASC,KAAT,CAAe;AAAA;AAAA,gDAAaO,WAAb,CAAyB,gBAAzB,CAAf;AACA,qBAAO,KAAP;AACH,aALmF,CAOpF;;;AACA,gBAAMI,QAAQ,uBAAG,MAAI,CAAC3D,SAAR,qBAAG,iBAAgB8B,YAAjC;;AACA,gBAAK6B,QAAL,YAAKA,QAAD,CAAmBC,eAAvB,EAAwC;AACpC;AAAA;AAAA,gCAAKnD,GAAL,CAASC,WAAT,CAAqB,uBAArB,EADoC,CAGpC;;AACA,kBAAM6B,QAAQ,GAAG,MAAI,CAACG,kBAAL,CAAwBL,QAAxB,CAAjB;;AACA,kBAAIE,QAAJ,EAAc;AACVA,gBAAAA,QAAQ,CAACa,MAAT,IAAmBA,MAAnB;AACAb,gBAAAA,QAAQ,CAACsB,cAAT,GAA0B,IAAIC,IAAJ,EAA1B;AAEA;AAAA;AAAA,kCAAKrD,GAAL,CAASC,WAAT,mDACiB2B,QADjB,UAC6Be,MAAM,GAAG,CAAT,GAAa,GAAb,GAAmB,EADhD,IACqDA,MADrD,sBAJU,CAQV;;AACA,oBAAIA,MAAM,GAAG,CAAb,EAAgB;AACZ;AAAA;AAAA,oCAAKnB,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,8CAAU6B,OAArC,EAA8C;AAAE1B,oBAAAA,QAAF;AAAYe,oBAAAA;AAAZ,mBAA9C;AACH,iBAXS,CAaV;;;AACA,gBAAA,MAAI,CAACY,sBAAL,CAA4B3B,QAA5B,EAAsCe,MAAtC,EAA8CM,MAA9C;;AACA,uBAAO,IAAP;AACH;AACJ;;AAED,gBAAI;AACA,kBAAMzC,QAAQ,SAAS;AAAA;AAAA,8BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,YAAvB,EAAqC;AACxDiB,gBAAAA,QADwD;AAExDe,gBAAAA,MAFwD;AAGxDM,gBAAAA,MAAM,EAAEA,MAAM,IAAI;AAHsC,eAArC,CAAvB;;AAMA,kBAAIzC,QAAQ,CAACI,MAAb,EAAqB;AACjB;AAAA;AAAA,kCAAKZ,GAAL,CAASC,WAAT,mDACiB2B,QADjB,UAC6Be,MAAM,GAAG,CAAT,GAAa,GAAb,GAAmB,EADhD,IACqDA,MADrD,EADiB,CAKjB;;AACA,oBAAIA,MAAM,GAAG,CAAb,EAAgB;AACZ;AAAA;AAAA,oCAAKnB,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,8CAAU6B,OAArC,EAA8C;AAAE1B,oBAAAA,QAAF;AAAYe,oBAAAA;AAAZ,mBAA9C;AACH;;AAED,uBAAO,IAAP;AACH,eAXD,MAWO;AACH,gBAAA,MAAI,CAAC9B,cAAL,CAAoB,YAApB,EAAkCL,QAAQ,CAACM,GAA3C;;AACA,uBAAO,KAAP;AACH;AACJ,aAtBD,CAsBE,OAAOT,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKL,GAAL,CAASe,QAAT,CAAkB,WAAlB,EAA+BV,KAA/B;AACA;AAAA;AAAA,gCAAKiC,GAAL,CAASC,KAAT,CAAe,UAAf;AACA,qBAAO,KAAP;AACH;AA3DmF;AA4DvF;AAED;AACJ;AACA;;;AACUiB,QAAAA,gBAAgB,CAClBC,OADkB,EAEF;AAAA;;AAAA;AAChB,iBAAK,IAAMC,MAAX,IAAqBD,OAArB,EAA8B;AAC1B,kBAAME,OAAO,SAAS,MAAI,CAACX,UAAL,CAAgBU,MAAM,CAAC9B,QAAvB,EAAiC8B,MAAM,CAACf,MAAxC,EAAgDe,MAAM,CAACT,MAAvD,CAAtB;;AACA,kBAAI,CAACU,OAAL,EAAc;AACV,uBAAO,KAAP;AACH;AACJ;;AACD,mBAAO,IAAP;AAPgB;AAQnB,SA/PgC,CAiQjC;;AAEA;AACJ;AACA;;;AACIC,QAAAA,eAAe,GAAW;AACtB,cAAI,CAAC,KAAKzC,WAAL,EAAL,EAAyB;AACrB,mBAAO,CAAP;AACH;;AACD,iBAAO,KAAK5B,SAAL,CAAe8B,YAAf,CAA4BwC,KAA5B,IAAqC,CAA5C;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,YAAY,GAAW;AACnB,iBAAO,KAAKF,eAAL,EAAP;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,iBAAiB,CAACC,eAAD,EAAmC;AAChD,cAAMC,QAAQ,GAAGD,eAAH,WAAGA,eAAH,GAAsB,KAAKJ,eAAL,EAApC;AACA,cAAMM,SAAS,GAAG,CAACD,QAAQ,GAAG,CAAZ,IAAiB;AAAA;AAAA,8CAAcE,GAAjD;AACA,iBAAOD,SAAS,KAAK,CAAd,GAAkB,CAAlB,GAAsBA,SAA7B;AACH;AAED;AACJ;AACA;;;AACUE,QAAAA,kBAAkB,CAACC,QAAD,EAAoBC,IAApB,EAA6D;AAAA;;AAAA;AAAA,gBAAzCA,IAAyC;AAAzCA,cAAAA,IAAyC,GAAzB,KAAyB;AAAA;;AACjF,gBAAMC,WAAW,GAAGF,QAAH,WAAGA,QAAH,GAAe,MAAI,CAACT,eAAL,KAAyB,CAAzD;;AAEA,gBAAI;AACA,kBAAMpD,QAAQ,SAAS;AAAA;AAAA,8BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,gBAAvB,EAAyC;AAC5DkD,gBAAAA,KAAK,EAAEU,WADqD;AAE5DC,gBAAAA,IAAI,EAAEF;AAFsD,eAAzC,CAAvB;;AAKA,kBAAI9D,QAAQ,CAACI,MAAb,EAAqB;AACjB;AAAA;AAAA,kCAAKZ,GAAL,CAASC,WAAT,+DAAoCsE,WAApC,EADiB,CAGjB;;AACA;AAAA;AAAA,kCAAK/C,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,4CAAUgD,QAArC,EAA+C;AAC3CC,kBAAAA,QAAQ,EAAE,MAAI,CAACd,eAAL,EADiC;AAE3CS,kBAAAA,QAAQ,EAAEE,WAFiC;AAG3CC,kBAAAA,IAAI,EAAEF;AAHqC,iBAA/C;AAMA,uBAAO,IAAP;AACH,eAXD,MAWO;AACH,gBAAA,MAAI,CAACzD,cAAL,CAAoB,gBAApB,EAAsCL,QAAQ,CAACM,GAA/C;;AACA,uBAAO,KAAP;AACH;AACJ,aArBD,CAqBE,OAAOT,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKL,GAAL,CAASe,QAAT,CAAkB,aAAlB,EAAiCV,KAAjC;AACA;AAAA;AAAA,gCAAKiC,GAAL,CAASC,KAAT,CAAe,UAAf;AACA,qBAAO,KAAP;AACH;AA5BgF;AA6BpF,SA7TgC,CA+TjC;;AAEA;AACJ;AACA;;;AACIxC,QAAAA,WAAW,GAAY;AACnB;AACA,cAAI,KAAKoB,WAAL,EAAJ,EAAwB;AAAA;;AACpB,6CAAO,KAAK5B,SAAL,CAAe8B,YAAf,CAA4BtB,WAAnC,qCAAkD,IAAlD;AACH,WAJkB,CAMnB;;;AACA,cAAM4E,cAAc,GAAG,CAAC,CAAC;AAAA;AAAA,4BAAKC,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,sDAAkBC,QAAnC,CAAzB;;AACA,cAAIH,cAAJ,EAAoB;AAChB;AAAA;AAAA,8BAAK3E,GAAL,CAASC,WAAT,CAAqB,mBAArB;AACA,mBAAO,KAAP;AACH,WAXkB,CAanB;;;AACA,cAAM8E,WAAW,GAAG;AAAA;AAAA,4BAAKH,OAAL,CAAaI,OAAb,CAAqB;AAAA;AAAA,sDAAkBC,WAAvC,EAAoD,IAApD,CAApB;;AACA,cAAIF,WAAW,IAAIG,MAAM,CAACH,WAAD,CAAN,KAAwB,GAA3C,EAAgD;AAC5C;AAAA;AAAA,8BAAK/E,GAAL,CAASC,WAAT,CAAqB,qBAArB;AACA,mBAAO,KAAP;AACH,WAlBkB,CAoBnB;;;AACA;AAAA;AAAA,4BAAKD,GAAL,CAASC,WAAT,CAAqB,sBAArB;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACUkF,QAAAA,sBAAsB,GAAqB;AAAA;;AAAA;AAAA;;AAC7C,gBAAI,CAAC,MAAI,CAACpF,WAAL,EAAL,EAAyB;AACrB;AAAA;AAAA,gCAAKC,GAAL,CAASC,WAAT,CAAqB,aAArB;AACA,qBAAO,IAAP;AACH,aAJ4C,CAM7C;;;AACA,gBAAMiD,QAAQ,uBAAG,MAAI,CAAC3D,SAAR,qBAAG,iBAAgB8B,YAAjC;;AACA,gBAAK6B,QAAL,YAAKA,QAAD,CAAmBC,eAAvB,EAAwC;AACpC;AAAA;AAAA,gCAAKnD,GAAL,CAASC,WAAT,CAAqB,uBAArB,EADoC,CAEpC;;AACA,kBAAI,MAAI,CAACV,SAAL,IAAkB,MAAI,CAACA,SAAL,CAAe8B,YAArC,EAAmD;AAC/C,gBAAA,MAAI,CAAC9B,SAAL,CAAe8B,YAAf,CAA4BtB,WAA5B,GAA0C,KAA1C;AACA;AAAA;AAAA,kCAAKC,GAAL,CAASC,WAAT,CAAqB,wBAArB;AACH,eANmC,CAQpC;;;AACA;AAAA;AAAA,gCAAKuB,OAAL,CAAa4D,IAAb,CACI,gBADJ,iCAEI,aAAY;AACR,sBAAM,MAAI,CAACC,2BAAL,EAAN;AACH,eAJL,GAKI,MALJ;AAQA,qBAAO,IAAP;AACH;;AAED,yBAAa,MAAI,CAACA,2BAAL,EAAb;AA5B6C;AA6BhD;AAED;AACJ;AACA;;;AACkBA,QAAAA,2BAA2B,GAAqB;AAAA;;AAAA;AAC1D,gBAAI;AACA,kBAAM7E,QAAQ,SAAS;AAAA;AAAA,8BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,sBAAvB,EAA+C;AAClEZ,gBAAAA,WAAW,EAAE;AADqD,eAA/C,CAAvB;;AAIA,kBAAIS,QAAQ,CAACI,MAAb,EAAqB;AACjB;AACA,oBAAI,MAAI,CAACrB,SAAL,IAAkB,MAAI,CAACA,SAAL,CAAe8B,YAArC,EAAmD;AAC/C,kBAAA,MAAI,CAAC9B,SAAL,CAAe8B,YAAf,CAA4BtB,WAA5B,GAA0C,KAA1C;AACA;AAAA;AAAA,oCAAKC,GAAL,CAASC,WAAT,CAAqB,oBAArB;AACH;;AAED;AAAA;AAAA,kCAAKD,GAAL,CAASC,WAAT,CAAqB,UAArB;AACA;AAAA;AAAA,kCAAKuB,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,4CAAU6D,eAArC,EAAsD;AAAEvF,kBAAAA,WAAW,EAAE;AAAf,iBAAtD;AACA,uBAAO,IAAP;AACH,eAVD,MAUO;AACH,gBAAA,MAAI,CAACc,cAAL,CAAoB,sBAApB,EAA4CL,QAAQ,CAACM,GAArD;;AACA,uBAAO,KAAP;AACH;AACJ,aAnBD,CAmBE,OAAOT,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKL,GAAL,CAASe,QAAT,CAAkB,aAAlB,EAAiCV,KAAjC;AACA;AAAA;AAAA,gCAAKiC,GAAL,CAASC,KAAT,CAAe,UAAf;AACA,qBAAO,KAAP;AACH;AAxByD;AAyB7D;AAED;AACJ;AACA;;;AACYgB,QAAAA,sBAAsB,CAAC3B,QAAD,EAAqBe,MAArB,EAAqCM,MAArC,EAA4D;AAAA;;AACtF,eAAKzD,kBAAL,CAAwB+F,IAAxB,CAA6B;AAAE3D,YAAAA,QAAF;AAAYe,YAAAA,MAAZ;AAAoBM,YAAAA;AAApB,WAA7B;AACA;AAAA;AAAA,4BAAKjD,GAAL,CAASC,WAAT,uFAAwC2B,QAAxC,SAAoDe,MAApD,EAFsF,CAItF;;AACA;AAAA;AAAA,4BAAKnB,OAAL,CAAa4D,IAAb,CACI,gBADJ,iCAEI,aAAY;AACR,kBAAM,MAAI,CAACI,sBAAL,EAAN;AACH,WAJL,GAKI,IALJ;AAOH;AAED;AACJ;AACA;;;AACkBA,QAAAA,sBAAsB,GAAkB;AAAA;;AAAA;AAClD,gBAAI,OAAI,CAAChG,kBAAL,CAAwBiG,MAAxB,KAAmC,CAAvC,EAA0C;AACtC;AACH;;AAED;AAAA;AAAA,8BAAKzF,GAAL,CAASC,WAAT,4CAAgC,OAAI,CAACT,kBAAL,CAAwBiG,MAAxD,sCALkD,CAOlD;;AACA,gBAAMhC,OAAO,GAAG,CAAC,GAAG,OAAI,CAACjE,kBAAT,CAAhB;AACA,YAAA,OAAI,CAACA,kBAAL,GAA0B,EAA1B;;AAEA,iBAAK,IAAMkE,MAAX,IAAqBD,OAArB,EAA8B;AAC1B,kBAAI;AACA,oBAAMjD,QAAQ,SAAS;AAAA;AAAA,gCAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,YAAvB,EAAqC;AACxDiB,kBAAAA,QAAQ,EAAE8B,MAAM,CAAC9B,QADuC;AAExDe,kBAAAA,MAAM,EAAEe,MAAM,CAACf,MAFyC;AAGxDM,kBAAAA,MAAM,EAAES,MAAM,CAACT,MAAP,IAAiB;AAH+B,iBAArC,CAAvB;;AAMA,oBAAIzC,QAAQ,CAACI,MAAb,EAAqB;AACjB;AAAA;AAAA,oCAAKZ,GAAL,CAASC,WAAT,mDACiByD,MAAM,CAAC9B,QADxB,UACoC8B,MAAM,CAACf,MAAP,GAAgB,CAAhB,GAAoB,GAApB,GAA0B,EAD9D,IACmEe,MAAM,CAACf,MAD1E;AAGH,iBAJD,MAIO;AACH;AAAA;AAAA,oCAAK3C,GAAL,CAASM,OAAT,yDAA+BoD,MAAM,CAAC9B,QAAtC,EAAkDpB,QAAQ,CAACM,GAA3D;AACH;AACJ,eAdD,CAcE,OAAOT,KAAP,EAAc;AACZ;AAAA;AAAA,kCAAKL,GAAL,CAASe,QAAT,mDAA+B2C,MAAM,CAAC9B,QAAtC,EAAkDvB,KAAlD;AACH;AACJ;;AAED;AAAA;AAAA,8BAAKL,GAAL,CAASC,WAAT,CAAqB,YAArB;AA/BkD;AAgCrD,SAjdgC,CAmdjC;;AAEA;AACJ;AACA;;;AACkBC,QAAAA,uBAAuB,GAAkB;AAAA;;AAAA;AACnD,gBAAI;AACA;AAAA;AAAA,gCAAKF,GAAL,CAASC,WAAT,CAAqB,qBAArB,EADA,CAGA;;AACA,kBAAMyF,WAAW,GAAG,IAAIrC,IAAJ,EAApB;AACA,kBAAMsC,aAAa,GAAG;AAClB;AACAC,gBAAAA,GAAG,EAAE,CAFa;AAGlBC,gBAAAA,KAAK,EAAE,EAHW;AAIlBC,gBAAAA,UAAU,EAAE,EAJM;AAKlBC,gBAAAA,UAAU,EAAE,EALM;AAMlBC,gBAAAA,QAAQ,EAAE,EANQ;AAOlBC,gBAAAA,QAAQ,EAAE,EAPQ;AAQlBC,gBAAAA,GAAG,EAAE,CARa;AAQV;AACRC,gBAAAA,UAAU,EAAET,WATM;AAUlBU,gBAAAA,MAAM,EAAE,EAVU;AAWlBC,gBAAAA,QAAQ,EAAE,KAXQ;AAYlBC,gBAAAA,YAAY,EAAE,KAZI;AAalBC,gBAAAA,MAAM,EAAE,EAbU;AAclBC,gBAAAA,QAAQ,EAAE,CAdQ;AAelBC,gBAAAA,WAAW,EAAE,OAfK;AAiBlB;AACAC,gBAAAA,SAAS,EAAE,CAlBO;AAmBlB7C,gBAAAA,KAAK,EAAE,CAnBW;AAoBlB8C,gBAAAA,oBAAoB,EAAE,CApBJ;AAqBlBC,gBAAAA,qBAAqB,EAAElB,WArBL;AAsBlBmB,gBAAAA,eAAe,EAAE,CAtBC;AAwBlB;AACA9E,gBAAAA,WAAW,EAAE,OAAI,CAAC+E,kBAAL,EAzBK;AA0BlBC,gBAAAA,UAAU,EAAE,EA1BM;AA4BlB;AACAhH,gBAAAA,WAAW,EAAE,IA7BK;AA8BlBiH,gBAAAA,OAAO,EAAE,IA9BS;AA+BlBC,gBAAAA,QAAQ,EAAE,CA/BQ;AAiClB;AACA9D,gBAAAA,eAAe,EAAE;AAlCC,eAAtB,CALA,CA0CA;;AACA,cAAA,OAAI,CAACnC,cAAL,CAAoB2E,aAApB;;AACA;AAAA;AAAA,gCAAK3F,GAAL,CAASC,WAAT,CAAqB,eAArB;AACH,aA7CD,CA6CE,OAAOI,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKL,GAAL,CAASM,OAAT,CAAiB,iBAAjB,EAAoCD,KAApC;AACH;AAhDkD;AAiDtD;AAED;AACJ;AACA;;;AACYyG,QAAAA,kBAAkB,GAA2B;AACjD,cAAMpB,WAAW,GAAG,IAAIrC,IAAJ,EAApB;AACA,cAAM6D,cAAsC,GAAG,EAA/C,CAFiD,CAIjD;;AACA,cAAMC,SAAS,GAAG,CACd;AAAA;AAAA,oCAASC,YADK,EAEd;AAAA;AAAA,oCAASC,SAFK,EAGd;AAAA;AAAA,oCAASC,cAHK,EAId;AAAA;AAAA,oCAASC,iBAJK,EAKd;AAAA;AAAA,oCAASC,WALK,EAMd;AAAA;AAAA,oCAASC,QANK,EAOd;AAAA;AAAA,oCAASC,SAPK,CAAlB;AAUAP,UAAAA,SAAS,CAACQ,OAAV,CAAkB/F,QAAQ,IAAI;AAC1BsF,YAAAA,cAAc,CAACtF,QAAD,CAAd,GAA2B;AACvBA,cAAAA,QAAQ,EAAEA,QADa;AAEvBe,cAAAA,MAAM,EAAE,KAAKiF,yBAAL,CAA+BhG,QAA/B,CAFe;AAGvBwB,cAAAA,cAAc,EAAEsC;AAHO,aAA3B;AAKH,WAND;AAQA;AAAA;AAAA,4BAAK1F,GAAL,CAASC,WAAT,CAAqB,eAArB,EAAsC;AAClC4H,YAAAA,SAAS,EAAEC,MAAM,CAACC,IAAP,CAAYb,cAAZ,EAA4BzB;AADL,WAAtC;AAIA,iBAAOyB,cAAP;AACH;AAED;AACJ;AACA;;;AACY/G,QAAAA,oBAAoB,GAAS;AAAA;;AACjC;AACA6H,UAAAA,UAAU,iCAAC,aAAY;AACnB,gBAAI;AACA;AAAA;AAAA,gCAAKhI,GAAL,CAASC,WAAT,CAAqB,oBAArB,EADA,CAGA;;AACA,oBAAM,OAAI,CAACgI,yBAAL,EAAN;AAEA;AAAA;AAAA,gCAAKjI,GAAL,CAASC,WAAT,CAAqB,cAArB,EANA,CAQA;;AACA;AAAA;AAAA,gCAAKuB,OAAL,CAAaC,aAAb,CAA2B,gBAA3B;AACH,aAVD,CAUE,OAAOpB,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKL,GAAL,CAASM,OAAT,CAAiB,gBAAjB,EAAmCD,KAAnC;AACH;AACJ,WAdS,GAcP,GAdO,CAAV;AAeH;AAED;AACJ;AACA;;;AACkB4H,QAAAA,yBAAyB,GAAkB;AAAA;;AAAA;AACrD,gBAAI;AAAA;;AACA;AACA,kBAAMC,YAAY,wBAAG,OAAI,CAAC3I,SAAR,qBAAG,kBAAgB8B,YAArC;;AACA,kBAAI6G,YAAJ,EAAkB;AACd;AACCA,gBAAAA,YAAD,CAAsB/E,eAAtB,GAAwC,IAAxC;AACH,eAND,CAQA;;;AACA,oBAAM,OAAI,CAACgF,oBAAL,EAAN;AACH,aAVD,CAUE,OAAO9H,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKL,GAAL,CAASM,OAAT,CAAiB,cAAjB,EAAiCD,KAAjC;AACA,oBAAMA,KAAN;AACH;AAdoD;AAexD;AAED;AACJ;AACA;;;AACkB8H,QAAAA,oBAAoB,GAAkB;AAAA;AAChD;AAAA;AAAA,8BAAKnI,GAAL,CAASC,WAAT,CAAqB,kBAArB;;AAEA,gBAAI;AACA,kBAAM;AAAEmI,gBAAAA;AAAF,0DAAN,CADA,CAGA;;AACA,kBAAI;AAAA;AAAA,8CAAY/B,QAAZ,KAAyB;AAAA;AAAA,wCAASgC,QAAtC,EAAgD;AAC5C;AACA;AAAA;AAAA,kCAAKrI,GAAL,CAASC,WAAT,CAAqB,yBAArB;AACA,oBAAMqI,YAAY,SAASF,aAAa,CAACG,eAAd,EAA3B;;AAEA,oBAAID,YAAJ,EAAkB;AACd;AAAA;AAAA,oCAAKtI,GAAL,CAASC,WAAT,CAAqB,kBAArB;AACH,iBAFD,MAEO;AACH;AAAA;AAAA,oCAAKD,GAAL,CAASM,OAAT,CAAiB,mBAAjB;AACH;AACJ,eAVD,MAUO;AACH;AACA;AAAA;AAAA,kCAAKN,GAAL,CAASC,WAAT,CAAqB,gBAArB,EAFG,CAIH;;AACA,oBAAMuI,aAAa,GAAG,IAAIJ,aAAJ,EAAtB;;AACA,oBAAME,aAAY,SAASE,aAAa,CAACC,gBAAd,EAA3B;;AAEA,oBAAIH,aAAJ,EAAkB;AACd;AAAA;AAAA,oCAAKtI,GAAL,CAASC,WAAT,CAAqB,UAArB;AACH,iBAFD,MAEO;AACH;AAAA;AAAA,oCAAKD,GAAL,CAASM,OAAT,CAAiB,WAAjB;AACH;AACJ;;AAED;AAAA;AAAA,gCAAKN,GAAL,CAASC,WAAT,CAAqB,UAArB;AACH,aA9BD,CA8BE,OAAOI,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKL,GAAL,CAASM,OAAT,CAAiB,YAAjB,EAA+BD,KAA/B,EADY,CAEZ;AACH;AApC+C;AAqCnD,SA9nBgC,CAgoBjC;;AAEA;AACJ;AACA;;;AACIqI,QAAAA,aAAa,CAACC,UAAD,EAAyBC,UAAzB,EAAqE;AAAA;;AAC9E,cAAI,CAAC,KAAKzH,WAAL,EAAL,EAAyB;AACrB,mBAAO,IAAP;AACH;;AAED,cAAM0H,UAAU,GAAGD,UAAU,IAAI,IAAIvF,IAAJ,GAAWyF,YAAX,EAAjC;AACA,cAAM/B,UAAU,6BAAG,KAAKxH,SAAL,CAAe8B,YAAf,CAA4B0F,UAA/B,qBAAG,uBAAyC8B,UAAzC,CAAnB;AAEA,iBAAO,CAAA9B,UAAU,QAAV,YAAAA,UAAU,CAAG4B,UAAH,CAAV,KAA4B,IAAnC;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACII,QAAAA,sBAAsB,CAACC,OAAD,EAAkBJ,UAAlB,EAA+C;AAAA;;AACjE,cAAM7B,UAAU,GAAG,KAAK2B,aAAL,CAAmB;AAAA;AAAA,wCAAWO,KAA9B,EAAqCL,UAArC,CAAnB;;AACA,cAAI,EAAC7B,UAAD,YAACA,UAAU,CAAEmC,YAAb,CAAJ,EAA+B;AAC3B,mBAAO,CAAP;AACH;;AAED,cAAMC,QAAQ,cAAYH,OAA1B;AACA,iBAAO,0BAAAjC,UAAU,CAACmC,YAAX,CAAwBC,QAAxB,4CAAmCC,QAAnC,KAA+C,CAAtD;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,6BAA6B,CAACT,UAAD,EAA8B;AACvD,cAAMU,cAAc,GAAG,KAAKvF,iBAAL,EAAvB,CADuD,CACN;;AACjD,iBAAO,KAAKgF,sBAAL,CAA4BO,cAA5B,EAA4CV,UAA5C,CAAP;AACH,SAxqBgC,CA0qBjC;;AAEA;AACJ;AACA;;;AACIW,QAAAA,eAAe,GAAiB;AAC5B,cAAI,CAAC,KAAKpI,WAAL,EAAL,EAAyB;AACrB,kBAAM,IAAIqI,KAAJ,CAAU,mBAAV,CAAN;AACH;;AACD,iBAAO,KAAKjK,SAAL,CAAe8B,YAAtB;AACH,SAprBgC,CAsrBjC;;AAEA;AACJ;AACA;;;AACYF,QAAAA,WAAW,GAAY;AAAA;;AAC3B,iBAAO,CAAC,qBAAC,KAAK5B,SAAN,aAAC,gBAAgB8B,YAAjB,CAAR;AACH;AAED;AACJ;AACA;;;AACYd,QAAAA,yBAAyB,GAAY;AAAA;;AACzC,cAAI,UAAC;AAAA;AAAA,0BAAIE,GAAL,aAAC,KAASC,MAAV,CAAJ,EAAsB;AAClB;AAAA;AAAA,8BAAKV,GAAL,CAASe,QAAT,CAAkB,YAAlB;AACA,mBAAO,KAAP;AACH;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACYK,QAAAA,mBAAmB,CAACqI,IAAD,EAA2B;AAAA;;AAClDA,UAAAA,IAAI,CAAC7D,GAAL,GAAW6D,IAAI,CAAC7D,GAAL,IAAY,CAAvB;AACA6D,UAAAA,IAAI,CAACzD,QAAL,GAAgByD,IAAI,CAACzD,QAAL,IAAiB,QAAjC;AACAyD,UAAAA,IAAI,CAAC5F,KAAL,GAAa4F,IAAI,CAAC5F,KAAL,IAAc,CAA3B;AACA4F,UAAAA,IAAI,CAAC1J,WAAL,wBAAmB0J,IAAI,CAAC1J,WAAxB,gCAAuC,IAAvC;AACA0J,UAAAA,IAAI,CAAC1H,WAAL,GAAmB0H,IAAI,CAAC1H,WAAL,IAAoB,EAAvC;AACA0H,UAAAA,IAAI,CAAC1C,UAAL,GAAkB0C,IAAI,CAAC1C,UAAL,IAAmB,EAArC;AACH;AAED;AACJ;AACA;;;AACYzF,QAAAA,aAAa,CAACoI,MAAD,EAAuBC,MAAvB,EAAmD;AACpE7B,UAAAA,MAAM,CAACC,IAAP,CAAY4B,MAAZ,EAAoBhC,OAApB,CAA4B/B,GAAG,IAAI;AAC/B,gBAAMgE,WAAW,GAAGD,MAAM,CAAC/D,GAAD,CAA1B;AACA,gBAAMiE,WAAW,GAAGH,MAAM,CAAC9D,GAAD,CAA1B;;AAEA,gBAAIgE,WAAW,IAAI,OAAOA,WAAP,KAAuB,QAAtC,IAAkD,CAACE,KAAK,CAACC,OAAN,CAAcH,WAAd,CAAvD,EAAmF;AAC/E,kBAAIC,WAAW,IAAI,OAAOA,WAAP,KAAuB,QAA1C,EAAoD;AAChD,qBAAKvI,aAAL,CAAmBuI,WAAnB,EAAuCD,WAAvC;AACH,eAFD,MAEO;AACFF,gBAAAA,MAAD,CAAgB9D,GAAhB,IAAuBgE,WAAvB;AACH;AACJ,aAND,MAMO;AACFF,cAAAA,MAAD,CAAgB9D,GAAhB,IAAuBgE,WAAvB;AACH;AACJ,WAbD;AAcH;AAED;AACJ;AACA;;;AACY/H,QAAAA,qBAAqB,CAACD,QAAD,EAA0B;AACnD;AACA,cAAMoI,aAAa,GAAG,KAAKpC,yBAAL,CAA+BhG,QAA/B,CAAtB;AAEA,iBAAO;AACHe,YAAAA,MAAM,EAAEqH,aADL;AAEHpI,YAAAA,QAFG;AAGHqI,YAAAA,MAAM,EAAErI,QAHL;AAIHsI,YAAAA,IAAI,mBAAOtI,QAJR;AAKHuI,YAAAA,OAAO,EAAE,IAAI9G,IAAJ,EALN;AAMH+G,YAAAA,aAAa,EAAE,IAAI/G,IAAJ,EANZ;AAOHD,YAAAA,cAAc,EAAE,IAAIC,IAAJ;AAPb,WAAP;AASH;AAED;AACJ;AACA;;;AACYuE,QAAAA,yBAAyB,CAAChG,QAAD,EAA6B;AAC1D,cAAI,CAAC,KAAK7B,WAAL,EAAL,EAAyB;AACrB,mBAAO,CAAP;AACH,WAHyD,CAK1D;;;AACA,kBAAQ6B,QAAR;AACI,iBAAK;AAAA;AAAA,sCAASwF,YAAd;AAA4B;AACxB,qBAAO;AAAA;AAAA,0CAAUiD,qBAAV,CAAgCC,OAAvC;;AACJ,iBAAK;AAAA;AAAA,sCAASjD,SAAd;AAAyB;AACrB,qBAAO;AAAA;AAAA,0CAAUgD,qBAAV,CAAgCE,IAAvC;;AACJ,iBAAK;AAAA;AAAA,sCAASjD,cAAd;AAA8B;AAC1B,qBAAO;AAAA;AAAA,0CAAU+C,qBAAV,CAAgCG,SAAvC;;AACJ,iBAAK;AAAA;AAAA,sCAASjD,iBAAd;AAAiC;AAC7B,qBAAO;AAAA;AAAA,0CAAUkD,YAAjB;AAA+B;;AACnC,iBAAK;AAAA;AAAA,sCAASjD,WAAd;AAA2B;AACvB,qBAAO;AAAA;AAAA,0CAAU6C,qBAAV,CAAgCK,MAAvC;;AACJ,iBAAK;AAAA;AAAA,sCAASjD,QAAd;AAAwB;AACpB,qBAAO,CAAP;;AACJ,iBAAK;AAAA;AAAA,sCAASC,SAAd;AAAyB;AACrB,qBAAO,CAAP;;AACJ;AACI,qBAAO,CAAP;AAhBR;AAkBH;AAED;AACJ;AACA;;;AACYnG,QAAAA,eAAe,GAAS;AAC5B,cAAI,CAAC,KAAKJ,WAAL,EAAL,EAAyB;AACrB;AACH;;AAED,eAAKvB,mBAAL;;AAEA,cAAM+K,aAAa;AACfC,YAAAA,MAAM,EAAE,KAAKrL,SAAL,CAAe8B,YAAf,CAA4BuE,GADrB;AAEfI,YAAAA,QAAQ,EAAE,KAAKzG,SAAL,CAAe8B,YAAf,CAA4B2E,QAFvB;AAGf6E,YAAAA,KAAK,EAAE,KAAKjH,eAAL,EAHQ;AAIf7D,YAAAA,WAAW,EAAE,KAAKR,SAAL,CAAe8B,YAAf,CAA4BtB,WAJ1B;AAKf8D,YAAAA,KAAK,EAAE,KAAKtE,SAAL,CAAe8B,YAAf,CAA4BwC,KALpB;AAMf9B,YAAAA,WAAW,EAAE,KAAKxC,SAAL,CAAe8B,YAAf,CAA4BU;AAN1B,aAQZ,KAAKxC,SAAL,CAAe8B,YARH,CAAnB;;AAWA;AAAA;AAAA,wBAAG3B,GAAH,CAAOiL,aAAP,EAAsB,MAAtB;AACA;AAAA;AAAA,4BAAK3K,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH;AAED;AACJ;AACA;;;AACYL,QAAAA,mBAAmB,GAAS;AAChC;AAAA;AAAA,wBAAGC,MAAH,CAAU,MAAV;AACH;AAED;AACJ;AACA;;;AACYgB,QAAAA,cAAc,CAACiK,OAAD,EAAkBzK,KAAlB,EAAoC;AAAA;;AACtD;AAAA;AAAA,4BAAKL,GAAL,CAASe,QAAT,aAAuB+J,OAAvB,wBAAyCzK,KAAzC;AAEA,cAAM0K,YAAY,GAAG,CAAA1K,KAAK,QAAL,YAAAA,KAAK,CAAEmB,OAAP,MAAkBnB,KAAlB,2BAAkBA,KAAK,CAAE2K,IAAzB,qBAAkB,YAAaC,QAAb,EAAlB,KAA6C,MAAlE;AACA;AAAA;AAAA,4BAAK3I,GAAL,CAASC,KAAT,CAAewI,YAAf;AACH;;AAj0BgC,O", "sourcesContent": ["import { oops } from '../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { ecs } from '../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';\nimport { LanguageData } from '../../../../extensions/oops-plugin-framework/assets/libs/gui/language/LanguageData';\nimport { VM } from '../../../../extensions/oops-plugin-framework/assets/libs/model-view/ViewModel';\nimport { GameConst } from '../../tsrpc/models/GameConst';\nimport { Platform, ShareConfig } from '../../tsrpc/models/ShareConfig';\nimport {\n    PropType,\n    RecordType,\n    RecordTypeData,\n    SceneItemType,\n    UserGameData,\n} from '../../tsrpc/protocols/base';\nimport { GameStorageConfig } from '../common/config/GameStorageConfig';\nimport { GameEvent } from '../common/Enum';\nimport { smc } from '../common/SingletonModuleComp';\nimport { RoleModelComp } from './model/RoleModelComp';\n\n/**\n * 角色管理器 - 重构版本\n * 职责：\n * 1. 用户数据的加载、更新和管理\n * 2. 道具系统的操作和验证\n * 3. 游戏进度的管理\n * 4. ViewModel的数据绑定\n */\**************('Role')\nexport class Role extends ecs.Entity {\n    RoleModel!: RoleModelComp;\n\n    // 🔄 待同步的道具更新队列\n    private pendingPropUpdates: Array<{\n        propType: PropType;\n        amount: number;\n        reason?: string;\n    }> = [];\n\n    // ==================== 初始化 ====================\n\n    protected init() {\n        this.add(RoleModelComp);\n    }\n\n    destroy(): void {\n        this.removeFromViewModel();\n        this.remove(RoleModelComp);\n    }\n\n    // ==================== 数据加载 ====================\n\n    /**\n     * 🚀 快速初始化用户数据 - 统一入口\n     */\n    async quickInitialize(): Promise<void> {\n        try {\n            if (this.isNewPlayer()) {\n                oops.log.logBusiness('🚀 启用新手快速启动模式');\n                await this.initializeNewPlayerData();\n\n                // 🔄 在后台继续完整的用户数据加载\n                this.loadDataInBackground();\n            } else {\n                oops.log.logBusiness('🚀 老玩家完整登录');\n                // 🔄 传统模式：完整加载用户数据\n                await this.loadData();\n            }\n        } catch (error) {\n            oops.log.logWarn('⚠️ 快速用户数据初始化失败:', error);\n            // 失败时创建基础数据，确保游戏可以启动\n            await this.initializeNewPlayerData();\n        }\n    }\n\n    /**\n     * 加载用户数据\n     * @returns 是否加载成功\n     */\n    async loadData(): Promise<boolean> {\n        oops.log.logBusiness('🔄 开始加载用户数据...');\n\n        if (!this.validateNetworkConnection()) {\n            return false;\n        }\n\n        try {\n            const response = await smc.net.hcGame.callApi('UserInfo', {});\n\n            if (response.isSucc) {\n                // UserInfo API成功后，数据应该已经通过DataManager更新了\n                oops.log.logBusiness('✅ 用户数据加载成功');\n                return true;\n            } else {\n                this.handleAPIError('UserInfo', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 用户数据加载异常:', error);\n            return false;\n        }\n    }\n\n    /**\n     * 更新用户数据（由DataManager调用）\n     */\n    updateUserData(newData: UserGameData): void {\n        const isFirstInitialization = !this.hasUserData();\n\n        // 确保数据结构完整\n        this.ensureDataIntegrity(newData);\n\n        // 合并数据\n        if (this.RoleModel.userGameData) {\n            this.mergeUserData(this.RoleModel.userGameData, newData);\n        } else {\n            this.RoleModel.userGameData = newData;\n        }\n\n        // 更新ViewModel\n        this.updateViewModel();\n\n        // 首次初始化触发事件\n        if (isFirstInitialization) {\n            oops.log.logBusiness('🎉 用户数据首次初始化完成');\n            oops.message.dispatchEvent(GameEvent.UserDataInitialized, this.RoleModel.userGameData);\n        }\n    }\n\n    // ==================== 道具系统 ====================\n\n    /**\n     * 获取道具数据\n     */\n    getPropData(propType: PropType): any {\n        if (!this.hasUserData()) {\n            return this.createDefaultPropData(propType);\n        }\n\n        const propData = this.RoleModel.userGameData.propUseData?.[propType];\n        if (!propData) {\n            // 创建默认道具数据\n            const defaultData = this.createDefaultPropData(propType);\n            this.RoleModel.userGameData.propUseData = this.RoleModel.userGameData.propUseData || {};\n            this.RoleModel.userGameData.propUseData[propType] = defaultData;\n            return defaultData;\n        }\n\n        return propData;\n    }\n\n    /**\n     * 获取道具数据（别名方法，兼容现有代码）\n     */\n    getPropsDataByType(propType: PropType): any {\n        return this.getPropData(propType);\n    }\n\n    /**\n     * 尝试消耗道具（检查数量并显示提示）\n     * @param args 道具参数（包含类型和数量）\n     * @param showToast 是否显示提示\n     * @param failureMessageKey 失败时的消息键\n     * @returns 是否可以消耗\n     */\n    tryCostProp(\n        args: { propType: PropType; amount: number },\n        showToast: boolean = true,\n        failureMessageKey?: string\n    ): boolean {\n        if (!this.hasUserData()) {\n            if (showToast) {\n                oops.gui.toast('用户数据未初始化');\n            }\n            return false;\n        }\n\n        const absAmount = Math.abs(args.amount);\n        const canUse = this.canUseProp(args.propType, absAmount);\n\n        if (!canUse && showToast && failureMessageKey) {\n            oops.gui.toast(LanguageData.getLangByID(failureMessageKey) || failureMessageKey);\n        }\n\n        return canUse;\n    }\n\n    /**\n     * 更新道具数据（别名方法，兼容现有代码）\n     */\n    async updatePropData(args: {\n        propType: PropType;\n        amount: number;\n        reason?: string;\n    }): Promise<boolean> {\n        return await this.updateProp(args.propType, args.amount, args.reason);\n    }\n\n    /**\n     * 检查道具是否足够\n     */\n    canUseProp(propType: PropType, amount: number): boolean {\n        const propData = this.getPropData(propType);\n        return propData.amount >= Math.abs(amount);\n    }\n\n    /**\n     * 更新道具数量\n     */\n    async updateProp(propType: PropType, amount: number, reason?: string): Promise<boolean> {\n        // 消耗操作前检查数量\n        if (amount < 0 && !this.canUseProp(propType, Math.abs(amount))) {\n            oops.gui.toast(LanguageData.getLangByID('UseLimitsDaily'));\n            return false;\n        }\n\n        // 🔍 检查是否为临时数据状态\n        const userData = this.RoleModel?.userGameData;\n        if ((userData as any)?.isTemporaryData) {\n            oops.log.logBusiness('🔄 检测到临时数据状态，本地更新道具数量');\n\n            // 在临时状态下，直接更新本地数据\n            const propData = this.getPropsDataByType(propType);\n            if (propData) {\n                propData.amount += amount;\n                propData.lastUpdateTime = new Date();\n\n                oops.log.logBusiness(\n                    `✅ 道具本地更新: ${propType} ${amount > 0 ? '+' : ''}${amount} (临时)`\n                );\n\n                // 触发道具使用事件\n                if (amount < 0) {\n                    oops.message.dispatchEvent(GameEvent.UseProp, { propType, amount });\n                }\n\n                // 等后台登录完成后同步到服务器\n                this.queuePropUpdateForSync(propType, amount, reason);\n                return true;\n            }\n        }\n\n        try {\n            const response = await smc.net.hcGame.callApi('UpdateProp', {\n                propType,\n                amount,\n                reason: reason || 'player_action',\n            });\n\n            if (response.isSucc) {\n                oops.log.logBusiness(\n                    `✅ 道具更新成功: ${propType} ${amount > 0 ? '+' : ''}${amount}`\n                );\n\n                // 触发道具使用事件\n                if (amount < 0) {\n                    oops.message.dispatchEvent(GameEvent.UseProp, { propType, amount });\n                }\n\n                return true;\n            } else {\n                this.handleAPIError('UpdateProp', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 道具更新异常:', error);\n            oops.gui.toast('网络错误，请重试');\n            return false;\n        }\n    }\n\n    /**\n     * 批量更新道具\n     */\n    async batchUpdateProps(\n        updates: Array<{ propType: PropType; amount: number; reason?: string }>\n    ): Promise<boolean> {\n        for (const update of updates) {\n            const success = await this.updateProp(update.propType, update.amount, update.reason);\n            if (!success) {\n                return false;\n            }\n        }\n        return true;\n    }\n\n    // ==================== 游戏进度 ====================\n\n    /**\n     * 获取当前通关进度\n     */\n    getGameProgress(): number {\n        if (!this.hasUserData()) {\n            return 0;\n        }\n        return this.RoleModel.userGameData.index || 0;\n    }\n\n    /**\n     * 获取已通过的关卡索引（别名方法，兼容现有代码）\n     */\n    getPassIndex(): number {\n        return this.getGameProgress();\n    }\n\n    /**\n     * 获取下一关卡索引（循环通关）\n     */\n    getNextLevelIndex(currentProgress?: number): number {\n        const progress = currentProgress ?? this.getGameProgress();\n        const nextLevel = (progress + 1) % SceneItemType.Max;\n        return nextLevel === 0 ? 1 : nextLevel;\n    }\n\n    /**\n     * 更新游戏通关进度\n     */\n    async updateGameProgress(newIndex?: number, isGM: boolean = false): Promise<boolean> {\n        const targetIndex = newIndex ?? this.getGameProgress() + 1;\n\n        try {\n            const response = await smc.net.hcGame.callApi('UpdateProgress', {\n                index: targetIndex,\n                isGm: isGM,\n            });\n\n            if (response.isSucc) {\n                oops.log.logBusiness(`✅ 游戏进度更新成功: ${targetIndex}`);\n\n                // 触发通关事件\n                oops.message.dispatchEvent(GameEvent.GamePass, {\n                    oldIndex: this.getGameProgress(),\n                    newIndex: targetIndex,\n                    isGm: isGM,\n                });\n\n                return true;\n            } else {\n                this.handleAPIError('UpdateProgress', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 游戏进度更新异常:', error);\n            oops.gui.toast('网络错误，请重试');\n            return false;\n        }\n    }\n\n    // ==================== 新手引导 ====================\n\n    /**\n     * 检查是否为新玩家 - 统一判断逻辑\n     */\n    isNewPlayer(): boolean {\n        // 1. 优先使用服务端数据\n        if (this.hasUserData()) {\n            return this.RoleModel.userGameData.isNewPlayer ?? true;\n        }\n\n        // 2. 检查本地存储的登录记录\n        const hasLoginRecord = !!oops.storage.get(GameStorageConfig.SSOToken);\n        if (hasLoginRecord) {\n            oops.log.logBusiness('🔍 检测到登录记录，判定为老玩家');\n            return false;\n        }\n\n        // 3. 检查是否有其他用户数据痕迹\n        const userDumpKey = oops.storage.getJson(GameStorageConfig.UserDumpKey, null);\n        if (userDumpKey && String(userDumpKey) !== '0') {\n            oops.log.logBusiness('🔍 检测到用户数据痕迹，判定为老玩家');\n            return false;\n        }\n\n        // 4. 默认判定为新手\n        oops.log.logBusiness('🆕 无任何用户数据痕迹，判定为新手玩家');\n        return true;\n    }\n\n    /**\n     * 完成新手引导\n     */\n    async completeNewPlayerGuide(): Promise<boolean> {\n        if (!this.isNewPlayer()) {\n            oops.log.logBusiness('✅ 用户已完成新手引导');\n            return true;\n        }\n\n        // 🔍 检查是否为临时数据状态\n        const userData = this.RoleModel?.userGameData;\n        if ((userData as any)?.isTemporaryData) {\n            oops.log.logBusiness('🔄 检测到临时数据状态，延迟新手引导完成');\n            // 先更新本地状态，等后台登录完成后再同步到服务器\n            if (this.RoleModel && this.RoleModel.userGameData) {\n                this.RoleModel.userGameData.isNewPlayer = false;\n                oops.log.logBusiness('🔄 本地新手状态已更新为false（临时）');\n            }\n\n            // 监听后台登录完成事件，然后同步状态\n            oops.message.once(\n                'UserDataLoaded',\n                async () => {\n                    await this.syncNewPlayerStatusToServer();\n                },\n                this\n            );\n\n            return true;\n        }\n\n        return await this.syncNewPlayerStatusToServer();\n    }\n\n    /**\n     * 🔄 同步新手状态到服务器\n     */\n    private async syncNewPlayerStatusToServer(): Promise<boolean> {\n        try {\n            const response = await smc.net.hcGame.callApi('GameUpdateSimpleData', {\n                isNewPlayer: false,\n            });\n\n            if (response.isSucc) {\n                // 🎯 立即更新本地数据，确保状态同步\n                if (this.RoleModel && this.RoleModel.userGameData) {\n                    this.RoleModel.userGameData.isNewPlayer = false;\n                    oops.log.logBusiness('🔄 本地新手状态已更新为false');\n                }\n\n                oops.log.logBusiness('✅ 新手引导完成');\n                oops.message.dispatchEvent(GameEvent.BasicInfoUpdate, { isNewPlayer: false });\n                return true;\n            } else {\n                this.handleAPIError('GameUpdateSimpleData', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 新手状态更新异常:', error);\n            oops.gui.toast('网络错误，请重试');\n            return false;\n        }\n    }\n\n    /**\n     * 🔄 将道具更新加入同步队列\n     */\n    private queuePropUpdateForSync(propType: PropType, amount: number, reason?: string): void {\n        this.pendingPropUpdates.push({ propType, amount, reason });\n        oops.log.logBusiness(`📝 道具更新已加入同步队列: ${propType} ${amount}`);\n\n        // 监听后台登录完成事件\n        oops.message.once(\n            'UserDataLoaded',\n            async () => {\n                await this.syncPendingPropUpdates();\n            },\n            this\n        );\n    }\n\n    /**\n     * 🔄 同步待处理的道具更新到服务器\n     */\n    private async syncPendingPropUpdates(): Promise<void> {\n        if (this.pendingPropUpdates.length === 0) {\n            return;\n        }\n\n        oops.log.logBusiness(`🔄 开始同步 ${this.pendingPropUpdates.length} 个道具更新`);\n\n        // 复制队列并清空原队列\n        const updates = [...this.pendingPropUpdates];\n        this.pendingPropUpdates = [];\n\n        for (const update of updates) {\n            try {\n                const response = await smc.net.hcGame.callApi('UpdateProp', {\n                    propType: update.propType,\n                    amount: update.amount,\n                    reason: update.reason || 'delayed_sync',\n                });\n\n                if (response.isSucc) {\n                    oops.log.logBusiness(\n                        `✅ 道具同步成功: ${update.propType} ${update.amount > 0 ? '+' : ''}${update.amount}`\n                    );\n                } else {\n                    oops.log.logWarn(`⚠️ 道具同步失败: ${update.propType}`, response.err);\n                }\n            } catch (error) {\n                oops.log.logError(`❌ 道具同步异常: ${update.propType}`, error);\n            }\n        }\n\n        oops.log.logBusiness('✅ 道具更新同步完成');\n    }\n\n    // ==================== 新手数据初始化 ====================\n\n    /**\n     * 🚀 初始化新手玩家数据\n     */\n    private async initializeNewPlayerData(): Promise<void> {\n        try {\n            oops.log.logBusiness('🚀 开始创建默认的新手玩家数据...');\n\n            // 创建完整的基础用户数据结构\n            const currentTime = new Date();\n            const basicUserData = {\n                // 基础标识\n                key: 0,\n                guuid: '',\n                googleUuid: '',\n                facebookId: '',\n                userName: '',\n                nickName: '',\n                sex: 1, // SexType.None\n                createtime: currentTime,\n                openid: '',\n                platform: 'web',\n                platformType: 'web',\n                avatar: '',\n                avatarId: 0,\n                countryCode: 'Other',\n\n                // 游戏进度\n                passTimes: 0,\n                index: 0,\n                currCountryPassTimes: 0,\n                lastChangeCountryTime: currentTime,\n                selfCountryRank: 0,\n\n                // 道具和记录数据\n                propUseData: this.createDefaultProps(),\n                recordData: {},\n\n                // 新手和游客状态\n                isNewPlayer: true,\n                isGuest: true,\n                lastStep: 0,\n\n                // 🔄 标记为临时数据\n                isTemporaryData: true,\n            };\n\n            // 🎯 设置用户数据\n            this.updateUserData(basicUserData as any);\n            oops.log.logBusiness('✅ 新手玩家数据初始化完成');\n        } catch (error) {\n            oops.log.logWarn('⚠️ 新手玩家数据初始化失败:', error);\n        }\n    }\n\n    /**\n     * 🎁 创建新手玩家默认道具\n     */\n    private createDefaultProps(): { [key: number]: any } {\n        const currentTime = new Date();\n        const newPlayerProps: { [key: number]: any } = {};\n\n        // 🎯 为每种道具类型创建默认数据\n        const propTypes = [\n            PropType.PropsMoveOut,\n            PropType.PropsTips,\n            PropType.PropsReShuffle,\n            PropType.PropsDayLeftCount,\n            PropType.PropsRevive,\n            PropType.PropsExp,\n            PropType.PropsCoin,\n        ];\n\n        propTypes.forEach(propType => {\n            newPlayerProps[propType] = {\n                propType: propType,\n                amount: this.getNewPlayerDefaultAmount(propType),\n                lastUpdateTime: currentTime,\n            };\n        });\n\n        oops.log.logBusiness('🎁 新手默认道具创建完成', {\n            propCount: Object.keys(newPlayerProps).length,\n        });\n\n        return newPlayerProps;\n    }\n\n    /**\n     * 🔄 后台加载完整用户数据\n     */\n    private loadDataInBackground(): void {\n        // 使用setTimeout确保不阻塞当前流程\n        setTimeout(async () => {\n            try {\n                oops.log.logBusiness('🔄 开始后台加载完整用户数据...');\n\n                // 🚀 强制执行完整的登录流程，不跳过\n                await this.forceCompleteUserDataLoad();\n\n                oops.log.logBusiness('✅ 后台用户数据加载完成');\n\n                // 触发数据更新事件，通知游戏其他模块\n                oops.message.dispatchEvent('UserDataLoaded');\n            } catch (error) {\n                oops.log.logWarn('⚠️ 后台用户数据加载失败:', error);\n            }\n        }, 100);\n    }\n\n    /**\n     * 🔄 强制执行完整的用户数据加载流程\n     */\n    private async forceCompleteUserDataLoad(): Promise<void> {\n        try {\n            // 🔄 临时清除基础数据标记，强制执行完整登录\n            const tempUserData = this.RoleModel?.userGameData;\n            if (tempUserData) {\n                // 标记这是临时数据，需要完整登录\n                (tempUserData as any).isTemporaryData = true;\n            }\n\n            // 🔄 执行完整的登录流程\n            await this.performCompleteLogin();\n        } catch (error) {\n            oops.log.logWarn('⚠️ 强制完整登录失败:', error);\n            throw error;\n        }\n    }\n\n    /**\n     * 🔐 执行完整的登录流程\n     */\n    private async performCompleteLogin(): Promise<void> {\n        oops.log.logBusiness('🔐 开始执行完整登录流程...');\n\n        try {\n            const { LoginViewComp } = await import('../initialize/view/LoginViewComp');\n\n            // 🔧 根据平台选择不同的登录方式\n            if (ShareConfig.platform === Platform.FACEBOOK) {\n                // Facebook环境：执行Facebook自动登录\n                oops.log.logBusiness('🔐 Facebook环境：执行自动登录...');\n                const loginSuccess = await LoginViewComp.doFacebookLogin();\n\n                if (loginSuccess) {\n                    oops.log.logBusiness('✅ Facebook自动登录成功');\n                } else {\n                    oops.log.logWarn('⚠️ Facebook自动登录失败');\n                }\n            } else {\n                // 🔐 其他环境：使用现有的游客登录逻辑\n                oops.log.logBusiness('🔐 执行游客登录流程...');\n\n                // 🚀 直接使用LoginViewComp的游客登录方法\n                const loginViewComp = new LoginViewComp();\n                const loginSuccess = await loginViewComp.loginGuestButton();\n\n                if (loginSuccess) {\n                    oops.log.logBusiness('✅ 游客登录成功');\n                } else {\n                    oops.log.logWarn('⚠️ 游客登录失败');\n                }\n            }\n\n            oops.log.logBusiness('✅ 登录流程完成');\n        } catch (error) {\n            oops.log.logWarn('⚠️ 登录流程失败:', error);\n            // 不抛出错误，允许游戏继续运行\n        }\n    }\n\n    // ==================== 记录数据 ====================\n\n    /**\n     * 获取指定日期的记录数据\n     */\n    getRecordData(recordType: RecordType, dateString?: string): RecordTypeData | null {\n        if (!this.hasUserData()) {\n            return null;\n        }\n\n        const targetDate = dateString || new Date().toDateString();\n        const recordData = this.RoleModel.userGameData.recordData?.[targetDate];\n\n        return recordData?.[recordType] || null;\n    }\n\n    /**\n     * 🔧 新增：获取指定关卡的今日挑战次数\n     * @param levelId 关卡ID\n     * @param dateString 可选的日期字符串，默认为今日\n     * @returns 该关卡的挑战次数\n     */\n    getLevelChallengeCount(levelId: number, dateString?: string): number {\n        const recordData = this.getRecordData(RecordType.Level, dateString);\n        if (!recordData?.levelDetails) {\n            return 0;\n        }\n\n        const levelKey = `level_${levelId}`;\n        return recordData.levelDetails[levelKey]?.attempts || 0;\n    }\n\n    /**\n     * 🔧 新增：获取当前关卡的今日挑战次数\n     * @param dateString 可选的日期字符串，默认为今日\n     * @returns 当前关卡的挑战次数\n     */\n    getCurrentLevelChallengeCount(dateString?: string): number {\n        const currentLevelId = this.getNextLevelIndex(); // 获取当前要挑战的关卡\n        return this.getLevelChallengeCount(currentLevelId, dateString);\n    }\n\n    // ==================== 工具方法 ====================\n\n    /**\n     * 获取完整的用户游戏数据\n     */\n    getUserGameData(): UserGameData {\n        if (!this.hasUserData()) {\n            throw new Error('用户数据尚未初始化，请等待登录完成');\n        }\n        return this.RoleModel.userGameData;\n    }\n\n    // ==================== 私有方法 ====================\n\n    /**\n     * 检查是否有用户数据\n     */\n    private hasUserData(): boolean {\n        return !!this.RoleModel?.userGameData;\n    }\n\n    /**\n     * 验证网络连接\n     */\n    private validateNetworkConnection(): boolean {\n        if (!smc.net?.hcGame) {\n            oops.log.logError('❌ 网络连接未初始化');\n            return false;\n        }\n        return true;\n    }\n\n    /**\n     * 确保数据结构完整性\n     */\n    private ensureDataIntegrity(data: UserGameData): void {\n        data.key = data.key || 0;\n        data.userName = data.userName || 'Player';\n        data.index = data.index || 0;\n        data.isNewPlayer = data.isNewPlayer ?? true;\n        data.propUseData = data.propUseData || {};\n        data.recordData = data.recordData || {};\n    }\n\n    /**\n     * 深度合并用户数据\n     */\n    private mergeUserData(target: UserGameData, source: UserGameData): void {\n        Object.keys(source).forEach(key => {\n            const sourceValue = source[key as keyof UserGameData];\n            const targetValue = target[key as keyof UserGameData];\n\n            if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {\n                if (targetValue && typeof targetValue === 'object') {\n                    this.mergeUserData(targetValue as any, sourceValue as any);\n                } else {\n                    (target as any)[key] = sourceValue;\n                }\n            } else {\n                (target as any)[key] = sourceValue;\n            }\n        });\n    }\n\n    /**\n     * 创建默认道具数据 - 优化版本\n     */\n    private createDefaultPropData(propType: PropType): any {\n        // 🎁 为新手玩家提供默认道具数量\n        const defaultAmount = this.getNewPlayerDefaultAmount(propType);\n\n        return {\n            amount: defaultAmount,\n            propType,\n            propId: propType,\n            desc: `道具${propType}`,\n            getTime: new Date(),\n            lastResetTime: new Date(),\n            lastUpdateTime: new Date(),\n        };\n    }\n\n    /**\n     * 🎁 获取新手玩家的默认道具数量\n     */\n    private getNewPlayerDefaultAmount(propType: PropType): number {\n        if (!this.isNewPlayer()) {\n            return 0;\n        }\n\n        // 🎯 新手玩家默认道具配置（使用配置常量）\n        switch (propType) {\n            case PropType.PropsMoveOut: // 移出道具\n                return GameConst.newPlayerDefaultProps.moveOut;\n            case PropType.PropsTips: // 提示道具\n                return GameConst.newPlayerDefaultProps.tips;\n            case PropType.PropsReShuffle: // 重新洗牌道具\n                return GameConst.newPlayerDefaultProps.reShuffle;\n            case PropType.PropsDayLeftCount: // 每日挑战剩余次数\n                return GameConst.dayFreeLimts; // 使用配置的默认值\n            case PropType.PropsRevive: // 复活道具\n                return GameConst.newPlayerDefaultProps.revive;\n            case PropType.PropsExp: // 游戏经验\n                return 0;\n            case PropType.PropsCoin: // 玩家金币\n                return 0;\n            default:\n                return 0;\n        }\n    }\n\n    /**\n     * 更新ViewModel\n     */\n    private updateViewModel(): void {\n        if (!this.hasUserData()) {\n            return;\n        }\n\n        this.removeFromViewModel();\n\n        const viewModelData = {\n            userId: this.RoleModel.userGameData.key,\n            userName: this.RoleModel.userGameData.userName,\n            level: this.getGameProgress(),\n            isNewPlayer: this.RoleModel.userGameData.isNewPlayer,\n            index: this.RoleModel.userGameData.index,\n            propUseData: this.RoleModel.userGameData.propUseData,\n            // 扩展其他需要的数据\n            ...this.RoleModel.userGameData,\n        };\n\n        VM.add(viewModelData, 'role');\n        oops.log.logBusiness('🎯 ViewModel已更新');\n    }\n\n    /**\n     * 从ViewModel移除数据\n     */\n    private removeFromViewModel(): void {\n        VM.remove('role');\n    }\n\n    /**\n     * 统一的API错误处理\n     */\n    private handleAPIError(apiName: string, error: any): void {\n        oops.log.logError(`❌ ${apiName} API失败:`, error);\n\n        const errorMessage = error?.message || error?.code?.toString() || '操作失败';\n        oops.gui.toast(errorMessage);\n    }\n}\n"]}