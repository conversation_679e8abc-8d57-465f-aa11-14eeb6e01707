{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/role/Role.ts"], "names": ["oops", "ecs", "LanguageData", "VM", "RecordType", "SceneItemType", "GameStorageConfig", "DataManager", "GameEvent", "smc", "RoleModelComp", "Role", "register", "Entity", "RoleModel", "dataManager", "init", "add", "getInstance", "destroy", "removeFromViewModel", "remove", "loadData", "log", "logBusiness", "validateNetworkConnection", "response", "net", "hcGame", "callApi", "isSucc", "waitForUserDataInitialization", "handleAPIError", "err", "error", "logError", "updateUserData", "newData", "isFirstInitialization", "hasUserData", "ensureDataIntegrity", "userGameData", "mergeUserData", "updateViewModel", "message", "dispatchEvent", "UserDataInitialized", "getPropData", "propType", "validateUserData", "createDefaultPropData", "propData", "propUseData", "defaultData", "getPropsDataByType", "tryCostProp", "args", "showToast", "failureMessageKey", "gui", "toast", "absAmount", "Math", "abs", "amount", "canUse", "canUseProp", "getLangByID", "updatePropData", "updateProp", "reason", "UseProp", "batchUpdateProps", "updates", "update", "success", "getGameProgress", "index", "getPassIndex", "getNextLevelIndex", "currentProgress", "progress", "nextLevel", "Max", "updateGameProgress", "newIndex", "isGM", "targetIndex", "isGm", "GamePass", "oldIndex", "isNewPlayer", "localRecord", "storage", "get<PERSON>son", "UserDumpKey", "String", "completeNewPlayerGuide", "BasicInfoUpdate", "getRecordData", "recordType", "dateString", "targetDate", "Date", "toDateString", "recordData", "getLevelChallengeCount", "levelId", "Level", "levelDetails", "level<PERSON><PERSON>", "attempts", "getCurrentLevelChallengeCount", "currentLevelId", "getUserGameData", "Error", "maxRetries", "retryCount", "Promise", "resolve", "setTimeout", "log<PERSON>arn", "data", "key", "userName", "target", "source", "Object", "keys", "for<PERSON>ach", "sourceValue", "targetValue", "Array", "isArray", "propId", "desc", "getTime", "lastResetTime", "lastUpdateTime", "viewModelData", "userId", "level", "apiName", "errorMessage", "code", "toString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,E,iBAAAA,E;;AAGLC,MAAAA,U,iBAAAA,U;AAEAC,MAAAA,a,iBAAAA,a;;AAGKC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,G,kBAAAA,G;;AACAC,MAAAA,a,kBAAAA,a;;;;;;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;sBAEaC,I,WADZ;AAAA;AAAA,sBAAIC,QAAJ,CAAa,MAAb,C,gBAAD,MACaD,IADb,SAC0B;AAAA;AAAA,sBAAIE,MAD9B,CACqC;AAAA;AAAA;AAAA,eACjCC,SADiC;AAAA,eAEzBC,WAFyB;AAAA;;AAIjC;AAEUC,QAAAA,IAAI,GAAG;AACb,eAAKC,GAAL;AAAA;AAAA;AACA,eAAKF,WAAL,GAAmB;AAAA;AAAA,0CAAYG,WAAZ,EAAnB;AACH;;AAEDC,QAAAA,OAAO,GAAS;AACZ,eAAKC,mBAAL;AACA,eAAKC,MAAL;AAAA;AAAA;AACH,SAdgC,CAgBjC;;AAEA;AACJ;AACA;AACA;;;AACUC,QAAAA,QAAQ,GAAqB;AAAA;;AAAA;AAC/B;AAAA;AAAA,8BAAKC,GAAL,CAASC,WAAT,CAAqB,gBAArB;;AAEA,gBAAI,CAAC,KAAI,CAACC,yBAAL,EAAL,EAAuC;AACnC,qBAAO,KAAP;AACH;;AAED,gBAAI;AACA,kBAAMC,QAAQ,SAAS;AAAA;AAAA,8BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,EAAnC,CAAvB;;AAEA,kBAAIH,QAAQ,CAACI,MAAb,EAAqB;AACjB,sBAAM,KAAI,CAACC,6BAAL,EAAN;AACA,uBAAO,IAAP;AACH,eAHD,MAGO;AACH,gBAAA,KAAI,CAACC,cAAL,CAAoB,UAApB,EAAgCN,QAAQ,CAACO,GAAzC;;AACA,uBAAO,KAAP;AACH;AACJ,aAVD,CAUE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKX,GAAL,CAASY,QAAT,CAAkB,aAAlB,EAAiCD,KAAjC;AACA,qBAAO,KAAP;AACH;AApB8B;AAqBlC;AAED;AACJ;AACA;;;AACIE,QAAAA,cAAc,CAACC,OAAD,EAA8B;AACxC,cAAMC,qBAAqB,GAAG,CAAC,KAAKC,WAAL,EAA/B,CADwC,CAGxC;;AACA,eAAKC,mBAAL,CAAyBH,OAAzB,EAJwC,CAMxC;;AACA,cAAI,KAAKvB,SAAL,CAAe2B,YAAnB,EAAiC;AAC7B,iBAAKC,aAAL,CAAmB,KAAK5B,SAAL,CAAe2B,YAAlC,EAAgDJ,OAAhD;AACH,WAFD,MAEO;AACH,iBAAKvB,SAAL,CAAe2B,YAAf,GAA8BJ,OAA9B;AACH,WAXuC,CAaxC;;;AACA,eAAKM,eAAL,GAdwC,CAgBxC;;AACA,cAAIL,qBAAJ,EAA2B;AACvB;AAAA;AAAA,8BAAKf,GAAL,CAASC,WAAT,CAAqB,gBAArB;AACA;AAAA;AAAA,8BAAKoB,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,wCAAUC,mBAArC,EAA0D,KAAKhC,SAAL,CAAe2B,YAAzE;AACH;AACJ,SArEgC,CAuEjC;;AAEA;AACJ;AACA;;;AACIM,QAAAA,WAAW,CAACC,QAAD,EAA0B;AAAA;;AACjC,cAAI,CAAC,KAAKC,gBAAL,EAAL,EAA8B;AAC1B,mBAAO,KAAKC,qBAAL,CAA2BF,QAA3B,CAAP;AACH;;AAED,cAAMG,QAAQ,4BAAG,KAAKrC,SAAL,CAAe2B,YAAf,CAA4BW,WAA/B,qBAAG,sBAA0CJ,QAA1C,CAAjB;;AACA,cAAI,CAACG,QAAL,EAAe;AACX;AACA,gBAAME,WAAW,GAAG,KAAKH,qBAAL,CAA2BF,QAA3B,CAApB;AACA,iBAAKlC,SAAL,CAAe2B,YAAf,CAA4BW,WAA5B,GAA0C,KAAKtC,SAAL,CAAe2B,YAAf,CAA4BW,WAA5B,IAA2C,EAArF;AACA,iBAAKtC,SAAL,CAAe2B,YAAf,CAA4BW,WAA5B,CAAwCJ,QAAxC,IAAoDK,WAApD;AACA,mBAAOA,WAAP;AACH;;AAED,iBAAOF,QAAP;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,kBAAkB,CAACN,QAAD,EAA0B;AACxC,iBAAO,KAAKD,WAAL,CAAiBC,QAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIO,QAAAA,WAAW,CACPC,IADO,EAEPC,SAFO,EAGPC,iBAHO,EAIA;AAAA,cAFPD,SAEO;AAFPA,YAAAA,SAEO,GAFc,IAEd;AAAA;;AACP,cAAI,CAAC,KAAKR,gBAAL,EAAL,EAA8B;AAC1B,gBAAIQ,SAAJ,EAAe;AACX;AAAA;AAAA,gCAAKE,GAAL,CAASC,KAAT,CAAe,UAAf;AACH;;AACD,mBAAO,KAAP;AACH;;AAED,cAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAASP,IAAI,CAACQ,MAAd,CAAlB;AACA,cAAMC,MAAM,GAAG,KAAKC,UAAL,CAAgBV,IAAI,CAACR,QAArB,EAA+Ba,SAA/B,CAAf;;AAEA,cAAI,CAACI,MAAD,IAAWR,SAAX,IAAwBC,iBAA5B,EAA+C;AAC3C;AAAA;AAAA,8BAAKC,GAAL,CAASC,KAAT,CAAe;AAAA;AAAA,8CAAaO,WAAb,CAAyBT,iBAAzB,KAA+CA,iBAA9D;AACH;;AAED,iBAAOO,MAAP;AACH;AAED;AACJ;AACA;;;AACUG,QAAAA,cAAc,CAACZ,IAAD,EAIC;AAAA;;AAAA;AACjB,yBAAa,MAAI,CAACa,UAAL,CAAgBb,IAAI,CAACR,QAArB,EAA+BQ,IAAI,CAACQ,MAApC,EAA4CR,IAAI,CAACc,MAAjD,CAAb;AADiB;AAEpB;AAED;AACJ;AACA;;;AACIJ,QAAAA,UAAU,CAAClB,QAAD,EAAqBgB,MAArB,EAA8C;AACpD,cAAMb,QAAQ,GAAG,KAAKJ,WAAL,CAAiBC,QAAjB,CAAjB;AACA,iBAAOG,QAAQ,CAACa,MAAT,IAAmBF,IAAI,CAACC,GAAL,CAASC,MAAT,CAA1B;AACH;AAED;AACJ;AACA;;;AACUK,QAAAA,UAAU,CAACrB,QAAD,EAAqBgB,MAArB,EAAqCM,MAArC,EAAwE;AAAA;;AAAA;AACpF;AACA,gBAAIN,MAAM,GAAG,CAAT,IAAc,CAAC,MAAI,CAACE,UAAL,CAAgBlB,QAAhB,EAA0Bc,IAAI,CAACC,GAAL,CAASC,MAAT,CAA1B,CAAnB,EAAgE;AAC5D;AAAA;AAAA,gCAAKL,GAAL,CAASC,KAAT,CAAe;AAAA;AAAA,gDAAaO,WAAb,CAAyB,gBAAzB,CAAf;AACA,qBAAO,KAAP;AACH;;AAED,gBAAI;AACA,kBAAMzC,QAAQ,SAAS;AAAA;AAAA,8BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,YAAvB,EAAqC;AACxDmB,gBAAAA,QADwD;AAExDgB,gBAAAA,MAFwD;AAGxDM,gBAAAA,MAAM,EAAEA,MAAM,IAAI;AAHsC,eAArC,CAAvB;;AAMA,kBAAI5C,QAAQ,CAACI,MAAb,EAAqB;AACjB;AAAA;AAAA,kCAAKP,GAAL,CAASC,WAAT,mDACiBwB,QADjB,UAC6BgB,MAAM,GAAG,CAAT,GAAa,GAAb,GAAmB,EADhD,IACqDA,MADrD,EADiB,CAKjB;;AACA,oBAAIA,MAAM,GAAG,CAAb,EAAgB;AACZ;AAAA;AAAA,oCAAKpB,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,8CAAU0B,OAArC,EAA8C;AAAEvB,oBAAAA,QAAF;AAAYgB,oBAAAA;AAAZ,mBAA9C;AACH;;AAED,uBAAO,IAAP;AACH,eAXD,MAWO;AACH,gBAAA,MAAI,CAAChC,cAAL,CAAoB,YAApB,EAAkCN,QAAQ,CAACO,GAA3C;;AACA,uBAAO,KAAP;AACH;AACJ,aAtBD,CAsBE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKX,GAAL,CAASY,QAAT,CAAkB,WAAlB,EAA+BD,KAA/B;AACA;AAAA;AAAA,gCAAKyB,GAAL,CAASC,KAAT,CAAe,UAAf;AACA,qBAAO,KAAP;AACH;AAjCmF;AAkCvF;AAED;AACJ;AACA;;;AACUY,QAAAA,gBAAgB,CAClBC,OADkB,EAEF;AAAA;;AAAA;AAChB,iBAAK,IAAMC,MAAX,IAAqBD,OAArB,EAA8B;AAC1B,kBAAME,OAAO,SAAS,MAAI,CAACN,UAAL,CAAgBK,MAAM,CAAC1B,QAAvB,EAAiC0B,MAAM,CAACV,MAAxC,EAAgDU,MAAM,CAACJ,MAAvD,CAAtB;;AACA,kBAAI,CAACK,OAAL,EAAc;AACV,uBAAO,KAAP;AACH;AACJ;;AACD,mBAAO,IAAP;AAPgB;AAQnB,SAxMgC,CA0MjC;;AAEA;AACJ;AACA;;;AACIC,QAAAA,eAAe,GAAW;AACtB,cAAI,CAAC,KAAK3B,gBAAL,EAAL,EAA8B;AAC1B,mBAAO,CAAP;AACH;;AACD,iBAAO,KAAKnC,SAAL,CAAe2B,YAAf,CAA4BoC,KAA5B,IAAqC,CAA5C;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,YAAY,GAAW;AACnB,iBAAO,KAAKF,eAAL,EAAP;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,iBAAiB,CAACC,eAAD,EAAmC;AAChD,cAAMC,QAAQ,GAAGD,eAAH,WAAGA,eAAH,GAAsB,KAAKJ,eAAL,EAApC;AACA,cAAMM,SAAS,GAAG,CAACD,QAAQ,GAAG,CAAZ,IAAiB;AAAA;AAAA,8CAAcE,GAAjD;AACA,iBAAOD,SAAS,KAAK,CAAd,GAAkB,CAAlB,GAAsBA,SAA7B;AACH;AAED;AACJ;AACA;;;AACUE,QAAAA,kBAAkB,CAACC,QAAD,EAAoBC,IAApB,EAA6D;AAAA;;AAAA;AAAA,gBAAzCA,IAAyC;AAAzCA,cAAAA,IAAyC,GAAzB,KAAyB;AAAA;;AACjF,gBAAMC,WAAW,GAAGF,QAAH,WAAGA,QAAH,GAAe,MAAI,CAACT,eAAL,KAAyB,CAAzD;;AAEA,gBAAI;AACA,kBAAMlD,QAAQ,SAAS;AAAA;AAAA,8BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,gBAAvB,EAAyC;AAC5DgD,gBAAAA,KAAK,EAAEU,WADqD;AAE5DC,gBAAAA,IAAI,EAAEF;AAFsD,eAAzC,CAAvB;;AAKA,kBAAI5D,QAAQ,CAACI,MAAb,EAAqB;AACjB;AAAA;AAAA,kCAAKP,GAAL,CAASC,WAAT,+DAAoC+D,WAApC,EADiB,CAGjB;;AACA;AAAA;AAAA,kCAAK3C,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,4CAAU4C,QAArC,EAA+C;AAC3CC,kBAAAA,QAAQ,EAAE,MAAI,CAACd,eAAL,EADiC;AAE3CS,kBAAAA,QAAQ,EAAEE,WAFiC;AAG3CC,kBAAAA,IAAI,EAAEF;AAHqC,iBAA/C;AAMA,uBAAO,IAAP;AACH,eAXD,MAWO;AACH,gBAAA,MAAI,CAACtD,cAAL,CAAoB,gBAApB,EAAsCN,QAAQ,CAACO,GAA/C;;AACA,uBAAO,KAAP;AACH;AACJ,aArBD,CAqBE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKX,GAAL,CAASY,QAAT,CAAkB,aAAlB,EAAiCD,KAAjC;AACA;AAAA;AAAA,gCAAKyB,GAAL,CAASC,KAAT,CAAe,UAAf;AACA,qBAAO,KAAP;AACH;AA5BgF;AA6BpF,SAtQgC,CAwQjC;;AAEA;AACJ;AACA;;;AACI+B,QAAAA,WAAW,GAAY;AACnB;AACA,cAAI,KAAKpD,WAAL,EAAJ,EAAwB;AAAA;;AACpB,6CAAO,KAAKzB,SAAL,CAAe2B,YAAf,CAA4BkD,WAAnC,qCAAkD,IAAlD;AACH,WAJkB,CAMnB;;;AACA,cAAMC,WAAW,GAAG;AAAA;AAAA,4BAAKC,OAAL,CAAaC,OAAb,CAAqB;AAAA;AAAA,sDAAkBC,WAAvC,EAAoD,CAApD,CAApB;AACA,iBAAO,CAACH,WAAD,IAAgBI,MAAM,CAACJ,WAAD,CAAN,KAAwB,GAA/C;AACH;AAED;AACJ;AACA;;;AACUK,QAAAA,sBAAsB,GAAqB;AAAA;;AAAA;AAC7C,gBAAI,CAAC,MAAI,CAACN,WAAL,EAAL,EAAyB;AACrB;AAAA;AAAA,gCAAKpE,GAAL,CAASC,WAAT,CAAqB,aAArB;AACA,qBAAO,IAAP;AACH;;AAED,gBAAI;AACA,kBAAME,QAAQ,SAAS;AAAA;AAAA,8BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,sBAAvB,EAA+C;AAClE8D,gBAAAA,WAAW,EAAE;AADqD,eAA/C,CAAvB;;AAIA,kBAAIjE,QAAQ,CAACI,MAAb,EAAqB;AACjB;AACA,oBAAI,MAAI,CAAChB,SAAL,IAAkB,MAAI,CAACA,SAAL,CAAe2B,YAArC,EAAmD;AAC/C,kBAAA,MAAI,CAAC3B,SAAL,CAAe2B,YAAf,CAA4BkD,WAA5B,GAA0C,KAA1C;AACA;AAAA;AAAA,oCAAKpE,GAAL,CAASC,WAAT,CAAqB,oBAArB;AACH;;AAED;AAAA;AAAA,kCAAKD,GAAL,CAASC,WAAT,CAAqB,UAArB;AACA;AAAA;AAAA,kCAAKoB,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,4CAAUqD,eAArC,EAAsD;AAAEP,kBAAAA,WAAW,EAAE;AAAf,iBAAtD;AACA,uBAAO,IAAP;AACH,eAVD,MAUO;AACH,gBAAA,MAAI,CAAC3D,cAAL,CAAoB,sBAApB,EAA4CN,QAAQ,CAACO,GAArD;;AACA,uBAAO,KAAP;AACH;AACJ,aAnBD,CAmBE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKX,GAAL,CAASY,QAAT,CAAkB,aAAlB,EAAiCD,KAAjC;AACA;AAAA;AAAA,gCAAKyB,GAAL,CAASC,KAAT,CAAe,UAAf;AACA,qBAAO,KAAP;AACH;AA7B4C;AA8BhD,SAzTgC,CA2TjC;;AAEA;AACJ;AACA;;;AACIuC,QAAAA,aAAa,CAACC,UAAD,EAAyBC,UAAzB,EAAqE;AAAA;;AAC9E,cAAI,CAAC,KAAKpD,gBAAL,EAAL,EAA8B;AAC1B,mBAAO,IAAP;AACH;;AAED,cAAMqD,UAAU,GAAGD,UAAU,IAAI,IAAIE,IAAJ,GAAWC,YAAX,EAAjC;AACA,cAAMC,UAAU,6BAAG,KAAK3F,SAAL,CAAe2B,YAAf,CAA4BgE,UAA/B,qBAAG,uBAAyCH,UAAzC,CAAnB;AAEA,iBAAO,CAAAG,UAAU,QAAV,YAAAA,UAAU,CAAGL,UAAH,CAAV,KAA4B,IAAnC;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIM,QAAAA,sBAAsB,CAACC,OAAD,EAAkBN,UAAlB,EAA+C;AAAA;;AACjE,cAAMI,UAAU,GAAG,KAAKN,aAAL,CAAmB;AAAA;AAAA,wCAAWS,KAA9B,EAAqCP,UAArC,CAAnB;;AACA,cAAI,EAACI,UAAD,YAACA,UAAU,CAAEI,YAAb,CAAJ,EAA+B;AAC3B,mBAAO,CAAP;AACH;;AAED,cAAMC,QAAQ,cAAYH,OAA1B;AACA,iBAAO,0BAAAF,UAAU,CAACI,YAAX,CAAwBC,QAAxB,4CAAmCC,QAAnC,KAA+C,CAAtD;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,6BAA6B,CAACX,UAAD,EAA8B;AACvD,cAAMY,cAAc,GAAG,KAAKlC,iBAAL,EAAvB,CADuD,CACN;;AACjD,iBAAO,KAAK2B,sBAAL,CAA4BO,cAA5B,EAA4CZ,UAA5C,CAAP;AACH,SAnWgC,CAqWjC;;AAEA;AACJ;AACA;;;AACIa,QAAAA,eAAe,GAAiB;AAC5B,cAAI,CAAC,KAAKjE,gBAAL,EAAL,EAA8B;AAC1B,kBAAM,IAAIkE,KAAJ,CAAU,mBAAV,CAAN;AACH;;AACD,iBAAO,KAAKrG,SAAL,CAAe2B,YAAtB;AACH,SA/WgC,CAiXjC;;AAEA;AACJ;AACA;;;AACYQ,QAAAA,gBAAgB,GAAY;AAAA;;AAChC,iBAAO,CAAC,qBAAC,KAAKnC,SAAN,aAAC,gBAAgB2B,YAAjB,CAAR;AACH;AAED;AACJ;AACA;;;AACYF,QAAAA,WAAW,GAAY;AAC3B,iBAAO,KAAKU,gBAAL,EAAP;AACH;AAED;AACJ;AACA;;;AACYxB,QAAAA,yBAAyB,GAAY;AAAA;;AACzC,cAAI,UAAC;AAAA;AAAA,0BAAIE,GAAL,aAAC,KAASC,MAAV,CAAJ,EAAsB;AAClB;AAAA;AAAA,8BAAKL,GAAL,CAASY,QAAT,CAAkB,YAAlB;AACA,mBAAO,KAAP;AACH;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACkBJ,QAAAA,6BAA6B,GAAkB;AAAA;;AAAA;AACzD,gBAAMqF,UAAU,GAAG,EAAnB,CADyD,CAClC;;AACvB,gBAAIC,UAAU,GAAG,CAAjB;;AAEA,mBAAOA,UAAU,GAAGD,UAApB,EAAgC;AAC5B,kBAAI,MAAI,CAAC7E,WAAL,EAAJ,EAAwB;AACpB;AACH,eAH2B,CAK5B;;;AACA,oBAAM,IAAI+E,OAAJ,CAAYC,OAAO,IAAIC,UAAU,CAACD,OAAD,EAAU,EAAV,CAAjC,CAAN;AACAF,cAAAA,UAAU;;AAEV,kBAAIA,UAAU,GAAG,CAAb,KAAmB,CAAvB,EAA0B;AACtB;AAAA;AAAA,kCAAK9F,GAAL,CAASC,WAAT,uEAAuC6F,UAAvC,SAAqDD,UAArD;AACH;AACJ,aAhBwD,CAkBzD;;;AACA;AAAA;AAAA,8BAAK7F,GAAL,CAASkG,OAAT,CAAiB,oBAAjB;AAnByD;AAoB5D;AAED;AACJ;AACA;;;AACYjF,QAAAA,mBAAmB,CAACkF,IAAD,EAA2B;AAAA;;AAClDA,UAAAA,IAAI,CAACC,GAAL,GAAWD,IAAI,CAACC,GAAL,IAAY,CAAvB;AACAD,UAAAA,IAAI,CAACE,QAAL,GAAgBF,IAAI,CAACE,QAAL,IAAiB,QAAjC;AACAF,UAAAA,IAAI,CAAC7C,KAAL,GAAa6C,IAAI,CAAC7C,KAAL,IAAc,CAA3B;AACA6C,UAAAA,IAAI,CAAC/B,WAAL,wBAAmB+B,IAAI,CAAC/B,WAAxB,gCAAuC,IAAvC;AACA+B,UAAAA,IAAI,CAACtE,WAAL,GAAmBsE,IAAI,CAACtE,WAAL,IAAoB,EAAvC;AACAsE,UAAAA,IAAI,CAACjB,UAAL,GAAkBiB,IAAI,CAACjB,UAAL,IAAmB,EAArC;AACH;AAED;AACJ;AACA;;;AACY/D,QAAAA,aAAa,CAACmF,MAAD,EAAuBC,MAAvB,EAAmD;AACpEC,UAAAA,MAAM,CAACC,IAAP,CAAYF,MAAZ,EAAoBG,OAApB,CAA4BN,GAAG,IAAI;AAC/B,gBAAMO,WAAW,GAAGJ,MAAM,CAACH,GAAD,CAA1B;AACA,gBAAMQ,WAAW,GAAGN,MAAM,CAACF,GAAD,CAA1B;;AAEA,gBAAIO,WAAW,IAAI,OAAOA,WAAP,KAAuB,QAAtC,IAAkD,CAACE,KAAK,CAACC,OAAN,CAAcH,WAAd,CAAvD,EAAmF;AAC/E,kBAAIC,WAAW,IAAI,OAAOA,WAAP,KAAuB,QAA1C,EAAoD;AAChD,qBAAKzF,aAAL,CAAmByF,WAAnB,EAAuCD,WAAvC;AACH,eAFD,MAEO;AACFL,gBAAAA,MAAD,CAAgBF,GAAhB,IAAuBO,WAAvB;AACH;AACJ,aAND,MAMO;AACFL,cAAAA,MAAD,CAAgBF,GAAhB,IAAuBO,WAAvB;AACH;AACJ,WAbD;AAcH;AAED;AACJ;AACA;;;AACYhF,QAAAA,qBAAqB,CAACF,QAAD,EAA0B;AACnD,iBAAO;AACHgB,YAAAA,MAAM,EAAE,CADL;AAEHhB,YAAAA,QAFG;AAGHsF,YAAAA,MAAM,EAAEtF,QAHL;AAIHuF,YAAAA,IAAI,mBAAOvF,QAJR;AAKHwF,YAAAA,OAAO,EAAE,IAAIjC,IAAJ,EALN;AAMHkC,YAAAA,aAAa,EAAE,IAAIlC,IAAJ,EANZ;AAOHmC,YAAAA,cAAc,EAAE,IAAInC,IAAJ;AAPb,WAAP;AASH;AAED;AACJ;AACA;;;AACY5D,QAAAA,eAAe,GAAS;AAC5B,cAAI,CAAC,KAAKJ,WAAL,EAAL,EAAyB;AACrB;AACH;;AAED,eAAKnB,mBAAL;;AAEA,cAAMuH,aAAa;AACfC,YAAAA,MAAM,EAAE,KAAK9H,SAAL,CAAe2B,YAAf,CAA4BkF,GADrB;AAEfC,YAAAA,QAAQ,EAAE,KAAK9G,SAAL,CAAe2B,YAAf,CAA4BmF,QAFvB;AAGfiB,YAAAA,KAAK,EAAE,KAAKjE,eAAL,EAHQ;AAIfe,YAAAA,WAAW,EAAE,KAAK7E,SAAL,CAAe2B,YAAf,CAA4BkD,WAJ1B;AAKfd,YAAAA,KAAK,EAAE,KAAK/D,SAAL,CAAe2B,YAAf,CAA4BoC,KALpB;AAMfzB,YAAAA,WAAW,EAAE,KAAKtC,SAAL,CAAe2B,YAAf,CAA4BW;AAN1B,aAQZ,KAAKtC,SAAL,CAAe2B,YARH,CAAnB;;AAWA;AAAA;AAAA,wBAAGxB,GAAH,CAAO0H,aAAP,EAAsB,MAAtB;AACA;AAAA;AAAA,4BAAKpH,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH;AAED;AACJ;AACA;;;AACYJ,QAAAA,mBAAmB,GAAS;AAChC;AAAA;AAAA,wBAAGC,MAAH,CAAU,MAAV;AACH;AAED;AACJ;AACA;;;AACYW,QAAAA,cAAc,CAAC8G,OAAD,EAAkB5G,KAAlB,EAAoC;AAAA;;AACtD;AAAA;AAAA,4BAAKX,GAAL,CAASY,QAAT,aAAuB2G,OAAvB,wBAAyC5G,KAAzC;AAEA,cAAM6G,YAAY,GAAG,CAAA7G,KAAK,QAAL,YAAAA,KAAK,CAAEU,OAAP,MAAkBV,KAAlB,2BAAkBA,KAAK,CAAE8G,IAAzB,qBAAkB,YAAaC,QAAb,EAAlB,KAA6C,MAAlE;AACA;AAAA;AAAA,4BAAKtF,GAAL,CAASC,KAAT,CAAemF,YAAf;AACH;;AA5fgC,O", "sourcesContent": ["import { oops } from '../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { ecs } from '../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';\nimport { LanguageData } from '../../../../extensions/oops-plugin-framework/assets/libs/gui/language/LanguageData';\nimport { VM } from '../../../../extensions/oops-plugin-framework/assets/libs/model-view/ViewModel';\nimport {\n    PropType,\n    RecordType,\n    RecordTypeData,\n    SceneItemType,\n    UserGameData,\n} from '../../tsrpc/protocols/base';\nimport { GameStorageConfig } from '../common/config/GameStorageConfig';\nimport { DataManager } from '../common/DataManager';\nimport { GameEvent } from '../common/Enum';\nimport { smc } from '../common/SingletonModuleComp';\nimport { RoleModelComp } from './model/RoleModelComp';\n\n/**\n * 角色管理器 - 重构版本\n * 职责：\n * 1. 用户数据的加载、更新和管理\n * 2. 道具系统的操作和验证\n * 3. 游戏进度的管理\n * 4. ViewModel的数据绑定\n */\**************('Role')\nexport class Role extends ecs.Entity {\n    RoleModel!: RoleModelComp;\n    private dataManager!: DataManager;\n\n    // ==================== 初始化 ====================\n\n    protected init() {\n        this.add(RoleModelComp);\n        this.dataManager = DataManager.getInstance();\n    }\n\n    destroy(): void {\n        this.removeFromViewModel();\n        this.remove(RoleModelComp);\n    }\n\n    // ==================== 数据加载 ====================\n\n    /**\n     * 加载用户数据\n     * @returns 是否加载成功\n     */\n    async loadData(): Promise<boolean> {\n        oops.log.logBusiness('🔄 开始加载用户数据...');\n\n        if (!this.validateNetworkConnection()) {\n            return false;\n        }\n\n        try {\n            const response = await smc.net.hcGame.callApi('UserInfo', {});\n\n            if (response.isSucc) {\n                await this.waitForUserDataInitialization();\n                return true;\n            } else {\n                this.handleAPIError('UserInfo', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 用户数据加载异常:', error);\n            return false;\n        }\n    }\n\n    /**\n     * 更新用户数据（由DataManager调用）\n     */\n    updateUserData(newData: UserGameData): void {\n        const isFirstInitialization = !this.hasUserData();\n\n        // 确保数据结构完整\n        this.ensureDataIntegrity(newData);\n\n        // 合并数据\n        if (this.RoleModel.userGameData) {\n            this.mergeUserData(this.RoleModel.userGameData, newData);\n        } else {\n            this.RoleModel.userGameData = newData;\n        }\n\n        // 更新ViewModel\n        this.updateViewModel();\n\n        // 首次初始化触发事件\n        if (isFirstInitialization) {\n            oops.log.logBusiness('🎉 用户数据首次初始化完成');\n            oops.message.dispatchEvent(GameEvent.UserDataInitialized, this.RoleModel.userGameData);\n        }\n    }\n\n    // ==================== 道具系统 ====================\n\n    /**\n     * 获取道具数据\n     */\n    getPropData(propType: PropType): any {\n        if (!this.validateUserData()) {\n            return this.createDefaultPropData(propType);\n        }\n\n        const propData = this.RoleModel.userGameData.propUseData?.[propType];\n        if (!propData) {\n            // 创建默认道具数据\n            const defaultData = this.createDefaultPropData(propType);\n            this.RoleModel.userGameData.propUseData = this.RoleModel.userGameData.propUseData || {};\n            this.RoleModel.userGameData.propUseData[propType] = defaultData;\n            return defaultData;\n        }\n\n        return propData;\n    }\n\n    /**\n     * 获取道具数据（别名方法，兼容现有代码）\n     */\n    getPropsDataByType(propType: PropType): any {\n        return this.getPropData(propType);\n    }\n\n    /**\n     * 尝试消耗道具（检查数量并显示提示）\n     * @param args 道具参数（包含类型和数量）\n     * @param showToast 是否显示提示\n     * @param failureMessageKey 失败时的消息键\n     * @returns 是否可以消耗\n     */\n    tryCostProp(\n        args: { propType: PropType; amount: number },\n        showToast: boolean = true,\n        failureMessageKey?: string\n    ): boolean {\n        if (!this.validateUserData()) {\n            if (showToast) {\n                oops.gui.toast('用户数据未初始化');\n            }\n            return false;\n        }\n\n        const absAmount = Math.abs(args.amount);\n        const canUse = this.canUseProp(args.propType, absAmount);\n\n        if (!canUse && showToast && failureMessageKey) {\n            oops.gui.toast(LanguageData.getLangByID(failureMessageKey) || failureMessageKey);\n        }\n\n        return canUse;\n    }\n\n    /**\n     * 更新道具数据（别名方法，兼容现有代码）\n     */\n    async updatePropData(args: {\n        propType: PropType;\n        amount: number;\n        reason?: string;\n    }): Promise<boolean> {\n        return await this.updateProp(args.propType, args.amount, args.reason);\n    }\n\n    /**\n     * 检查道具是否足够\n     */\n    canUseProp(propType: PropType, amount: number): boolean {\n        const propData = this.getPropData(propType);\n        return propData.amount >= Math.abs(amount);\n    }\n\n    /**\n     * 更新道具数量\n     */\n    async updateProp(propType: PropType, amount: number, reason?: string): Promise<boolean> {\n        // 消耗操作前检查数量\n        if (amount < 0 && !this.canUseProp(propType, Math.abs(amount))) {\n            oops.gui.toast(LanguageData.getLangByID('UseLimitsDaily'));\n            return false;\n        }\n\n        try {\n            const response = await smc.net.hcGame.callApi('UpdateProp', {\n                propType,\n                amount,\n                reason: reason || 'player_action',\n            });\n\n            if (response.isSucc) {\n                oops.log.logBusiness(\n                    `✅ 道具更新成功: ${propType} ${amount > 0 ? '+' : ''}${amount}`\n                );\n\n                // 触发道具使用事件\n                if (amount < 0) {\n                    oops.message.dispatchEvent(GameEvent.UseProp, { propType, amount });\n                }\n\n                return true;\n            } else {\n                this.handleAPIError('UpdateProp', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 道具更新异常:', error);\n            oops.gui.toast('网络错误，请重试');\n            return false;\n        }\n    }\n\n    /**\n     * 批量更新道具\n     */\n    async batchUpdateProps(\n        updates: Array<{ propType: PropType; amount: number; reason?: string }>\n    ): Promise<boolean> {\n        for (const update of updates) {\n            const success = await this.updateProp(update.propType, update.amount, update.reason);\n            if (!success) {\n                return false;\n            }\n        }\n        return true;\n    }\n\n    // ==================== 游戏进度 ====================\n\n    /**\n     * 获取当前通关进度\n     */\n    getGameProgress(): number {\n        if (!this.validateUserData()) {\n            return 0;\n        }\n        return this.RoleModel.userGameData.index || 0;\n    }\n\n    /**\n     * 获取已通过的关卡索引（别名方法，兼容现有代码）\n     */\n    getPassIndex(): number {\n        return this.getGameProgress();\n    }\n\n    /**\n     * 获取下一关卡索引（循环通关）\n     */\n    getNextLevelIndex(currentProgress?: number): number {\n        const progress = currentProgress ?? this.getGameProgress();\n        const nextLevel = (progress + 1) % SceneItemType.Max;\n        return nextLevel === 0 ? 1 : nextLevel;\n    }\n\n    /**\n     * 更新游戏通关进度\n     */\n    async updateGameProgress(newIndex?: number, isGM: boolean = false): Promise<boolean> {\n        const targetIndex = newIndex ?? this.getGameProgress() + 1;\n\n        try {\n            const response = await smc.net.hcGame.callApi('UpdateProgress', {\n                index: targetIndex,\n                isGm: isGM,\n            });\n\n            if (response.isSucc) {\n                oops.log.logBusiness(`✅ 游戏进度更新成功: ${targetIndex}`);\n\n                // 触发通关事件\n                oops.message.dispatchEvent(GameEvent.GamePass, {\n                    oldIndex: this.getGameProgress(),\n                    newIndex: targetIndex,\n                    isGm: isGM,\n                });\n\n                return true;\n            } else {\n                this.handleAPIError('UpdateProgress', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 游戏进度更新异常:', error);\n            oops.gui.toast('网络错误，请重试');\n            return false;\n        }\n    }\n\n    // ==================== 新手引导 ====================\n\n    /**\n     * 检查是否为新玩家\n     */\n    isNewPlayer(): boolean {\n        // 优先使用服务端数据\n        if (this.hasUserData()) {\n            return this.RoleModel.userGameData.isNewPlayer ?? true;\n        }\n\n        // 备用方案：检查本地存储\n        const localRecord = oops.storage.getJson(GameStorageConfig.UserDumpKey, 0);\n        return !localRecord || String(localRecord) === '0';\n    }\n\n    /**\n     * 完成新手引导\n     */\n    async completeNewPlayerGuide(): Promise<boolean> {\n        if (!this.isNewPlayer()) {\n            oops.log.logBusiness('✅ 用户已完成新手引导');\n            return true;\n        }\n\n        try {\n            const response = await smc.net.hcGame.callApi('GameUpdateSimpleData', {\n                isNewPlayer: false,\n            });\n\n            if (response.isSucc) {\n                // 🎯 立即更新本地数据，确保状态同步\n                if (this.RoleModel && this.RoleModel.userGameData) {\n                    this.RoleModel.userGameData.isNewPlayer = false;\n                    oops.log.logBusiness('🔄 本地新手状态已更新为false');\n                }\n\n                oops.log.logBusiness('✅ 新手引导完成');\n                oops.message.dispatchEvent(GameEvent.BasicInfoUpdate, { isNewPlayer: false });\n                return true;\n            } else {\n                this.handleAPIError('GameUpdateSimpleData', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 新手状态更新异常:', error);\n            oops.gui.toast('网络错误，请重试');\n            return false;\n        }\n    }\n\n    // ==================== 记录数据 ====================\n\n    /**\n     * 获取指定日期的记录数据\n     */\n    getRecordData(recordType: RecordType, dateString?: string): RecordTypeData | null {\n        if (!this.validateUserData()) {\n            return null;\n        }\n\n        const targetDate = dateString || new Date().toDateString();\n        const recordData = this.RoleModel.userGameData.recordData?.[targetDate];\n\n        return recordData?.[recordType] || null;\n    }\n\n    /**\n     * 🔧 新增：获取指定关卡的今日挑战次数\n     * @param levelId 关卡ID\n     * @param dateString 可选的日期字符串，默认为今日\n     * @returns 该关卡的挑战次数\n     */\n    getLevelChallengeCount(levelId: number, dateString?: string): number {\n        const recordData = this.getRecordData(RecordType.Level, dateString);\n        if (!recordData?.levelDetails) {\n            return 0;\n        }\n\n        const levelKey = `level_${levelId}`;\n        return recordData.levelDetails[levelKey]?.attempts || 0;\n    }\n\n    /**\n     * 🔧 新增：获取当前关卡的今日挑战次数\n     * @param dateString 可选的日期字符串，默认为今日\n     * @returns 当前关卡的挑战次数\n     */\n    getCurrentLevelChallengeCount(dateString?: string): number {\n        const currentLevelId = this.getNextLevelIndex(); // 获取当前要挑战的关卡\n        return this.getLevelChallengeCount(currentLevelId, dateString);\n    }\n\n    // ==================== 工具方法 ====================\n\n    /**\n     * 获取完整的用户游戏数据\n     */\n    getUserGameData(): UserGameData {\n        if (!this.validateUserData()) {\n            throw new Error('用户数据尚未初始化，请等待登录完成');\n        }\n        return this.RoleModel.userGameData;\n    }\n\n    // ==================== 私有方法 ====================\n\n    /**\n     * 验证用户数据是否可用\n     */\n    private validateUserData(): boolean {\n        return !!this.RoleModel?.userGameData;\n    }\n\n    /**\n     * 检查是否有用户数据\n     */\n    private hasUserData(): boolean {\n        return this.validateUserData();\n    }\n\n    /**\n     * 验证网络连接\n     */\n    private validateNetworkConnection(): boolean {\n        if (!smc.net?.hcGame) {\n            oops.log.logError('❌ 网络连接未初始化');\n            return false;\n        }\n        return true;\n    }\n\n    /**\n     * 等待用户数据初始化完成 - 优化版本\n     */\n    private async waitForUserDataInitialization(): Promise<void> {\n        const maxRetries = 30; // 🚀 减少最大重试次数\n        let retryCount = 0;\n\n        while (retryCount < maxRetries) {\n            if (this.hasUserData()) {\n                return;\n            }\n\n            // 🚀 使用更短的等待时间，但增加检查频率\n            await new Promise(resolve => setTimeout(resolve, 30));\n            retryCount++;\n\n            if (retryCount % 5 === 0) {\n                oops.log.logBusiness(`⏳ 等待用户数据初始化... ${retryCount}/${maxRetries}`);\n            }\n        }\n\n        // 🚀 超时后不抛出错误，而是记录警告并继续\n        oops.log.logWarn('⚠️ 用户数据初始化超时，但继续执行');\n    }\n\n    /**\n     * 确保数据结构完整性\n     */\n    private ensureDataIntegrity(data: UserGameData): void {\n        data.key = data.key || 0;\n        data.userName = data.userName || 'Player';\n        data.index = data.index || 0;\n        data.isNewPlayer = data.isNewPlayer ?? true;\n        data.propUseData = data.propUseData || {};\n        data.recordData = data.recordData || {};\n    }\n\n    /**\n     * 深度合并用户数据\n     */\n    private mergeUserData(target: UserGameData, source: UserGameData): void {\n        Object.keys(source).forEach(key => {\n            const sourceValue = source[key as keyof UserGameData];\n            const targetValue = target[key as keyof UserGameData];\n\n            if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {\n                if (targetValue && typeof targetValue === 'object') {\n                    this.mergeUserData(targetValue as any, sourceValue as any);\n                } else {\n                    (target as any)[key] = sourceValue;\n                }\n            } else {\n                (target as any)[key] = sourceValue;\n            }\n        });\n    }\n\n    /**\n     * 创建默认道具数据\n     */\n    private createDefaultPropData(propType: PropType): any {\n        return {\n            amount: 0,\n            propType,\n            propId: propType,\n            desc: `道具${propType}`,\n            getTime: new Date(),\n            lastResetTime: new Date(),\n            lastUpdateTime: new Date(),\n        };\n    }\n\n    /**\n     * 更新ViewModel\n     */\n    private updateViewModel(): void {\n        if (!this.hasUserData()) {\n            return;\n        }\n\n        this.removeFromViewModel();\n\n        const viewModelData = {\n            userId: this.RoleModel.userGameData.key,\n            userName: this.RoleModel.userGameData.userName,\n            level: this.getGameProgress(),\n            isNewPlayer: this.RoleModel.userGameData.isNewPlayer,\n            index: this.RoleModel.userGameData.index,\n            propUseData: this.RoleModel.userGameData.propUseData,\n            // 扩展其他需要的数据\n            ...this.RoleModel.userGameData,\n        };\n\n        VM.add(viewModelData, 'role');\n        oops.log.logBusiness('🎯 ViewModel已更新');\n    }\n\n    /**\n     * 从ViewModel移除数据\n     */\n    private removeFromViewModel(): void {\n        VM.remove('role');\n    }\n\n    /**\n     * 统一的API错误处理\n     */\n    private handleAPIError(apiName: string, error: any): void {\n        oops.log.logError(`❌ ${apiName} API失败:`, error);\n\n        const errorMessage = error?.message || error?.code?.toString() || '操作失败';\n        oops.gui.toast(errorMessage);\n    }\n}\n"]}