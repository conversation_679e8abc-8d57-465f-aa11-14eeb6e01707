{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/common/ClientConst.ts"], "names": ["ClientConst", "MusicConf", "Enum", "Gravity", "LinearDamping", "AngularDamping", "pickBoxScale", "eazyModeItemKinds", "eazyModeItemNums", "eazyModeItemArr", "eazyModeItemArrNew", "initialRadius", "initConeAngle", "wallCellNum", "hallGroundBgSize", "hallGroundDistance", "hallGroundNum", "hallMaxRank", "increaseVertexCount", "increaseVertexCountPct", "commonPrefabs", "itemPrefabPaths", "defaultBgPath", "dissoveCreatedDuration", "alwaysNewPlayerTest", "btnclick", "freeze", "pass", "softFail", "tap", "gameMusic1", "gameMusic2", "hallmusic", "commonPickEffect", "commonClearEffect", "gameFailEffect", "PickBoxCommonIndexLenth", "PickBoxCommonExtraIndexLenth", "PHY_GROUP", "DEFAULT", "WALL", "ITEM", "ITEM_BOX", "ITEM_BOX_EXTRA", "GROUND", "LAYER_GROUP", "RimLight", "UI_3D"], "mappings": ";;;wEAEaA,W,EA4BAC,S;;;;;;;;;;;;AA9BJC,MAAAA,I,OAAAA,I;;;;;;;;;6BAEIF,W,GAAN,MAAMA,WAAN,CAAkB,E;;AACrB;AADSA,MAAAA,W,CAEKG,O,GAAU,E;AAAI;AAFnBH,MAAAA,W,CAGKI,a,GAAgB,G;AAAK;AAH1BJ,MAAAA,W,CAIKK,c,GAAiB,G;AAAK;AAJ3BL,MAAAA,W,CAKKM,Y,GAAe,C;AAAG;AALvBN,MAAAA,W,CAOKO,iB,GAAoB,C;AAAG;AAP5BP,MAAAA,W,CAQKQ,gB,GAAmB,E;AAAI;AAR5BR,MAAAA,W,CASKS,e,GAAkB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,C;AATvBT,MAAAA,W,CAUKU,kB,GAAqB,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,C;AAV1BV,MAAAA,W,CAYKW,a,GAAgB,C;AAAG;AAZxBX,MAAAA,W,CAaKY,a,GAAgB,E;AAAI;AAbzBZ,MAAAA,W,CAcKa,W,GAAc,C;AAAG;AAdtBb,MAAAA,W,CAeKc,gB,GAAmB,G;AAAK;AAf7Bd,MAAAA,W,CAgBKe,kB,GAAqB,E;AAAI;AAhB9Bf,MAAAA,W,CAiBKgB,a,GAAgB,C;AAAG;AAjBxBhB,MAAAA,W,CAkBKiB,W,GAAc,E;AAAI;AAlBvBjB,MAAAA,W,CAmBKkB,mB,GAAsB,I;AAAM;AAnBjClB,MAAAA,W,CAoBKmB,sB,GAAyB,I;AAAM;AApBpCnB,MAAAA,W,CAqBKoB,a,GAAwB,wB;AArB7BpB,MAAAA,W,CAsBKqB,e,GAA0B,e;AAAiB;AAtBhDrB,MAAAA,W,CAuBKsB,a,GAAwB,a;AAAe;AAvB5CtB,MAAAA,W,CAwBKuB,sB,GAAiC,G;AAAK;AAxB3CvB,MAAAA,W,CAyBKwB,mB,GAA+B,K;;2BAGpCvB,S,GAAN,MAAMA,SAAN,CAAgB,E;;AAAVA,MAAAA,S,CACKwB,Q,GAAW,sB;AAAwB;AADxCxB,MAAAA,S,CAGKyB,M,GAAS,sB;AAAwB;AAHtCzB,MAAAA,S,CAIK0B,I,GAAO,oB;AAAsB;AAJlC1B,MAAAA,S,CAKK2B,Q,GAAW,wB;AAA0B;AAL1C3B,MAAAA,S,CAMK4B,G,GAAM,mB;AAAqB;AANhC5B,MAAAA,S,CAOK6B,U,GAAa,oB;AAPlB7B,MAAAA,S,CAQK8B,U,GAAa,sB;AARlB9B,MAAAA,S,CASK+B,S,GAAY,sB;AATjB/B,MAAAA,S,CAUKgC,gB,GAAmB,oB;AAAsB;AAV9ChC,MAAAA,S,CAWKiC,iB,GAAoB,qB;AAAuB;AAXhDjC,MAAAA,S,CAYKkC,c,GAAiB,sB;;yCAGtBC,uB,GAA0B,CAAC,CAAD,EAAI,CAAJ,C,GAAQ;;;8CAClCC,4B,GAA+B,CAAC,CAAD,EAAI,CAAJ,C,GAAQ;AAEpD;;;2BACaC,S,GAAY;AACrBC,QAAAA,OAAO,EAAE,KAAK,CADO;AAErBC,QAAAA,IAAI,EAAE,KAAK,CAFU;AAEP;AACdC,QAAAA,IAAI,EAAE,KAAK,CAHU;AAGP;AACdC,QAAAA,QAAQ,EAAE,KAAK,CAJM;AAIH;AAClBC,QAAAA,cAAc,EAAE,KAAK,CALA;AAKG;AACxBC,QAAAA,MAAM,EAAE,KAAK,CANQ,CAML;;AANK,O,GAQzB;;;6BACaC,W,GAAc;AACvBN,QAAAA,OAAO,EAAE,KAAK,EADS;AAEvBO,QAAAA,QAAQ,EAAE,KAAK,EAFQ;AAEJ;AACnBC,QAAAA,KAAK,EAAE,KAAK,EAHW;AAGP;AAChBP,QAAAA,IAAI,EAAE,KAAK,EAJY;AAIR;AACfI,QAAAA,MAAM,EAAE,KAAK,EALU,CAKN;;AALM,O;;AAQ3B1C,MAAAA,IAAI,CAACoC,SAAD,CAAJ;AACApC,MAAAA,IAAI,CAAC2C,WAAD,CAAJ", "sourcesContent": ["import { Enum } from 'cc';\n\nexport class ClientConst {\n    //重力相关\n    public static Gravity = 30; //  数值越大。下落越快\n    public static LinearDamping = 0.1; //控制物体线性速度衰减的程度。值越大，物体的速度衰减越快 如果希望物品下落过程中减速不那么明显，可以减小该值\n    public static AngularDamping = 0.5; //如果希望物品在下落过程中旋转得更稳定或者减少旋转速度的衰减，可以减小该值\n    public static pickBoxScale = 8; //盒子大小\n\n    public static eazyModeItemKinds = 3; // 简单模式下的道具种类\n    public static eazyModeItemNums = 15; // 简单模式下的道具数量\n    public static eazyModeItemArr = [6, 6, 3];\n    public static eazyModeItemArrNew = [3, 3, 3];\n\n    public static initialRadius = 4; // 圆锥的底部半径 [3-6] 为佳\n    public static initConeAngle = 80; // 圆锥的底部角度(锥形) [60~80] 为佳\n    public static wallCellNum = 3; // wallCell的个数。个数越多。底部越近似一个圆 [3-5]为佳。\n    public static hallGroundBgSize = 1.6; // wallCell的大小。越大。越接近一个圆 [16-20]为佳。\n    public static hallGroundDistance = 16; // wallCell的距离。\n    public static hallGroundNum = 4; //初始地面的个数。\n    public static hallMaxRank = 50; // 排行榜最大排名限制\n    public static increaseVertexCount = 5000; // 原固定顶点数（保留，若不需要可删除）\n    public static increaseVertexCountPct = 0.02; // 新增：百分比权重（例如0.1表示10%）\n    public static commonPrefabs: string = 'prefabs/commonPrefabs/';\n    public static itemPrefabPaths: string = 'prefabs/game/'; // 默认物品预制体路径\n    public static defaultBgPath: string = 'img/bg/bg_1'; // 默认背景图片路径\n    public static dissoveCreatedDuration: number = 1.5; // 消融生成动画时长\n    public static alwaysNewPlayerTest: boolean = false; // 是否总是新手玩家,测试用，别删\n}\n\nexport class MusicConf {\n    public static btnclick = 'boot/audios/btnclick'; // 按钮点击音效\n\n    public static freeze = 'common/audios/freeze'; //  消除音效\n    public static pass = 'common/audios/pass'; //  过关音效\n    public static softFail = 'common/audios/softFail'; //  失败音效\n    public static tap = 'common/audios/tap'; //  轻触音效\n    public static gameMusic1 = 'common/audios/game';\n    public static gameMusic2 = 'common/audios/game_2';\n    public static hallmusic = 'common/audios/hall_1';\n    public static commonPickEffect = 'common/audios/pick'; // 闲置音效\n    public static commonClearEffect = 'common/audios/clear'; // 删除音效\n    public static gameFailEffect = 'common/audios/fail_1'; // 游戏失败音效\n}\n\nexport const PickBoxCommonIndexLenth = [0, 6]; // 普通格子索引\nexport const PickBoxCommonExtraIndexLenth = [7, 9]; // 额外格子索引\n\n//在PhysicsSystem 中设置 用来控制碰撞组\nexport const PHY_GROUP = {\n    DEFAULT: 1 << 0,\n    WALL: 1 << 1, // 墙\n    ITEM: 1 << 2, // 道具\n    ITEM_BOX: 1 << 3, // 普通格子中的道具\n    ITEM_BOX_EXTRA: 1 << 4, // 额外格子中的道具\n    GROUND: 1 << 5, // 地面\n};\n//在Layers 中设置  用来控制摄像机可见的层级\nexport const LAYER_GROUP = {\n    DEFAULT: 1 << 30,\n    RimLight: 1 << 18, //\n    UI_3D: 1 << 23, //\n    WALL: 1 << 19, //\n    GROUND: 1 << 17, // 地面\n};\n\nEnum(PHY_GROUP);\nEnum(LAYER_GROUP);\n"]}