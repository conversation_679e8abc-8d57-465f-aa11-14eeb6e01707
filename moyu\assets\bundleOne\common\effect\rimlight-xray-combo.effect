// xray.effect
CCEffect %{
  techniques:
  - name: transparent-xray
    passes:
    - vert: unlit-vs:vert
      frag: xray-fs:frag
      embeddedMacros: { CC_FORWARD_ADD: true }
      depthStencilState:
        depthTest: true
        depthWrite: true
        depthFunc: less_equal
      blendState:
        targets:
        - blend: false
      instancedAttributes:
      - name: a_instanced_rimStrength
        format: R32F
        location: 10
      properties: &allProps
        tilingOffset:         { value: [1.0, 1.0, 0.0, 0.0] }
        albedoMap:            { value: grey }
        mainColor:            { value: [1.0, 1.0, 1.0, 1.0], target: albedo, linear: true, editor: { type: color } }
        albedoScale:          { value: [1.0, 1.0, 1.0], target: albedoScaleAndCutoff.xyz }
        normalMap:            { value: normal }
        pbrMap:               { value: grey }
        glowColor:            { value: [0.2, 1.0, 0.5, 1.0], linear: true, editor: { type: color, displayName: "💚 外发光颜色" } }
        breatheSpeed:         { value: 2.0, editor: { slide: true, range: [0.1, 8.0], step: 0.1, displayName: "💓 呼吸速度" } }
        breatheIntensity:     { value: 1.0, editor: { slide: true, range: [0.1, 3.0], step: 0.1, displayName: "🌟 呼吸强度" } }
        xrayAlpha:            { value: 0.5, editor: { slide: true, range: [0.0, 1.0], step: 0.01, displayName: "👻 发光透明度" } }

    - vert: unlit-vs:vert
      frag: xray-fs:frag
      embeddedMacros: { CC_FORWARD_ADD: true, XRAY_PASS: true }
      depthStencilState:
        depthTest: true
        depthWrite: false
        depthFunc: greater
      blendState:
        targets:
        - blend: true
          blendSrc: src_alpha
          blendDst: one_minus_src_alpha
          blendDstAlpha: one_minus_src_alpha
      instancedAttributes:
      - name: a_instanced_rimStrength
        format: R32F
        location: 10
      properties: *allProps
}%
CCProgram shared-ubos %{
  uniform Constants {
    vec4 tilingOffset;
    vec4 albedo;
    vec4 albedoScaleAndCutoff;
    vec4 glowColor;
    float xrayAlpha;
    float breatheSpeed;
    float breatheIntensity;
  };
}%

CCProgram unlit-vs %{
  precision highp float;
  #include <legacy/input-standard>
  #include <builtin/uniforms/cc-global>
  #include <builtin/uniforms/cc-local>
  #include <legacy/local-batch>
  #include <shared-ubos>
  #include <legacy/fog-vs>

  #if USE_VERTEX_COLOR
    in vec4 a_color;
    out lowp vec4 v_color;
  #endif

  #if USE_INSTANCING
    in float a_instanced_rimStrength;
    out float v_rimStrength;
  #endif

  out vec3 v_position;
  out vec3 v_normal;
  out vec2 v_uv;

  #if HAS_SECOND_UV
    out mediump vec2 v_uv1;
  #endif

  #if USE_NORMAL_MAP
    out mediump vec4 v_tangent;
  #endif

  #if HAS_SECOND_UV || CC_USE_LIGHTMAP
    in vec2 a_texCoord1;
  #endif

  vec4 vert () {
    StandardVertInput In;
    CCVertInput(In);

    #if USE_INSTANCING
      v_rimStrength = a_instanced_rimStrength;
    #endif

    mat4 matWorld, matWorldIT;
    CCGetWorldMatrixFull(matWorld, matWorldIT);

    vec4 pos = matWorld * In.position;
    v_position = pos.xyz;
    v_normal = normalize((matWorldIT * vec4(In.normal, 0.0)).xyz);
    
    v_uv = a_texCoord * tilingOffset.xy + tilingOffset.zw;
    #if SAMPLE_FROM_RT
      CC_HANDLE_RT_SAMPLE_FLIP(v_uv);
    #endif
    #if HAS_SECOND_UV
      v_uv1 = a_texCoord1 * tilingOffset.xy + tilingOffset.zw;
      #if SAMPLE_FROM_RT
        CC_HANDLE_RT_SAMPLE_FLIP(v_uv1);
      #endif
    #endif

    #if USE_VERTEX_COLOR
      v_color = a_color;
    #endif

    #if USE_NORMAL_MAP
      v_tangent.xyz = normalize((matWorld * vec4(In.tangent.xyz, 0.0)).xyz);
      v_tangent.w = In.tangent.w;
    #endif

    CC_TRANSFER_FOG(pos);

    return cc_matProj * (cc_matView * matWorld) * In.position;
  }
}%

CCProgram xray-fs %{
  precision highp float;
  #include <legacy/output>
  #include <legacy/fog-fs>
  #include <builtin/uniforms/cc-global>
  #include <shared-ubos>

  in vec3 v_position;
  in vec3 v_normal;
  in vec2 v_uv;

  #if HAS_SECOND_UV
    in mediump vec2 v_uv1;
  #endif

  #if USE_VERTEX_COLOR
    in lowp vec4 v_color;
  #endif

  #if USE_NORMAL_MAP
    in mediump vec4 v_tangent;
    uniform sampler2D normalMap;
    #pragma define-meta NORMAL_UV options([v_uv, v_uv1])
  #endif

  #if USE_ALBEDO_MAP
    uniform sampler2D albedoMap;
    #pragma define-meta ALBEDO_UV options([v_uv, v_uv1])
  #endif

  #if USE_PBR_MAP
    uniform sampler2D pbrMap;
    #pragma define-meta PBR_UV options([v_uv, v_uv1])
  #endif

  #if USE_EMISSIVE_MAP
    uniform sampler2D emissiveMap;
    #pragma define-meta EMISSIVE_UV options([v_uv, v_uv1])
  #endif

  #if USE_ALPHA_TEST
    #pragma define-meta ALPHA_TEST_CHANNEL options([a, r])
  #endif

  #if USE_INSTANCING
    in float v_rimStrength;
  #endif

  // 获取世界空间中的相机位置
  vec3 getCameraPosition() {
      return cc_cameraPos.xyz;
  }

  vec4 frag () {
      vec4 baseColor = albedo;
      
      #if USE_VERTEX_COLOR
        baseColor.rgb *= SRGBToLinear(v_color.rgb);
        baseColor.a *= v_color.a;
      #endif
      
      #if USE_ALBEDO_MAP
        vec4 texColor = texture(albedoMap, ALBEDO_UV);
        texColor.rgb = SRGBToLinear(texColor.rgb);
        baseColor *= texColor;
      #endif
      
      baseColor.rgb *= albedoScaleAndCutoff.xyz;

      #if USE_ALPHA_TEST
        if (baseColor.ALPHA_TEST_CHANNEL < albedoScaleAndCutoff.w) discard;
      #endif

      // PBR贴图支持
      float metallic = 0.0;
      float roughness = 1.0;
      #if USE_PBR_MAP
        vec4 pbrSample = texture(pbrMap, PBR_UV);
        metallic = pbrSample.r;
        roughness = pbrSample.g;
      #endif

      vec3 normal = normalize(v_normal);
      vec4 col;

      #if XRAY_PASS
          vec3 cameraPos = getCameraPosition();
          vec3 viewDir = normalize(cameraPos - v_position);
          
          // 🎯 计算法线和视线的夹角 (0=平行, 1=垂直)
          float rimFactor = 1.0 - dot(normalize(v_normal), viewDir);
          
          // 🌟 平滑的边缘强度过渡
          float edgeStrength = smoothstep(0.0, 1.0, rimFactor);
          
          #if USE_INSTANCING
            float rimStrength = v_rimStrength;
          #else
            float rimStrength = 1.0;
          #endif

          // 🌟 呼吸灯效果计算 - 大幅增强速度让参数调节立即可见
          float breatheCycle = sin(cc_time.x * breatheSpeed * 6.28) * 0.5 + 0.5; // 使用2π倍数，让速度调节更敏感
          float finalRimStrength = rimStrength * (0.5 + breatheCycle * breatheIntensity); // 基础0.5 + 呼吸变化

          // 🎯 边缘光强度：夹角越大越明显，平行时为0
          float glowIntensity = finalRimStrength * edgeStrength;
          vec3 rimColor = glowColor.xyz;  // 使用uniform中的发光颜色
          
          // 💡 添加基础光照计算，解决模型偏暗问题
          vec3 lightDir = normalize(vec3(0.5, 1.0, 0.5)); // 模拟主光源方向
          float NdotL = max(dot(normal, lightDir), 0.0);
          vec3 lightColor = vec3(1.0, 1.0, 1.0); // 白光
          vec3 ambientColor = vec3(0.3, 0.3, 0.3); // 环境光
          
          // 🌟 光照后的基础颜色 = 环境光 + 漫反射光
          vec3 litBaseColor = baseColor.rgb * (ambientColor + lightColor * NdotL * 0.8);
          
          // 🎨 光照后的基础颜色 + 边缘光
          vec3 finalColor = litBaseColor + rimColor * glowIntensity;
          col = vec4(finalColor, xrayAlpha);
      #else
          // 💡 Pass 0 也添加光照，保持一致性
          vec3 lightDir = normalize(vec3(0.5, 1.0, 0.5));
          float NdotL = max(dot(normal, lightDir), 0.0);
          vec3 lightColor = vec3(1.0, 1.0, 1.0);
          vec3 ambientColor = vec3(0.3, 0.3, 0.3);
          
          vec3 litColor = baseColor.rgb * (ambientColor + lightColor * NdotL * 0.8);
          col = vec4(litColor, baseColor.a);
      #endif

      CC_APPLY_FOG(col, v_position);
      return CCFragOutput(col);
  }
}%