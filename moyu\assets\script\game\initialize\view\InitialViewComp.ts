import { _decorator, tween, UIOpacity, Vec3 } from 'cc';

import { DEBUG, DEV } from 'cc/env';
import { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';
import { JsonUtil } from '../../../../../extensions/oops-plugin-framework/assets/core/utils/JsonUtil';
import { ecs } from '../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';
import { CCComp } from '../../../../../extensions/oops-plugin-framework/assets/module/common/CCComp';
import { GameConst } from '../../../tsrpc/models/GameConst';
import { Platform, ShareConfig } from '../../../tsrpc/models/ShareConfig';
import { SceneType } from '../../../tsrpc/protocols/base';
import { ClientConst } from '../../common/ClientConst';
import { GameStorageConfig, StorageTestUtils } from '../../common/config/GameStorageConfig';
import { smc } from '../../common/SingletonModuleComp';

// 扩展window类型
declare global {
    interface Window {
        updateProgress?: (progress: number, message?: string, detail?: string) => void;
        hideLoader?: () => void;
        FBInstant?: any;
    }
}

const { ccclass, property } = _decorator;

/**
 * 游戏完整初始化组件 - 自定义加载版本
 *
 * 职责：
 * - UI动画播放
 * - Bundle验证和加载
 * - 配置数据加载（配置表 + 语言包）
 * - 用户数据加载
 * - 进入游戏流程
 *
 * 优化：
 * - 所有初始化逻辑集中管理
 * - Bundle加载失败直接报错
 * - 配置表和语言包并行加载
 * - 支持自定义加载界面（Facebook + 个人平台）
 */
@ccclass('InitialViewComp')
@ecs.register('InitialView', false)
export class InitialViewComp extends CCComp {
    private waitComplete: boolean = false;
    private loadComplete: boolean = false;
    private currentProgress: number = 0;
    private startTime: number = 0;

    start() {
        // 🚀 记录启动开始时间
        this.startTime = Date.now();
        oops.log.logBusiness('🚀 游戏初始化开始', { startTime: this.startTime });

        // 🚀 统一使用自定义加载界面，跳过Cocos内置动画
        if (ShareConfig.platform === Platform.FACEBOOK) {
            oops.log.logBusiness('🚀 Facebook环境: 自定义加载模式');
        } else if (ShareConfig.platform === Platform.PERSONAL) {
            oops.log.logBusiness('🚀 个人环境: 自定义加载模式');
            this.updateLoadingProgress(50, '游戏引擎已启动', '个人环境初始化完成');
        } else {
            // 其他环境：使用传统动画
            this.playWaitAnimation();
        }

        this.startFullInitialization();
    }

    /** 🎬 播放等待动画（传统环境） */
    private playWaitAnimation() {
        let logoOpacity = this.node.getChildByName('logo')?.getComponent(UIOpacity);
        if (logoOpacity) {
            logoOpacity.opacity = 50;
            tween(logoOpacity.node)
                .to(1, { opacity: 255, position: new Vec3(0, 0, 0) })
                .call(() => {
                    this.waitComplete = true;
                    if (this.loadComplete) {
                        this.enterGame();
                    }
                })
                .start();
        } else {
            this.waitComplete = true;
        }
    }

    /** 🎯 更新加载进度 */
    private updateLoadingProgress(progress: number, message: string, detail?: string) {
        this.currentProgress = progress;

        if (window.updateProgress && typeof window.updateProgress === 'function') {
            window.updateProgress(progress, message, detail);
            oops.log.logBusiness(`🎯 加载进度: ${progress}% - ${message}`);
        }
    }

    /** 🚀 完整初始化流程 - 重构版本 */
    private async startFullInitialization() {
        try {
            // 🧪 测试开关：控制数据清空行为
            if (ClientConst.alwaysNewPlayerTest && (DEV || DEBUG)) {
                StorageTestUtils.forceNewPlayerState();
                oops.log.logBusiness('🎓 [测试模式] 强制设置为新手状态');
            }

            // 🚀 优化：并行执行可独立的初始化任务
            await Promise.all([
                // 1️⃣ 验证和加载Bundle + 基础UI资源
                this.loadBundleAndUIResources(),
                // 2️⃣ 加载配置数据（配置表 + 语言包）
                this.loadConfigurationData(),
            ]);

            // 3️⃣ 尝试快速用户数据初始化
            await this.quickUserDataInitialization();

            // 设置窗口打开失败事件
            oops.gui.setOpenFailure(this.onOpenFailure);

            this.loadComplete = true;

            // 🚀 自定义加载环境直接进入游戏
            if (
                ShareConfig.platform === Platform.FACEBOOK ||
                ShareConfig.platform === Platform.PERSONAL
            ) {
                this.enterGame();
            } else if (this.waitComplete) {
                this.enterGame();
            }
        } catch (error) {
            this.handleInitializationError(error);
        }
    }

    /** � 快速用户数据初始化 */
    private async quickUserDataInitialization(): Promise<void> {
        try {
            // 🔍 首先尝试快速加载现有用户数据
            const isNewPlayer = this.determineNewPlayerStatus();

            // 🚀 如果没有现有数据，启用快速启动模式
            if (isNewPlayer && GameConst.newPlayerFastStart) {
                oops.log.logBusiness('🚀 启用新手快速启动模式');
                await this.initializeBasicRoleData();

                // 🔄 在后台继续完整的用户数据加载
                this.loadFullUserDataInBackground();
            } else {
                // 🔄 传统模式：完整加载用户数据
                await this.ensureUserDataLoaded();
            }
        } catch (error) {
            oops.log.logWarn('⚠️ 快速用户数据初始化失败:', error);
            // 失败时创建基础数据，确保游戏可以启动
            await this.initializeBasicRoleData();
        }
    }

    /** 🎁 为新手玩家创建默认道具数据 */
    private createNewPlayerDefaultProps(): { [key: number]: any } {
        const currentTime = new Date();

        // 🎯 新手玩家默认道具配置
        const newPlayerProps = {
            // 1. 移出道具 - 新手给3个
            1: {
                amount: 3,
                propType: 1,
                propId: 1,
                desc: '移出道具',
                getTime: currentTime,
                lastResetTime: currentTime,
                lastUpdateTime: currentTime,
            },
            // 2. 提示道具 - 新手给3个
            2: {
                amount: 3,
                propType: 2,
                propId: 2,
                desc: '提示道具',
                getTime: currentTime,
                lastResetTime: currentTime,
                lastUpdateTime: currentTime,
            },
            // 3. 重新洗牌道具 - 新手给2个
            3: {
                amount: 2,
                propType: 3,
                propId: 3,
                desc: '重新洗牌道具',
                getTime: currentTime,
                lastResetTime: currentTime,
                lastUpdateTime: currentTime,
            },
            // 7. 每日挑战剩余次数 - 新手给足够的次数
            7: {
                amount: 9999,
                propType: 7,
                propId: 7,
                desc: '每日挑战剩余次数',
                getTime: currentTime,
                lastResetTime: currentTime,
                lastUpdateTime: currentTime,
            },
        };

        oops.log.logBusiness('🎁 新手默认道具配置:', {
            移出道具: newPlayerProps[1].amount,
            提示道具: newPlayerProps[2].amount,
            洗牌道具: newPlayerProps[3].amount,
            挑战次数: newPlayerProps[7].amount,
        });

        return newPlayerProps;
    }

    /** 🔄 后台加载完整用户数据 */
    private loadFullUserDataInBackground(): void {
        // 使用setTimeout确保不阻塞当前流程
        setTimeout(async () => {
            try {
                oops.log.logBusiness('🔄 开始后台加载完整用户数据...');

                // 🚀 强制执行完整的登录流程，不跳过
                await this.forceCompleteUserDataLoad();

                oops.log.logBusiness('✅ 后台用户数据加载完成');

                // 触发数据更新事件，通知游戏其他模块
                oops.message.dispatchEvent('UserDataLoaded');
            } catch (error) {
                oops.log.logWarn('⚠️ 后台用户数据加载失败:', error);
            }
        }, 100);
    }

    /** 🔄 强制执行完整的用户数据加载流程 */
    private async forceCompleteUserDataLoad(): Promise<void> {
        try {
            // 🔄 临时清除基础数据标记，强制执行完整登录
            const tempUserData = smc.role.RoleModel?.userGameData;
            if (tempUserData) {
                // 标记这是临时数据，需要完整登录
                (tempUserData as any).isTemporaryData = true;
            }

            // 🔄 执行完整的登录流程
            await this.performCompleteLogin();
        } catch (error) {
            oops.log.logWarn('⚠️ 强制完整登录失败:', error);
            throw error;
        }
    }

    /** 🔐 执行完整的登录流程 */
    private async performCompleteLogin(): Promise<void> {
        oops.log.logBusiness('🔐 开始执行完整登录流程...');

        // 🔧 根据平台选择不同的登录方式
        if (ShareConfig.platform === Platform.FACEBOOK) {
            // Facebook环境：执行Facebook自动登录
            oops.log.logBusiness('🔐 Facebook环境：执行自动登录...');

            try {
                const { LoginViewComp } = await import('./LoginViewComp');
                const loginSuccess = await LoginViewComp.doFacebookLogin();

                if (loginSuccess) {
                    oops.log.logBusiness('✅ Facebook自动登录成功');
                } else {
                    oops.log.logWarn('⚠️ Facebook自动登录失败，继续尝试加载数据');
                }
            } catch (error) {
                oops.log.logError('❌ Facebook自动登录异常:', error);
            }
        } else {
            // 🔐 其他环境：执行标准登录流程
            oops.log.logBusiness('🔐 执行标准登录流程...');

            try {
                // 调用Role模块的数据加载方法
                await smc.role.loadData();
                oops.log.logBusiness('✅ 标准登录流程完成');
            } catch (error) {
                oops.log.logWarn('⚠️ 标准登录流程失败:', error);
                throw error;
            }
        }
    }

    /** � 智能判断新手状态 */
    private determineNewPlayerStatus(): boolean {
        // 1. 优先使用Role模块的判断
        if (smc.role.RoleModel && smc.role.RoleModel.userGameData) {
            const isNewFromRole = smc.role.isNewPlayer();
            oops.log.logBusiness(`🎯 Role模块判断新手状态: ${isNewFromRole}`);
            return isNewFromRole;
        }

        // 2. 检查本地存储的登录记录
        const hasLoginRecord = !!oops.storage.get(GameStorageConfig.SSOToken);
        if (hasLoginRecord) {
            oops.log.logBusiness('🔍 检测到登录记录，判定为老玩家');
            return false;
        }

        // 3. 检查是否有其他用户数据痕迹
        const userDumpKey = oops.storage.getJson(GameStorageConfig.UserDumpKey, null);
        if (userDumpKey && String(userDumpKey) !== '0') {
            oops.log.logBusiness('🔍 检测到用户数据痕迹，判定为老玩家');
            return false;
        }

        // 4. 默认判定为新手
        oops.log.logBusiness('🆕 无任何用户数据痕迹，判定为新手玩家');
        return true;
    }

    /** �🚀 初始化基础Role数据，确保道具系统可用 */
    private async initializeBasicRoleData(): Promise<void> {
        try {
            oops.log.logBusiness('🚀 开始初始化基础Role数据...');

            // 创建完整的基础用户数据结构
            const currentTime = new Date();
            const basicUserData = {
                // 基础标识
                key: 0,
                guuid: '',
                googleUuid: '',
                facebookId: '',
                userName: '',
                nickName: '',
                sex: 1, // SexType.None
                createtime: currentTime,
                openid: '',
                platform: 'web',
                platformType: 'web',
                avatar: '',
                avatarId: 0,
                countryCode: 'Other',

                // 游戏进度
                passTimes: 0,
                index: 0,
                currCountryPassTimes: 0,
                lastChangeCountryTime: currentTime,
                selfCountryRank: 0,

                // 道具和记录数据
                propUseData: this.createNewPlayerDefaultProps(),
                recordData: {},

                // 新手和游客状态
                isNewPlayer: true,
                isGuest: true,
                lastStep: 0,
            };

            // 🎯 使用Role模块的updateUserData方法来设置数据
            // 这样可以确保数据完整性和触发相关事件
            if (smc.role && smc.role.updateUserData) {
                smc.role.updateUserData(basicUserData as any);
                oops.log.logBusiness('✅ 基础用户数据已通过updateUserData设置');
            } else {
                oops.log.logWarn('⚠️ Role模块或updateUserData方法不可用');
            }

            oops.log.logBusiness('🚀 基础Role数据初始化完成', {
                isNewPlayer: basicUserData.isNewPlayer,
                propCount: Object.keys(basicUserData.propUseData).length,
            });
        } catch (error) {
            oops.log.logWarn('⚠️ 基础Role数据初始化失败:', error);
        }
    }

    /** 1️⃣ 验证和加载Bundle + 基础UI资源 */
    private async loadBundleAndUIResources(): Promise<void> {
        oops.log.logBusiness('🔍 开始验证Bundle和加载基础UI资源...');

        try {
            // 🚀 直接验证bundleOne是否存在，不存在就报错
            try {
                await oops.res.loadBundle('bundleOne');
                oops.log.logBusiness('✅ bundleOne加载成功');
            } catch (error) {
                throw new Error(`❌ 关键Bundle 'bundleOne' 加载失败: ${error}`);
            }

            // 🚀 延迟导入，减少初始bundle大小
            const { simpleLoader } = await import('../../common/loader/SimpleLoadingManager');

            // 🎯 加载基础UI资源
            const tasks = [
                {
                    name: '加载基础UI资源',
                    dirs: ['boot'],
                    priority: 'high',
                    bundle: 'bundleOne',
                },
            ];

            const result = await simpleLoader.loadTasks(tasks);

            if (!result) {
                throw new Error('基础UI资源加载失败');
            }

            oops.log.logBusiness('✅ Bundle和基础UI资源加载完成');
        } catch (error) {
            oops.log.logError('❌ Bundle和UI资源加载失败:', error);
            throw error;
        }
    }

    /** 2️⃣ 加载配置数据资源（配置表 + 语言包） */
    private async loadConfigurationData(): Promise<void> {
        oops.log.logBusiness('🔄 开始加载配置数据资源...');

        try {
            // 🚀 优化：配置表和语言包可以并行加载
            const [configResult] = await Promise.all([
                JsonUtil.loadDirAsync(),
                this.loadLanguage(),
            ]);

            if (!configResult) {
                throw new Error('配置表加载失败');
            }

            oops.log.logBusiness('✅ 配置数据资源加载完成');
        } catch (error) {
            oops.log.logError('❌ 配置数据资源加载失败:', error);
            throw error;
        }
    }

    /** 加载语言包 */
    private async loadLanguage(): Promise<void> {
        let language = oops.storage.get(GameStorageConfig.Language, 'en');
        language = 'en';
        oops.language.setLanguage(language);
    }

    /** 3️⃣ 确保用户数据已加载 - 完整登录流程 */
    private async ensureUserDataLoaded(): Promise<void> {
        // 检查是否已有用户数据，但排除临时数据
        const userData = smc.role.RoleModel?.userGameData;
        if (userData && !(userData as any).isTemporaryData) {
            oops.log.logBusiness('✅ 用户数据已存在，跳过加载');
            return;
        }

        if ((userData as any)?.isTemporaryData) {
            oops.log.logBusiness('🔄 检测到临时数据，执行完整登录流程...');
        } else {
            oops.log.logBusiness('🔄 开始完整用户数据加载流程...');
        }

        // 🔧 根据平台选择不同的登录方式
        if (ShareConfig.platform === Platform.FACEBOOK) {
            // Facebook环境：执行Facebook自动登录
            oops.log.logBusiness('🔐 Facebook环境：执行自动登录...');

            try {
                const { LoginViewComp } = await import('./LoginViewComp');
                const loginSuccess = await LoginViewComp.doFacebookLogin();

                if (loginSuccess) {
                    oops.log.logBusiness('✅ Facebook自动登录成功');
                } else {
                    oops.log.logWarn('⚠️ Facebook自动登录失败，继续尝试加载数据');
                }
            } catch (error) {
                oops.log.logError('❌ Facebook自动登录异常:', error);
            }
        } else if (ShareConfig.platform === Platform.PERSONAL) {
            //

            try {
                const { LoginViewComp } = await import('./LoginViewComp');

                // 🔍 先检查token过期时间，再尝试加载数据
                const existingToken = oops.storage.get(GameStorageConfig.SSOToken);
                const tokenInfo = this.getTokenInfo();
                let needRelogin = false;

                if (existingToken && tokenInfo) {
                    // 检查token是否过期
                    if (tokenInfo.expiredTime < Date.now()) {
                        oops.log.logWarn('⏰ SSO Token已过期，需要重新登录', {
                            创建时间: new Date(tokenInfo.createdTime).toLocaleString(),
                            过期时间: new Date(tokenInfo.expiredTime).toLocaleString(),
                            当前时间: new Date().toLocaleString(),
                        });
                        needRelogin = true;
                    } else {
                        oops.log.logBusiness('🔑 检测到有效SSO Token，尝试加载用户数据...', {
                            剩余时间:
                                Math.round(
                                    (tokenInfo.expiredTime - Date.now()) / (60 * 60 * 1000)
                                ) + '小时',
                        });

                        // 尝试加载用户数据以验证token有效性
                        try {
                            await smc.role.loadData();

                            if (smc.role.RoleModel && smc.role.RoleModel.userGameData) {
                                oops.log.logBusiness('✅ 用户数据加载成功，token有效');
                                return; // 成功加载，直接返回
                            } else {
                                needRelogin = true;
                            }
                        } catch (error) {
                            oops.log.logWarn('⚠️ token验证失败，需要重新登录:', error);
                            needRelogin = true;
                        }
                    }
                } else {
                    oops.log.logBusiness('🔍 未检测到有效token或token信息');
                    needRelogin = true;
                }

                // 如果需要重新登录
                if (needRelogin) {
                    oops.log.logBusiness('🔄 执行游客登录...');

                    // 清除旧token和token信息
                    if (existingToken) {
                        oops.storage.remove(GameStorageConfig.SSOToken);
                        oops.storage.remove(GameStorageConfig.SSOTokenInfo);
                        oops.log.logBusiness('🗑️ 已清除无效的SSO Token和相关信息');
                    }

                    // 创建临时LoginViewComp实例进行游客登录
                    const tempLoginComp = new LoginViewComp();
                    const guestLoginSuccess = await tempLoginComp.loginGuestButton();

                    if (guestLoginSuccess) {
                        oops.log.logBusiness('✅ 游客登录成功');

                        // 重新加载用户数据
                        await smc.role.loadData();

                        if (smc.role.RoleModel && smc.role.RoleModel.userGameData) {
                            oops.log.logBusiness('✅ 用户数据加载成功');
                        } else {
                            oops.log.logWarn('⚠️ 游客登录后用户数据加载失败');
                        }
                    } else {
                        oops.log.logWarn('⚠️ 游客登录失败');
                    }
                }
            } catch (error) {
                oops.log.logError('❌ 游客登录流程异常:', error);
            }
        } else {
            // 其他平台，直接尝试加载数据
            await smc.role.loadData();
        }

        // 最终检查结果
        if (smc.role.RoleModel && smc.role.RoleModel.userGameData) {
            oops.log.logBusiness('✅ 用户数据最终加载成功');
        } else {
            oops.log.logWarn('⚠️ 用户数据最终加载失败，但继续进入游戏');
        }
    }

    /** 🎮 进入游戏流程 - 优化版本 */
    private async enterGame(): Promise<void> {
        try {
            oops.log.logBusiness('🎓 开始进入游戏...');

            // 🔍 智能判断新手状态
            let isNewPlayer = this.determineNewPlayerStatus();

            oops.log.logBusiness(`🎯 最终判定新手状态: ${isNewPlayer}`);
            oops.log.logBusiness(`🎮 进入${isNewPlayer ? '新手游戏' : '大厅'}场景`);

            let success = false;
            let showLoading = true;

            // 🎯 使用自定义加载界面的平台不显示Cocos默认加载
            if (
                ShareConfig.platform === Platform.FACEBOOK ||
                ShareConfig.platform === Platform.PERSONAL
            ) {
                showLoading = false;
            }

            if (isNewPlayer) {
                oops.log.logBusiness(
                    `🆕 ${ShareConfig.platform === Platform.FACEBOOK ? 'Facebook' : ShareConfig.platform === Platform.PERSONAL ? '个人' : ''}新手玩家：进入游戏场景（Foods）`
                );

                success = await smc.sceneMgr.switchToScene(
                    SceneType.Foods,
                    1,
                    showLoading,
                    this.closeLoadingUI.bind(this)
                );
            } else {
                // 老玩家：进入大厅场景
                oops.log.logBusiness(
                    `👤 ${ShareConfig.platform === Platform.FACEBOOK ? 'Facebook' : ShareConfig.platform === Platform.PERSONAL ? '个人' : ''}老玩家：进入大厅场景（Hall）`
                );
                success = await smc.sceneMgr.switchToScene(
                    SceneType.Hall,
                    undefined,
                    showLoading,
                    this.closeLoadingUI.bind(this)
                );
            }

            if (success) {
                oops.log.logBusiness(`🎮 ${isNewPlayer ? '游戏' : '大厅'}场景加载成功`);
            } else {
                throw new Error(`${isNewPlayer ? '游戏' : '大厅'}场景加载失败`);
            }
        } catch (error) {
            oops.log.logError('🔥 进入游戏失败:', error);

            // 更新加载界面显示错误
            this.updateLoadingProgress(0, '进入游戏失败', '请刷新页面重试');

            oops.gui.toast('进入游戏失败，请刷新页面重试');

            // 🎯 失败时也要移除组件，避免内存泄漏
            this.ent.remove(InitialViewComp);
        }
    }

    /** 🚨 错误处理 */
    private handleInitializationError(error: any): void {
        oops.log.logError('❌ 游戏初始化失败:', error);

        // 更新加载界面显示错误
        this.updateLoadingProgress(0, '初始化失败', error.message || '未知错误');

        // 🚨 如果是Bundle加载失败，显示更明确的错误信息
        if (error.message.includes('bundleOne')) {
            oops.gui.toast('❌ 游戏资源包缺失，请检查资源配置');
            // 🎯 严重错误时移除组件，避免内存泄漏
            this.ent.remove(InitialViewComp);
            return; // 不重试，直接失败
        }

        // 其他错误可以重试
        oops.gui.toast('初始化失败，请检查网络后重试');
        this.retryInitialization();
    }

    /** 🔧 初始化重试机制 */
    private retryInitialization(): void {
        setTimeout(() => {
            oops.log.logBusiness('🔄 尝试重新初始化...');
            this.currentProgress = 0;
            this.updateLoadingProgress(0, '正在重试...', '重新初始化游戏');
            this.startFullInitialization();
        }, 2000);
    }

    /** 通知HTML加载完成 */
    private notifyHTMLLoadingComplete(): void {
        oops.log.logBusiness('🚪 通知HTML加载完成，关闭自定义加载界面');

        try {
            // 更新进度到100%
            if (window.updateProgress && typeof window.updateProgress === 'function') {
                window.updateProgress(100, '加载完成');
                oops.log.logBusiness('✅ HTML进度更新为100%');
            } else {
                oops.log.logBusiness('ℹ️ window.updateProgress 方法不存在（正常情况）');
            }

            // 隐藏加载界面
            if (window.hideLoader && typeof window.hideLoader === 'function') {
                window.hideLoader();
                oops.log.logBusiness('✅ 自定义加载界面已隐藏');
            } else {
                oops.log.logBusiness('ℹ️ window.hideLoader 方法不存在（正常情况）');
            }
        } catch (error) {
            oops.log.logWarn('⚠️ 通知HTML加载完成时出现异常:', error);
        }
    }

    /** 关闭加载界面 */
    private closeLoadingUI(): void {
        // 🎯 计算启动总时间
        const endTime = Date.now();
        const totalTime = endTime - this.startTime;

        // 🎯 自定义加载界面的平台都调用HTML通知
        if (
            ShareConfig.platform === Platform.FACEBOOK ||
            ShareConfig.platform === Platform.PERSONAL
        ) {
            this.notifyHTMLLoadingComplete();
        }

        // Facebook生产环境额外通知
        if (ShareConfig.platform === Platform.FACEBOOK) {
            this.notifyFacebookComplete();
        }

        oops.log.logBusiness('🎉 游戏启动完成！', {
            totalTime: `${totalTime}ms`,
            performance: totalTime < 3000 ? '优秀' : totalTime < 5000 ? '良好' : '需要优化',
        });

        // 移除自己
        this.ent.remove(InitialViewComp);
    }

    /** 通知Facebook完成 */
    private notifyFacebookComplete(): void {
        if (window.FBInstant && window.FBInstant.setLoadingProgress) {
            window.FBInstant.setLoadingProgress(100);
            oops.log.logBusiness('📊 Facebook SDK: 进度已设置为100%');
        }
    }

    /** 窗口打开失败回调 */
    onOpenFailure(): void {
        oops.log.logError('❌ 窗口打开失败');
    }

    /** 获取token信息 */
    private getTokenInfo(): { token: string; expiredTime: number; createdTime: number } | null {
        try {
            const tokenInfoStr = oops.storage.get(GameStorageConfig.SSOTokenInfo);
            if (tokenInfoStr) {
                return JSON.parse(tokenInfoStr);
            }
        } catch (error) {
            oops.log.logWarn('⚠️ 解析token信息失败:', error);
        }
        return null;
    }

    reset(): void {
        this.waitComplete = false;
        this.loadComplete = false;
        this.currentProgress = 0;
    }
}
