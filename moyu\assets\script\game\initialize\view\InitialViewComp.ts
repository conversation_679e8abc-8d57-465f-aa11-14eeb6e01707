import { _decorator, tween, UIOpacity, Vec3 } from 'cc';

import { DEBUG, DEV } from 'cc/env';
import { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';
import { JsonUtil } from '../../../../../extensions/oops-plugin-framework/assets/core/utils/JsonUtil';
import { ecs } from '../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';
import { CCComp } from '../../../../../extensions/oops-plugin-framework/assets/module/common/CCComp';
import { Platform, ShareConfig } from '../../../tsrpc/models/ShareConfig';
import { SceneType } from '../../../tsrpc/protocols/base';
import { ClientConst } from '../../common/ClientConst';
import { GameStorageConfig, StorageTestUtils } from '../../common/config/GameStorageConfig';
import { smc } from '../../common/SingletonModuleComp';

// 扩展window类型
declare global {
    interface Window {
        updateProgress?: (progress: number, message?: string, detail?: string) => void;
        hideLoader?: () => void;
        FBInstant?: any;
    }
}

const { ccclass, property } = _decorator;

/**
 * 游戏完整初始化组件 - 自定义加载版本
 *
 * 职责：
 * - UI动画播放
 * - Bundle验证和加载
 * - 配置数据加载（配置表 + 语言包）
 * - 用户数据加载
 * - 进入游戏流程
 *
 * 优化：
 * - 所有初始化逻辑集中管理
 * - Bundle加载失败直接报错
 * - 配置表和语言包并行加载
 * - 支持自定义加载界面（Facebook + 个人平台）
 */
@ccclass('InitialViewComp')
@ecs.register('InitialView', false)
export class InitialViewComp extends CCComp {
    private waitComplete: boolean = false;
    private loadComplete: boolean = false;
    private currentProgress: number = 0;
    private startTime: number = 0;

    start() {
        // 🚀 记录启动开始时间
        this.startTime = Date.now();
        oops.log.logBusiness('🚀 游戏初始化开始', { startTime: this.startTime });

        // 🚀 统一使用自定义加载界面，跳过Cocos内置动画
        if (ShareConfig.platform === Platform.FACEBOOK) {
            oops.log.logBusiness('🚀 Facebook环境: 自定义加载模式');
        } else if (ShareConfig.platform === Platform.PERSONAL) {
            oops.log.logBusiness('🚀 个人环境: 自定义加载模式');
            this.updateLoadingProgress(50, '游戏引擎已启动', '个人环境初始化完成');
        } else {
            // 其他环境：使用传统动画
            this.playWaitAnimation();
        }

        this.startFullInitialization();
    }

    /** 🎬 播放等待动画（传统环境） */
    private playWaitAnimation() {
        let logoOpacity = this.node.getChildByName('logo')?.getComponent(UIOpacity);
        if (logoOpacity) {
            logoOpacity.opacity = 50;
            tween(logoOpacity.node)
                .to(1, { opacity: 255, position: new Vec3(0, 0, 0) })
                .call(() => {
                    this.waitComplete = true;
                    if (this.loadComplete) {
                        this.enterGame();
                    }
                })
                .start();
        } else {
            this.waitComplete = true;
        }
    }

    /** 🎯 更新加载进度 */
    private updateLoadingProgress(progress: number, message: string, detail?: string) {
        this.currentProgress = progress;

        if (window.updateProgress && typeof window.updateProgress === 'function') {
            window.updateProgress(progress, message, detail);
            oops.log.logBusiness(`🎯 加载进度: ${progress}% - ${message}`);
        }
    }

    /** 🚀 完整初始化流程 */
    private async startFullInitialization() {
        try {
            // 🧪 测试开关：控制数据清空行为
            if (ClientConst.alwaysNewPlayerTest && (DEV || DEBUG)) {
                StorageTestUtils.forceNewPlayerState();
                oops.log.logBusiness('🎓 [测试模式] 强制设置为新手状态');
            }

            // 🚀 优化：并行执行可独立的初始化任务
            await Promise.all([
                // 1️⃣ 验证和加载Bundle + 基础UI资源
                this.loadBundleAndUIResources(),
                // 2️⃣ 加载配置数据（配置表 + 语言包）
                this.loadConfigurationData(),
            ]);

            // 3️⃣ 尝试快速用户数据初始化
            await smc.role.quickInitialize();

            // 设置窗口打开失败事件
            oops.gui.setOpenFailure(this.onOpenFailure);

            this.loadComplete = true;

            // 🚀 自定义加载环境直接进入游戏
            if (
                ShareConfig.platform === Platform.FACEBOOK ||
                ShareConfig.platform === Platform.PERSONAL
            ) {
                this.enterGame();
            } else if (this.waitComplete) {
                this.enterGame();
            }
        } catch (error) {
            this.handleInitializationError(error);
        }
    }

    /** 1️⃣ 验证和加载Bundle + 基础UI资源 */
    private async loadBundleAndUIResources(): Promise<void> {
        oops.log.logBusiness('🔍 开始验证Bundle和加载基础UI资源...');

        try {
            // 🚀 直接验证bundleOne是否存在，不存在就报错
            try {
                await oops.res.loadBundle('bundleOne');
                oops.log.logBusiness('✅ bundleOne加载成功');
            } catch (error) {
                throw new Error(`❌ 关键Bundle 'bundleOne' 加载失败: ${error}`);
            }

            // 🚀 延迟导入，减少初始bundle大小
            const { simpleLoader } = await import('../../common/loader/SimpleLoadingManager');

            // 🎯 加载基础UI资源
            const tasks = [
                {
                    name: '加载基础UI资源',
                    dirs: ['boot'],
                    priority: 'high',
                    bundle: 'bundleOne',
                },
            ];

            const result = await simpleLoader.loadTasks(tasks);

            if (!result) {
                throw new Error('基础UI资源加载失败');
            }

            oops.log.logBusiness('✅ Bundle和基础UI资源加载完成');
        } catch (error) {
            oops.log.logError('❌ Bundle和UI资源加载失败:', error);
            throw error;
        }
    }

    /** 2️⃣ 加载配置数据资源（配置表 + 语言包） */
    private async loadConfigurationData(): Promise<void> {
        oops.log.logBusiness('🔄 开始加载配置数据资源...');

        try {
            // 🚀 优化：配置表和语言包可以并行加载
            const [configResult] = await Promise.all([
                JsonUtil.loadDirAsync(),
                this.loadLanguage(),
            ]);

            if (!configResult) {
                throw new Error('配置表加载失败');
            }

            oops.log.logBusiness('✅ 配置数据资源加载完成');
        } catch (error) {
            oops.log.logError('❌ 配置数据资源加载失败:', error);
            throw error;
        }
    }

    /** 加载语言包 */
    private async loadLanguage(): Promise<void> {
        let language = oops.storage.get(GameStorageConfig.Language, 'en');
        language = 'en';
        oops.language.setLanguage(language);
    }

    /** 🎮 进入游戏流程 - 优化版本 */
    private async enterGame(): Promise<void> {
        try {
            oops.log.logBusiness('🎓 开始进入游戏...');

            // 🔍 智能判断新手状态
            let isNewPlayer = smc.role.isNewPlayer();

            oops.log.logBusiness(`🎯 最终判定新手状态: ${isNewPlayer}`);
            oops.log.logBusiness(`🎮 进入${isNewPlayer ? '新手游戏' : '大厅'}场景`);

            let success = false;
            let showLoading = true;

            // 🎯 使用自定义加载界面的平台不显示Cocos默认加载
            if (
                ShareConfig.platform === Platform.FACEBOOK ||
                ShareConfig.platform === Platform.PERSONAL
            ) {
                showLoading = false;
            }

            if (isNewPlayer) {
                oops.log.logBusiness(
                    `🆕 ${ShareConfig.platform === Platform.FACEBOOK ? 'Facebook' : ShareConfig.platform === Platform.PERSONAL ? '个人' : ''}新手玩家：进入游戏场景（Foods）`
                );

                success = await smc.sceneMgr.switchToScene(
                    SceneType.Foods,
                    1,
                    showLoading,
                    this.closeLoadingUI.bind(this)
                );
            } else {
                // 老玩家：进入大厅场景
                oops.log.logBusiness(
                    `👤 ${ShareConfig.platform === Platform.FACEBOOK ? 'Facebook' : ShareConfig.platform === Platform.PERSONAL ? '个人' : ''}老玩家：进入大厅场景（Hall）`
                );
                success = await smc.sceneMgr.switchToScene(
                    SceneType.Hall,
                    undefined,
                    showLoading,
                    this.closeLoadingUI.bind(this)
                );
            }

            if (success) {
                oops.log.logBusiness(`🎮 ${isNewPlayer ? '游戏' : '大厅'}场景加载成功`);
            } else {
                throw new Error(`${isNewPlayer ? '游戏' : '大厅'}场景加载失败`);
            }
        } catch (error) {
            oops.log.logError('🔥 进入游戏失败:', error);

            // 更新加载界面显示错误
            this.updateLoadingProgress(0, '进入游戏失败', '请刷新页面重试');

            oops.gui.toast('进入游戏失败，请刷新页面重试');

            // 🎯 失败时也要移除组件，避免内存泄漏
            this.ent.remove(InitialViewComp);
        }
    }

    /** 🚨 错误处理 */
    private handleInitializationError(error: any): void {
        oops.log.logError('❌ 游戏初始化失败:', error);

        // 更新加载界面显示错误
        this.updateLoadingProgress(0, '初始化失败', error.message || '未知错误');

        // 🚨 如果是Bundle加载失败，显示更明确的错误信息
        if (error.message.includes('bundleOne')) {
            oops.gui.toast('❌ 游戏资源包缺失，请检查资源配置');
            // 🎯 严重错误时移除组件，避免内存泄漏
            this.ent.remove(InitialViewComp);
            return; // 不重试，直接失败
        }

        // 其他错误可以重试
        oops.gui.toast('初始化失败，请检查网络后重试');
        this.retryInitialization();
    }

    /** 🔧 初始化重试机制 */
    private retryInitialization(): void {
        setTimeout(() => {
            oops.log.logBusiness('🔄 尝试重新初始化...');
            this.currentProgress = 0;
            this.updateLoadingProgress(0, '正在重试...', '重新初始化游戏');
            this.startFullInitialization();
        }, 2000);
    }

    /** 通知HTML加载完成 */
    private notifyHTMLLoadingComplete(): void {
        oops.log.logBusiness('🚪 通知HTML加载完成，关闭自定义加载界面');

        try {
            // 更新进度到100%
            if (window.updateProgress && typeof window.updateProgress === 'function') {
                window.updateProgress(100, '加载完成');
                oops.log.logBusiness('✅ HTML进度更新为100%');
            } else {
                oops.log.logBusiness('ℹ️ window.updateProgress 方法不存在（正常情况）');
            }

            // 隐藏加载界面
            if (window.hideLoader && typeof window.hideLoader === 'function') {
                window.hideLoader();
                oops.log.logBusiness('✅ 自定义加载界面已隐藏');
            } else {
                oops.log.logBusiness('ℹ️ window.hideLoader 方法不存在（正常情况）');
            }
        } catch (error) {
            oops.log.logWarn('⚠️ 通知HTML加载完成时出现异常:', error);
        }
    }

    /** 关闭加载界面 */
    private closeLoadingUI(): void {
        // 🎯 计算启动总时间
        const endTime = Date.now();
        const totalTime = endTime - this.startTime;

        // 🎯 自定义加载界面的平台都调用HTML通知
        if (
            ShareConfig.platform === Platform.FACEBOOK ||
            ShareConfig.platform === Platform.PERSONAL
        ) {
            this.notifyHTMLLoadingComplete();
        }

        // Facebook生产环境额外通知
        if (ShareConfig.platform === Platform.FACEBOOK) {
            this.notifyFacebookComplete();
        }

        oops.log.logBusiness('🎉 游戏启动完成！', {
            totalTime: `${totalTime}ms`,
            performance: totalTime < 3000 ? '优秀' : totalTime < 5000 ? '良好' : '需要优化',
        });

        // 移除自己
        this.ent.remove(InitialViewComp);
    }

    /** 通知Facebook完成 */
    private notifyFacebookComplete(): void {
        if (window.FBInstant && window.FBInstant.setLoadingProgress) {
            window.FBInstant.setLoadingProgress(100);
            oops.log.logBusiness('📊 Facebook SDK: 进度已设置为100%');
        }
    }

    /** 窗口打开失败回调 */
    onOpenFailure(): void {
        oops.log.logError('❌ 窗口打开失败');
    }

    reset(): void {
        this.waitComplete = false;
        this.loadComplete = false;
        this.currentProgress = 0;
    }
}
