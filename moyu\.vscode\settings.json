{"files.exclude": {"*.c.orig": true, ".git": true, ".meta": false}, "search.exclude": {"*.c.orig": true, ".git": true, ".meta": false}, "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.suggest.includeCompletionsForModuleExports": true, "typescript.preferences.importModuleSpecifier": "relative", "files.associations": {"*.ts": "typescript"}, "typescript.tsc.autoDetect": "off", "typescript.preferences.typescript.path": "./temp/tsconfig.cocos.json", "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.removeUnusedImports": "explicit", "source.sortImports": "explicit"}, "typescript.preferences.organizeImports.enabled": true, "typescript.suggest.includeAutomaticOptionalChainCompletions": true, "editor.formatOnSave": true, "typescript.updateImportsOnFileMove.enabled": "always", "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": false}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": false}, "prettier.requireConfig": false, "prettier.useEditorConfig": true, "prettier.resolveGlobalModules": false, "files.watcherExclude": {"**/assets/resources/config/*.json": true, "**/assets/script/game/common/table/*.json": true}}