{"__type__": "cc.Json<PERSON>set", "_name": "cc_obfuscated_js", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "json": {"许可证": {"deviceId": "670b37b158772c816163f762d0325c68c33da01a623507760a1f39d3c25d1f65", "storeId": "STOREb98c4d93d4f8b", "hardwareHash": "670b37b158772c816163f762d0325c68c33da01a623507760a1f39d3c25d1f65", "expiryDate": "2025-5-2", "_meta": {"description": "📝 设备许可证信息", "emoji": "🔐", "createdAt": "2025-03-31T08:49:32.707Z", "status": "valid", "lastChecked": "2025-07-12T06:41:14.856Z"}, "value": "544f3ed6fa8085b4248df2d204e30ae7小有QQ386437237@qq.com761d5948d3cb7e8fe62877e4a84ae48de112bee176dc8e57fd89a32fc926cd050e8b807fc5370232bf620d43606b44d1722fe4197090e126bbe3e591935a323bc8c2a92332b1aabbbad5541bed4f627d25941b9e52a89f3330a2e08519ad9b8906f004b65f905c2617de200f06a02800f81af6581118ce6ac34a78f6405c5560d092418297aaa967fe3b511f02acd7058d01bde2ee2e6f12d24ff1ecbaf0573dd0ac9c9a5c5307ce198f3a4c64e39f4f"}, "基本设置": {"createBackup": false, "compact": true, "target": "browser", "log": false, "simplify": false}, "保护设置": {"selfDefending": false, "debugProtection": false, "debugProtectionInterval": 0, "disableConsoleOutput": false}, "混淆强度": {"controlFlowFlattening": true, "controlFlowFlatteningThreshold": 0.25, "deadCodeInjection": true, "deadCodeInjectionThreshold": 0.25, "stringArrayCallsTransformThreshold": 0.2, "stringArrayThreshold": 0.2}, "字符串处理": {"stringArray": true, "stringArrayRotate": false, "stringArrayShuffle": false, "stringArrayEncoding": ["rc4"], "stringArrayIndexesType": ["hexadecimal-number"], "stringArrayIndexShift": false, "stringArrayWrappersCount": 1, "stringArrayWrappersType": "variable", "stringArrayWrappersChainedCalls": false, "stringArrayWrappersParametersMaxCount": 2, "stringArrayThreshold": 0.3, "stringArrayCallsTransform": false, "forceTransformStrings": [], "splitStrings": true, "splitStringsChunkLength": 3, "rotateStringArray": false, "shuffleStringArray": false}, "标识符处理": {"identifierNamesGenerator": "hexadecimal", "identifiersPrefix": "", "identifiersDictionary": [], "transformObjectKeys": false, "renameGlobals": false, "renameProperties": false, "renamePropertiesMode": "safe", "reservedNames": []}, "源码映射": {"sourceMap": false, "sourceMapMode": "separate", "sourceMapBaseUrl": "", "sourceMapFileName": "", "sourceMapSourcesMode": "sources-content"}, "过滤设置": {"filePathExclude": ["node_modules", "js backups (useful for debugging)", "dist", "cocos-js/", "/node_modules/", "/cocos2d/", "/cocos-2d/", "/cocos-js/", "libs"], "fileNameExclude": ["副本", "jquery*.js", "vue.min.js", "config.js", "vconsole", "physics", "cocos", "cocos2d", "cocos-2d", "cc"], "excludeReasons": {"engineFile": "引擎核心文件，混淆可能导致游戏异常", "userExcluded": "用户手动排除的文件", "过滤设置": "引擎相关的文件和目录，混淆后可能导致功能异常", "filePathExclude": "不需要混淆的目录路径", "fileNameExclude": "不需要混淆的文件名模式"}}, "auto": true, "特殊功能": {"domainLock": [], "domainLockEnabled": false, "domainLockRedirectUrl": "about:blank", "reservedNames": [], "reservedStrings": [], "seed": 214, "numbersToExpressions": false, "unicodeEscapeSequence": false}, "compact": true, "controlFlowFlattening": false, "controlFlowFlatteningThreshold": 0.25, "deadCodeInjection": false, "deadCodeInjectionThreshold": 0.35, "stringArray": true, "stringArrayThreshold": 0.3, "createBackup": false, "target": "browser", "renameGlobals": false, "renameProperties": false, "renamePropertiesMode": "safe", "rotateStringArray": false, "seed": 214, "shuffleStringArray": false, "selfDefending": false, "disableConsoleOutput": false, "debugProtection": false, "debugProtectionInterval": 0, "splitStrings": true, "splitStringsChunkLength": 3, "splitStringsChunkLengthEnabled": false, "stringArrayRotate": false, "stringArrayRotateEnabled": false, "stringArrayShuffle": false, "stringArrayShuffleEnabled": false, "simplify": false, "stringArrayThresholdEnabled": false, "stringArrayIndexesType": ["hexadecimal-number"], "stringArrayIndexShift": false, "stringArrayCallsTransform": false, "stringArrayEncoding": ["rc4"], "stringArrayEncodingEnabled": false, "stringArrayWrappersCount": 1, "stringArrayWrappersChainedCalls": false, "stringArrayWrappersParametersMaxCount": 2, "stringArrayWrappersType": "variable", "numbersToExpressions": false, "sourceMap": false, "sourceMapMode": "separate", "sourceMapBaseUrl": "", "sourceMapFileName": "", "domainLock": [], "domainLockRedirectUrl": "about:blank", "domainLockEnabled": false, "forceTransformStrings": [], "reservedNames": [], "unicodeEscapeSequence": false, "identifiersDictionary": [], "identifiersPrefix": "", "ignoreImports": false, "config": "", "exclude": [], "identifierNamesCache": null, "inputFileName": "", "sourceMapSourcesMode": "sources-content"}}