[1, ["14I5OoJkFHdaecy/4b2eXK"], ["node", "root", "asset", "data"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_children", "_components", "_parent"], -1, 4, 2, 9, 1], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["cc.TargetInfo", ["localID"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["f804bSDawpGdLpqrYqiL1nd", ["node", "__prefab"], 3, 1, 4]], [[6, 0, 1, 2, 2], [9, 0, 2], [1, 0, 2], [0, 0, 1, 5, 6, 4, 3], [0, 2, 3, 7, 4, 3], [2, 0, 1, 2, 3, 4, 5, 4], [3, 0, 1, 2, 3, 4, 5, 4], [4, 0, 1, 2, 2], [5, 0, 1, 2, 3], [7, 0, 2], [8, 0, 1, 2, 1], [10, 0, 1, 2, 3, 4, 4], [11, 0, 1, 1]], [[2, "2dExample"], [3, "2dExample", 33554432, [-6], [[10, -3, [1, "77N2cid5pKDpXplRH/AWEU"], [5, 1080, 1920]], [11, 45, 2, 2, -4, [1, "63zNQq8NlBQ5QWzOJ4Kgjs"]], [12, -5, [1, "3a3NZbSQpLkqZf0VEZrcPk"]]], [6, "a0daVw8DRLi6ToMaTA0VS2", null, null, -2, 0, [-1]]], [9, ["a0daVw8DRLi6ToMaTA0VS2"]], [4, 0, {}, 1, [5, "a0daVw8DRLi6ToMaTA0VS2", null, null, -7, [7, "4bd7+FLBlMqJQRLHhjl3Px", 1, [[8, "mask", ["_name"], 2], [0, ["_lpos"], 2, [1, 0, 0, 0]], [0, ["_lrot"], 2, [3, 0, 0, 0, 1]], [0, ["_euler"], 2, [1, 0, 0, 0]]]], 0]]], 0, [0, -1, 3, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, 1, 3, 0, 3, 1, 7], [0], [2], [0]]