[1, ["e5LUoqx3RAr41dA5QrbKMj", "33Kf06/kpOuIuw8W1AaM24@2dd4e"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 438234331, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 55392, "length": 9372, "count": 4686, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 55392, "count": 1154, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.5784791111946106, -0.5561023950576782, -0.18376025557518005], "maxPosition", 8, [1, 0.5810109376907349, 0.5564538836479187, 0.18039080500602722]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_06"], [3, "日式寿司_06", [[4, 1, -2, [0, "2bVVKlJmZOlI9wrVZ3M0Pm"], [0], [5, true, true], 1], [6, 4, -3, [0, "d4o/eKf99JhKuyy2j0GxvH"]], [7, 0.558, 0, -4, [0, "8b5mo+oH9ATZVLav3nX+iW"], [1, 0.01433485746383667, -0.025503411889076233, 5.37186861038208e-05]]], [8, "51gRP0l3RFS6i9carYB7Az", null, null, null, -1, 0], [1, 3.588, 0, -0.69]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]