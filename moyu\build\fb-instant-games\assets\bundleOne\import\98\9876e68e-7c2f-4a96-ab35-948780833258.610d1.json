[1, 0, 0, [["cc.EffectAsset", ["_name", "shaders", "techniques"], 0]], [[0, 0, 1, 2, 4]], [[0, "../bundleOne/common/effect/surface-effect", [{"hash": 485432764, "name": "../bundleOne/common/effect/surface-effect|standard-vs|standard-fs", "blocks": [{"name": "Constants", "stageFlags": 17, "binding": 0, "members": [{"name": "albedo", "type": 16, "count": 1}, {"name": "albedoScaleAnd<PERSON><PERSON>ff", "type": 16, "count": 1}, {"name": "pbrParams", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [{"name": "albedoMap", "type": 28, "count": 1, "stageFlags": 16, "binding": 1, "defines": ["USE_ALBEDO_MAP"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_color", "format": 44, "location": 6, "defines": ["USE_VERTEX_COLOR"]}, {"name": "a_texCoord1", "format": 21, "location": 7, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 16, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 17, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 18, "defines": ["CC_USE_MORPH"]}], "fragColors": [{"name": "fragColorX", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "tags": [], "defines": []}, {"name": "albedoOut", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 1, "tags": ["CC_PIPELINE_TYPE"], "defines": ["CC_PIPELINE_TYPE"]}, {"name": "emissiveOut", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 2, "defines": ["CC_PIPELINE_TYPE"]}, {"name": "normalOut", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 3, "defines": ["CC_PIPELINE_TYPE"]}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 17, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["!USE_INSTANCING"]}, {"name": "CCMorph", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_displacementWeights", "typename": "vec4", "type": 16, "count": 15, "isArray": true}, {"name": "cc_displacementTextureInfo", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointTextureInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointAnimInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_joints", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}], "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCForwardLight", "stageFlags": 16, "tags": {"builtin": "local"}, "members": [{"name": "cc_lightPos", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}, {"name": "cc_lightColor", "typename": "vec4", "type": 16, "count": 0, "isArray": true}, {"name": "cc_lightSizeRangeAngle", "typename": "vec4", "type": 16, "count": 0, "isArray": true}, {"name": "cc_lightDir", "typename": "vec4", "type": 16, "count": 0, "isArray": true}, {"name": "cc_lightBoundingSizeVS", "typename": "vec4", "type": 16, "count": 0, "isArray": true}], "defines": ["CC_FORWARD_ADD", "CC_ENABLE_CLUSTERED_LIGHT_CULLING"]}, {"name": "CCSH", "stageFlags": 16, "tags": {"builtin": "local"}, "members": [{"name": "cc_sh_linear_const_r", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_g", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_b", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_r", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_g", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_b", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_a", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_LIGHT_PROBE", "!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "cc_reflectionProbeCubemap", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbePlanarMap", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbeDataMap", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbeBlendCubemap", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_lightingMap", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_LIGHTMAP", "!CC_FORWARD_ADD"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constants", "stageFlags": 17, "binding": 0, "members": [{"name": "albedo", "type": 16, "count": 1}, {"name": "albedoScaleAnd<PERSON><PERSON>ff", "type": 16, "count": 1}, {"name": "pbrParams", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [{"name": "albedoMap", "type": 28, "count": 1, "stageFlags": 16, "binding": 1, "defines": ["USE_ALBEDO_MAP"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCShadow", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matLightView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matLightViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_shadowInvProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowNFLSInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowWHPBInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowLPNNInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowColor", "typename": "vec4", "type": 16, "count": 1, "precision": "lowp "}, {"name": "cc_planarNDInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCSM", "stageFlags": 16, "tags": {"builtin": "global"}, "members": [{"name": "cc_csmViewDir0", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmViewDir1", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmViewDir2", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmAtlas", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_matCSMViewProj", "typename": "mat4", "type": 25, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmProjDepthInfo", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmProjInfo", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmSplitsInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_SUPPORT_CASCADED_SHADOW_MAP"]}], "samplerTextures": [{"name": "cc_environment", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "global"}, "defines": []}, {"name": "cc_diffuseMap", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "global"}, "defines": ["CC_USE_IBL", "CC_USE_DIFFUSEMAP"]}, {"name": "cc_shadowMap", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 16, "tags": {"builtin": "global"}, "defines": ["CC_RECEIVE_SHADOW"]}, {"name": "cc_spotShadowMap", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 16, "tags": {"builtin": "global"}, "defines": ["CC_RECEIVE_SHADOW"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision highp float;\n#define CC_SURFACES_USE_TWO_SIDED USE_TWOSIDE\n#define CC_SURFACES_USE_VERTEX_COLOR USE_VERTEX_COLOR\n#ifndef CC_SURFACES_USE_SECOND_UV\n  #define CC_SURFACES_USE_SECOND_UV 0\n#endif\n#ifndef CC_SURFACES_USE_TANGENT_SPACE\n  #define CC_SURFACES_USE_TANGENT_SPACE 0\n#endif\n#ifndef CC_SURFACES_USE_VERTEX_COLOR\n  #define CC_SURFACES_USE_VERTEX_COLOR 0\n#endif\n#ifndef CC_SURFACES_TRANSFER_LOCAL_POS\n  #define CC_SURFACES_TRANSFER_LOCAL_POS 0\n#endif\n#ifndef CC_SURFACES_TRANSFER_CLIP_POS\n  #define CC_SURFACES_TRANSFER_CLIP_POS 0\n#endif\n#ifndef CC_SURFACES_USE_LIGHT_MAP\n  #ifdef CC_USE_LIGHTMAP\n    #define CC_SURFACES_USE_LIGHT_MAP CC_USE_LIGHTMAP\n  #else\n    #define CC_SURFACES_USE_LIGHT_MAP 0\n  #endif\n#endif\n#ifndef CC_SURFACES_FLIP_UV\n  #define CC_SURFACES_FLIP_UV 0\n#endif\n#ifndef CC_SURFACES_USE_TWO_SIDED\n  #define CC_SURFACES_USE_TWO_SIDED 0\n#endif\n#ifndef CC_SURFACES_USE_REFLECTION_DENOISE\n  #define CC_SURFACES_USE_REFLECTION_DENOISE 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_ANISOTROPIC\n  #define CC_SURFACES_LIGHTING_ANISOTROPIC 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT\n  #define CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT 0\n#endif\n#ifndef CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING\n  #define CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_FRESNEL\n  #define CC_SURFACES_LIGHTING_USE_FRESNEL 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n  #define CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  #define CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT\n  #define CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TRT\n  #define CC_SURFACES_LIGHTING_TRT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR\n  #define CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_SHEEN\n  #define CC_SURFACES_LIGHTING_SHEEN 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_CLEAR_COAT\n  #define CC_SURFACES_LIGHTING_CLEAR_COAT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TT\n  #define CC_SURFACES_LIGHTING_TT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_SSS\n  #define CC_SURFACES_LIGHTING_SSS 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n  #if CC_SURFACES_LIGHTING_TRT || CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR || CC_SURFACES_LIGHTING_SHEEN || CC_SURFACES_LIGHTING_CLEAR_COAT\n    #define CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR 1\n  #endif\n#endif\n#ifndef CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n  #define CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n  #if CC_SURFACES_LIGHTING_CLEAR_COAT\n    #define CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND 1\n  #endif\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n  #define CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND 0\n#endif\n#ifndef CC_SURFACES_ENABLE_DEBUG_VIEW\n  #define CC_SURFACES_ENABLE_DEBUG_VIEW 1\n#endif\n#define CC_USE_SURFACE_SHADER 1\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\n#if CC_SURFACES_USE_TANGENT_SPACE\n  in vec4 a_tangent;\n#endif\n#if CC_SURFACES_USE_VERTEX_COLOR\n  in vec4 a_color;\n#endif\n#if CC_SURFACES_USE_SECOND_UV || CC_USE_LIGHTMAP\n  in vec2 a_texCoord1;\n#endif\n#if CC_USE_SKINNING\n    in vec4 a_joints;\n  in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    in highp vec4 a_jointAnimInfo;\n  #endif\n  in vec4 a_matWorld0;\n  in vec4 a_matWorld1;\n  in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n    in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    in vec4 a_sh_linear_const_r;\n    in vec4 a_sh_linear_const_g;\n    in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    in float a_vertexId;\n#endif\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nout highp vec3 v_worldPos;\nout vec4 v_normal;\nout vec2 v_uv;\n#if CC_SURFACES_USE_VERTEX_COLOR\n  out lowp vec4 v_color;\n#endif\n#if CC_SURFACES_USE_TANGENT_SPACE\n  out mediump vec4 v_tangent;\n#endif\n#if CC_SURFACES_USE_SECOND_UV\n  out mediump vec2 v_uv1;\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  out mediump vec3 v_luv;\n#endif\n#if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n  out mediump vec4 v_shadowBiasAndProbeId;\n#endif\n#if CC_USE_REFLECTION_PROBE && USE_INSTANCING\n  out mediump vec4 v_reflectionProbeData;\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  out mediump float v_fogFactor;\n#endif\n#if CC_SURFACES_TRANSFER_LOCAL_POS\n  out highp vec4 v_localPos;\n#endif\n#if CC_SURFACES_TRANSFER_CLIP_POS\n  out highp vec4 v_clipPos;\n#endif\n#if CC_USE_LIGHT_PROBE\n  #if USE_INSTANCING\n    out mediump vec4 v_sh_linear_const_r;\n    out mediump vec4 v_sh_linear_const_g;\n    out mediump vec4 v_sh_linear_const_b;\n  #endif\n#endif\n#define VSOutput_worldPos v_worldPos\n#define VSOutput_worldNormal v_normal.xyz\n#define VSOutput_faceSideSign v_normal.w\n#define VSOutput_texcoord v_uv\n#if CC_SURFACES_USE_VERTEX_COLOR\n  #define VSOutput_vertexColor v_color\n#endif\n#if CC_SURFACES_USE_TANGENT_SPACE\n  #define VSOutput_worldTangent v_tangent.xyz\n  #define VSOutput_mirrorNormal v_tangent.w\n#endif\n#if CC_SURFACES_USE_SECOND_UV\n  #define VSOutput_texcoord1 v_uv1\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  #define VSOutput_lightMapUV v_luv\n#endif\n#if CC_RECEIVE_SHADOW\n  #define VSOutput_shadowBias v_shadowBiasAndProbeId.xy\n#endif\n#if CC_USE_REFLECTION_PROBE\n  #define VSOutput_reflectionProbeId v_shadowBiasAndProbeId.z\n  #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n    #define VSOutput_reflectionProbeBlendId v_shadowBiasAndProbeId.w\n  #endif\n  #if USE_INSTANCING\n    #define VSOutput_reflectionProbeData v_reflectionProbeData\n  #endif\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  #define VSOutput_fogFactor v_fogFactor\n#endif\n#if CC_SURFACES_TRANSFER_LOCAL_POS\n  #define VSOutput_localPos v_localPos\n#endif\n#if CC_SURFACES_TRANSFER_CLIP_POS\n  #define VSOutput_clipPos v_clipPos\n#endif\nstruct SurfacesStandardVertexIntermediate\n{\n  highp vec4 position;\n  vec3 normal;\n#if CC_SURFACES_USE_TANGENT_SPACE\n  vec4 tangent;\n#endif\n#if CC_SURFACES_USE_VERTEX_COLOR\n  vec4 color;\n#endif\n  vec2 texCoord;\n#if CC_SURFACES_USE_SECOND_UV\n  vec2 texCoord1;\n#endif\n  highp vec4 clipPos;\n  highp vec3 worldPos;\n  vec4 worldNormal;\n  #if CC_SURFACES_USE_TANGENT_SPACE\n    vec3 worldTangent, worldBinormal;\n  #endif\n#if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n  vec4 shadowBiasAndProbeId;\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  float fogFactor;\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  vec3 lightmapUV;\n#endif\n};\n#if CC_USE_MORPH\n    int getVertexId() {\n      return int(a_vertexId);\n    }\n#endif\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(std140) uniform CCShadow {\n  highp mat4 cc_matLightView;\n  highp mat4 cc_matLightViewProj;\n  highp vec4 cc_shadowInvProjDepthInfo;\n  highp vec4 cc_shadowProjDepthInfo;\n  highp vec4 cc_shadowProjInfo;\n  mediump vec4 cc_shadowNFLSInfo;\n  mediump vec4 cc_shadowWHPBInfo;\n  mediump vec4 cc_shadowLPNNInfo;\n  lowp vec4 cc_shadowColor;\n  mediump vec4 cc_planarNDInfo;\n};\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if !USE_INSTANCING\n  layout(std140) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n#endif\nvoid CCGetWorldMatrixFull(out mat4 matWorld, out mat4 matWorldIT)\n{\n  #if USE_INSTANCING\n    matWorld = mat4(\n      vec4(a_matWorld0.xyz, 0.0),\n      vec4(a_matWorld1.xyz, 0.0),\n      vec4(a_matWorld2.xyz, 0.0),\n      vec4(a_matWorld0.w, a_matWorld1.w, a_matWorld2.w, 1.0)\n    );\n    vec3 scale = 1.0 / vec3(length(a_matWorld0.xyz), length(a_matWorld1.xyz), length(a_matWorld2.xyz));\n    vec3 scale2 = scale * scale;\n    matWorldIT = mat4(\n      vec4(a_matWorld0.xyz * scale2.x, 0.0),\n      vec4(a_matWorld1.xyz * scale2.y, 0.0),\n      vec4(a_matWorld2.xyz * scale2.z, 0.0),\n      vec4(0.0, 0.0, 0.0, 1.0)\n    );\n  #else\n    matWorld = cc_matWorld;\n    matWorldIT = cc_matWorldIT;\n  #endif\n}\n#if CC_USE_MORPH\n  layout(std140) uniform CCMorph {\n    vec4 cc_displacementWeights[15];\n    vec4 cc_displacementTextureInfo;\n  };\n  #if CC_MORPH_TARGET_HAS_POSITION\n    uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int pixelIndex) {\n        ivec2 texSize = textureSize(tex, 0);\n        return texelFetch(tex, ivec2(pixelIndex % texSize.x, pixelIndex / texSize.x), 0);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture(tex, x)),\n        decode32(texture(tex, y)),\n        decode32(texture(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    layout(std140) uniform CCSkinningTexture {\n      highp vec4 cc_jointTextureInfo;\n    };\n    layout(std140) uniform CCSkinningAnimation {\n      highp vec4 cc_jointAnimInfo;\n    };\n    uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      uniform highp sampler2D cc_realtimeJoint;\n    #else\n      layout(std140) uniform CCSkinning {\n        highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n      };\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  #if CC_USE_FOG != 4\n    float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n        vec4 wPos = pos;\n        float cam_dis = distance(cameraPos, wPos.xyz);\n        return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n    }\n    float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n        vec4 wPos = pos;\n        float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n        float f = exp(-cam_dis * fogDensity);\n        return f;\n    }\n    float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n        vec4 wPos = pos;\n        float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n        float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n        return f;\n    }\n    float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n        vec4 wPos = pos;\n        vec3 camWorldProj = cameraPos.xyz;\n        camWorldProj.y = 0.;\n        vec3 worldPosProj = wPos.xyz;\n        worldPosProj.y = 0.;\n        float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n        float fDeltaY, fDensityIntegral;\n        if (cameraPos.y > fogTop) {\n            if (wPos.y < fogTop) {\n                fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n                fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n            }\n            else {\n                fDeltaY = 0.;\n                fDensityIntegral = 0.;\n            }\n        }\n        else {\n            if (wPos.y < fogTop) {\n                float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n                float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n                fDeltaY = abs(fDeltaA - fDeltaB);\n                fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n            }\n            else {\n                fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n                fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n            }\n        }\n        float fDensity;\n        if (fDeltaY != 0.) {\n            fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n        }\n        else {\n            fDensity = 0.;\n        }\n        float f = exp(-fDensity);\n        return f;\n    }\n  #endif\n  void CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n  {\n  #if CC_USE_FOG == 0\n  \tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n  #elif CC_USE_FOG == 1\n  \tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n  #elif CC_USE_FOG == 2\n  \tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n  #elif CC_USE_FOG == 3\n  \tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n  #else\n  \tfactor = 1.0;\n  #endif\n  }\n#endif\nlayout(std140) uniform Constants {\n  vec4 albedo;\n  vec4 albedoScaleAndCutoff;\n  vec4 pbrParams;\n};\n#define CC_SURFACES_VERTEX_MODIFY_WORLD_POS\nvec3 SurfacesVertexModifyWorldPos(in SurfacesStandardVertexIntermediate In)\n{\n  return In.worldPos;\n}\n#define CC_SURFACES_VERTEX_MODIFY_WORLD_NORMAL\nvec3 SurfacesVertexModifyWorldNormal(in SurfacesStandardVertexIntermediate In)\n{\n  return In.worldNormal.xyz;\n}\n#define CC_SURFACES_VERTEX_MODIFY_UV\nvoid SurfacesVertexModifyUV(inout SurfacesStandardVertexIntermediate In)\n{\n}\n#ifndef CC_SURFACES_VERTEX_MODIFY_LOCAL_POS\nvec3 SurfacesVertexModifyLocalPos(in SurfacesStandardVertexIntermediate In)\n{\n  return vec3(In.position.xyz);\n}\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_LOCAL_NORMAL\nvec3 SurfacesVertexModifyLocalNormal(in SurfacesStandardVertexIntermediate In)\n{\n  return In.normal.xyz;\n}\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_LOCAL_TANGENT\n  #if CC_SURFACES_USE_TANGENT_SPACE\n  vec4 SurfacesVertexModifyLocalTangent(in SurfacesStandardVertexIntermediate In)\n    {\n      return In.tangent;\n    }\n  #endif\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_LOCAL_SHARED_DATA\nvoid SurfacesVertexModifyLocalSharedData(inout SurfacesStandardVertexIntermediate In)\n{\n}\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_WORLD_POS\nvec3 SurfacesVertexModifyWorldPos(in SurfacesStandardVertexIntermediate In)\n{\n  return In.worldPos;\n}\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_CLIP_POS\nvec4 SurfacesVertexModifyClipPos(in SurfacesStandardVertexIntermediate In)\n{\n  return In.clipPos;\n}\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_UV\nvoid SurfacesVertexModifyUV(inout SurfacesStandardVertexIntermediate In)\n{\n}\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_WORLD_NORMAL\nvec3 SurfacesVertexModifyWorldNormal(in SurfacesStandardVertexIntermediate In)\n{\n    vec3 worldNormal = In.worldNormal.xyz;\n  #if CC_SURFACES_USE_TWO_SIDED\n      worldNormal.xyz *= In.worldNormal.w;\n  #endif\n  return worldNormal;\n}\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_SHADOW_BIAS\nvec2 SurfacesVertexModifyShadowBias(in SurfacesStandardVertexIntermediate In, vec2 originShadowBias)\n{\n  return originShadowBias;\n}\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_SHARED_DATA\nvoid SurfacesVertexModifySharedData(inout SurfacesStandardVertexIntermediate In)\n{\n}\n#endif\nvoid CCSurfacesVertexInput(out SurfacesStandardVertexIntermediate In)\n{\n  In.position = vec4(a_position, 1.0);\n  In.normal = a_normal;\n#if CC_SURFACES_USE_TANGENT_SPACE\n  In.tangent = a_tangent;\n#endif\n#if CC_SURFACES_USE_VERTEX_COLOR\n  In.color = a_color;\n#endif\n  In.texCoord = a_texCoord;\n#if CC_SURFACES_USE_SECOND_UV\n  In.texCoord1 = a_texCoord1;\n#endif\n}\nvoid CCSurfacesVertexOutput(in SurfacesStandardVertexIntermediate In)\n{\n  gl_Position = In.clipPos;\n  VSOutput_worldNormal = In.worldNormal.xyz;\n  VSOutput_faceSideSign = In.worldNormal.w;\n  VSOutput_worldPos = In.worldPos;\n#if CC_SURFACES_USE_TANGENT_SPACE\n  VSOutput_worldTangent = In.worldTangent.xyz;\n  VSOutput_mirrorNormal = In.tangent.w > 0.0 ? 1.0 : -1.0;\n#endif\n#if CC_SURFACES_USE_VERTEX_COLOR\n  VSOutput_vertexColor = In.color;\n#endif\n  VSOutput_texcoord = In.texCoord;\n#if CC_SURFACES_USE_SECOND_UV\n  VSOutput_texcoord1 = In.texCoord1;\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  VSOutput_fogFactor = In.fogFactor;\n#endif\n#if CC_RECEIVE_SHADOW\n  VSOutput_shadowBias = In.shadowBiasAndProbeId.xy;\n#endif\n#if CC_USE_REFLECTION_PROBE\n  VSOutput_reflectionProbeId = In.shadowBiasAndProbeId.z;\n  #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n    VSOutput_reflectionProbeBlendId = In.shadowBiasAndProbeId.w;\n  #endif\n  #if USE_INSTANCING\n    v_reflectionProbeData = a_reflectionProbeData;\n  #endif\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  VSOutput_lightMapUV = In.lightmapUV;\n#endif\n#if CC_SURFACES_TRANSFER_LOCAL_POS\n  VSOutput_localPos = In.position;\n#endif\n#if CC_SURFACES_TRANSFER_CLIP_POS\n  VSOutput_clipPos = In.clipPos;\n#endif\n#if CC_USE_LIGHT_PROBE\n  #if USE_INSTANCING\n    v_sh_linear_const_r = a_sh_linear_const_r;\n    v_sh_linear_const_g = a_sh_linear_const_g;\n    v_sh_linear_const_b = a_sh_linear_const_b;\n  #endif\n#endif\n}\nvoid CCSurfacesVertexAnimation(inout SurfacesStandardVertexIntermediate In)\n{\nvec4 temp = vec4(0.0);\n#if CC_USE_MORPH\n  #if CC_SURFACES_USE_TANGENT_SPACE\n    applyMorph(In.position, In.normal, In.tangent);\n  #else\n    applyMorph(In.position, In.normal, temp);\n  #endif\n#endif\n#if CC_USE_SKINNING\n  #if CC_SURFACES_USE_TANGENT_SPACE\n    CCSkin(In.position, In.normal, In.tangent);\n  #else\n    CCSkin(In.position, In.normal, temp);\n  #endif\n#endif\n}\nvoid CCSurfacesVertexWorldTransform(inout SurfacesStandardVertexIntermediate In)\n{\n    mat4 matWorld, matWorldIT;\n    CCGetWorldMatrixFull(matWorld, matWorldIT);\n    In.worldPos = (matWorld * In.position).xyz;\n    In.worldNormal.xyz = normalize((matWorldIT * vec4(In.normal.xyz, 0.0)).xyz);\n    #if CC_SURFACES_USE_TANGENT_SPACE\n      In.worldTangent = normalize((matWorld * vec4(In.tangent.xyz, 0.0)).xyz);\n      In.worldBinormal = cross(In.worldNormal.xyz, In.worldTangent) * In.tangent.w;\n    #endif\n}\nvoid CCSurfacesVertexTransformUV(inout SurfacesStandardVertexIntermediate In)\n{\n  #if CC_SURFACES_FLIP_UV\n    In.texCoord = cc_cameraPos.w > 1.0 ? vec2(In.texCoord.x, 1.0 - In.texCoord.y) : In.texCoord;\n    #if CC_SURFACES_USE_SECOND_UV\n      In.texCoord1 = cc_cameraPos.w > 1.0 ? vec2(In.texCoord1.x, 1.0 - In.texCoord1.y) : In.texCoord1;\n    #endif\n  #endif\n}\nvoid CCSurfacesVertexTransferFog(inout SurfacesStandardVertexIntermediate In)\n{\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n    CC_TRANSFER_FOG_BASE(vec4(In.worldPos, 1.0), In.fogFactor);\n#endif\n}\nvoid CCSurfacesVertexTransferShadow(inout SurfacesStandardVertexIntermediate In)\n{\n  #if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n    In.shadowBiasAndProbeId = vec4(0.0);\n  #endif\n  #if CC_RECEIVE_SHADOW\n    In.shadowBiasAndProbeId.xy = vec2(cc_shadowWHPBInfo.w, cc_shadowLPNNInfo.z);\n    #if USE_INSTANCING\n      In.shadowBiasAndProbeId.xy += a_localShadowBiasAndProbeId.xy;\n    #else\n      In.shadowBiasAndProbeId.xy += cc_localShadowBias.xy;\n    #endif\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    #if USE_INSTANCING\n      In.shadowBiasAndProbeId.zw = a_localShadowBiasAndProbeId.zw;\n    #else\n      In.shadowBiasAndProbeId.zw = cc_localShadowBias.zw;\n    #endif\n  #endif\n}\nvoid CCSurfacesVertexTransferLightMapUV(inout SurfacesStandardVertexIntermediate In)\n{\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  #if USE_INSTANCING\n    In.lightmapUV.xy = a_lightingMapUVParam.xy + a_texCoord1 * a_lightingMapUVParam.z;\n    In.lightmapUV.z = a_lightingMapUVParam.w;\n  #else\n    In.lightmapUV.xy = cc_lightingMapUVParam.xy + a_texCoord1 * cc_lightingMapUVParam.z;\n    In.lightmapUV.z = cc_lightingMapUVParam.w;\n  #endif\n#endif\n}\nvoid main()\n{\n  SurfacesStandardVertexIntermediate In;\n  CCSurfacesVertexInput(In);\n  CCSurfacesVertexAnimation(In);\n  In.position.xyz = SurfacesVertexModifyLocalPos(In);\n  In.normal.xyz = SurfacesVertexModifyLocalNormal(In);\n  #if CC_SURFACES_USE_TANGENT_SPACE\n    In.tangent = SurfacesVertexModifyLocalTangent(In);\n  #endif\n  SurfacesVertexModifyLocalSharedData(In);\n  CCSurfacesVertexWorldTransform(In);\n  In.worldPos = SurfacesVertexModifyWorldPos(In);\n  In.clipPos = cc_matProj * cc_matView * vec4(In.worldPos, 1.0);\n  In.clipPos = SurfacesVertexModifyClipPos(In);\n  vec3 viewDirect = normalize(cc_cameraPos.xyz - In.worldPos);\n  In.worldNormal.w = dot(In.worldNormal.xyz, viewDirect) < 0.0 ? -1.0 : 1.0;\n  In.worldNormal.xyz = SurfacesVertexModifyWorldNormal(In);\n  SurfacesVertexModifyUV(In);\n  SurfacesVertexModifySharedData(In);\n  CCSurfacesVertexTransformUV(In);\n  CCSurfacesVertexTransferFog(In);\n  CCSurfacesVertexTransferLightMapUV(In);\n  CCSurfacesVertexTransferShadow(In);\n  #if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n    In.shadowBiasAndProbeId.xy = SurfacesVertexModifyShadowBias(In, In.shadowBiasAndProbeId.xy);\n  #endif\n  CCSurfacesVertexOutput(In);\n}", "frag": "\nprecision highp float;\n#define CC_SURFACES_USE_TWO_SIDED USE_TWOSIDE\n#define CC_SURFACES_USE_VERTEX_COLOR USE_VERTEX_COLOR\n#ifndef CC_SURFACES_USE_SECOND_UV\n  #define CC_SURFACES_USE_SECOND_UV 0\n#endif\n#ifndef CC_SURFACES_USE_TANGENT_SPACE\n  #define CC_SURFACES_USE_TANGENT_SPACE 0\n#endif\n#ifndef CC_SURFACES_USE_VERTEX_COLOR\n  #define CC_SURFACES_USE_VERTEX_COLOR 0\n#endif\n#ifndef CC_SURFACES_TRANSFER_LOCAL_POS\n  #define CC_SURFACES_TRANSFER_LOCAL_POS 0\n#endif\n#ifndef CC_SURFACES_TRANSFER_CLIP_POS\n  #define CC_SURFACES_TRANSFER_CLIP_POS 0\n#endif\n#ifndef CC_SURFACES_USE_LIGHT_MAP\n  #ifdef CC_USE_LIGHTMAP\n    #define CC_SURFACES_USE_LIGHT_MAP CC_USE_LIGHTMAP\n  #else\n    #define CC_SURFACES_USE_LIGHT_MAP 0\n  #endif\n#endif\n#ifndef CC_SURFACES_FLIP_UV\n  #define CC_SURFACES_FLIP_UV 0\n#endif\n#ifndef CC_SURFACES_USE_TWO_SIDED\n  #define CC_SURFACES_USE_TWO_SIDED 0\n#endif\n#ifndef CC_SURFACES_USE_REFLECTION_DENOISE\n  #define CC_SURFACES_USE_REFLECTION_DENOISE 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_ANISOTROPIC\n  #define CC_SURFACES_LIGHTING_ANISOTROPIC 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT\n  #define CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT 0\n#endif\n#ifndef CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING\n  #define CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_FRESNEL\n  #define CC_SURFACES_LIGHTING_USE_FRESNEL 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n  #define CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  #define CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT\n  #define CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TRT\n  #define CC_SURFACES_LIGHTING_TRT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR\n  #define CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_SHEEN\n  #define CC_SURFACES_LIGHTING_SHEEN 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_CLEAR_COAT\n  #define CC_SURFACES_LIGHTING_CLEAR_COAT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TT\n  #define CC_SURFACES_LIGHTING_TT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_SSS\n  #define CC_SURFACES_LIGHTING_SSS 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n  #if CC_SURFACES_LIGHTING_TRT || CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR || CC_SURFACES_LIGHTING_SHEEN || CC_SURFACES_LIGHTING_CLEAR_COAT\n    #define CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR 1\n  #endif\n#endif\n#ifndef CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n  #define CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n  #if CC_SURFACES_LIGHTING_CLEAR_COAT\n    #define CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND 1\n  #endif\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n  #define CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND 0\n#endif\n#ifndef CC_SURFACES_ENABLE_DEBUG_VIEW\n  #define CC_SURFACES_ENABLE_DEBUG_VIEW 1\n#endif\n#define CC_USE_SURFACE_SHADER 1\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\nfloat saturate(float value) { return clamp(value, 0.0, 1.0); }\nvec2 saturate(vec2 value) { return clamp(value, vec2(0.0), vec2(1.0)); }\nvec3 saturate(vec3 value) { return clamp(value, vec3(0.0), vec3(1.0)); }\nvec4 saturate(vec4 value) { return clamp(value, vec4(0.0), vec4(1.0)); }\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nin highp vec3 v_worldPos;\nin vec4 v_normal;\nin vec2 v_uv;\n#if CC_SURFACES_USE_VERTEX_COLOR\n  in lowp vec4 v_color;\n#endif\n#if CC_SURFACES_USE_TANGENT_SPACE\n  in mediump vec4 v_tangent;\n#endif\n#if CC_SURFACES_USE_SECOND_UV\n  in mediump vec2 v_uv1;\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  in mediump vec3 v_luv;\n#endif\n#if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n  in mediump vec4 v_shadowBiasAndProbeId;\n#endif\n#if CC_USE_REFLECTION_PROBE && USE_INSTANCING\n  in mediump vec4 v_reflectionProbeData;\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  in mediump float v_fogFactor;\n#endif\n#if CC_SURFACES_TRANSFER_LOCAL_POS\n  in highp vec4 v_localPos;\n#endif\n#if CC_SURFACES_TRANSFER_CLIP_POS\n  in highp vec4 v_clipPos;\n#endif\n#if CC_USE_LIGHT_PROBE\n  #if USE_INSTANCING\n    in mediump vec4 v_sh_linear_const_r;\n    in mediump vec4 v_sh_linear_const_g;\n    in mediump vec4 v_sh_linear_const_b;\n  #endif\n#endif\n#define FSInput_worldPos v_worldPos\n#define FSInput_worldNormal v_normal.xyz\n#define FSInput_faceSideSign v_normal.w\n#define FSInput_texcoord v_uv\n#if CC_SURFACES_USE_VERTEX_COLOR\n  #define FSInput_vertexColor v_color\n#else\n  #define FSInput_vertexColor vec4(1.0)\n#endif\n#if CC_SURFACES_USE_TANGENT_SPACE\n  #define FSInput_worldTangent v_tangent.xyz\n  #define FSInput_mirrorNormal v_tangent.w\n#else\n  #define FSInput_worldTangent vec3(1.0, 1.0, 1.0)\n  #define FSInput_mirrorNormal 1.0\n#endif\n#if CC_SURFACES_USE_SECOND_UV\n  #define FSInput_texcoord1 v_uv1\n#else\n  #define FSInput_texcoord1 vec2(0.0, 0.0)\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  #define FSInput_lightMapUV v_luv\n#endif\n#if CC_RECEIVE_SHADOW\n  #define FSInput_shadowBias v_shadowBiasAndProbeId.xy\n#endif\n#if CC_USE_REFLECTION_PROBE\n  #define FSInput_reflectionProbeId v_shadowBiasAndProbeId.z\n  #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n    #define FSInput_reflectionProbeBlendId v_shadowBiasAndProbeId.w\n  #endif\n  #if USE_INSTANCING\n    #define FSInput_reflectionProbeData v_reflectionProbeData\n  #endif\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  #define FSInput_fogFactor v_fogFactor\n#endif\n#if CC_SURFACES_TRANSFER_LOCAL_POS\n  #define FSInput_localPos v_localPos\n#endif\n#if CC_SURFACES_TRANSFER_CLIP_POS\n  #define FSInput_clipPos v_clipPos\n#endif\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_COLOR 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_NORMAL CC_SURFACES_DEBUG_VIEW_VERTEX_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_TANGENT CC_SURFACES_DEBUG_VIEW_VERTEX_NORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_WORLD_POS CC_SURFACES_DEBUG_VIEW_VERTEX_TANGENT + 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_MIRROR CC_SURFACES_DEBUG_VIEW_WORLD_POS + 1\n#define CC_SURFACES_DEBUG_VIEW_FACE_SIDE CC_SURFACES_DEBUG_VIEW_VERTEX_MIRROR + 1\n#define CC_SURFACES_DEBUG_VIEW_UV0 CC_SURFACES_DEBUG_VIEW_FACE_SIDE + 1\n#define CC_SURFACES_DEBUG_VIEW_UV1 CC_SURFACES_DEBUG_VIEW_UV0 + 1\n#define CC_SURFACES_DEBUG_VIEW_UVLIGHTMAP CC_SURFACES_DEBUG_VIEW_UV1 + 1\n#define CC_SURFACES_DEBUG_VIEW_PROJ_DEPTH CC_SURFACES_DEBUG_VIEW_UVLIGHTMAP + 1\n#define CC_SURFACES_DEBUG_VIEW_LINEAR_DEPTH CC_SURFACES_DEBUG_VIEW_PROJ_DEPTH + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL CC_SURFACES_DEBUG_VIEW_LINEAR_DEPTH + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT + 1\n#define CC_SURFACES_DEBUG_VIEW_BASE_COLOR CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR CC_SURFACES_DEBUG_VIEW_BASE_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSPARENCY CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_METALLIC CC_SURFACES_DEBUG_VIEW_TRANSPARENCY + 1\n#define CC_SURFACES_DEBUG_VIEW_ROUGHNESS CC_SURFACES_DEBUG_VIEW_METALLIC + 1\n#define CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY CC_SURFACES_DEBUG_VIEW_ROUGHNESS + 1\n#define CC_SURFACES_DEBUG_VIEW_IOR CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_DIFFUSE CC_SURFACES_DEBUG_VIEW_IOR + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_SPECULAR CC_SURFACES_DEBUG_VIEW_DIRECT_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_ALL CC_SURFACES_DEBUG_VIEW_DIRECT_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_DIFFUSE CC_SURFACES_DEBUG_VIEW_DIRECT_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_SPECULAR CC_SURFACES_DEBUG_VIEW_ENV_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_ALL CC_SURFACES_DEBUG_VIEW_ENV_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_EMISSIVE CC_SURFACES_DEBUG_VIEW_ENV_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_LIGHT_MAP CC_SURFACES_DEBUG_VIEW_EMISSIVE + 1\n#define CC_SURFACES_DEBUG_VIEW_SHADOW CC_SURFACES_DEBUG_VIEW_LIGHT_MAP + 1\n#define CC_SURFACES_DEBUG_VIEW_AO CC_SURFACES_DEBUG_VIEW_SHADOW + 1\n#define CC_SURFACES_DEBUG_VIEW_FRESNEL CC_SURFACES_DEBUG_VIEW_AO + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_DIFFUSE CC_SURFACES_DEBUG_VIEW_FRESNEL + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_DIFFUSE CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ALL CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_2ND_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_ENVIRONMENT_2ND_SPECULAR CC_SURFACES_DEBUG_VIEW_DIRECT_2ND_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_2ND_SPECULAR_ALL CC_SURFACES_DEBUG_VIEW_ENVIRONMENT_2ND_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_FOG CC_SURFACES_DEBUG_VIEW_2ND_SPECULAR_ALL + 1\n#define IS_DEBUG_VIEW_ENABLE_WITH_CAMERA (cc_surfaceTransform.y != 3.0)\nbool equalf_mode(float data1, float data2) { return abs(float(data1) - float(data2)) < 0.001; }\n#define IS_DEBUG_VIEW_LIGHTING_ENABLE_WITH_ALBEDO (UnpackBitFromFloat(cc_debug_view_mode.w, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_MISC_ENABLE_CSM_LAYER_COLORATION (UnpackBitFromFloat(cc_debug_view_mode.w, 7) && IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_DIRECT_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.y, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_DIRECT_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.y, 1) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_ENV_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.y, 2) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_ENV_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.y, 3) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_EMISSIVE (UnpackBitFromFloat(cc_debug_view_mode.y, 4) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_LIGHT_MAP (UnpackBitFromFloat(cc_debug_view_mode.y, 5) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_SHADOW (UnpackBitFromFloat(cc_debug_view_mode.y, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_AO (UnpackBitFromFloat(cc_debug_view_mode.y, 7) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_NORMAL_MAP (UnpackBitFromFloat(cc_debug_view_mode.z, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_FOG (UnpackBitFromFloat(cc_debug_view_mode.z, 1) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TONE_MAPPING (UnpackBitFromFloat(cc_debug_view_mode.z, 2) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION (UnpackBitFromFloat(cc_debug_view_mode.z, 3) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_FRESNEL (UnpackBitFromFloat(cc_debug_view_mode.z, 4) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TRANSMIT_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.z, 5) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TRANSMIT_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.z, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_2ND_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.z, 7) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TT (UnpackBitFromFloat(cc_debug_view_mode.w, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#if (CC_PIPELINE_TYPE == 0 || CC_FORCE_FORWARD_SHADING)\n  #if CC_FORWARD_ADD\n    #if CC_PIPELINE_TYPE == 0\n      #define LIGHTS_PER_PASS 1\n    #else\n      #define LIGHTS_PER_PASS 10\n    #endif\n    #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 0\n    layout(std140) uniform CCForwardLight {\n      highp vec4 cc_lightPos[LIGHTS_PER_PASS];\n      vec4 cc_lightColor[LIGHTS_PER_PASS];\n      vec4 cc_lightSizeRangeAngle[LIGHTS_PER_PASS];\n      vec4 cc_lightDir[LIGHTS_PER_PASS];\n      vec4 cc_lightBoundingSizeVS[LIGHTS_PER_PASS];\n    };\n    #endif\n  #endif\n#endif\n#if CC_USE_LIGHT_PROBE\n  #if !USE_INSTANCING\n    layout(std140) uniform CCSH {\n      vec4 cc_sh_linear_const_r;\n      vec4 cc_sh_linear_const_g;\n      vec4 cc_sh_linear_const_b;\n      vec4 cc_sh_quadratic_r;\n      vec4 cc_sh_quadratic_g;\n      vec4 cc_sh_quadratic_b;\n      vec4 cc_sh_quadratic_a;\n    };\n  #endif\n#endif\nlayout(std140) uniform CCShadow {\n  highp mat4 cc_matLightView;\n  highp mat4 cc_matLightViewProj;\n  highp vec4 cc_shadowInvProjDepthInfo;\n  highp vec4 cc_shadowProjDepthInfo;\n  highp vec4 cc_shadowProjInfo;\n  mediump vec4 cc_shadowNFLSInfo;\n  mediump vec4 cc_shadowWHPBInfo;\n  mediump vec4 cc_shadowLPNNInfo;\n  lowp vec4 cc_shadowColor;\n  mediump vec4 cc_planarNDInfo;\n};\n#if CC_SUPPORT_CASCADED_SHADOW_MAP\n  layout(std140) uniform CCCSM {\n    highp vec4 cc_csmViewDir0[4];\n    highp vec4 cc_csmViewDir1[4];\n    highp vec4 cc_csmViewDir2[4];\n    highp vec4 cc_csmAtlas[4];\n    highp mat4 cc_matCSMViewProj[4];\n    highp vec4 cc_csmProjDepthInfo[4];\n    highp vec4 cc_csmProjInfo[4];\n    highp vec4 cc_csmSplitsInfo;\n  };\n#endif\nuniform samplerCube cc_environment;\n#if CC_USE_IBL\n  #if CC_USE_DIFFUSEMAP\n    uniform samplerCube cc_diffuseMap;\n  #endif\n#endif\n#if CC_USE_REFLECTION_PROBE\n  uniform samplerCube cc_reflectionProbeCubemap;\n  uniform sampler2D cc_reflectionProbePlanarMap;\n  uniform sampler2D cc_reflectionProbeDataMap;\n  uniform samplerCube cc_reflectionProbeBlendCubemap;\n#endif\nvec4 fragTextureLod (sampler2D tex, vec2 coord, float lod) {\n    return textureLod(tex, coord, lod);\n}\nvec4 fragTextureLod (samplerCube tex, vec3 coord, float lod) {\n      return textureLod(tex, coord, lod);\n}\n#define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\nhighp float unpackHighpData (float mainPart, float modPart) {\n  highp float data = mainPart;\n  return data + modPart;\n}\nvoid packHighpData (out float mainPart, out float modPart, highp float data) {\n  mainPart = fract(data);\n  modPart = data - mainPart;\n}\nhighp float unpackHighpData (float mainPart, float modPart, const float modValue) {\n  highp float data = mainPart * modValue;\n  return data + modPart * modValue;\n}\nvoid packHighpData (out float mainPart, out float modPart, highp float data, const float modValue) {\n  highp float divide = data / modValue;\n  mainPart = floor(divide);\n  modPart = (data - mainPart * modValue) / modValue;\n}\nhighp vec2 unpackHighpData (vec2 mainPart, vec2 modPart) {\n  highp vec2 data = mainPart;\n  return data + modPart;\n}\nvoid packHighpData (out vec2 mainPart, out vec2 modPart, highp vec2 data) {\n  mainPart = fract(data);\n  modPart = data - mainPart;\n}\nhighp vec2 unpackHighpData (vec2 mainPart, vec2 modPart, const float modValue) {\n  highp vec2 data = mainPart * modValue;\n  return data + modPart * modValue;\n}\nvoid packHighpData (out vec2 mainPart, out vec2 modPart, highp vec2 data, const float modValue) {\n  highp vec2 divide = data / modValue;\n  mainPart = floor(divide);\n  modPart = (data - mainPart * modValue) / modValue;\n}\nhighp vec3 unpackHighpData (vec3 mainPart, vec3 modPart) {\n  highp vec3 data = mainPart;\n  return data + modPart;\n}\nvoid packHighpData (out vec3 mainPart, out vec3 modPart, highp vec3 data) {\n  mainPart = fract(data);\n  modPart = data - mainPart;\n}\nhighp vec3 unpackHighpData (vec3 mainPart, vec3 modPart, const float modValue) {\n  highp vec3 data = mainPart * modValue;\n  return data + modPart * modValue;\n}\nvoid packHighpData (out vec3 mainPart, out vec3 modPart, highp vec3 data, const float modValue) {\n  highp vec3 divide = data / modValue;\n  mainPart = floor(divide);\n  modPart = (data - mainPart * modValue) / modValue;\n}\nhighp vec4 unpackHighpData (vec4 mainPart, vec4 modPart) {\n  highp vec4 data = mainPart;\n  return data + modPart;\n}\nvoid packHighpData (out vec4 mainPart, out vec4 modPart, highp vec4 data) {\n  mainPart = fract(data);\n  modPart = data - mainPart;\n}\nhighp vec4 unpackHighpData (vec4 mainPart, vec4 modPart, const float modValue) {\n  highp vec4 data = mainPart * modValue;\n  return data + modPart * modValue;\n}\nvoid packHighpData (out vec4 mainPart, out vec4 modPart, highp vec4 data, const float modValue) {\n  highp vec4 divide = data / modValue;\n  mainPart = floor(divide);\n  modPart = (data - mainPart * modValue) / modValue;\n}\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\nvec4 packRGBE (vec3 rgb) {\n  highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n  highp float e = 128.0;\n  if (maxComp > 0.0001) {\n    e = log(maxComp) / log(1.1);\n    e = ceil(e);\n    e = clamp(e + 128.0, 0.0, 255.0);\n  }\n  highp float sc = 1.0 / pow(1.1, e - 128.0);\n  vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n  vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n  return vec4(encode_rounded, e) / 255.0;\n}\nvec3 unpackRGBE (vec4 rgbe) {\n  return rgbe.rgb * pow(1.1, rgbe.a * 255.0 - 128.0);\n}\nvec3 ACESToneMap (vec3 color) {\n  color = min(color, vec3(8.0));\n  const float A = 2.51;\n  const float B = 0.03;\n  const float C = 2.43;\n  const float D = 0.59;\n  const float E = 0.14;\n  return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nvec3 HDRToLDR(vec3 color)\n{\n  #if CC_USE_HDR\n    #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n      if (IS_DEBUG_VIEW_COMPOSITE_ENABLE_TONE_MAPPING)\n    #endif\n    {\n    #if CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n      color.rgb = ACESToneMap(color.rgb);\n    #endif\n    }\n  #endif\n  return color;\n}\nvec3 SRGBToLinear (vec3 gamma) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return gamma;\n    }\n  #endif\n#endif\n  return gamma * gamma;\n}\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\nbool isnans(vec2 val) {\n    return isnan(val.x) || isnan(val.y);\n}\nbool isnans(vec3 val) {\n    return isnan(val.x) || isnan(val.y) || isnan(val.z);\n}\nbool isnans(vec4 val) {\n    return isnan(val.x) || isnan(val.y) || isnan(val.z) || isnan(val.w);\n}\nbool isinfs(vec2 val) {\n    return isinf(val.x) || isinf(val.y);\n}\nbool isinfs(vec3 val) {\n    return isinf(val.x) || isinf(val.y) || isinf(val.z);\n}\nbool isinfs(vec4 val) {\n    return isinf(val.x) || isinf(val.y) || isinf(val.z) || isinf(val.w);\n}\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nvec2 GetPlanarReflectScreenUV(vec3 worldPos, mat4 matVirtualCameraViewProj, float flipNDCSign, vec3 viewDir, vec3 reflectDir)\n{\n  vec4 clipPos = matVirtualCameraViewProj * vec4(worldPos, 1.0);\n  vec2 screenUV = clipPos.xy / clipPos.w * 0.5 + 0.5;\n  screenUV = vec2(1.0 - screenUV.x, screenUV.y);\n  screenUV = flipNDCSign == 1.0 ? vec2(screenUV.x, 1.0 - screenUV.y) : screenUV;\n  return screenUV;\n}\nfloat GetLinearDepthFromViewSpace(vec3 viewPos, float near, float far) {\n  float dist = length(viewPos);\n  return (dist - near) / (far - near);\n}\nvec3 CalculateBinormal(vec3 normal, vec3 tangent, float mirrorNormal)\n{\n    return cross(normal, tangent) * mirrorNormal;\n}\nvec3 CalculateTangent(vec3 normal, vec3 binormal)\n{\n    return cross(binormal, normal);\n}\nvec3 CalculateNormal(vec3 tangent, vec3 binormal)\n{\n    return cross(tangent, binormal);\n}\nvec3 RotationVecFromAxisY(vec3 v, float cosTheta, float sinTheta)\n{\n    vec3 result;\n    result.x = dot(v, vec3(cosTheta, 0.0, -sinTheta));\n    result.y = v.y;\n    result.z = dot(v, vec3(sinTheta, 0.0,  cosTheta));\n    return result;\n}\nvec3 RotationVecFromAxisY(vec3 v, float rotateAngleArc)\n{\n  return RotationVecFromAxisY(v, cos(rotateAngleArc), sin(rotateAngleArc));\n}\nvoid RotateTangentAndBinormal(inout vec3 tangent, inout vec3 binormal, vec3 normal, float rotationAngle)\n{\n    float cosTheta = cos(rotationAngle), sinTheta = sin(rotationAngle);\n    vec3 B = RotationVecFromAxisY(vec3(1.0, 0.0, 0.0), cosTheta, sinTheta);\n    vec3 T = RotationVecFromAxisY(vec3(0.0, 0.0, 1.0), cosTheta, sinTheta);\n    vec3 tangentNew, binormalNew;\n    binormalNew = B.x * binormal + B.y * normal + B.z * tangent;\n    binormal = normalize(binormalNew);\n    tangentNew = T.x * binormal + T.y * normal + T.z * tangent;\n    tangent = normalize(tangentNew);\n}\nvoid RotateNormalAndBinormal(inout vec3 binormal, inout vec3 normal, in vec3 tangent, float rotationAngle, float mirrorNormal)\n{\n  if(rotationAngle > 0.0)\n  {\n    normal += (binormal - normal) * rotationAngle;\n    normal = normalize(normal);\n    binormal = CalculateBinormal(normal, tangent, mirrorNormal);\n  }\n  else if(rotationAngle < 0.0)\n  {\n    binormal += (binormal - normal) * rotationAngle;\n    binormal = normalize(binormal);\n    normal = CalculateNormal(tangent, binormal);\n  }\n}\nvec2 signNotZero(vec2 v) {\n  return vec2((v.x >= 0.0) ? +1.0 : -1.0, (v.y >= 0.0) ? +1.0 : -1.0);\n}\nvec2 float32x3_to_oct(in vec3 v) {\n  vec2 p = v.xy * (1.0 / (abs(v.x) + abs(v.y) + abs(v.z)));\n  return (v.z <= 0.0) ? ((1.0 - abs(p.yx)) * signNotZero(p)) : p;\n}\nfloat RoughnessToPerceptualRoughness(float roughness)\n{\n  return sqrt(roughness);\n}\n  vec3 EnvReflectionWithMipFiltering(vec3 R, float roughness, float mipCount, float denoiseIntensity) {\n    #if CC_USE_IBL\n      #if !CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING && !CC_IBL_CONVOLUTED\n        roughness = RoughnessToPerceptualRoughness(roughness);\n      #endif\n    \tfloat mip = roughness * (mipCount - 1.0);\n    \tfloat delta = (dot(dFdx(R), dFdy(R))) * 1000.0;\n    \tfloat mipBias = mix(0.0, 5.0, clamp(delta, 0.0, 1.0));\n      vec3 rotationDir = RotationVecFromAxisY(R.xyz, cc_surfaceTransform.z, cc_surfaceTransform.w);\n    \tvec4 biased = fragTextureLod(cc_environment, rotationDir, mip + mipBias);\n     \tvec4 filtered = texture(cc_environment, rotationDir);\n      #if CC_USE_IBL == 2\n      \tbiased.rgb = unpackRGBE(biased);\n      \tfiltered.rgb = unpackRGBE(filtered);\n      #else\n      \tbiased.rgb = SRGBToLinear(biased.rgb);\n      \tfiltered.rgb = SRGBToLinear(filtered.rgb);\n      #endif\n      return mix(biased.rgb, filtered.rgb, denoiseIntensity);\n    #else\n      return vec3(0.0, 0.0, 0.0);\n    #endif\n  }\n  vec3 EnvReflection(samplerCube tex, vec3 R, float roughness, float mipCount) {\n    #if !CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING && !CC_IBL_CONVOLUTED\n      roughness = RoughnessToPerceptualRoughness(roughness);\n    #endif\n    vec3 rotationDir = RotationVecFromAxisY(R.xyz, cc_surfaceTransform.z, cc_surfaceTransform.w);\n    vec4 envmap = fragTextureLod(tex, rotationDir, roughness * (mipCount - 1.0));\n    #if CC_USE_IBL == 2\n      return unpackRGBE(envmap);\n    #else\n      return SRGBToLinear(envmap.rgb);\n    #endif\n  }\n  vec3 EnvReflectionOfReflectionProbe(samplerCube tex, vec3 R, float roughness, float mipCount, bool isRGBE) {\n    #if !CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING && !CC_IBL_CONVOLUTED\n      roughness = RoughnessToPerceptualRoughness(roughness);\n    #endif\n    vec4 envmap = fragTextureLod(tex, R, roughness * (mipCount - 1.0));\n    return isRGBE ? unpackRGBE(envmap) : SRGBToLinear(envmap.rgb);\n  }\n#if CC_SUPPORT_CASCADED_SHADOW_MAP\n#endif\nfloat CCGetLinearDepth(vec3 worldPos, float viewSpaceBias) {\n\tvec4 viewPos = cc_matLightView * vec4(worldPos.xyz, 1.0);\n  viewPos.z += viewSpaceBias;\n\treturn GetLinearDepthFromViewSpace(viewPos.xyz, cc_shadowNFLSInfo.x, cc_shadowNFLSInfo.y);\n}\nfloat CCGetLinearDepth(vec3 worldPos) {\n\treturn CCGetLinearDepth(worldPos, 0.0);\n}\n#if CC_RECEIVE_SHADOW\n  uniform highp sampler2D cc_shadowMap;\n  uniform highp sampler2D cc_spotShadowMap;\n  vec4 shadowTexure(highp sampler2D shadowMap, vec2 coord) {\n      #if defined(CC_USE_WGPU)\n          return textureLod(shadowMap, coord, 0.0);\n      #else\n        return texture(shadowMap, coord);\n      #endif\n  }\n  float SampleShadowMap (vec3 shadowNDCPos, highp sampler2D shadowMap)\n  {\n    #if CC_SHADOWMAP_FORMAT == 1\n      return dot(shadowTexure(shadowMap, shadowNDCPos.xy), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0));\n    #else\n      return shadowTexure(shadowMap, shadowNDCPos.xy).x;\n    #endif\n  }\n  float SampleShadowMapSoft (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    vec2 oneTap = 1.0 / shadowMapResolution;\n    vec2 shadowNDCPos_offset = shadowNDCPos.xy + oneTap;\n    float block0 = SampleShadowMap(vec3(shadowNDCPos.x, shadowNDCPos.y, shadowNDCPos.z), shadowMap);\n    float block1 = SampleShadowMap(vec3(shadowNDCPos_offset.x, shadowNDCPos.y, shadowNDCPos.z), shadowMap);\n    float block2 = SampleShadowMap(vec3(shadowNDCPos.x, shadowNDCPos_offset.y, shadowNDCPos.z), shadowMap);\n    float block3 = SampleShadowMap(vec3(shadowNDCPos_offset.x, shadowNDCPos_offset.y, shadowNDCPos.z), shadowMap);\n    float coefX   = mod(shadowNDCPos.x, oneTap.x) * shadowMapResolution.x;\n    float resultX = mix(block0, block1, coefX);\n    float resultY = mix(block2, block3, coefX);\n    float coefY   = mod(shadowNDCPos.y, oneTap.y) * shadowMapResolution.y;\n    return mix(resultX, resultY, coefY);\n  }\n  float NativePCFShadowFactorHard (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    #if CC_SHADOWMAP_FORMAT == 1\n      return step(shadowNDCPos.z, dot(shadowTexure(shadowMap, shadowNDCPos.xy), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      return step(shadowNDCPos.z, shadowTexure(shadowMap, shadowNDCPos.xy).x);\n    #endif\n  }\n  float NativePCFShadowFactorSoft (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    vec2 oneTap = 1.0 / shadowMapResolution;\n    vec2 shadowNDCPos_offset = shadowNDCPos.xy + oneTap;\n    float block0, block1, block2, block3;\n    #if CC_SHADOWMAP_FORMAT == 1\n      block0 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos_offset.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      block0 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)).x);\n      block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos.y)).x);\n      block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset.y)).x);\n      block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset.x, shadowNDCPos_offset.y)).x);\n    #endif\n    float coefX   = mod(shadowNDCPos.x, oneTap.x) * shadowMapResolution.x;\n    float resultX = mix(block0, block1, coefX);\n    float resultY = mix(block2, block3, coefX);\n    float coefY   = mod(shadowNDCPos.y, oneTap.y) * shadowMapResolution.y;\n    return mix(resultX, resultY, coefY);\n  }\n  float NativePCFShadowFactorSoft3X (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    vec2 oneTap = 1.0 / shadowMapResolution;\n    float shadowNDCPos_offset_L = shadowNDCPos.x - oneTap.x;\n    float shadowNDCPos_offset_R = shadowNDCPos.x + oneTap.x;\n    float shadowNDCPos_offset_U = shadowNDCPos.y - oneTap.y;\n    float shadowNDCPos_offset_D = shadowNDCPos.y + oneTap.y;\n    float block0, block1, block2, block3, block4, block5, block6, block7, block8;\n    #if CC_SHADOWMAP_FORMAT == 1\n      block0 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_U)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block4 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block5 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos.y)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block6 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block7 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block8 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_D)), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      block0 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_U)).x);\n      block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_U)).x);\n      block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_U)).x);\n      block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos.y)).x);\n      block4 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos.y)).x);\n      block5 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos.y)).x);\n      block6 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_L, shadowNDCPos_offset_D)).x);\n      block7 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos.x, shadowNDCPos_offset_D)).x);\n      block8 = step(shadowNDCPos.z, shadowTexure(shadowMap, vec2(shadowNDCPos_offset_R, shadowNDCPos_offset_D)).x);\n    #endif\n    float coefX = mod(shadowNDCPos.x, oneTap.x) * shadowMapResolution.x;\n    float coefY = mod(shadowNDCPos.y, oneTap.y) * shadowMapResolution.y;\n    float shadow = 0.0;\n    float resultX = mix(block0, block1, coefX);\n    float resultY = mix(block3, block4, coefX);\n    shadow += mix(resultX , resultY, coefY);\n    resultX = mix(block1, block2, coefX);\n    resultY = mix(block4, block5, coefX);\n    shadow += mix(resultX , resultY, coefY);\n    resultX = mix(block3, block4, coefX);\n    resultY = mix(block6, block7, coefX);\n    shadow += mix(resultX, resultY, coefY);\n    resultX = mix(block4, block5, coefX);\n    resultY = mix(block7, block8, coefX);\n    shadow += mix(resultX, resultY, coefY);\n    return shadow * 0.25;\n  }\n  float NativePCFShadowFactorSoft5X (vec3 shadowNDCPos, highp sampler2D shadowMap, vec2 shadowMapResolution)\n  {\n    vec2 oneTap = 1.0 / shadowMapResolution;\n    vec2 twoTap = oneTap * 2.0;\n    vec2 offset1 = shadowNDCPos.xy + vec2(-twoTap.x, -twoTap.y);\n    vec2 offset2 = shadowNDCPos.xy + vec2(-oneTap.x, -twoTap.y);\n    vec2 offset3 = shadowNDCPos.xy + vec2(0.0, -twoTap.y);\n    vec2 offset4 = shadowNDCPos.xy + vec2(oneTap.x, -twoTap.y);\n    vec2 offset5 = shadowNDCPos.xy + vec2(twoTap.x, -twoTap.y);\n    vec2 offset6 = shadowNDCPos.xy + vec2(-twoTap.x, -oneTap.y);\n    vec2 offset7 = shadowNDCPos.xy + vec2(-oneTap.x, -oneTap.y);\n    vec2 offset8 = shadowNDCPos.xy + vec2(0.0, -oneTap.y);\n    vec2 offset9 = shadowNDCPos.xy + vec2(oneTap.x, -oneTap.y);\n    vec2 offset10 = shadowNDCPos.xy + vec2(twoTap.x, -oneTap.y);\n    vec2 offset11 = shadowNDCPos.xy + vec2(-twoTap.x, 0.0);\n    vec2 offset12 = shadowNDCPos.xy + vec2(-oneTap.x, 0.0);\n    vec2 offset13 = shadowNDCPos.xy + vec2(0.0, 0.0);\n    vec2 offset14 = shadowNDCPos.xy + vec2(oneTap.x, 0.0);\n    vec2 offset15 = shadowNDCPos.xy + vec2(twoTap.x, 0.0);\n    vec2 offset16 = shadowNDCPos.xy + vec2(-twoTap.x, oneTap.y);\n    vec2 offset17 = shadowNDCPos.xy + vec2(-oneTap.x, oneTap.y);\n    vec2 offset18 = shadowNDCPos.xy + vec2(0.0, oneTap.y);\n    vec2 offset19 = shadowNDCPos.xy + vec2(oneTap.x, oneTap.y);\n    vec2 offset20 = shadowNDCPos.xy + vec2(twoTap.x, oneTap.y);\n    vec2 offset21 = shadowNDCPos.xy + vec2(-twoTap.x, twoTap.y);\n    vec2 offset22 = shadowNDCPos.xy + vec2(-oneTap.x, twoTap.y);\n    vec2 offset23 = shadowNDCPos.xy + vec2(0.0, twoTap.y);\n    vec2 offset24 = shadowNDCPos.xy + vec2(oneTap.x, twoTap.y);\n    vec2 offset25 = shadowNDCPos.xy + vec2(twoTap.x, twoTap.y);\n    float block1, block2, block3, block4, block5, block6, block7, block8, block9, block10, block11, block12, block13, block14, block15, block16, block17, block18, block19, block20, block21, block22, block23, block24, block25;\n    #if CC_SHADOWMAP_FORMAT == 1\n      block1 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset1), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block2 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset2), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block3 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset3), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block4 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset4), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block5 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset5), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block6 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset6), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block7 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset7), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block8 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset8), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block9 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset9), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block10 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset10), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block11 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset11), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block12 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset12), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block13 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset13), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block14 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset14), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block15 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset15), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block16 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset16), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block17 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset17), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block18 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset18), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block19 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset19), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block20 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset20), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block21 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset21), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block22 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset22), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block23 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset23), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block24 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset24), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n      block25 = step(shadowNDCPos.z, dot(shadowTexure(shadowMap, offset25), vec4(1.0, 1.0 / 255.0, 1.0 / 65025.0, 1.0 / 16581375.0)));\n    #else\n      block1 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset1).x);\n      block2 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset2).x);\n      block3 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset3).x);\n      block4 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset4).x);\n      block5 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset5).x);\n      block6 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset6).x);\n      block7 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset7).x);\n      block8 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset8).x);\n      block9 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset9).x);\n      block10 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset10).x);\n      block11 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset11).x);\n      block12 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset12).x);\n      block13 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset13).x);\n      block14 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset14).x);\n      block15 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset15).x);\n      block16 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset16).x);\n      block17 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset17).x);\n      block18 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset18).x);\n      block19 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset19).x);\n      block20 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset20).x);\n      block21 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset21).x);\n      block22 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset22).x);\n      block23 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset23).x);\n      block24 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset24).x);\n      block25 = step(shadowNDCPos.z, shadowTexure(shadowMap, offset25).x);\n    #endif\n    vec2 coef = fract(shadowNDCPos.xy * shadowMapResolution);\n    vec2 v1X1 = mix(vec2(block1, block6), vec2(block2, block7), coef.xx);\n    vec2 v1X2 = mix(vec2(block2, block7), vec2(block3, block8), coef.xx);\n    vec2 v1X3 = mix(vec2(block3, block8), vec2(block4, block9), coef.xx);\n    vec2 v1X4 = mix(vec2(block4, block9), vec2(block5, block10), coef.xx);\n    float v1 = mix(v1X1.x, v1X1.y, coef.y) + mix(v1X2.x, v1X2.y, coef.y) + mix(v1X3.x, v1X3.y, coef.y) + mix(v1X4.x, v1X4.y, coef.y);\n    vec2 v2X1 = mix(vec2(block6, block11), vec2(block7, block12), coef.xx);\n    vec2 v2X2 = mix(vec2(block7, block12), vec2(block8, block13), coef.xx);\n    vec2 v2X3 = mix(vec2(block8, block13), vec2(block9, block14), coef.xx);\n    vec2 v2X4 = mix(vec2(block9, block14), vec2(block10, block15), coef.xx);\n    float v2 = mix(v2X1.x, v2X1.y, coef.y) + mix(v2X2.x, v2X2.y, coef.y) + mix(v2X3.x, v2X3.y, coef.y) + mix(v2X4.x, v2X4.y, coef.y);\n    vec2 v3X1 = mix(vec2(block11, block16), vec2(block12, block17), coef.xx);\n    vec2 v3X2 = mix(vec2(block12, block17), vec2(block13, block18), coef.xx);\n    vec2 v3X3 = mix(vec2(block13, block18), vec2(block14, block19), coef.xx);\n    vec2 v3X4 = mix(vec2(block14, block19), vec2(block15, block20), coef.xx);\n    float v3 = mix(v3X1.x, v3X1.y, coef.y) + mix(v3X2.x, v3X2.y, coef.y) + mix(v3X3.x, v3X3.y, coef.y) + mix(v3X4.x, v3X4.y, coef.y);\n    vec2 v4X1 = mix(vec2(block16, block21), vec2(block17, block22), coef.xx);\n    vec2 v4X2 = mix(vec2(block17, block22), vec2(block18, block23), coef.xx);\n    vec2 v4X3 = mix(vec2(block18, block23), vec2(block19, block24), coef.xx);\n    vec2 v4X4 = mix(vec2(block19, block24), vec2(block20, block25), coef.xx);\n    float v4 = mix(v4X1.x, v4X1.y, coef.y) + mix(v4X2.x, v4X2.y, coef.y) + mix(v4X3.x, v4X3.y, coef.y) + mix(v4X4.x, v4X4.y, coef.y);\n    float fAvg = (v1 + v2 + v3 + v4) * 0.0625;\n    return fAvg;\n  }\n  bool GetShadowNDCPos(out vec3 shadowNDCPos, vec4 shadowPosWithDepthBias)\n  {\n  \tshadowNDCPos = shadowPosWithDepthBias.xyz / shadowPosWithDepthBias.w * 0.5 + 0.5;\n  \tif (shadowNDCPos.x < 0.0 || shadowNDCPos.x > 1.0 ||\n  \t\tshadowNDCPos.y < 0.0 || shadowNDCPos.y > 1.0 ||\n  \t\tshadowNDCPos.z < 0.0 || shadowNDCPos.z > 1.0) {\n  \t\treturn false;\n  \t}\n  \tshadowNDCPos.xy = cc_cameraPos.w == 1.0 ? vec2(shadowNDCPos.xy.x, 1.0 - shadowNDCPos.xy.y) : shadowNDCPos.xy;\n  \treturn true;\n  }\n  vec4 ApplyShadowDepthBias_FaceNormal(vec4 shadowPos, vec3 worldNormal, float normalBias, vec3 matViewDir0, vec3 matViewDir1, vec3 matViewDir2, vec2 projScaleXY)\n  {\n    vec4 newShadowPos = shadowPos;\n    if (normalBias > EPSILON_LOWP)\n    {\n      vec3 viewNormal = vec3(dot(matViewDir0, worldNormal), dot(matViewDir1, worldNormal), dot(matViewDir2, worldNormal));\n      if (viewNormal.z < 0.1)\n        newShadowPos.xy += viewNormal.xy * projScaleXY * normalBias * clamp(viewNormal.z, 0.001, 0.1);\n    }\n    return newShadowPos;\n  }\n  vec4 ApplyShadowDepthBias_FaceNormal(vec4 shadowPos, vec3 worldNormal, float normalBias, mat4 matLightView, vec2 projScaleXY)\n  {\n  \tvec4 newShadowPos = shadowPos;\n  \tif (normalBias > EPSILON_LOWP)\n  \t{\n  \t\tvec4 viewNormal = matLightView * vec4(worldNormal, 0.0);\n  \t\tif (viewNormal.z < 0.1)\n  \t\t\tnewShadowPos.xy += viewNormal.xy * projScaleXY * normalBias * clamp(viewNormal.z, 0.001, 0.1);\n  \t}\n  \treturn newShadowPos;\n  }\n  float GetViewSpaceDepthFromNDCDepth_Orthgraphic(float NDCDepth, float projScaleZ, float projBiasZ)\n  {\n  \treturn (NDCDepth - projBiasZ) / projScaleZ;\n  }\n  float GetViewSpaceDepthFromNDCDepth_Perspective(float NDCDepth, float homogenousDividW, float invProjScaleZ, float invProjBiasZ)\n  {\n  \treturn NDCDepth * invProjScaleZ + homogenousDividW * invProjBiasZ;\n  }\n  vec4 ApplyShadowDepthBias_Perspective(vec4 shadowPos, float viewspaceDepthBias)\n  {\n  \tvec3 viewSpacePos;\n  \tviewSpacePos.xy = shadowPos.xy * cc_shadowProjInfo.zw;\n  \tviewSpacePos.z = GetViewSpaceDepthFromNDCDepth_Perspective(shadowPos.z, shadowPos.w, cc_shadowInvProjDepthInfo.x, cc_shadowInvProjDepthInfo.y);\n  \tviewSpacePos.xyz += cc_shadowProjDepthInfo.z * normalize(viewSpacePos.xyz) * viewspaceDepthBias;\n  \tvec4 clipSpacePos;\n  \tclipSpacePos.xy = viewSpacePos.xy * cc_shadowProjInfo.xy;\n  \tclipSpacePos.zw = viewSpacePos.z * cc_shadowProjDepthInfo.xz + vec2(cc_shadowProjDepthInfo.y, 0.0);\n  \t#if CC_SHADOWMAP_USE_LINEAR_DEPTH\n  \t\tclipSpacePos.z = GetLinearDepthFromViewSpace(viewSpacePos.xyz, cc_shadowNFLSInfo.x, cc_shadowNFLSInfo.y);\n  \t\tclipSpacePos.z = (clipSpacePos.z * 2.0 - 1.0) * clipSpacePos.w;\n  \t#endif\n  \treturn clipSpacePos;\n  }\n  vec4 ApplyShadowDepthBias_Orthographic(vec4 shadowPos, float viewspaceDepthBias, float projScaleZ, float projBiasZ)\n  {\n  \tfloat coeffA = projScaleZ;\n  \tfloat coeffB = projBiasZ;\n  \tfloat viewSpacePos_z = GetViewSpaceDepthFromNDCDepth_Orthgraphic(shadowPos.z, projScaleZ, projBiasZ);\n  \tviewSpacePos_z += viewspaceDepthBias;\n  \tvec4 result = shadowPos;\n  \tresult.z = viewSpacePos_z * coeffA + coeffB;\n  \treturn result;\n  }\n  vec4 ApplyShadowDepthBias_PerspectiveLinearDepth(vec4 shadowPos, float viewspaceDepthBias, vec3 worldPos)\n  {\n    shadowPos.z = CCGetLinearDepth(worldPos, viewspaceDepthBias) * 2.0 - 1.0;\n    shadowPos.z *= shadowPos.w;\n    return shadowPos;\n  }\n  float CCGetDirLightShadowFactorHard (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorHard(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetDirLightShadowFactorSoft (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetDirLightShadowFactorSoft3X (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft3X(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetDirLightShadowFactorSoft5X (vec4 shadowPosWithDepthBias) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft5X(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorHard (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorHard(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorSoft (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorSoft3X (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft3X(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCGetSpotLightShadowFactorSoft5X (vec4 shadowPosWithDepthBias, vec3 worldPos) {\n\t  vec3 shadowNDCPos;\n\t  if (!GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias)) {\n\t\t  return 1.0;\n\t  }\n    return NativePCFShadowFactorSoft5X(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy);\n  }\n  float CCSpotShadowFactorBase(out vec4 shadowPosWithDepthBias, vec4 shadowPos, vec3 worldPos, vec2 shadowBias)\n  {\n    float pcf = cc_shadowWHPBInfo.z;\n    vec4 pos = vec4(1.0);\n    #if CC_SHADOWMAP_USE_LINEAR_DEPTH\n      pos = ApplyShadowDepthBias_PerspectiveLinearDepth(shadowPos, shadowBias.x, worldPos);\n    #else\n      pos = ApplyShadowDepthBias_Perspective(shadowPos, shadowBias.x);\n    #endif\n    float realtimeShadow = 1.0;\n    if (pcf > 2.9) {\n      realtimeShadow = CCGetSpotLightShadowFactorSoft5X(pos, worldPos);\n    }else if (pcf > 1.9) {\n      realtimeShadow = CCGetSpotLightShadowFactorSoft3X(pos, worldPos);\n    }else if (pcf > 0.9) {\n      realtimeShadow = CCGetSpotLightShadowFactorSoft(pos, worldPos);\n    }else {\n      realtimeShadow = CCGetSpotLightShadowFactorHard(pos, worldPos);\n    }\n    shadowPosWithDepthBias = pos;\n    return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n  }\n  float CCShadowFactorBase(out vec4 shadowPosWithDepthBias, vec4 shadowPos, vec3 N, vec2 shadowBias)\n  {\n    vec4 pos = ApplyShadowDepthBias_FaceNormal(shadowPos, N, shadowBias.y, cc_matLightView, cc_shadowProjInfo.xy);\n    pos = ApplyShadowDepthBias_Orthographic(pos, shadowBias.x, cc_shadowProjDepthInfo.x, cc_shadowProjDepthInfo.y);\n    float realtimeShadow = 1.0;\n    #if CC_DIR_SHADOW_PCF_TYPE == 3\n      realtimeShadow = CCGetDirLightShadowFactorSoft5X(pos);\n    #endif\n    #if CC_DIR_SHADOW_PCF_TYPE == 2\n      realtimeShadow =  CCGetDirLightShadowFactorSoft3X(pos);\n    #endif\n    #if CC_DIR_SHADOW_PCF_TYPE == 1\n      realtimeShadow = CCGetDirLightShadowFactorSoft(pos);\n    #endif\n    #if CC_DIR_SHADOW_PCF_TYPE == 0\n      realtimeShadow = CCGetDirLightShadowFactorHard(pos);\n    #endif\n    shadowPosWithDepthBias = pos;\n    return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n  }\n  #if CC_SUPPORT_CASCADED_SHADOW_MAP\n    bool CCGetCSMLevelWithTransition(out highp float ratio, vec3 clipPos) {\n      highp float maxRange = 1.0 - cc_csmSplitsInfo.x;\n      highp float minRange = cc_csmSplitsInfo.x;\n      highp float thresholdInvert = 1.0 / cc_csmSplitsInfo.x;\n      ratio = 0.0;\n      if (clipPos.x <= minRange) {\n        ratio = clipPos.x * thresholdInvert;\n        return true;\n      }\n      if (clipPos.x >= maxRange) {\n        ratio = 1.0 - (clipPos.x - maxRange) * thresholdInvert;\n        return true;\n      }\n      if (clipPos.y <= minRange) {\n        ratio = clipPos.y  * thresholdInvert;\n        return true;\n      }\n      if (clipPos.y >= maxRange) {\n        ratio = 1.0 - (clipPos.y - maxRange) * thresholdInvert;\n        return true;\n      }\n      return false;\n    }\n    bool CCHasCSMLevel(int level, vec3 worldPos) {\n      highp float layerThreshold = cc_csmViewDir0[0].w;\n      bool hasLevel = false;\n      for (int i = 0; i < 4; i++) {\n        if (i == level) {\n          vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n          if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n              clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n              clipPos.z >= 0.0 && clipPos.z <= 1.0) {\n            hasLevel = true;\n          }\n        }\n      }\n      return hasLevel;\n    }\n    void CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos, int level) {\n      highp float layerThreshold = cc_csmViewDir0[0].w;\n      for (int i = 0; i < 4; i++) {\n        vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n        vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n        if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n            clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n            clipPos.z >= 0.0 && clipPos.z <= 1.0 && i == level) {\n          csmPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          csmPos.xy = csmPos.xy * cc_csmAtlas[i].xy + cc_csmAtlas[i].zw;\n          shadowProjDepthInfo = cc_csmProjDepthInfo[i];\n          shadowProjInfo = cc_csmProjInfo[i];\n          shadowViewDir0 = cc_csmViewDir0[i].xyz;\n          shadowViewDir1 = cc_csmViewDir1[i].xyz;\n          shadowViewDir2 = cc_csmViewDir2[i].xyz;\n        }\n      }\n    }\n    int CCGetCSMLevel(out bool isTransitionArea, out highp float transitionRatio, out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos)\n    {\n      int level = -1;\n      highp float layerThreshold = cc_csmViewDir0[0].w;\n      for (int i = 0; i < 4; i++) {\n        vec4 shadowPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n        vec3 clipPos = shadowPos.xyz / shadowPos.w * 0.5 + 0.5;\n        if (clipPos.x >= layerThreshold && clipPos.x <= (1.0 - layerThreshold) &&\n            clipPos.y >= layerThreshold && clipPos.y <= (1.0 - layerThreshold) &&\n            clipPos.z >= 0.0 && clipPos.z <= 1.0 && level < 0) {\n          #if CC_CASCADED_LAYERS_TRANSITION\n            isTransitionArea = CCGetCSMLevelWithTransition(transitionRatio, clipPos);\n          #endif\n          csmPos = cc_matCSMViewProj[i] * vec4(worldPos.xyz, 1.0);\n          csmPos.xy = csmPos.xy * cc_csmAtlas[i].xy + cc_csmAtlas[i].zw;\n          shadowProjDepthInfo = cc_csmProjDepthInfo[i];\n          shadowProjInfo = cc_csmProjInfo[i];\n          shadowViewDir0 = cc_csmViewDir0[i].xyz;\n          shadowViewDir1 = cc_csmViewDir1[i].xyz;\n          shadowViewDir2 = cc_csmViewDir2[i].xyz;\n          level = i;\n        }\n      }\n      return level;\n    }\n    int CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos)\n    {\n      bool isTransitionArea = false;\n      highp float transitionRatio = 0.0;\n      return CCGetCSMLevel(isTransitionArea, transitionRatio, csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n    }\n    float CCCSMFactorBase(out vec4 csmPos, out vec4 csmPosWithBias, vec3 worldPos, vec3 N, vec2 shadowBias)\n    {\n      bool isTransitionArea = false;\n      highp float ratio = 0.0;\n      csmPos = vec4(1.0);\n      vec4 shadowProjDepthInfo, shadowProjInfo;\n      vec3 shadowViewDir0, shadowViewDir1, shadowViewDir2;\n      int level = -1;\n      #if CC_CASCADED_LAYERS_TRANSITION\n        level = CCGetCSMLevel(isTransitionArea, ratio, csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n      #else\n        level = CCGetCSMLevel(csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n      #endif\n      if (level < 0) { return 1.0; }\n      vec4 pos = ApplyShadowDepthBias_FaceNormal(csmPos, N, shadowBias.y, shadowViewDir0, shadowViewDir1, shadowViewDir2, shadowProjInfo.xy);\n      pos = ApplyShadowDepthBias_Orthographic(pos, shadowBias.x, shadowProjDepthInfo.x, shadowProjDepthInfo.y);\n      csmPosWithBias = pos;\n      float realtimeShadow = 1.0;\n      #if CC_DIR_SHADOW_PCF_TYPE == 3\n        realtimeShadow = CCGetDirLightShadowFactorSoft5X(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 2\n        realtimeShadow = CCGetDirLightShadowFactorSoft3X(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 1\n        realtimeShadow = CCGetDirLightShadowFactorSoft(pos);\n      #endif\n      #if CC_DIR_SHADOW_PCF_TYPE == 0\n        realtimeShadow = CCGetDirLightShadowFactorHard(pos);\n      #endif\n      #if CC_CASCADED_LAYERS_TRANSITION\n        vec4 nextCSMPos = vec4(1.0);\n        vec4 nextShadowProjDepthInfo, nextShadowProjInfo;\n        vec3 nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2;\n        float nextRealtimeShadow = 1.0;\n        CCGetCSMLevel(nextCSMPos, nextShadowProjDepthInfo, nextShadowProjInfo, nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2, worldPos, level + 1);\n        bool hasNextLevel = CCHasCSMLevel(level + 1, worldPos);\n        if (hasNextLevel && isTransitionArea) {\n          vec4 nexPos = ApplyShadowDepthBias_FaceNormal(nextCSMPos, N, shadowBias.y, nextShadowViewDir0, nextShadowViewDir1, nextShadowViewDir2, nextShadowProjInfo.xy);\n          nexPos = ApplyShadowDepthBias_Orthographic(nexPos, shadowBias.x, nextShadowProjDepthInfo.x, nextShadowProjDepthInfo.y);\n          #if CC_DIR_SHADOW_PCF_TYPE == 3\n            nextRealtimeShadow = CCGetDirLightShadowFactorSoft5X(nexPos);\n          #endif\n          #if CC_DIR_SHADOW_PCF_TYPE == 2\n            nextRealtimeShadow = CCGetDirLightShadowFactorSoft3X(nexPos);\n          #endif\n          #if CC_DIR_SHADOW_PCF_TYPE == 1\n            nextRealtimeShadow = CCGetDirLightShadowFactorSoft(nexPos);\n          #endif\n          #if CC_DIR_SHADOW_PCF_TYPE == 0\n            nextRealtimeShadow = CCGetDirLightShadowFactorHard(nexPos);\n          #endif\n          return mix(mix(nextRealtimeShadow, realtimeShadow, ratio), 1.0, cc_shadowNFLSInfo.w);\n        }\n        return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n      #else\n        return mix(realtimeShadow, 1.0, cc_shadowNFLSInfo.w);\n      #endif\n    }\n  #else\n    int CCGetCSMLevel(out vec4 csmPos, out vec4 shadowProjDepthInfo, out vec4 shadowProjInfo, out vec3 shadowViewDir0, out vec3 shadowViewDir1, out vec3 shadowViewDir2, vec3 worldPos) {\n      return -1;\n    }\n    float CCCSMFactorBase(out vec4 csmPos, out vec4 csmPosWithBias, vec3 worldPos, vec3 N, vec2 shadowBias) {\n      csmPos = cc_matLightViewProj * vec4(worldPos, 1.0);\n      return CCShadowFactorBase(csmPosWithBias, csmPos, N, shadowBias);\n    }\n  #endif\n  float CCShadowFactorBase(vec4 shadowPos, vec3 N, vec2 shadowBias) {\n    vec4 shadowPosWithDepthBias;\n    return CCShadowFactorBase(shadowPosWithDepthBias, shadowPos, N, shadowBias);\n  }\n  float CCCSMFactorBase(vec3 worldPos, vec3 N, vec2 shadowBias) {\n    vec4 csmPos, csmPosWithBias;\n    return CCCSMFactorBase(csmPos, csmPosWithBias, worldPos, N, shadowBias);\n  }\n  float CCSpotShadowFactorBase(vec4 shadowPos, vec3 worldPos, vec2 shadowBias)\n  {\n    vec4 shadowPosWithDepthBias;\n    return CCSpotShadowFactorBase(shadowPosWithDepthBias, shadowPos, worldPos, shadowBias);\n  }\n#endif\n#if CC_USE_FOG != 4\n  float LinearFog(vec4 pos, vec3 cameraPos, float fogStart, float fogEnd) {\n      vec4 wPos = pos;\n      float cam_dis = distance(cameraPos, wPos.xyz);\n      return clamp((fogEnd - cam_dis) / (fogEnd - fogStart), 0., 1.);\n  }\n  float ExpFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * fogDensity);\n      return f;\n  }\n  float ExpSquaredFog(vec4 pos, vec3 cameraPos, float fogStart, float fogDensity, float fogAtten) {\n      vec4 wPos = pos;\n      float cam_dis = max(distance(cameraPos, wPos.xyz) - fogStart, 0.0) / fogAtten * 4.;\n      float f = exp(-cam_dis * cam_dis * fogDensity * fogDensity);\n      return f;\n  }\n  float LayeredFog(vec4 pos, vec3 cameraPos, float fogTop, float fogRange, float fogAtten) {\n      vec4 wPos = pos;\n      vec3 camWorldProj = cameraPos.xyz;\n      camWorldProj.y = 0.;\n      vec3 worldPosProj = wPos.xyz;\n      worldPosProj.y = 0.;\n      float fDeltaD = distance(worldPosProj, camWorldProj) / fogAtten * 2.0;\n      float fDeltaY, fDensityIntegral;\n      if (cameraPos.y > fogTop) {\n          if (wPos.y < fogTop) {\n              fDeltaY = (fogTop - wPos.y) / fogRange * 2.0;\n              fDensityIntegral = fDeltaY * fDeltaY * 0.5;\n          }\n          else {\n              fDeltaY = 0.;\n              fDensityIntegral = 0.;\n          }\n      }\n      else {\n          if (wPos.y < fogTop) {\n              float fDeltaA = (fogTop - cameraPos.y) / fogRange * 2.;\n              float fDeltaB = (fogTop - wPos.y) / fogRange * 2.;\n              fDeltaY = abs(fDeltaA - fDeltaB);\n              fDensityIntegral = abs((fDeltaA * fDeltaA * 0.5) - (fDeltaB * fDeltaB * 0.5));\n          }\n          else {\n              fDeltaY = abs(fogTop - cameraPos.y) / fogRange * 2.;\n              fDensityIntegral = abs(fDeltaY * fDeltaY * 0.5);\n          }\n      }\n      float fDensity;\n      if (fDeltaY != 0.) {\n          fDensity = (sqrt(1.0 + ((fDeltaD / fDeltaY) * (fDeltaD / fDeltaY)))) * fDensityIntegral;\n      }\n      else {\n          fDensity = 0.;\n      }\n      float f = exp(-fDensity);\n      return f;\n  }\n#endif\nvoid CC_TRANSFER_FOG_BASE(vec4 pos, out float factor)\n{\n#if CC_USE_FOG == 0\n\tfactor = LinearFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.y);\n#elif CC_USE_FOG == 1\n\tfactor = ExpFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 2\n\tfactor = ExpSquaredFog(pos, cc_cameraPos.xyz, cc_fogBase.x, cc_fogBase.z, cc_fogAdd.z);\n#elif CC_USE_FOG == 3\n\tfactor = LayeredFog(pos, cc_cameraPos.xyz, cc_fogAdd.x, cc_fogAdd.y, cc_fogAdd.z);\n#else\n\tfactor = 1.0;\n#endif\n}\nvoid CC_APPLY_FOG_BASE(inout vec4 color, float factor) {\n\tcolor = vec4(mix(cc_fogColor.rgb, color.rgb, factor), color.a);\n}\n#if CC_USE_LIGHT_PROBE\n  #if CC_USE_LIGHT_PROBE\n  vec3 SHEvaluate(vec3 normal)\n  {\n      vec3 result;\n  #if USE_INSTANCING\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(v_sh_linear_const_r, normal4);\n      result.g = dot(v_sh_linear_const_g, normal4);\n      result.b = dot(v_sh_linear_const_b, normal4);\n  #else\n      vec4 normal4 = vec4(normal, 1.0);\n      result.r = dot(cc_sh_linear_const_r, normal4);\n      result.g = dot(cc_sh_linear_const_g, normal4);\n      result.b = dot(cc_sh_linear_const_b, normal4);\n      vec4 n14 = normal.xyzz * normal.yzzx;\n      float n5 = normal.x * normal.x - normal.y * normal.y;\n      result.r += dot(cc_sh_quadratic_r, n14);\n      result.g += dot(cc_sh_quadratic_g, n14);\n      result.b += dot(cc_sh_quadratic_b, n14);\n      result += (cc_sh_quadratic_a.rgb * n5);\n  #endif\n    #if CC_USE_HDR\n      result *= cc_exposure.w * cc_exposure.x;\n    #endif\n    return result;\n  }\n  #endif\n#endif\n#if CC_USE_REFLECTION_PROBE\n  layout(std140) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n  vec4 GetTexData(sampler2D dataMap, float dataMapWidth, float x, float uv_y)\n  {\n    return vec4(\n        decode32(texture(dataMap, vec2(((x + 0.5)/dataMapWidth), uv_y))),\n        decode32(texture(dataMap, vec2(((x + 1.5)/dataMapWidth), uv_y))),\n        decode32(texture(dataMap, vec2(((x + 2.5)/dataMapWidth), uv_y))),\n        decode32(texture(dataMap, vec2(((x + 3.5)/dataMapWidth), uv_y)))\n      );\n  }\n  void GetPlanarReflectionProbeData(out vec4 plane, out float planarReflectionDepthScale, out float mipCount, float probeId)\n  {\n      #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData1 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 0.0, uv_y);\n        vec4 texData2 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 4.0, uv_y);\n        plane.xyz = texData1.xyz;\n        plane.w = texData2.x;\n        planarReflectionDepthScale = texData2.y;\n        mipCount = texData2.z;\n      #else\n        plane = cc_reflectionProbeData1;\n        planarReflectionDepthScale = cc_reflectionProbeData2.x;\n        mipCount = cc_reflectionProbeData2.w;\n      #endif\n  }\n  void GetCubeReflectionProbeData(out vec3 centerPos, out vec3 boxHalfSize, out float mipCount, float probeId)\n  {\n      #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData1 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 0.0, uv_y);\n        vec4 texData2 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 4.0, uv_y);\n        vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n        centerPos = texData1.xyz;\n        boxHalfSize = texData2.xyz;\n        mipCount = texData3.x;\n      #else\n        centerPos = cc_reflectionProbeData1.xyz;\n        boxHalfSize = cc_reflectionProbeData2.xyz;\n        mipCount = cc_reflectionProbeData2.w;\n      #endif\n      if (mipCount > 1000.0) mipCount -= 1000.0;\n  }\n  bool isReflectProbeUsingRGBE(float probeId)\n  {\n    #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n        return texData3.x > 1000.0;\n    #else\n      return cc_reflectionProbeData2.w > 1000.0;\n    #endif\n  }\n  bool isBlendReflectProbeUsingRGBE(float probeId)\n  {\n    #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n        return texData3.x > 1000.0;\n    #else\n      return cc_reflectionProbeBlendData2.w > 1000.0;\n    #endif\n  }\n  void GetBlendCubeReflectionProbeData(out vec3 centerPos, out vec3 boxHalfSize, out float mipCount, float probeId)\n  {\n      #if USE_INSTANCING\n        float uv_y = (probeId + 0.5) / cc_probeInfo.x;\n        float dataMapWidth = 12.0;\n        vec4 texData1 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 0.0, uv_y);\n        vec4 texData2 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 4.0, uv_y);\n        vec4 texData3 = GetTexData(cc_reflectionProbeDataMap, dataMapWidth, 8.0, uv_y);\n        centerPos = texData1.xyz;\n        boxHalfSize = texData2.xyz;\n        mipCount = texData3.x;\n      #else\n        centerPos = cc_reflectionProbeBlendData1.xyz;\n        boxHalfSize = cc_reflectionProbeBlendData2.xyz;\n        mipCount = cc_reflectionProbeBlendData2.w;\n      #endif\n      if (mipCount > 1000.0) mipCount -= 1000.0;\n  }\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  uniform sampler2D cc_lightingMap;\n  void SampleAndDecodeLightMapColor(out vec3 lightmapColor, out float dirShadow, out float ao, sampler2D lightingMap, vec2 luv, float lum, vec3 worldNormal)\n  {\n  #if CC_LIGHT_MAP_VERSION > 2\n  #elif CC_LIGHT_MAP_VERSION > 1\n  \tvec4 dataLow = texture(lightingMap, luv);\n  \tvec4 dataHigh = texture(lightingMap, luv + vec2(0.5, 0.0));\n  \tlightmapColor.xyz = dataLow.xyz + dataHigh.xyz * 0.00392156862745098;\n      lightmapColor.rgb *= lum;\n  \tdirShadow = dataLow.a;\n  \tao = dataHigh.a;\n  #else\n      vec4 lightmap = texture(lightingMap, luv);\n      lightmapColor = lightmap.rgb * lum;\n  \tdirShadow = lightmap.a;\n  \tao = 1.0;\n  #endif\n  }\n  void GetLightMapColor(out vec3 lightmapColor, out float dirShadow, out float ao, sampler2D lightingMap, vec2 luv, float lum, vec3 worldNormal)\n  {\n  \tvec4 lightmap;\n  \tvec2 occlusion;\n  \tSampleAndDecodeLightMapColor(lightmapColor, dirShadow, ao, lightingMap, luv, lum, worldNormal);\n  #if CC_USE_HDR\n      lightmapColor.rgb *= cc_exposure.w * cc_exposure.x;\n  #endif\n  }\n#endif\nlayout(std140) uniform Constants {\n  vec4 albedo;\n  vec4 albedoScaleAndCutoff;\n  vec4 pbrParams;\n};\n#if USE_ALBEDO_MAP\n  uniform sampler2D albedoMap;\n#endif\n#if USE_ALPHA_TEST\n#endif\n#define CC_SURFACES_FRAGMENT_MODIFY_BASECOLOR_AND_TRANSPARENCY\nvec4 SurfacesFragmentModifyBaseColorAndTransparency()\n{\n  vec4 baseColor = albedo;\n  #if USE_ALBEDO_MAP\n    vec4 texColor = texture(albedoMap, ALBEDO_UV);\n    texColor.rgb = SRGBToLinear(texColor.rgb);\n    baseColor *= texColor;\n  #endif\n  #if USE_ALPHA_TEST\n    if (baseColor.ALPHA_TEST_CHANNEL < albedoScaleAndCutoff.w) discard;\n  #endif\n  baseColor.rgb *= albedoScaleAndCutoff.xyz;\n  return baseColor;\n}\n#define CC_SURFACES_FRAGMENT_ALPHA_CLIP_ONLY\n#define CC_SURFACES_FRAGMENT_MODIFY_WORLD_NORMAL\nvec3 SurfacesFragmentModifyWorldNormal()\n{\n  return normalize(FSInput_worldNormal);\n}\n#define CC_SURFACES_FRAGMENT_MODIFY_EMISSIVE\nvec3 SurfacesFragmentModifyEmissive()\n{\n  return vec3(0.0, 0.0, 0.0);\n}\n#define CC_SURFACES_FRAGMENT_MODIFY_PBRPARAMS\nvec4 SurfacesFragmentModifyPBRParams()\n{\n  return vec4(1.0, pbrParams.y, pbrParams.z, 0.5);\n}\nstruct LightingIntermediateData\n{\n  vec3 N, H, L, V;\n  float distToLight, distToLightSqr;\n  float distToCamera, distToCameraSqr;\n  float angleAttenuation, distAttenuation;\n  float NoL, NoV, NoH, VoH;\n  float NoLSat, NoVSat, NoHSat;\n  float NoVAbsSat, VoHAbsSat;\n  #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n  vec3 worldPosition, worldPosition_fract_part;\n  #else\n  vec3 worldPosition;\n  #endif\n  vec3 T, B;\n  float specularParam;\n  float ior, layerOpacity;\n#if CC_SURFACES_LIGHTING_ANISOTROPIC\n  float anisotropyShape;\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  vec4 shadowPosAndDepth;\n  vec4 transmitDiffuseParams;\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR || CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  vec4 transmitScatteringParams;\n  vec3 outScatteringColor, inScatteringColor;\n#endif\n#if CC_SURFACES_LIGHTING_TT\n  vec3 baseColorTT;\n  float ttIntensity, ttScatterCoef;\n#endif\n};\nvoid CCSurfacesLightingGetIntermediateData_PerPixel(inout LightingIntermediateData data, vec3 worldNormal, vec3 worldPos, vec3 worldTangent, vec3 worldBinormal\n#if CC_SURFACES_LIGHTING_ANISOTROPIC\n    , float anisotropyShape\n#endif\n)\n{\n  data.N = worldNormal;\n  data.V = cc_cameraPos.xyz - worldPos;\n  data.distToCameraSqr = dot(data.V, data.V);\n  data.distToCamera = sqrt(data.distToCameraSqr);\n  data.V /= data.distToCamera;\n  data.angleAttenuation = data.distAttenuation = 1.0;\n  data.NoV = dot(data.N, data.V);\n  data.NoVSat = max(data.NoV, 0.0);\n  data.NoVAbsSat = max(abs(data.NoV), 0.0);\n  #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n  packHighpData(data.worldPosition, data.worldPosition_fract_part, worldPos);\n  #else\n  data.worldPosition = worldPos;\n  #endif\n  data.T = worldTangent;\n  data.B = worldBinormal;\n#if CC_SURFACES_LIGHTING_ANISOTROPIC\n  data.anisotropyShape = anisotropyShape;\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR || CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  data.outScatteringColor = vec3(1.0);\n  data.inScatteringColor = vec3(0.0);\n  data.transmitScatteringParams = vec4(0.0);\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  data.shadowPosAndDepth = vec4(0.0, 0.0, SURFACES_MAX_TRANSMIT_DEPTH_VALUE, SURFACES_MAX_TRANSMIT_DEPTH_VALUE);\n#endif\n#if CC_SURFACES_LIGHTING_TT\n  data.baseColorTT = vec3(0.0);\n  data.ttIntensity = data.ttScatterCoef = 0.0;\n#endif\n}\nvoid CCSurfacesLightingGetIntermediateData_PerLight(inout LightingIntermediateData data, vec3 lightDirWithDist)\n{\n  data.L = lightDirWithDist;\n  data.distToLightSqr = dot(data.L, data.L);\n  data.distToLight = sqrt(data.distToLightSqr);\n  data.L /= data.distToLight;\n  data.H = normalize(data.L + data.V);\n  data.NoL = dot(data.N, data.L);\n  data.NoH = dot(data.N, data.H);\n  data.VoH = dot(data.V, data.H);\n  data.NoLSat = max(data.NoL, 0.0);\n  data.NoHSat = max(data.NoH, 0.0);\n  data.VoHAbsSat = max(abs(data.VoH), 0.0);\n}\nstruct LightingResult\n{\n  vec3 diffuseColorWithLighting, specularColorWithLighting;\n  vec3 directDiffuse, directSpecular, directGF;\n  vec3 environmentDiffuse, environmentSpecular, environmentGF;\n  float shadow, ao;\n  vec3 lightmapColor;\n  vec3 emissive;\n  vec3 fresnel;\n#if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n  vec3 directDiffuseSubLayers, directSpecularSubLayers;\n  vec3 environmentDiffuseSubLayers, environmentSpecularSubLayers;\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n  vec3 directTransmitSpecular, environmentTransmitSpecular;\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  vec3 directTransmitDiffuse, environmentTransmitDiffuse;\n#endif\n#if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n  vec3 direct2ndSpecular, environment2ndSpecular;\n  vec3 specularColorWithLighting2ndSpecular;\n  vec3 directGF2ndSpecular, environmentGF2ndSpecular;\n  vec3 directSubLayerF, environmentSubLayerF;\n#endif\n#if CC_SURFACES_LIGHTING_TT\n  vec3 directTT;\n  vec3 diffuseColorWithLightingTT;\n#endif\n};\nstruct LightingMiscData\n{\n  float lightType;\n  vec3 lightPos, lightDir;\n  vec4 lightColorAndIntensity;\n  vec4 lightSizeRangeAngle;\n};\nfloat square(float a) { return a * a;}\nvec2 square(vec2 a) { return a * a;}\nvec3 square(vec3 a) { return a * a;}\nfloat G_Schlick( float roughness, float NoV, float NoL )\n{\n\tfloat k = square( 0.5 + 0.5*roughness );\n\tfloat G_SchlickV = NoV * (1.0 - k) + k;\n\tfloat G_SchlickL = NoL * (1.0 - k) + k;\n\treturn 0.25 / ( G_SchlickV * G_SchlickL );\n}\nvec3 F_SchlickMultiplier( vec3 specularColor, float VoH )\n{\n\tfloat Fc = exp2( (-5.55473 * VoH - 6.98316) * VoH );\n  float selfShadowTerm = saturate(50.0 * specularColor.g);\n  return vec3(1.0 - Fc) + vec3(selfShadowTerm * Fc) / (specularColor + vec3(EPSILON));\n}\nfloat D_GGX(float roughness, float NoH)\n{\n    float m = roughness * roughness;\n    float m2 = m * m;\n    float d = (NoH * m2 - NoH) * NoH + 1.0;\n    return m2 / max(EPSILON, d * d);\n}\nfloat D_GGXMobile(float roughness, float NoH) {\n  float OneMinusNoHSqr = 1.0 - NoH * NoH;\n  float a = roughness * roughness;\n  float n = NoH * a;\n  float p = a / max(EPSILON, OneMinusNoHSqr + n * n);\n  return p * p;\n}\nvoid GetAnisotropicRoughness(float roughness, float anisotropyShape, out float roughnessX, out float roughnessY)\n{\n    float shapeSign = sign(anisotropyShape);\n    anisotropyShape *= anisotropyShape;\n    float r1 = roughness, r2 = roughness;\n    float lerpedRoughness = mix(1.0, 10.0, anisotropyShape);\n    r2 *= shapeSign < 0.0 ? lerpedRoughness : 1.0;\n    r1 *= shapeSign > 0.0 ? lerpedRoughness : 1.0;\n    roughnessX = saturate(r1);\n    roughnessY = saturate(r2);\n}\nfloat D_GGXAniso(float RoughnessX, float RoughnessY, float NoH, vec3 H, vec3 X, vec3 Y)\n{\n    float mx = max(EPSILON_LOWP, RoughnessX * RoughnessX);\n    float my = max(EPSILON_LOWP, RoughnessY * RoughnessY);\n    float XoH = dot(X, H);\n    float YoH = dot(Y, H);\n    float d = XoH * XoH / (mx * mx) + YoH * YoH / (my * my) + NoH * NoH;\n    return 1.0 / max(EPSILON_LOWP, mx * my * d * d);\n}\nvec3 GetAnisotropicReflect(float roughness, float anisotropyShape, vec3 V, vec3 N, vec3 X, vec3 Y)\n{\n    float shapeSign = sign(anisotropyShape);\n    anisotropyShape *= anisotropyShape;\n    anisotropyShape = min(anisotropyShape, 0.4);\n    anisotropyShape *= smoothstep(0.0, 0.03, roughness);\n    vec3 reflectTarget = shapeSign < 0.0 ? mix(N, -Y, anisotropyShape) :\n                         shapeSign > 0.0 ? mix(N, -X, anisotropyShape) : N;\n    return reflect(-V, reflectTarget);\n}\nvoid IntegratedGFMultiplier (out vec3 integratedGF, out vec3 integratedF, vec3 specular, float roughness, float NoV) {\n  const vec4 c0 = vec4(-1.0, -0.0275, -0.572, 0.022);\n  const vec4 c1 = vec4(1.0, 0.0425, 1.04, -0.04);\n  vec4 r = roughness * c0 + c1;\n  float a004 = min(r.x * r.x, exp2(-9.28 * NoV)) * r.x + r.y;\n  vec2 AB = vec2(-1.04, 1.04) * a004 + r.zw;\n  AB.y *= clamp(50.0 * specular.g, 0.0, 1.0);\n  integratedF = vec3(max(0.0, AB.x));\n  integratedGF = max(vec3(0.0), vec3(AB.x) + vec3(AB.y) / (specular + EPSILON_LOWP));\n}\nfloat Sheen_HorizonFading(float NoL)\n{\n  const float horizonFade = 1.3;\n  float horiz = saturate( 1.0 + horizonFade * NoL);\n  return horiz * horiz;\n}\nfloat Sheen(float NoHSat, float NoL, float NoV, float roughness)\n{\n  if (NoL <= 0.0 || NoV <= 0.0)\n    return 0.0;\n  float NoH2 = NoHSat*NoHSat;\n  float NoL2 = NoL*NoL;\n  float NoV2 = NoV*NoV;\n  roughness += EPSILON_LOWP;\n  float r2 = roughness*roughness;\n  float t = 1.0 - NoH2 + NoH2/r2;\n  float Pi_D = 1.0 / (roughness * t * t);\n  float Li = sqrt(1.0 - NoL2 + r2*NoL2) / NoL;\n  float Lo = sqrt(1.0 - NoV2 + r2*NoV2) / NoV;\n  float G = (1.0 - exp(-(Li + Lo))) / (Li + Lo);\n  return Sheen_HorizonFading(NoL) * Pi_D * G / NoV;\n}\nfloat Sheen(float NoV, float roughness)\n{\n  NoV *= NoV;\n  float NoV2 = NoV*NoV;\n  roughness += EPSILON_LOWP;\n  float r2 = roughness*roughness;\n  float t = 1.0 - NoV2 + NoV2/r2;\n  float Pi_D = 1.0 / (roughness * t * t);\n  float Lo = sqrt(1.0 - NoV2 + r2*NoV2) / NoV;\n  float G = (1.0 - exp(-Lo)) / Lo;\n  float sheen = Pi_D * G / NoV;\n  return pow(max(0.0, sheen), 0.5);\n}\n#define DiffuseCoefficient_EnergyConservation INV_PI\nfloat CalculateFresnelCoefficient(float ior, float NoVSat)\n{\n\tfloat g, c, n, prev, next;\n\tn = ior;\n\tc = ior * NoVSat;\n\tg = sqrt(1.0 + c * c - c);\n\tprev = (g - c) / (g + c);\n\tnext = (c * (g+c) - n*n) / (c * (g-c) + n*n);\n\tprev *= prev;\n\tnext *= next;\n\treturn 0.5 * prev * (1.0 + next);\n}\nfloat CalculateFresnelCoefficient(float F0, float F90, float NoVSat)\n{\n  return mix(F90, F0, NoVSat);\n}\nvec3 CalculateScattering(vec3 unscatteredColor, float distance, float outScatterExtinctCoef, float inScatterExtinctCoef, float inScatterCoef, vec3 inScatterColor, vec3 outScatterColor)\n{\n  vec2 e = vec2(outScatterExtinctCoef, inScatterExtinctCoef * inScatterCoef);\n  vec2 extinction = exp(-e * distance);\n  vec3 inScattered = (1.0 - extinction.y) * inScatterColor;\n  vec3 outScattered = unscatteredColor * extinction.x * outScatterColor;\n  return outScattered + inScattered;\n}\nvoid InitializeLayerBlending(out vec3 blendedBaseLayerD, out vec3 blendedBaseLayerS,\n                             out vec3 blendedSubLayerD, out vec3 blendedSubLayerS,\n                             out vec3 lastLayerF,\n                             in vec3 baseD, in vec3 baseS\n                             )\n{\n  blendedBaseLayerD = baseD;\n  blendedBaseLayerS = baseS;\n  blendedSubLayerD = blendedSubLayerS = vec3(0.0);\n  lastLayerF = vec3(1.0);\n}\nvoid CalculateLayerBlending(inout vec3 blendedBaseLayerD, inout vec3 blendedBaseLayerS,\n                            inout vec3 blendedSubLayerD, inout vec3 blendedSubLayerS,\n                            inout vec3 lastLayerF,\n                            in vec3 subLayerD, in vec3 subLayerDiffuseColor,\n                            in vec3 subLayerS, in vec3 subLayerSpecularColor,\n                            in float subLayerOpacity, inout vec3 subLayerF\n                            )\n{\n  subLayerF = saturate(subLayerF * subLayerOpacity);\n  blendedSubLayerD = blendedSubLayerD * (vec3(1.0) - lastLayerF) + subLayerD * subLayerDiffuseColor * subLayerF;\n  blendedSubLayerS = blendedSubLayerS *(vec3(1.0) - lastLayerF) + subLayerS * subLayerSpecularColor * subLayerF;\n  blendedBaseLayerD *= vec3(1.0) - subLayerF;\n  blendedBaseLayerS *= vec3(1.0) - subLayerF;\n  lastLayerF = subLayerF;\n}\nfloat SmoothDistAtt (float distSqr, float invSqrAttRadius) {\n  float factor = distSqr * invSqrAttRadius;\n  float smoothFactor = clamp(1.0 - factor * factor, 0.0, 1.0);\n  return smoothFactor * smoothFactor;\n}\nfloat GetDistAtt (float distSqr, float invSqrAttRadius) {\n  float attenuation = 1.0 / max(distSqr, 0.01*0.01);\n  attenuation *= SmoothDistAtt(distSqr , invSqrAttRadius);\n  return attenuation;\n}\nfloat GetAngleAtt (vec3 L, vec3 litDir, float litAngleScale, float litAngleOffset) {\n  float cd = dot(litDir, L);\n  float attenuation = clamp(cd * litAngleScale + litAngleOffset, 0.0, 1.0);\n  return (attenuation * attenuation);\n}\nfloat GetOutOfRange (vec3 worldPos, vec3 lightPos, vec3 lookAt, vec3 right, vec3 BoundingHalfSizeVS) {\n  vec3 v = vec3(0.0);\n  vec3 up = cross(right, lookAt);\n  worldPos -= lightPos;\n  v.x = dot(worldPos, right);\n  v.y = dot(worldPos, up);\n  v.z = dot(worldPos, lookAt);\n  vec3 result = step(abs(v), BoundingHalfSizeVS);\n  return result.x * result.y * result.z;\n}\nfloat CalculateDistanceAttenuation(float distToLightSqr, float lightRadius, float lightRange, float lightType)\n{\n  float attRadiusSqrInv = 1.0 / max(lightRange, 0.01);\n  attRadiusSqrInv *= attRadiusSqrInv;\n  float litRadiusSqr = lightRadius * lightRadius;\n  float edgeAttenuation = (IS_POINT_LIGHT(lightType) || IS_RANGED_DIRECTIONAL_LIGHT(lightType)) ? 1.0 : litRadiusSqr / max(litRadiusSqr, distToLightSqr);\n  return GetDistAtt(distToLightSqr, attRadiusSqrInv) * edgeAttenuation;\n}\nfloat CalculateAngleAttenuation(vec3 spotLightDir, vec3 L, float cosAngleOuter)\n{\n  float cosInner = max(dot(spotLightDir, L), 0.01);\n  float litAngleScale = 1.0 / max(0.001, cosInner - cosAngleOuter);\n  float litAngleOffset = -cosAngleOuter * litAngleScale;\n  return GetAngleAtt(L, spotLightDir, litAngleScale, litAngleOffset);\n}\nvec3 CalculateRefractDirection(vec3 N, vec3 V, float NoV, float ior)\n{\n  float sideSign = NoV < 0.0 ? -1.0 : 1.0;\n  N *= sideSign;\n  float cosA = abs(NoV);\n  float sinA = sqrt(1.0 - cosA * cosA);\n  float sinB = saturate(sinA / ior);\n  float cosB = sqrt(1.0 - sinB * sinB);\n  vec3 edgeA = -V + N * cosA;\n  vec3 edgeB = normalize(edgeA) * sinB;\n  vec3 R = edgeB - N * cosB;\n  return R;\n}\nvec3 CalculateReflectDirection(vec3 N, vec3 V, float NoV)\n{\n  float sideSign = NoV < 0.0 ? -1.0 : 1.0;\n  N *= sideSign;\n  return reflect(-V, N);\n}\nvec3 CalculatePlanarReflectPositionOnPlane(vec3 N, vec3 V, vec3 worldPos, vec4 plane, vec3 cameraPos, float probeReflectedDepth)\n{\n  float distPixelToPlane = -dot(plane, vec4(worldPos, 1.0));\n  plane.w += distPixelToPlane;\n  float distCameraToPlane = abs(-dot(plane, vec4(cameraPos, 1.0)));\n  vec3 planeN = plane.xyz;\n  vec3 virtualCameraPos = cameraPos - 2.0 * distCameraToPlane * planeN;\n  vec3 bumpedR = normalize(reflect(-V, N));\n  vec3 reflectedPointPos = worldPos + probeReflectedDepth * bumpedR;\n  vec3 virtualCameraToReflectedPoint = normalize(reflectedPointPos - virtualCameraPos);\n  float y = distCameraToPlane / max(EPSILON_LOWP, dot(planeN, virtualCameraToReflectedPoint));\n  return virtualCameraPos + y * virtualCameraToReflectedPoint;\n}\nvec4 CalculateBoxProjectedDirection(vec3 R, vec3 worldPos, vec3 cubeCenterPos, vec3 cubeBoxHalfSize)\n{\n  vec3 W = worldPos - cubeCenterPos;\n  vec3 projectedLength = (sign(R) * cubeBoxHalfSize - W) / (R + vec3(EPSILON));\n  float len = min(min(projectedLength.x, projectedLength.y), projectedLength.z);\n  vec3 P = W + len * R;\n  float weight = len < 0.0 ? 0.0 : 1.0;\n  return vec4(P, weight);\n}\nvec3 CalculateDirectDiffuse(in LightingIntermediateData lightingData, in vec4 lightSourceColorAndIntensity)\n{\n    vec3 irradiance = vec3(lightingData.NoLSat) * lightSourceColorAndIntensity.rgb * lightSourceColorAndIntensity.w;\n    return irradiance * DiffuseCoefficient_EnergyConservation;\n}\nvec3 CalculateDirectSpecular(in LightingIntermediateData lightingData, in vec4 lightSourceColorAndIntensity)\n{\n    vec3 irradiance = vec3(lightingData.NoLSat) * lightSourceColorAndIntensity.rgb * lightSourceColorAndIntensity.w;\n    float roughness = lightingData.specularParam;\n  #if CC_SURFACES_LIGHTING_ANISOTROPIC\n      float rT, rB;\n      GetAnisotropicRoughness(roughness, lightingData.anisotropyShape, rT, rB);\n      float calcSpec = D_GGXAniso(rT, rB, lightingData.NoHSat, lightingData.H, lightingData.T, lightingData.B);\n  #else\n    #if CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING\n      float calcSpec = (roughness * 0.25 + 0.25) * D_GGXMobile(roughness, lightingData.NoHSat);\n    #else\n      float calcSpec = D_GGX(roughness, lightingData.NoHSat);\n    #endif\n  #endif\n    return irradiance * calcSpec;\n}\n#if CC_SURFACES_LIGHTING_ANISOTROPIC && CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT\n  vec3 EnvAnisotropicReflection(samplerCube tex, vec3 R, float roughness, float mipCount, float anisotropyShape, vec3 V, vec3 N, vec3 T, vec3 B) {\n      R = normalize(R);\n      float integratedBRDF = 0.0;\n      vec3 envSpec = vec3(0.0);\n      const int SAMPLE_STEP_COUNT = CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT;\n      float sampleAngleRange = PI * abs(anisotropyShape);\n      vec3 anisoDirection = anisotropyShape < 0.0 ? T : B;\n      vec3 ROnNormalPlane = normalize(R - anisoDirection * dot(R, anisoDirection));\n      vec3 stepOffset = normalize(ROnNormalPlane - N) * (sampleAngleRange / float(SAMPLE_STEP_COUNT * 2));\n      for (int i = -SAMPLE_STEP_COUNT; i <= SAMPLE_STEP_COUNT; ++i)\n      {\n          float rT, rB;\n          GetAnisotropicRoughness(roughness, anisotropyShape, rT, rB);\n          #if CC_IBL_CONVOLUTED\n            float coef = abs(float(i)) / float(SAMPLE_STEP_COUNT) * float(SAMPLE_STEP_COUNT);\n          #else\n            float coef = pow(abs(float(i)) / float(SAMPLE_STEP_COUNT), 1.3) * float(SAMPLE_STEP_COUNT);\n          #endif\n          vec3 H = normalize(N + stepOffset * sign(float(i)) * coef);\n          vec3 L = reflect(-V, H);\n          float NoHSat = saturate(dot(N, H));\n          float calcSpec = D_GGXAniso(rT, rB, NoHSat, H, T, B);\n          envSpec += calcSpec * EnvReflection(tex, L, roughness, mipCount);\n          integratedBRDF += calcSpec;\n      }\n      envSpec /= integratedBRDF;\n      return envSpec;\n  }\n#endif\nvec3 SampleEnvironmentSpecular(samplerCube tex, in LightingIntermediateData lightingData, float mipCount)\n{\n    vec3 envSpec = vec3(0.0);\n    float roughness = lightingData.specularParam;\n    #if CC_SURFACES_LIGHTING_ANISOTROPIC && !CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT\n      vec3 R = GetAnisotropicReflect(roughness, lightingData.anisotropyShape, lightingData.V, lightingData.N, lightingData.T, lightingData.B);\n    #else\n      vec3 R = CalculateReflectDirection(lightingData.N, lightingData.V, lightingData.NoV);\n    #endif\n    #if CC_SURFACES_LIGHTING_ANISOTROPIC && CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT\n      envSpec = EnvAnisotropicReflection(tex, R, roughness, mipCount, lightingData.anisotropyShape, lightingData.V, lightingData.N, lightingData.T, lightingData.B);\n    #else\n      #if CC_SURFACES_USE_REFLECTION_DENOISE && !CC_IBL_CONVOLUTED\n        envSpec = EnvReflectionWithMipFiltering(normalize(R), roughness, mipCount, 0.6);\n      #else\n        envSpec = EnvReflection(tex, R, roughness, mipCount);\n      #endif\n    #endif\n    return envSpec;\n}\nvec3 SampleEnvironmentSpecular(samplerCube tex, in LightingIntermediateData lightingData, float mipCount, vec3 worldPos, vec3 cubeCenterPos, vec3 boxHalfSize, bool isRGBE)\n{\n    vec3 envSpec = vec3(0.0);\n    float roughness = lightingData.specularParam;\n    #if CC_SURFACES_LIGHTING_ANISOTROPIC && !CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT\n      vec3 R = GetAnisotropicReflect(roughness, lightingData.anisotropyShape, lightingData.V, lightingData.N, lightingData.T, lightingData.B);\n    #else\n      vec3 R = CalculateReflectDirection(lightingData.N, lightingData.V, lightingData.NoV);\n    #endif\n    vec4 fixedR = CalculateBoxProjectedDirection(R, worldPos, cubeCenterPos, boxHalfSize);\n    R = fixedR.xyz;\n    vec3 envmap = SampleEnvironmentSpecular(cc_environment, lightingData, cc_ambientGround.w).xyz * cc_ambientSky.w;\n    #if CC_SURFACES_LIGHTING_ANISOTROPIC && CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT\n      envSpec = EnvAnisotropicReflection(tex, fixedR.xyz, roughness, mipCount, lightingData.anisotropyShape, lightingData.V, lightingData.N, lightingData.T, lightingData.B);\n      #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_CUBE\n        envSpec = mix(envmap, envSpec, fixedR.w);\n      #endif\n    #else\n      #if CC_SURFACES_USE_REFLECTION_DENOISE && !CC_IBL_CONVOLUTED\n        envSpec = EnvReflectionWithMipFiltering(normalize(R), roughness, mipCount, 0.6);\n      #else\n        envSpec = EnvReflectionOfReflectionProbe(tex, R, roughness, mipCount, isRGBE);\n        #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_CUBE\n          envSpec = mix(envmap, envSpec, fixedR.w);\n        #endif\n      #endif\n    #endif\n    return envSpec;\n}\nvec3 CalculateEnvironmentDiffuse(in LightingIntermediateData lightingData, float lightIntensity)\n{\n  float fAmb = max(EPSILON, 0.5 - lightingData.N.y * 0.5);\n  vec3 ambDiff = mix(cc_ambientSky.rgb, cc_ambientGround.rgb, fAmb);\n  #if CC_USE_IBL\n    #if CC_USE_DIFFUSEMAP && !CC_USE_LIGHT_PROBE\n      vec3 rotationDir = RotationVecFromAxisY(lightingData.N, cc_surfaceTransform.z, cc_surfaceTransform.w);\n      vec4 diffuseMap = texture(cc_diffuseMap, rotationDir);\n      #if CC_USE_DIFFUSEMAP == 2\n        ambDiff = unpackRGBE(diffuseMap);\n      #else\n        ambDiff = SRGBToLinear(diffuseMap.rgb);\n      #endif\n    #endif\n  #endif\n  ambDiff.rgb *= lightIntensity;\n  #if CC_USE_LIGHT_PROBE\n    ambDiff.rgb += SHEvaluate(lightingData.N);\n  #endif\n  return ambDiff.rgb;\n}\nvec3 CalculateEnvironmentSpecular(in LightingIntermediateData lightingData, float lightIntensity)\n{\n  vec3 envSpec = vec3(0.0);\n#if CC_USE_REFLECTION_PROBE\n    vec3 worldPos;\n    #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n    worldPos = unpackHighpData(lightingData.worldPosition, lightingData.worldPosition_fract_part);\n    #else\n    worldPos = lightingData.worldPosition;\n    #endif\n  #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_CUBE\n    if(FSInput_reflectionProbeId < 0.0){\n        envSpec = SampleEnvironmentSpecular(cc_environment, lightingData, cc_ambientGround.w);\n    }else{\n      vec3 centerPos, boxHalfSize;\n      float mipCount;\n      GetCubeReflectionProbeData(centerPos, boxHalfSize, mipCount, FSInput_reflectionProbeId);\n      envSpec = SampleEnvironmentSpecular(cc_reflectionProbeCubemap, lightingData, mipCount, worldPos, centerPos, boxHalfSize, isReflectProbeUsingRGBE(FSInput_reflectionProbeId));\n    }\n  #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_PLANAR\n    vec3 R = normalize(CalculateReflectDirection(lightingData.N, lightingData.V, lightingData.NoV));\n    if(FSInput_reflectionProbeId < 0.0){\n        vec2 screenUV = GetPlanarReflectScreenUV(worldPos, cc_matViewProj, cc_cameraPos.w, lightingData.V, R);\n        envSpec = unpackRGBE(fragTextureLod(cc_reflectionProbePlanarMap, screenUV, 1.0)).xyz;\n    }else{\n        vec4 plane;\n        float planarReflectionDepthScale, mipCount;\n        GetPlanarReflectionProbeData(plane, planarReflectionDepthScale, mipCount, FSInput_reflectionProbeId);\n        vec3 worldPosOffset = CalculatePlanarReflectPositionOnPlane(lightingData.N, lightingData.V, worldPos, plane, cc_cameraPos.xyz, planarReflectionDepthScale);\n        vec2 screenUV = GetPlanarReflectScreenUV(worldPosOffset, cc_matViewProj, cc_cameraPos.w, lightingData.V, R);\n        envSpec = unpackRGBE(fragTextureLod(cc_reflectionProbePlanarMap, screenUV, mipCount)).xyz;\n    }\n  #elif CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n    if(FSInput_reflectionProbeId < 0.0){\n        envSpec = SampleEnvironmentSpecular(cc_environment, lightingData, cc_ambientGround.w);\n    }else{\n      vec3 centerPos, boxHalfSize;\n      float mipCount;\n      GetCubeReflectionProbeData(centerPos, boxHalfSize, mipCount, FSInput_reflectionProbeId);\n      envSpec = SampleEnvironmentSpecular(cc_reflectionProbeCubemap, lightingData, mipCount, worldPos, centerPos, boxHalfSize, isReflectProbeUsingRGBE(FSInput_reflectionProbeId));\n      float blendFactor = 0.0;\n      #if USE_INSTANCING\n        blendFactor = FSInput_reflectionProbeData.x;\n      #else\n        blendFactor = cc_reflectionProbeBlendData1.w;\n      #endif\n      if(FSInput_reflectionProbeBlendId < 0.0)\n      {\n        vec3 skyBoxEnv = SampleEnvironmentSpecular(cc_environment, lightingData, cc_ambientGround.w).rgb * lightIntensity;\n        #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n          envSpec = mix(envSpec, skyBoxEnv, blendFactor);\n        #else\n          vec3 R = normalize(CalculateReflectDirection(lightingData.N, lightingData.V, lightingData.NoV));\n          vec4 fixedR = CalculateBoxProjectedDirection(R, worldPos, centerPos, boxHalfSize);\n          envSpec = mix(skyBoxEnv, envSpec, fixedR.w);\n        #endif\n      }else{\n        vec3 centerPosBlend, boxHalfSizeBlend;\n        float mipCountBlend;\n        GetBlendCubeReflectionProbeData(centerPosBlend, boxHalfSizeBlend, mipCountBlend, FSInput_reflectionProbeBlendId);\n        vec3 probeBlend = SampleEnvironmentSpecular(cc_reflectionProbeBlendCubemap, lightingData, mipCountBlend, worldPos, centerPosBlend, boxHalfSizeBlend, isBlendReflectProbeUsingRGBE(FSInput_reflectionProbeBlendId));\n        envSpec = mix(envSpec, probeBlend, blendFactor);\n      }\n    }\n  #endif\n#elif CC_USE_IBL\n    envSpec = SampleEnvironmentSpecular(cc_environment, lightingData, cc_ambientGround.w);\n#endif\n  #if CC_USE_REFLECTION_PROBE\n    lightIntensity = FSInput_reflectionProbeId < 0.0 ? lightIntensity : 1.0;\n  #endif\n  return envSpec * lightIntensity;\n}\nbool CCSurfacesLightingEnableShadow(in float NoL)\n{\n#if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  return true;\n#elif CC_SURFACES_LIGHTING_SSS\n  return true;\n#else\n  return NoL > 0.0;\n#endif\n}\nfloat CCSurfacesLightingCalculateDistanceAttenuation(in LightingIntermediateData lightingData, in vec4 lightSizeRangeAngle, in float lightType)\n{\n  return CalculateDistanceAttenuation(lightingData.distToLightSqr, lightSizeRangeAngle.x, lightSizeRangeAngle.y, lightType);\n}\nfloat CCSurfacesLightingCalculateAngleAttenuation(in LightingIntermediateData lightingData, in vec4 lightSizeRangeAngle, in vec3 spotLightDir)\n{\n  return CalculateAngleAttenuation(spotLightDir, lightingData.L, lightSizeRangeAngle.z);\n}\nvoid CCSurfacesLightingCalculateDirect(out vec3 lightingDiffuse, out vec3 lightingSpecular, in LightingIntermediateData lightingData, in vec4 lightSourceColorAndIntensity)\n{\n#if !CC_SURFACES_LIGHTING_DISABLE_DIFFUSE\n  lightingDiffuse = CalculateDirectDiffuse(lightingData, lightSourceColorAndIntensity);\n#else\n  lightingDiffuse = vec3(0.0);\n#endif\n#if !CC_SURFACES_LIGHTING_DISABLE_SPECULAR\n  lightingSpecular = CalculateDirectSpecular(lightingData, lightSourceColorAndIntensity);\n#else\n  lightingSpecular = vec3(0.0);\n#endif\n}\nvoid CCSurfacesLightingCalculateEnvironment(out vec3 lightingDiffuse, out vec3 lightingSpecular, in LightingIntermediateData lightingData, float lightIntensity)\n{\n#if !CC_SURFACES_LIGHTING_DISABLE_DIFFUSE\n  lightingDiffuse = CalculateEnvironmentDiffuse(lightingData, lightIntensity);\n#else\n  lightingDiffuse = vec3(0.0);\n#endif\n#if !CC_SURFACES_LIGHTING_DISABLE_SPECULAR\n  lightingSpecular = CalculateEnvironmentSpecular(lightingData, lightIntensity);\n#else\n  lightingSpecular = vec3(0.0);\n#endif\n}\nvec3 CCSurfaceLightingCalculateExtraFresnel(in LightingIntermediateData lightingData)\n{\n    float fresnel = CalculateFresnelCoefficient(lightingData.ior, lightingData.NoVAbsSat);\n    return vec3(fresnel);\n}\nvoid CCSurfaceLightingCalculateDirectFresnel(out vec3 directGF, in LightingIntermediateData lightingData, vec3 specularColor, in vec3 environmentGF)\n{\n  #if CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING\n    directGF = environmentGF;\n  #else\n    float roughness = lightingData.specularParam;\n    directGF = F_SchlickMultiplier(specularColor, lightingData.VoHAbsSat) * G_Schlick(roughness, lightingData.NoVSat, lightingData.NoLSat);\n  #endif\n}\nvoid CCSurfaceLightingCalculateEnvironmentFresnel(out vec3 integratedGF, vec3 integratedF, in LightingIntermediateData lightingData, vec3 specularColor)\n{\n  float roughness = lightingData.specularParam;\n  IntegratedGFMultiplier(integratedGF, integratedF, specularColor, roughness, lightingData.NoVAbsSat);\n}\n#if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n  void CCSurfaceLightingCalculateDirectSubLayerFresnel(out vec3 subLayerF, in LightingIntermediateData lightingData, vec3 specularColor)\n  {\n    float VoH = lightingData.VoHAbsSat;\n  \tsubLayerF = vec3(exp2( (-5.55473 * VoH - 6.98316) * VoH ));\n  }\n  void CCSurfaceLightingCalculateEnvironmentSubLayerFresnel(out vec3 subLayerF, in LightingIntermediateData lightingData, vec3 specularColor)\n  {\n    subLayerF = vec3(CalculateFresnelCoefficient(lightingData.ior, lightingData.NoVSat));\n  }\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n  void CCSurfacesLightingCalculateDirectTransmitSpecular(out vec3 lightingSpecular, in LightingIntermediateData lightingData, in vec4 lightSourceColorAndIntensity)\n  {\n    float roughness = lightingData.specularParam;\n    float NoLSat = saturate(dot(lightingData.N, -lightingData.L));\n    vec3 irradiance = NoLSat * lightSourceColorAndIntensity.rgb * lightSourceColorAndIntensity.w;\n    vec3 R = CalculateRefractDirection(lightingData.N, lightingData.V, lightingData.NoV, lightingData.ior);\n    float RoL = dot(lightingData.L, normalize(R));\n    float calcSpec = D_GGX(roughness, saturate(RoL));\n    lightingSpecular = irradiance * calcSpec;\n  }\n  void CCSurfacesLightingCalculateEnvironmentTransmitSpecular(out vec3 lightingSpecular, in LightingIntermediateData lightingData, float lightIntensity)\n  {\n    vec3 envSpec = vec3(0.0);\n    vec3 R = CalculateRefractDirection(lightingData.N, lightingData.V, lightingData.NoV, lightingData.ior);\n    float roughness = lightingData.specularParam;\n  #if CC_USE_REFLECTION_PROBE\n    #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_CUBE\n      envSpec = EnvReflection(cc_reflectionProbeCubemap, R, roughness, cc_ambientGround.w);\n    #endif\n  #endif\n  #if CC_USE_IBL && CC_USE_REFLECTION_PROBE != REFLECTION_PROBE_TYPE_CUBE\n    envSpec = EnvReflection(cc_environment, R, roughness, cc_ambientGround.w);\n  #endif\n    lightingSpecular = CalculateScattering(envSpec * lightIntensity, lightingData.transmitScatteringParams.w, lightingData.transmitScatteringParams.x, lightingData.transmitScatteringParams.x, lightingData.transmitScatteringParams.y, lightingData.inScatteringColor.rgb, lightingData.outScatteringColor.rgb);\n  }\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  #define objectThickness lightingData.transmitDiffuseParams.x\n  #define transmitMask lightingData.transmitDiffuseParams.y\n  #define envTransmitScale lightingData.transmitDiffuseParams.z\n  #define envFixedDistanceScale lightingData.transmitScatteringParams.w\n  #define transmitDistanceScale lightingData.transmitDiffuseParams.w\n  #define DONOT_USE_SHADOWMAP_DISTANCE ((abs(float(lightingData.shadowPosAndDepth.z) - float(lightingData.shadowPosAndDepth.w)) < EPSILON) && (abs(float(lightingData.shadowPosAndDepth.z) - float(SURFACES_MAX_TRANSMIT_DEPTH_VALUE)) < EPSILON))\n  #define SHADOWMAP_DISTANCE max(lightingData.shadowPosAndDepth.w - lightingData.shadowPosAndDepth.z, 0.0)\n  void CCSurfacesLightingCalculateDirectTransmitDiffuse(out vec3 transmitDiffuse, in LightingIntermediateData lightingData, in vec4 lightSourceColorAndIntensity, float shadow)\n  {\n  #if !CC_SURFACES_LIGHTING_DISABLE_DIFFUSE\n    float distance = lightingData.transmitScatteringParams.w;\n    if (!DONOT_USE_SHADOWMAP_DISTANCE)\n    {\n      distance = transmitDistanceScale * SHADOWMAP_DISTANCE;\n      shadow = step(SHADOWMAP_DISTANCE, objectThickness) > 0.0 ? 1.0 : shadow;\n    }\n    vec3 backIrradiance = CalculateDirectDiffuse(lightingData, lightSourceColorAndIntensity);\n    backIrradiance *= shadow * transmitMask;\n    transmitDiffuse = CalculateScattering(backIrradiance, distance, lightingData.transmitScatteringParams.x, lightingData.transmitScatteringParams.x, lightingData.transmitScatteringParams.y, lightingData.inScatteringColor.rgb, lightingData.outScatteringColor.rgb);\n  #else\n    transmitDiffuse = vec3(0.0);\n  #endif\n  }\n  void CCSurfacesLightingCalculateEnvironmentTransmitDiffuse(out vec3 transmitDiffuse, in LightingIntermediateData lightingData, float lightIntensity, float ao, vec3 shadowLightDirection)\n  {\n  #if !CC_SURFACES_LIGHTING_DISABLE_DIFFUSE\n    float distance = lightingData.transmitScatteringParams.w;\n    if (!DONOT_USE_SHADOWMAP_DISTANCE)\n    {\n      float shadowMapDistance = transmitDistanceScale * SHADOWMAP_DISTANCE;\n      float fixedDistance = transmitDistanceScale * envFixedDistanceScale;\n      float lerpCoef = saturate(dot(lightingData.N, shadowLightDirection));\n      distance = mix(fixedDistance, shadowMapDistance, lerpCoef);\n    }\n    vec3 backIrradiance = CalculateEnvironmentDiffuse(lightingData, lightIntensity);\n    backIrradiance *= ao * transmitMask;\n    transmitDiffuse = CalculateScattering(backIrradiance, distance, lightingData.transmitScatteringParams.x, lightingData.transmitScatteringParams.x, lightingData.transmitScatteringParams.y, lightingData.inScatteringColor.rgb, lightingData.outScatteringColor.rgb);\n    transmitDiffuse *= envTransmitScale;\n  #else\n    transmitDiffuse = vec3(0.0);\n  #endif\n  }\n  #undef objectThickness\n  #undef transmitMask\n  #undef envTransmitScale\n  #undef envFixedDistanceScale\n  #undef DONOT_USE_SHADOWMAP_DISTANCE\n  #undef SHADOWMAP_DISTANCE\n#endif\n#if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n  #ifndef CC_SURFACES_FRAGMENT_MODIFY_2ND_SPECULAR_COLOR\n  #endif\n  void CCSurfacesLightingCalculateDirect2ndSpecular(out vec3 specularLighting, in LightingIntermediateData lightingData, in vec4 lightSourceColorAndIntensity, float intensitySpecular, in vec3 originalSpecular)\n  {\n  #if !CC_SURFACES_LIGHTING_DISABLE_SPECULAR\n    vec3 unused;\n    CCSurfacesLightingCalculateDirect(unused, specularLighting, lightingData, lightSourceColorAndIntensity);\n    specularLighting *= intensitySpecular;\n  #else\n    specularLighting = vec3(0.0);\n  #endif\n  }\n  void CCSurfacesLightingCalculateEnvironment2ndSpecular(out vec3 specularLighting, in LightingIntermediateData lightingData, float lightIntensity, float intensitySpecular, in vec3 originalSpecular)\n  {\n  #if !CC_SURFACES_LIGHTING_DISABLE_SPECULAR\n    vec3 unused;\n    specularLighting = CalculateEnvironmentSpecular(lightingData, lightIntensity);\n    specularLighting *= intensitySpecular;\n  #else\n    specularLighting = vec3(0.0);\n  #endif\n  }\n  void CCSurfacesLightingCalculateDirectSheen(out vec3 specularLighting, out vec3 directGF, in LightingIntermediateData lightingData, in vec4 lightSourceColorAndIntensity, float intensitySpecular)\n  {\n  #if !CC_SURFACES_LIGHTING_DISABLE_SPECULAR\n    float sheen = Sheen(lightingData.NoHSat, lightingData.NoL, lightingData.NoV, lightingData.specularParam);\n    specularLighting = vec3(sheen) * intensitySpecular * lightSourceColorAndIntensity.xyz * lightSourceColorAndIntensity.w;\n    directGF = vec3(1.0);\n  #else\n    specularLighting = vec3(0.0);\n  #endif\n  }\n  void CCSurfacesLightingCalculateEnvironmentSheen(out vec3 specularLighting, out vec3 environmentGF, in LightingIntermediateData lightingData, float lightIntensity, float intensitySpecular)\n  {\n  #if !CC_SURFACES_LIGHTING_DISABLE_SPECULAR\n    LightingIntermediateData lightingDataSheen = lightingData;\n    float roughness = lightingData.specularParam;\n    vec3 L = normalize(mix(lightingDataSheen.B, lightingDataSheen.N, 0.3));\n    lightingDataSheen.specularParam = mix(0.5, 0.9, roughness);\n    lightingDataSheen.V = lightingDataSheen.N = L;\n    specularLighting = CalculateEnvironmentSpecular(lightingDataSheen, lightIntensity);\n    specularLighting *= intensitySpecular;\n    environmentGF = vec3(Sheen(lightingData.NoV, roughness));\n  #else\n    specularLighting = vec3(0.0);\n  #endif\n  }\n#endif\n#if CC_SURFACES_LIGHTING_TT\n  void CCSurfacesLightingCalculateDirectTT(inout LightingResult lightingResult, in LightingIntermediateData lightingData, in vec4 lightSourceColorAndIntensity)\n  {\n    lightingResult.diffuseColorWithLightingTT = lightingResult.diffuseColorWithLighting;\n    float w = lightingData.ttIntensity;\n    vec3 scatteredLighting = pow(saturate(lightingData.baseColorTT * w + lightingData.NoLSat) * lightingData.NoLSat, vec3(mix(0.5, 0.5 + lightingData.ttScatterCoef, w)));\n    vec3 ttLighting = scatteredLighting - lightingData.NoLSat;\n    lightingResult.directTT = ttLighting * DiffuseCoefficient_EnergyConservation * lightSourceColorAndIntensity.xyz* lightSourceColorAndIntensity.w;\n  }\n#endif\n#if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n  void CCSurfacesLightingCalculateDirectMultiLayerBlending(inout LightingResult lightingResult\n      , in LightingIntermediateData lightingData2ndLayer\n    #if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND > 1\n    #endif\n    )\n  {\n    vec3 lastLayerF, zeroDiffuse = vec3(0.0);\n    vec3 blendedBaseD = lightingResult.directDiffuse, blendedBaseS = lightingResult.directSpecular;\n    InitializeLayerBlending(blendedBaseD, blendedBaseS,\n                            lightingResult.directDiffuseSubLayers, lightingResult.directSpecularSubLayers,\n                            lastLayerF,\n                            lightingResult.directDiffuse, lightingResult.directSpecular\n                            );\n      CCSurfaceLightingCalculateDirectSubLayerFresnel(lightingResult.directSubLayerF, lightingData2ndLayer, lightingResult.specularColorWithLighting2ndSpecular);\n      CalculateLayerBlending (blendedBaseD, blendedBaseS,\n                              lightingResult.directDiffuseSubLayers, lightingResult.directSpecularSubLayers,\n                              lastLayerF,\n                              zeroDiffuse, zeroDiffuse,\n                              lightingResult.direct2ndSpecular, lightingResult.specularColorWithLighting2ndSpecular,\n                              lightingData2ndLayer.layerOpacity, lightingResult.directSubLayerF\n                              );\n    #if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND > 1\n    #endif\n    lightingResult.directDiffuse = blendedBaseD;\n    lightingResult.directSpecular = blendedBaseS;\n  }\n  void CCSurfacesLightingCalculateEnvironmentMultiLayerBlending(inout LightingResult lightingResult\n      , in LightingIntermediateData lightingData2ndLayer\n    #if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND > 1\n    #endif\n    )\n  {\n    vec3 lastLayerF, zeroDiffuse = vec3(0.0);\n    vec3 blendedBaseD = lightingResult.environmentDiffuse, blendedBaseS = lightingResult.environmentSpecular;\n    InitializeLayerBlending(blendedBaseD, blendedBaseS,\n                            lightingResult.environmentDiffuseSubLayers, lightingResult.environmentSpecularSubLayers,\n                            lastLayerF,\n                            lightingResult.environmentDiffuse, lightingResult.environmentSpecular\n                            );\n      CCSurfaceLightingCalculateEnvironmentSubLayerFresnel(lightingResult.environmentSubLayerF, lightingData2ndLayer, lightingResult.specularColorWithLighting2ndSpecular);\n      CalculateLayerBlending (blendedBaseD, blendedBaseS,\n                              lightingResult.environmentDiffuseSubLayers, lightingResult.environmentSpecularSubLayers,\n                              lastLayerF,\n                              zeroDiffuse, zeroDiffuse,\n                              lightingResult.environment2ndSpecular, lightingResult.specularColorWithLighting2ndSpecular,\n                              lightingData2ndLayer.layerOpacity, lightingResult.environmentSubLayerF\n                              );\n    #if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND > 1\n    #endif\n    lightingResult.environmentDiffuse = blendedBaseD;\n    lightingResult.environmentSpecular = blendedBaseS;\n  }\n#endif\nstruct SurfacesMaterialData\n{\n  #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n  vec3 worldPos, worldPos_fract_part;\n  #else\n  vec3 worldPos;\n  #endif\n  vec4 baseColor;\n  vec3 worldNormal;\n  vec3 emissive;\n  float specularIntensity;\n  float roughness;\n  float metallic;\n  float ao;\n  vec3 worldTangent, worldBinormal;\n  float ior;\n#if CC_SURFACES_LIGHTING_ANISOTROPIC\n  float anisotropyShape;\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR || CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  vec3 outScatteringColor, inScatteringColor;\n  vec4 transmitScatteringParams;\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  vec4 transmitDiffuseParams;\n#endif\n#if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n  vec3 baseColor2ndSpecular, color2ndSpecular;\n  float intensity2ndSpecular, roughness2ndSpecular, metallic2ndSpecular;\n  vec3 worldNormal2ndSpecular, worldTangent2ndSpecular, worldBinormal2ndSpecular;\n  #if CC_SURFACES_LIGHTING_ANISOTROPIC\n    float anisotropyShape2ndSpecular;\n  #endif\n  float ior2ndSpecular, opacity2ndSpecular;\n#endif\n#if CC_SURFACES_LIGHTING_TT\n  vec3 baseColorTT;\n  float ttIntensity, ttScatterCoef;\n#endif\n#if CC_SURFACES_LIGHTING_SSS\n  vec4 sssParams;\n#endif\n};\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_BASECOLOR_AND_TRANSPARENCY\nvec4 SurfacesFragmentModifyBaseColorAndTransparency()\n{\n    return FSInput_vertexColor;\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_ALPHA_CLIP_ONLY\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_WORLD_NORMAL\nvec3 SurfacesFragmentModifyWorldNormal()\n{\n    return normalize(FSInput_worldNormal);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_WORLD_TANGENT_AND_BINORMAL\nvoid SurfacesFragmentModifyWorldTangentAndBinormal(inout vec3 worldTangent, inout vec3 worldBinormal, vec3 worldNormal)\n{\n    vec3 tangent = normalize(FSInput_worldTangent);\n#if CC_SURFACES_USE_TANGENT_SPACE\n    vec3 binormal = normalize(CalculateBinormal(worldNormal.xyz, tangent, FSInput_mirrorNormal));\n    tangent = normalize(cross(binormal, worldNormal));\n#else\n    vec3 binormal = vec3(0.0, 0.0, 0.0);\n#endif\n    worldTangent = tangent;\n    worldBinormal = binormal;\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_IOR\nfloat SurfacesFragmentModifyIOR()\n{\n    return 1.0;\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_ANISOTROPY_PARAMS\nvec4 SurfacesFragmentModifyAnisotropyParams(out float isRotation)\n{\n    isRotation = 1.0;\n    return vec4(1.0, 0.0, 0.0, 0.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_EMISSIVE\nvec3 SurfacesFragmentModifyEmissive()\n{\n    return vec3(0.0, 0.0, 0.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_PBRPARAMS\nvec4 SurfacesFragmentModifyPBRParams()\n{\n    return vec4(1.0, 0.5, 0.0, 0.5);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_TRANSMIT_SCATTERING_PARAMS\nvec4 SurfacesFragmentModifyTransmitScatteringParams()\n{\n    return vec4(1.0, 1.0, 1.0, 1.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_TRANSMIT_IN_SCATTERING_COLOR\nvec3 SurfacesFragmentModifyTransmitInScatteringColor()\n{\n    return vec3(0.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_TRANSMIT_OUT_SCATTERING_COLOR\nvec3 SurfacesFragmentModifyTransmitOutScatteringColor()\n{\n    return vec3(1.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_TRANSMIT_DIFFUSE_PARAMS\nvec4 SurfacesFragmentModifyTransmitDiffuseParams()\n{\n    return vec4(1.0, 1.0, 1.0, 1.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_TRT_PARAMS\nvec4 SurfacesFragmentModifyTRTParams()\n{\n    return vec4(0.2, 0.0, 0.0, 1.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_TRT_COLOR\nvec3 SurfacesFragmentModifyTRTColor()\n{\n    return vec3(1.0, 1.0, 1.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_TT_PARAMS\nvec4 SurfacesFragmentModifyTTParams()\n{\n    return vec4(0.0, 0.0, 0.0, 1.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_TT_COLOR\nvec3 SurfacesFragmentModifyTTColor(in vec3 baseColor)\n{\n    return vec3(1.0, 1.0, 1.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_SSS_PARAMS\nvec4 SurfacesFragmentModifySSSParams()\n{\n    return vec4(1.0, 0.1, 1.0, 0.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_DUAL_LOBE_SPECULAR_PARAMS\nvec4 SurfacesFragmentModifyDualLobeSpecularParams(float roughness)\n{\n    return vec4(0.2, 0.0, 0.0, 1.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_CLEAR_COAT_PARAMS\nvec4 SurfacesFragmentModifyClearCoatParams()\n{\n    return vec4(0.2, 1.5, 1.0, 1.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_CLEAR_COAT_COLOR\nvec3 SurfacesFragmentModifyClearCoatColor()\n{\n    return vec3(1.0, 1.0, 1.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_CLEAR_COAT_WORLD_NORMAL\nvec3 SurfacesFragmentModifyClearCoatWorldNormal()\n{\n    return normalize(FSInput_worldNormal);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_SHEEN_PARAMS\nvec4 SurfacesFragmentModifySheenParams()\n{\n    return vec4(0.7, 1.0, 1.0, 1.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_SHEEN_COLOR\nvec3 SurfacesFragmentModifySheenColor()\n{\n    return vec3(1.0, 1.0, 1.0);\n}\n#endif\n#ifndef CC_SURFACES_FRAGMENT_MODIFY_SHARED_DATA\nvoid SurfacesFragmentModifySharedData(inout SurfacesMaterialData surfaceData)\n{\n}\n#endif\nvoid CCSurfacesFragmentGetMaterialData(inout SurfacesMaterialData surfaceData)\n{\n  #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n  packHighpData(surfaceData.worldPos, surfaceData.worldPos_fract_part, FSInput_worldPos);\n  #else\n  surfaceData.worldPos = FSInput_worldPos;\n  #endif\n  surfaceData.baseColor = SurfacesFragmentModifyBaseColorAndTransparency();\n  surfaceData.worldNormal = SurfacesFragmentModifyWorldNormal();\n  SurfacesFragmentModifyWorldTangentAndBinormal(surfaceData.worldTangent, surfaceData.worldBinormal, surfaceData.worldNormal);\n  surfaceData.ior = SurfacesFragmentModifyIOR();\n#if CC_SURFACES_LIGHTING_ANISOTROPIC\n  float isRotation;\n  vec4 anisotropyParams = SurfacesFragmentModifyAnisotropyParams(isRotation);\n  surfaceData.anisotropyShape = anisotropyParams.x;\n  if (isRotation > 0.0) {\n    RotateTangentAndBinormal(surfaceData.worldTangent, surfaceData.worldBinormal, surfaceData.worldNormal, anisotropyParams.y);\n  } else {\n    vec3 anisoDirTS = anisotropyParams.yzw;\n    vec3 tangentWS = anisoDirTS.x * surfaceData.worldTangent + anisoDirTS.y * surfaceData.worldBinormal + anisoDirTS.z * surfaceData.worldNormal;\n    surfaceData.worldTangent = normalize(tangentWS);\n    surfaceData.worldBinormal = cross(surfaceData.worldNormal, tangentWS);\n  }\n#endif\n  surfaceData.emissive = SurfacesFragmentModifyEmissive();\n  vec4 pbr = SurfacesFragmentModifyPBRParams();\n  surfaceData.ao = pbr.x;\n  surfaceData.roughness = pbr.y;\n  surfaceData.metallic = pbr.z;\n  surfaceData.specularIntensity = pbr.w;\n#if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR || CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  surfaceData.transmitScatteringParams = SurfacesFragmentModifyTransmitScatteringParams();\n  surfaceData.inScatteringColor = SurfacesFragmentModifyTransmitInScatteringColor();\n  surfaceData.outScatteringColor = SurfacesFragmentModifyTransmitOutScatteringColor();\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  surfaceData.transmitDiffuseParams = SurfacesFragmentModifyTransmitDiffuseParams();\n#endif\n#if CC_SURFACES_LIGHTING_TRT\n  vec4 trtParams = SurfacesFragmentModifyTRTParams();\n  surfaceData.roughness2ndSpecular = saturate(surfaceData.roughness + trtParams.x);\n  surfaceData.metallic2ndSpecular = surfaceData.metallic;\n  surfaceData.intensity2ndSpecular = trtParams.w;\n  surfaceData.baseColor2ndSpecular = vec3(1.0);\n  surfaceData.color2ndSpecular = SurfacesFragmentModifyTRTColor();\n  surfaceData.worldNormal2ndSpecular = surfaceData.worldNormal;\n  surfaceData.worldTangent2ndSpecular = surfaceData.worldTangent;\n  surfaceData.worldBinormal2ndSpecular = surfaceData.worldBinormal;\n  surfaceData.ior2ndSpecular = surfaceData.ior;\n  surfaceData.opacity2ndSpecular = 1.0;\n  #if CC_SURFACES_LIGHTING_ANISOTROPIC\n    surfaceData.anisotropyShape2ndSpecular = surfaceData.anisotropyShape;\n  #endif\n  RotateNormalAndBinormal(surfaceData.worldBinormal2ndSpecular, surfaceData.worldNormal2ndSpecular, surfaceData.worldTangent2ndSpecular, trtParams.y, FSInput_mirrorNormal);\n#endif\n#if CC_SURFACES_LIGHTING_TT\n  vec4 ttParams = SurfacesFragmentModifyTTParams();\n  surfaceData.ttScatterCoef = ttParams.x;\n  surfaceData.ttIntensity = ttParams.w;\n  surfaceData.baseColorTT = SurfacesFragmentModifyTTColor(surfaceData.baseColor.rgb);\n#endif\n#if CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR\n  vec4 dualLobeParams = SurfacesFragmentModifyDualLobeSpecularParams(surfaceData.roughness);\n  surfaceData.roughness2ndSpecular = saturate(dualLobeParams.x);\n  surfaceData.metallic2ndSpecular = surfaceData.metallic;\n  surfaceData.intensity2ndSpecular = dualLobeParams.w;\n  surfaceData.baseColor2ndSpecular = surfaceData.baseColor.rgb;\n  surfaceData.color2ndSpecular = vec3(1.0);\n  surfaceData.worldNormal2ndSpecular = surfaceData.worldNormal;\n  surfaceData.worldTangent2ndSpecular = surfaceData.worldTangent;\n  surfaceData.worldBinormal2ndSpecular = surfaceData.worldBinormal;\n  surfaceData.ior2ndSpecular = surfaceData.ior;\n  surfaceData.opacity2ndSpecular = 1.0;\n  #if CC_SURFACES_LIGHTING_ANISOTROPIC\n    surfaceData.anisotropyShape2ndSpecular = surfaceData.anisotropyShape;\n  #endif\n#endif\n#if CC_SURFACES_LIGHTING_SHEEN\n  vec4 sheenParams = SurfacesFragmentModifySheenParams();\n  surfaceData.roughness2ndSpecular = saturate(sheenParams.x);\n  surfaceData.intensity2ndSpecular = sheenParams.y * sheenParams.w;\n  surfaceData.metallic2ndSpecular = 1.0;\n  surfaceData.baseColor2ndSpecular = SurfacesFragmentModifySheenColor();\n  surfaceData.color2ndSpecular = vec3(1.0);\n  surfaceData.worldNormal2ndSpecular = surfaceData.worldNormal;\n  surfaceData.worldTangent2ndSpecular = surfaceData.worldTangent;\n  surfaceData.worldBinormal2ndSpecular = surfaceData.worldBinormal;\n  surfaceData.ior2ndSpecular = surfaceData.ior;\n  surfaceData.opacity2ndSpecular = 1.0;\n  #if CC_SURFACES_LIGHTING_ANISOTROPIC\n    surfaceData.anisotropyShape2ndSpecular = surfaceData.anisotropyShape;\n  #endif\n#endif\n#if CC_SURFACES_LIGHTING_CLEAR_COAT\n  vec4 clearCoatParams = SurfacesFragmentModifyClearCoatParams();\n  surfaceData.roughness2ndSpecular = clearCoatParams.x;\n  surfaceData.metallic2ndSpecular = 0.0;\n  surfaceData.ior2ndSpecular = clearCoatParams.y;\n  surfaceData.opacity2ndSpecular = clearCoatParams.z;\n  surfaceData.intensity2ndSpecular = clearCoatParams.w;\n  surfaceData.baseColor2ndSpecular = vec3(1.0);\n  surfaceData.color2ndSpecular = SurfacesFragmentModifyClearCoatColor();\n  surfaceData.worldNormal2ndSpecular = SurfacesFragmentModifyClearCoatWorldNormal();\n  surfaceData.worldBinormal2ndSpecular = surfaceData.worldBinormal;\n  surfaceData.worldTangent2ndSpecular = CalculateTangent(surfaceData.worldNormal2ndSpecular, surfaceData.worldBinormal2ndSpecular);\n  #if CC_SURFACES_LIGHTING_ANISOTROPIC\n    surfaceData.anisotropyShape2ndSpecular = surfaceData.anisotropyShape;\n  #endif\n#endif\n#if CC_SURFACES_LIGHTING_SSS\n  surfaceData.sssParams = SurfacesFragmentModifySSSParams();\n#endif\n  SurfacesFragmentModifySharedData(surfaceData);\n#if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_NORMAL_MAP) {\n      surfaceData.worldNormal = normalize(FSInput_worldNormal);\n      surfaceData.worldTangent = normalize(FSInput_worldTangent);\n  }\n#endif\n#if CC_USE_DEBUG_VIEW\n  if (!IS_DEBUG_VIEW_LIGHTING_ENABLE_WITH_ALBEDO)\n  {\n      surfaceData.baseColor.rgb = vec3(1.0);\n      #if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n        surfaceData.baseColor2ndSpecular.rgb = vec3(1.0);\n      #endif\n      #if CC_SURFACES_LIGHTING_TT\n        surfaceData.baseColorTT.rgb = vec3(1.0);\n      #endif\n  }\n#endif\n}\nvec3 CCSurfacesGetDiffuseColor(in SurfacesMaterialData surfaceData)\n{\n  return surfaceData.baseColor.rgb * (1.0 - surfaceData.metallic);\n}\nvec3 CCSurfacesGetSpecularColor(in SurfacesMaterialData surfaceData)\n{\n  float F0 = surfaceData.specularIntensity * 0.08;\n  return mix(vec3(F0), surfaceData.baseColor.rgb, surfaceData.metallic);\n}\nvoid CCSurfacesLightingInitializeColorWithLighting(inout vec3 diffuseColorWithLighting, inout vec3 specularColorWithLighting, in SurfacesMaterialData surfaceData, in LightingIntermediateData lightingData)\n{\n  diffuseColorWithLighting = CCSurfacesGetDiffuseColor(surfaceData);\n  specularColorWithLighting = CCSurfacesGetSpecularColor(surfaceData).xyz;\n}\nvoid CCSurfacesLightingCalculateColorWithLighting(inout vec3 diffuseColorWithLighting, inout vec3 specularColorWithLighting, in SurfacesMaterialData surfaceData, in LightingIntermediateData lightingData)\n{\n}\nvoid CCSurfacesInitializeLightingIntermediateData(inout LightingIntermediateData lightingData, in SurfacesMaterialData surfaceData)\n{\n  vec3 worldPos;\n  #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n  worldPos = unpackHighpData(surfaceData.worldPos, surfaceData.worldPos_fract_part);\n  #else\n  worldPos = surfaceData.worldPos;\n  #endif\n  CCSurfacesLightingGetIntermediateData_PerPixel(lightingData, surfaceData.worldNormal, worldPos, surfaceData.worldTangent, surfaceData.worldBinormal\n#if CC_SURFACES_LIGHTING_ANISOTROPIC\n      , surfaceData.anisotropyShape\n#endif\n  );\n  lightingData.specularParam = surfaceData.roughness;\n  lightingData.ior = surfaceData.ior;\n  lightingData.layerOpacity = surfaceData.baseColor.a;\n#if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR || CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  lightingData.transmitScatteringParams = surfaceData.transmitScatteringParams;\n  lightingData.inScatteringColor = surfaceData.inScatteringColor;\n  lightingData.outScatteringColor = surfaceData.outScatteringColor;\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  lightingData.transmitDiffuseParams = surfaceData.transmitDiffuseParams;\n#endif\n#if CC_SURFACES_LIGHTING_TT\n  lightingData.baseColorTT = surfaceData.baseColorTT;\n  lightingData.ttIntensity = surfaceData.ttIntensity;\n  lightingData.ttScatterCoef = surfaceData.ttScatterCoef;\n#endif\n}\nvoid CCSurfacesLightingCalculateIntermediateData_PerLight(inout LightingIntermediateData lightingData, in SurfacesMaterialData surfaceData, vec3 lightDirWithDist)\n{\n  CCSurfacesLightingGetIntermediateData_PerLight(lightingData, lightDirWithDist);\n}\n#if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\nvoid CCSurfacesGetLightingIntermediateDataTransmitDiffuse(inout LightingIntermediateData lightingDataTD, in LightingIntermediateData lightingData, in SurfacesMaterialData surfaceData)\n{\n  lightingDataTD = lightingData;\n  lightingDataTD.N = lightingData.transmitScatteringParams.z > 0.0 ? -FSInput_worldNormal : -(normalize(FSInput_worldNormal)+lightingData.V);\n  lightingDataTD.N = normalize(lightingDataTD.N);\n}\n#endif\n#if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\nvoid CCSurfacesGetSurfacesMaterialData2ndSpecular(inout SurfacesMaterialData surfaceData2ndSpecular, in SurfacesMaterialData surfaceData)\n{\n  surfaceData2ndSpecular = surfaceData;\n  surfaceData2ndSpecular.baseColor = vec4(surfaceData.baseColor2ndSpecular, surfaceData.opacity2ndSpecular);\n  surfaceData2ndSpecular.roughness = surfaceData.roughness2ndSpecular;\n  surfaceData2ndSpecular.metallic = surfaceData.metallic2ndSpecular;\n  surfaceData2ndSpecular.worldNormal = surfaceData.worldNormal2ndSpecular;\n  surfaceData2ndSpecular.worldTangent = surfaceData.worldTangent2ndSpecular;\n  surfaceData2ndSpecular.worldBinormal = surfaceData.worldBinormal2ndSpecular;\n  surfaceData2ndSpecular.ior = surfaceData.ior2ndSpecular;\n  #if CC_SURFACES_LIGHTING_ANISOTROPIC\n    surfaceData2ndSpecular.anisotropyShape = surfaceData.anisotropyShape2ndSpecular;\n  #endif\n}\n#endif\nvoid CCSurfacesInitializeLightingResult(inout LightingResult lightingResult, in SurfacesMaterialData surfaceData)\n{\n  lightingResult.ao = surfaceData.ao;\n  lightingResult.emissive = surfaceData.emissive;\n  lightingResult.directGF = vec3(1.0);\n#if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n  lightingResult.directDiffuseSubLayers = lightingResult.directSpecularSubLayers = vec3(0.0);\n  lightingResult.environmentDiffuseSubLayers = lightingResult.environmentSpecularSubLayers = vec3(0.0);\n#endif\n}\nvoid CCSurfacesInitializeLightingResult(inout LightingResult lightingResult)\n{\n  lightingResult.directDiffuse = lightingResult.directSpecular = vec3(0.0);\n  lightingResult.directGF = vec3(1.0);\n#if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n  lightingResult.directDiffuseSubLayers = lightingResult.directSpecularSubLayers = vec3(0.0);\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n  lightingResult.directTransmitSpecular = vec3(0.0);\n#endif\n#if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  lightingResult.directTransmitDiffuse = vec3(0.0);\n#endif\n#if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n  lightingResult.direct2ndSpecular = vec3(0.0);\n#endif\n#if CC_SURFACES_LIGHTING_TT\n  lightingResult.directTT = vec3(0.0);\n#endif\n}\nvoid CCSurfacesAccumulateLightingResult(inout LightingResult lightingResultAccumulated, in LightingResult lightingResult)\n{\n  lightingResultAccumulated.directDiffuse += lightingResult.directDiffuse * lightingResult.shadow;\n  lightingResultAccumulated.directSpecular += lightingResult.directSpecular * lightingResult.shadow * lightingResult.directGF * lightingResult.fresnel;\n  #if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n    lightingResultAccumulated.directDiffuseSubLayers += lightingResult.directDiffuseSubLayers * lightingResult.shadow;\n    lightingResultAccumulated.directSpecularSubLayers += lightingResult.directSpecularSubLayers * lightingResult.shadow;\n  #endif\n  #if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n    lightingResultAccumulated.directTransmitSpecular += lightingResult.directTransmitSpecular * (vec3(1.0) - lightingResult.fresnel);\n  #endif\n  #if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n    lightingResultAccumulated.directTransmitDiffuse += lightingResult.directTransmitDiffuse;\n  #endif\n  #if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n    lightingResultAccumulated.direct2ndSpecular += lightingResult.direct2ndSpecular * lightingResult.shadow * lightingResult.directGF2ndSpecular * lightingResult.fresnel;\n  #endif\n  #if CC_SURFACES_LIGHTING_TT\n    lightingResultAccumulated.directTT += lightingResult.directTT * lightingResult.shadow;\n    lightingResultAccumulated.diffuseColorWithLightingTT = lightingResult.diffuseColorWithLightingTT;\n  #endif\n}\n#if CC_PIPELINE_TYPE == 1\n  vec4 CCSurfacesDeferredOutputBaseColor(in SurfacesMaterialData surfaceData)\n  {\n    return surfaceData.baseColor;\n  }\n  vec4 CCSurfacesDeferredOutputNormalMR(in SurfacesMaterialData surfaceData)\n  {\n    return vec4(float32x3_to_oct(surfaceData.worldNormal), surfaceData.roughness, surfaceData.metallic);\n  }\n  vec4 CCSurfacesDeferredOutputEmissiveAO(in SurfacesMaterialData surfaceData)\n  {\n    return vec4(surfaceData.emissive, surfaceData.ao);\n  }\n#endif\nvec4 CCSurfacesShading(in SurfacesMaterialData surfaceData, in LightingResult lightingResult)\n{\n  vec4 color = vec4(0.0, 0.0, 0.0, surfaceData.baseColor.a);\n#if CC_FORWARD_ADD\n  color.xyz += lightingResult.directDiffuse * lightingResult.diffuseColorWithLighting\n    + lightingResult.directSpecular * lightingResult.specularColorWithLighting * lightingResult.directGF\n  #if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n    + lightingResult.directTransmitSpecular * lightingResult.specularColorWithLighting * lightingResult.directGF\n  #endif\n  #if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n    + lightingResult.directDiffuseSubLayers\n    + lightingResult.directSpecularSubLayers\n  #else\n    #if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n    + lightingResult.direct2ndSpecular * lightingResult.specularColorWithLighting2ndSpecular * surfaceData.color2ndSpecular * lightingResult.directGF2ndSpecular\n    #endif\n  #endif\n  #if CC_SURFACES_LIGHTING_TT\n    + lightingResult.directTT * lightingResult.diffuseColorWithLightingTT\n  #endif\n  #if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n    + lightingResult.directTransmitDiffuse\n  #endif\n  ;\n#else\n  vec3 fresnel = lightingResult.fresnel;\n  vec3 invFresnel = vec3(1.0) - fresnel;\n  color.xyz +=\n    ( lightingResult.directDiffuse * lightingResult.diffuseColorWithLighting\n    + lightingResult.directSpecular * lightingResult.specularColorWithLighting * lightingResult.directGF * fresnel\n  #if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n    + lightingResult.directTransmitSpecular * lightingResult.specularColorWithLighting * lightingResult.directGF * invFresnel\n  #endif\n  #if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n    + lightingResult.directDiffuseSubLayers\n    + lightingResult.directSpecularSubLayers\n  #else\n    #if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n    + lightingResult.direct2ndSpecular * lightingResult.specularColorWithLighting2ndSpecular * surfaceData.color2ndSpecular * lightingResult.directGF2ndSpecular\n    #endif\n  #endif\n  #if CC_SURFACES_LIGHTING_TT\n    + lightingResult.directTT * lightingResult.diffuseColorWithLightingTT\n  #endif\n    )\n    * lightingResult.shadow\n  #if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n    + lightingResult.directTransmitDiffuse\n  #endif\n  ;\n  #if CC_SURFACES_USE_LIGHT_MAP == LIGHT_MAP_TYPE_ALL_IN_ONE\n    color.xyz += lightingResult.lightmapColor * lightingResult.diffuseColorWithLighting * lightingResult.shadow;\n  #elif CC_SURFACES_USE_LIGHT_MAP == LIGHT_MAP_TYPE_INDIRECT_OCCLUSION\n    color.xyz += lightingResult.lightmapColor * lightingResult.diffuseColorWithLighting;\n  #endif\n  color.xyz +=\n    ( lightingResult.environmentDiffuse * lightingResult.diffuseColorWithLighting\n    + lightingResult.environmentSpecular * lightingResult.specularColorWithLighting * lightingResult.environmentGF * fresnel\n  #if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n    + lightingResult.environmentTransmitSpecular * lightingResult.specularColorWithLighting * lightingResult.environmentGF * invFresnel\n  #endif\n  #if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n    + lightingResult.environmentDiffuseSubLayers\n    + lightingResult.environmentSpecularSubLayers\n  #else\n    #if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n    + lightingResult.environment2ndSpecular * lightingResult.specularColorWithLighting2ndSpecular * surfaceData.color2ndSpecular * lightingResult.environmentGF2ndSpecular\n    #endif\n  #endif\n    )\n    * lightingResult.ao\n  #if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n    + lightingResult.environmentTransmitDiffuse\n  #endif\n  ;\n  color.xyz += lightingResult.emissive;\n#endif\n  return color;\n}\n#if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_SINGLE\nbool CCSurfacesDebugViewSurfaceData(inout vec4 color, in SurfacesMaterialData surfaceData)\n{\n    bool enableMaterialAlpha = true;\n    vec4 black = vec4(0.0, 0.0, 0.0, 1.0);\n    float scalar;\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL)) && (cc_surfaceTransform.y != 3.0)))\n        color = vec4(surfaceData.worldNormal * 0.5 + vec3(0.5), 1.0);\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT)) && (cc_surfaceTransform.y != 3.0)))\n    {\n      #if CC_SURFACES_USE_TANGENT_SPACE\n        color = vec4(surfaceData.worldTangent * 0.5 + vec3(0.5), 1.0);\n      #else\n        color = black;\n      #endif\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL)) && (cc_surfaceTransform.y != 3.0)))\n    {\n      #if CC_SURFACES_USE_TANGENT_SPACE\n        color = vec4(surfaceData.worldBinormal * 0.5 + vec3(0.5), 1.0);\n      #else\n        color = black;\n      #endif\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_TRANSPARENCY)) && (cc_surfaceTransform.y != 3.0))) {\n        scalar = surfaceData.baseColor.a;\n        color = vec4(scalar, scalar, scalar, 1.0);\n        enableMaterialAlpha = false;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_BASE_COLOR)) && (cc_surfaceTransform.y != 3.0)))\n        color = vec4(LinearToSRGB(surfaceData.baseColor.rgb), 1.0);\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR)) && (cc_surfaceTransform.y != 3.0)))\n        color = vec4(LinearToSRGB(CCSurfacesGetDiffuseColor(surfaceData)), 1.0);\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR)) && (cc_surfaceTransform.y != 3.0)))\n        color = vec4(LinearToSRGB(CCSurfacesGetSpecularColor(surfaceData)), 1.0);\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_ROUGHNESS)) && (cc_surfaceTransform.y != 3.0))) {\n        scalar = surfaceData.roughness;\n        color = vec4(scalar, scalar, scalar, 1.0);\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_METALLIC)) && (cc_surfaceTransform.y != 3.0))) {\n        scalar = surfaceData.metallic;\n        color = vec4(scalar, scalar, scalar, 1.0);\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY)) && (cc_surfaceTransform.y != 3.0))) {\n        scalar = surfaceData.specularIntensity;\n        color = vec4(scalar, scalar, scalar, 1.0);\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_IOR)) && (cc_surfaceTransform.y != 3.0))) {\n        scalar = surfaceData.ior - 1.0;\n        color = vec4(scalar, scalar, scalar, 1.0);\n    }\n    return enableMaterialAlpha;\n}\n#endif\n#if (CC_PIPELINE_TYPE == 0 || CC_FORCE_FORWARD_SHADING)\n  #if CC_FORWARD_ADD\n    void CCSurfacesLighting(inout LightingResult lightingResultAccumulated, in SurfacesMaterialData surfaceData, in vec2 shadowBias)\n    {\n      vec3 worldPos;\n      #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n      worldPos = unpackHighpData(surfaceData.worldPos, surfaceData.worldPos_fract_part);\n      #else\n      worldPos = surfaceData.worldPos;\n      #endif\n      CCSurfacesInitializeLightingResult(lightingResultAccumulated);\n      LightingIntermediateData lightingData;\n      CCSurfacesInitializeLightingIntermediateData(lightingData, surfaceData);\n      LightingResult lightingResult;\n      CCSurfacesLightingInitializeColorWithLighting(lightingResult.diffuseColorWithLighting, lightingResult.specularColorWithLighting, surfaceData, lightingData);\n      lightingResultAccumulated.diffuseColorWithLighting = lightingResult.diffuseColorWithLighting;\n      lightingResultAccumulated.specularColorWithLighting = lightingResult.specularColorWithLighting;\n      #if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n        vec3 diff;\n        SurfacesMaterialData surfaceData2ndSpecular;\n        CCSurfacesGetSurfacesMaterialData2ndSpecular(surfaceData2ndSpecular, surfaceData);\n        CCSurfacesLightingInitializeColorWithLighting(diff, lightingResult.specularColorWithLighting2ndSpecular, surfaceData2ndSpecular, lightingData);\n        lightingResultAccumulated.specularColorWithLighting2ndSpecular = lightingResult.specularColorWithLighting2ndSpecular;\n      #endif\n      int numLights = CC_PIPELINE_TYPE == 0 ? LIGHTS_PER_PASS : int(cc_lightDir[0].w);\n      for (int i = 0; i < LIGHTS_PER_PASS; i++) {\n        if (i >= numLights) break;\n        vec3 lightDirWithLength = IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w) ? -normalize(cc_lightDir[i].xyz) : cc_lightPos[i].xyz - worldPos;\n        CCSurfacesLightingCalculateIntermediateData_PerLight(lightingData, surfaceData, lightDirWithLength);\n        CCSurfacesLightingCalculateColorWithLighting(lightingResult.diffuseColorWithLighting, lightingResult.specularColorWithLighting, surfaceData, lightingData);\n        vec3 diffuseLighting, specularLighting;\n        CCSurfacesLightingCalculateDirect(diffuseLighting, specularLighting, lightingData, cc_lightColor[i]);\n      #if CC_SURFACES_LIGHTING_USE_FRESNEL\n        vec3 fresnel = CCSurfaceLightingCalculateExtraFresnel(lightingData);\n      #else\n        vec3 fresnel = vec3(1.0);\n      #endif\n        lightingResult.fresnel = fresnel;\n        float shadow = 1.0;\n      #if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == 2\n        if (cc_lightPos[i].w > 0.0 && cc_lightSizeRangeAngle[i].w > 0.0) {\n          vec4 shadowPos = vec4(0.0), shadowPosWithDepthBias = vec4(0.0);\n          if (CCSurfacesLightingEnableShadow(lightingData.NoL)) {\n            shadowPos = cc_matLightViewProj * vec4(worldPos, 1.0);\n            shadow = CCSpotShadowFactorBase(shadowPosWithDepthBias, shadowPos, worldPos, shadowBias);\n          }\n          #if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE && CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT\n            vec3 shadowNDCPos;\n            bool isExceedShadowMap = !GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias);\n            lightingData.shadowPosAndDepth.xy = shadowNDCPos.xy;\n            lightingData.shadowPosAndDepth.z = isExceedShadowMap ? 0.0 : GetViewSpaceDepthFromNDCDepth_Perspective(shadowNDCPos.z, shadowPosWithDepthBias.w, cc_shadowInvProjDepthInfo.x, cc_shadowInvProjDepthInfo.y);\n            lightingData.shadowPosAndDepth.w = isExceedShadowMap ? lightingData.shadowPosAndDepth.w : GetViewSpaceDepthFromNDCDepth_Perspective(SampleShadowMapSoft(shadowNDCPos, cc_spotShadowMap, cc_shadowWHPBInfo.xy), shadowPosWithDepthBias.w, cc_shadowInvProjDepthInfo.x, cc_shadowInvProjDepthInfo.y);\n          #endif\n        }\n      #endif\n      #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n        if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_SHADOW)\n          shadow = 1.0;\n      #endif\n        lightingResult.shadow = shadow;\n        float distAtt = 1.0;\n        if(IS_RANGED_DIRECTIONAL_LIGHT(cc_lightPos[i].w)) {\n          distAtt = GetOutOfRange(worldPos, cc_lightPos[i].xyz, cc_lightDir[i].xyz, cc_lightSizeRangeAngle[i].xyz, cc_lightBoundingSizeVS[i].xyz);\n        } else {\n          distAtt = CCSurfacesLightingCalculateDistanceAttenuation(lightingData, cc_lightSizeRangeAngle[i], cc_lightPos[i].w);\n        }\n        float angleAtt = 1.0;\n        if (IS_SPOT_LIGHT(cc_lightPos[i].w)) {\n          angleAtt = CCSurfacesLightingCalculateAngleAttenuation(lightingData, cc_lightSizeRangeAngle[i], -cc_lightDir[i].xyz);\n        }\n        float multiplier = distAtt * angleAtt;\n        lightingData.angleAttenuation = angleAtt;\n        lightingData.distAttenuation = distAtt;\n        lightingResult.directDiffuse = diffuseLighting * multiplier;\n        lightingResult.directSpecular = specularLighting * multiplier;\n        CCSurfaceLightingCalculateDirectFresnel(lightingResult.directGF, lightingData, lightingResult.specularColorWithLighting, vec3(1.0));\n        vec4 attenuatedLightColorAndIntensity = vec4(cc_lightColor[i].xyz, cc_lightColor[i].w * multiplier);\n        #if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n          vec3 transmitSpecularLighting;\n          CCSurfacesLightingCalculateDirectTransmitSpecular(transmitSpecularLighting, lightingData, attenuatedLightColorAndIntensity);\n          lightingResult.directTransmitSpecular = transmitSpecularLighting * multiplier * (1.0 - fresnel);\n        #endif\n        #if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n          LightingIntermediateData lightingDataTD;\n          CCSurfacesGetLightingIntermediateDataTransmitDiffuse(lightingDataTD, lightingData, surfaceData);\n          CCSurfacesLightingCalculateIntermediateData_PerLight(lightingDataTD, surfaceData, lightDirWithLength);\n          CCSurfacesLightingCalculateDirectTransmitDiffuse(lightingResult.directTransmitDiffuse, lightingDataTD, attenuatedLightColorAndIntensity, lightingResult.shadow);\n        #endif\n        #if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n          LightingIntermediateData lightingData2ndSpecular;\n          CCSurfacesInitializeLightingIntermediateData(lightingData2ndSpecular, surfaceData2ndSpecular);\n          CCSurfacesLightingCalculateIntermediateData_PerLight(lightingData2ndSpecular, surfaceData, lightDirWithLength);\n          #if !CC_SURFACES_LIGHTING_SHEEN\n            CCSurfacesLightingCalculateDirect2ndSpecular(lightingResult.direct2ndSpecular, lightingData2ndSpecular, attenuatedLightColorAndIntensity, surfaceData2ndSpecular.intensity2ndSpecular, lightingResult.directSpecular);\n            CCSurfaceLightingCalculateDirectFresnel(lightingResult.directGF2ndSpecular, lightingData2ndSpecular, lightingResult.specularColorWithLighting2ndSpecular, vec3(1.0));\n          #else\n            CCSurfacesLightingCalculateDirectSheen(lightingResult.direct2ndSpecular, lightingResult.directGF2ndSpecular, lightingData2ndSpecular, attenuatedLightColorAndIntensity, surfaceData2ndSpecular.intensity2ndSpecular);\n          #endif\n          lightingResult.direct2ndSpecular *= multiplier;\n        #endif\n        #if CC_SURFACES_LIGHTING_TT\n          CCSurfacesLightingCalculateDirectTT(lightingResult, lightingData, attenuatedLightColorAndIntensity);\n        #endif\n        #if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n          lightingResult.specularColorWithLighting2ndSpecular *= surfaceData.color2ndSpecular;\n          CCSurfacesLightingCalculateDirectMultiLayerBlending(lightingResult, lightingData2ndSpecular);\n        #endif\n        #ifdef CC_SURFACES_LIGHTING_MODIFY_FINAL_RESULT\n          LightingMiscData miscData;\n          miscData.lightType = cc_lightPos[i].w;\n          miscData.lightPos = cc_lightPos[i].xyz;\n          miscData.lightDir = cc_lightDir[i].xyz;\n          miscData.lightColorAndIntensity = cc_lightColor[i];\n          miscData.lightSizeRangeAngle = cc_lightSizeRangeAngle[i];\n          SurfacesLightingModifyFinalResult(lightingResult, lightingData, surfaceData, miscData);\n        #endif\n        CCSurfacesAccumulateLightingResult(lightingResultAccumulated, lightingResult);\n      }\n    }\n  #else\n    void CCSurfacesLighting(inout LightingResult lightingResult, in SurfacesMaterialData surfaceData, in vec2 shadowBias)\n    {\n      vec3 worldPos;\n      #if CC_PLATFORM_ANDROID_AND_WEBGL && CC_ENABLE_WEBGL_HIGHP_STRUCT_VALUES\n      worldPos = unpackHighpData(surfaceData.worldPos, surfaceData.worldPos_fract_part);\n      #else\n      worldPos = surfaceData.worldPos;\n      #endif\n      LightingIntermediateData lightingData;\n      CCSurfacesInitializeLightingIntermediateData(lightingData, surfaceData);\n      CCSurfacesInitializeLightingResult(lightingResult, surfaceData);\n      CCSurfacesLightingInitializeColorWithLighting(lightingResult.diffuseColorWithLighting, lightingResult.specularColorWithLighting, surfaceData, lightingData);\n      CCSurfacesLightingCalculateIntermediateData_PerLight(lightingData, surfaceData, -cc_mainLitDir.xyz);\n      lightingResult.shadow = 1.0;\n      #if CC_RECEIVE_SHADOW && CC_SHADOW_TYPE == 2\n        if (cc_mainLitDir.w > 0.0) {\n          vec4 shadowPos = vec4(0.0), shadowPosWithDepthBias = vec4(0.0);\n          vec4 shadowProjDepthInfo = vec4(0.0);\n          vec3 shadowNDCPos;\n          bool isExceedShadowMap = true;\n          if (CCSurfacesLightingEnableShadow(lightingData.NoL)) {\n            #if CC_DIR_LIGHT_SHADOW_TYPE == 2\n              lightingResult.shadow = CCCSMFactorBase(worldPos, lightingData.N, shadowBias);\n              #if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE && CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT\n                vec4 shadowProjInfo;\n                vec3 shadowViewDir0, shadowViewDir1, shadowViewDir2;\n                isExceedShadowMap = 0 > CCGetCSMLevel(shadowPosWithDepthBias, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, worldPos);\n                GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias);\n              #endif\n            #endif\n            #if CC_DIR_LIGHT_SHADOW_TYPE == 1\n              shadowPos = cc_matLightViewProj * vec4(worldPos, 1.0);\n              lightingResult.shadow = CCShadowFactorBase(shadowPosWithDepthBias, shadowPos, lightingData.N, shadowBias);\n              shadowProjDepthInfo = cc_shadowProjDepthInfo;\n              isExceedShadowMap = !GetShadowNDCPos(shadowNDCPos, shadowPosWithDepthBias);\n            #endif\n          }\n          #if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE && CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT\n            lightingData.shadowPosAndDepth.xy = shadowNDCPos.xy;\n            lightingData.shadowPosAndDepth.z = isExceedShadowMap ? 0.0 : GetViewSpaceDepthFromNDCDepth_Orthgraphic(shadowNDCPos.z, shadowProjDepthInfo.x, shadowProjDepthInfo.y);\n            lightingData.shadowPosAndDepth.w = isExceedShadowMap ? lightingData.shadowPosAndDepth.w : GetViewSpaceDepthFromNDCDepth_Orthgraphic(SampleShadowMapSoft(shadowNDCPos, cc_shadowMap, cc_shadowWHPBInfo.xy), shadowProjDepthInfo.x, shadowProjDepthInfo.y);\n          #endif\n        }\n      #endif\n      lightingResult.lightmapColor = vec3(0.0);\n      #if CC_SURFACES_USE_LIGHT_MAP && !CC_FORWARD_ADD\n        float lightmapShadow, lightmapAO;\n        GetLightMapColor(lightingResult.lightmapColor, lightmapShadow, lightmapAO, cc_lightingMap, FSInput_lightMapUV.xy, FSInput_lightMapUV.z, surfaceData.worldNormal);\n        #if CC_SURFACES_USE_LIGHT_MAP == LIGHT_MAP_TYPE_INDIRECT_OCCLUSION\n          lightingResult.shadow *= lightmapShadow;\n        #endif\n        lightingResult.ao *= lightmapAO;\n      #endif\n      #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n        if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_SHADOW)\n          lightingResult.shadow = 1.0;\n        if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_AO)\n          lightingResult.ao = 1.0;\n      #endif\n      vec3 unused;\n      CCSurfacesLightingCalculateEnvironment(lightingResult.environmentDiffuse, lightingResult.environmentSpecular, lightingData, cc_ambientSky.w);\n      CCSurfaceLightingCalculateEnvironmentFresnel(lightingResult.environmentGF, unused, lightingData, lightingResult.specularColorWithLighting);\n      lightingResult.directDiffuse = lightingResult.directSpecular = vec3(0.0);\n      #if !CC_DISABLE_DIRECTIONAL_LIGHT && !CC_FORWARD_ADD\n        CCSurfacesLightingCalculateColorWithLighting(lightingResult.diffuseColorWithLighting, lightingResult.specularColorWithLighting, surfaceData, lightingData);\n        CCSurfacesLightingCalculateDirect(lightingResult.directDiffuse, lightingResult.directSpecular, lightingData, cc_mainLitColor);\n        CCSurfaceLightingCalculateDirectFresnel(lightingResult.directGF, lightingData, lightingResult.specularColorWithLighting, lightingResult.environmentGF);\n      #endif\n      #if CC_SURFACES_LIGHTING_USE_FRESNEL\n        lightingResult.fresnel = CCSurfaceLightingCalculateExtraFresnel(lightingData);\n      #else\n        lightingResult.fresnel = vec3(1.0);\n      #endif\n      #if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n        CCSurfacesLightingCalculateDirectTransmitSpecular(lightingResult.directTransmitSpecular, lightingData, cc_mainLitColor);\n        CCSurfacesLightingCalculateEnvironmentTransmitSpecular(lightingResult.environmentTransmitSpecular, lightingData, cc_ambientSky.w);\n      #endif\n      #if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n        LightingIntermediateData lightingDataTD;\n        CCSurfacesGetLightingIntermediateDataTransmitDiffuse(lightingDataTD, lightingData, surfaceData);\n        CCSurfacesLightingCalculateIntermediateData_PerLight(lightingDataTD, surfaceData, -cc_mainLitDir.xyz);\n        CCSurfacesLightingCalculateDirectTransmitDiffuse(lightingResult.directTransmitDiffuse, lightingDataTD, cc_mainLitColor, lightingResult.shadow);\n        CCSurfacesLightingCalculateEnvironmentTransmitDiffuse(lightingResult.environmentTransmitDiffuse, lightingDataTD, cc_ambientSky.w, lightingResult.ao, -cc_mainLitDir.xyz);\n      #endif\n      #if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n        SurfacesMaterialData surfaceData2ndSpecular;\n        CCSurfacesGetSurfacesMaterialData2ndSpecular(surfaceData2ndSpecular, surfaceData);\n        LightingIntermediateData lightingData2ndSpecular;\n        CCSurfacesInitializeLightingIntermediateData(lightingData2ndSpecular, surfaceData2ndSpecular);\n        CCSurfacesLightingCalculateIntermediateData_PerLight(lightingData2ndSpecular, surfaceData2ndSpecular, -cc_mainLitDir.xyz);\n        vec3 diff;\n        CCSurfacesLightingInitializeColorWithLighting(diff, lightingResult.specularColorWithLighting2ndSpecular, surfaceData2ndSpecular, lightingData2ndSpecular);\n        #if !CC_SURFACES_LIGHTING_SHEEN\n          CCSurfacesLightingCalculateEnvironment2ndSpecular(lightingResult.environment2ndSpecular, lightingData2ndSpecular, cc_ambientSky.w, surfaceData2ndSpecular.intensity2ndSpecular, lightingResult.environmentSpecular);\n          CCSurfaceLightingCalculateEnvironmentFresnel(lightingResult.environmentGF2ndSpecular, lightingResult.environmentSubLayerF, lightingData2ndSpecular, lightingResult.specularColorWithLighting2ndSpecular);\n          CCSurfacesLightingCalculateDirect2ndSpecular(lightingResult.direct2ndSpecular, lightingData2ndSpecular, cc_mainLitColor, surfaceData2ndSpecular.intensity2ndSpecular, lightingResult.directSpecular);\n          CCSurfaceLightingCalculateDirectFresnel(lightingResult.directGF2ndSpecular, lightingData2ndSpecular, lightingResult.specularColorWithLighting2ndSpecular, lightingResult.environmentGF2ndSpecular);\n        #else\n          CCSurfacesLightingCalculateDirectSheen(lightingResult.direct2ndSpecular, lightingResult.directGF2ndSpecular, lightingData2ndSpecular, cc_mainLitColor, surfaceData2ndSpecular.intensity2ndSpecular);\n          CCSurfacesLightingCalculateEnvironmentSheen(lightingResult.environment2ndSpecular, lightingResult.environmentGF2ndSpecular, lightingData2ndSpecular, cc_ambientSky.w, surfaceData2ndSpecular.intensity2ndSpecular);\n        #endif\n      #endif\n      #if CC_SURFACES_LIGHTING_TT\n        CCSurfacesLightingCalculateDirectTT(lightingResult, lightingData, cc_mainLitColor);\n      #endif\n      #if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n        lightingResult.specularColorWithLighting2ndSpecular *= surfaceData.color2ndSpecular;\n        CCSurfacesLightingCalculateDirectMultiLayerBlending(lightingResult, lightingData2ndSpecular);\n        CCSurfacesLightingCalculateEnvironmentMultiLayerBlending(lightingResult, lightingData2ndSpecular);\n      #endif\n      #ifdef CC_SURFACES_LIGHTING_MODIFY_FINAL_RESULT\n        LightingMiscData miscData;\n        miscData.lightType = LIGHT_TYPE_DIRECTIONAL;\n        miscData.lightPos = vec3(0.0);\n        miscData.lightDir = cc_mainLitDir.xyz;\n        miscData.lightColorAndIntensity = cc_mainLitColor;\n        miscData.lightSizeRangeAngle = vec4(0.0, 0.0, 0.0, 0.0);\n        SurfacesLightingModifyFinalResult(lightingResult, lightingData, surfaceData, miscData);\n      #endif\n    }\n    #if CC_ENABLE_CLUSTERED_LIGHT_CULLING\n    #endif\n  #endif\n#endif\nvec4 CCSurfacesDebugDisplayInvalidNumber(vec4 color)\n{\n  float index = mod(cc_time.x * 10.0, 2.0);\n  vec4 error = index < 1.0 ? vec4(1.0, 0.0, 0.2, 1.0) : vec4(0.0, 1.0, 0.2, 1.0);\n  return (isnans(color.rgb) || isinfs(color.rgb)) ? error : color;\n}\nvec4 CCSurfacesDebugDisplayInvalidInputData(vec4 color, vec3 data)\n{\n  float index = mod(cc_time.x * 10.0, 2.0);\n  vec4 error = index < 1.0 ? vec4(1.0, 0.0, 0.2, 1.0) : vec4(0.0, 1.0, 0.2, 1.0);\n  return (isnans(data) || isinfs(data)) ? error : color;\n}\n#if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_SINGLE && !CC_FORWARD_ADD\n  void CCSurfacesDebugViewMeshData(inout vec4 color)\n  {\n    vec4 white = vec4(1.0, 1.0, 1.0, 1.0);\n    vec4 black = vec4(0.0, 0.0, 0.0, 1.0);\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_VERTEX_COLOR)) && (cc_surfaceTransform.y != 3.0)))\n    {\n      #if CC_SURFACES_USE_VERTEX_COLOR\n        color = FSInput_vertexColor;\n      #else\n        color = white;\n      #endif\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_VERTEX_NORMAL)) && (cc_surfaceTransform.y != 3.0)))\n        color = vec4(FSInput_worldNormal * 0.5 + vec3(0.5), 1.0);\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_VERTEX_TANGENT)) && (cc_surfaceTransform.y != 3.0)))\n    {\n      #if CC_SURFACES_USE_TANGENT_SPACE\n        color = vec4(FSInput_worldTangent * 0.5 + vec3(0.5), 1.0);\n      #else\n        color = black;\n      #endif\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_VERTEX_MIRROR)) && (cc_surfaceTransform.y != 3.0)))\n    {\n      #if CC_SURFACES_USE_TANGENT_SPACE\n        float sign = FSInput_mirrorNormal * 0.5 + 0.5;\n        color = vec4(sign, sign, sign, 1.0);\n      #else\n        color = black;\n      #endif\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FACE_SIDE)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        float scalar = clamp(FSInput_faceSideSign, 0.0, 1.0);\n        color = vec4(scalar, scalar, scalar, 1.0);\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_UV0)) && (cc_surfaceTransform.y != 3.0)))\n        color = vec4(FSInput_texcoord.xy, 0.0, 1.0);\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_UV1)) && (cc_surfaceTransform.y != 3.0)))\n      color = vec4(FSInput_texcoord1.xy, 0.0, 1.0);\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_UVLIGHTMAP)) && (cc_surfaceTransform.y != 3.0)))\n    {\n      #if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n        color = vec4(FSInput_lightMapUV.xy, 0.0, 1.0);\n      #else\n        color = vec4(0.0, 0.0, 0.0, 1.0);\n      #endif\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_PROJ_DEPTH)) && (cc_surfaceTransform.y != 3.0)))\n    {\n      vec4 clipPos = cc_matProj * cc_matView * vec4(FSInput_worldPos.xyz, 1.0);\n      float depth = clipPos.z / clipPos.w;\n      color = vec4(depth, depth, depth, 1.0);\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_LINEAR_DEPTH)) && (cc_surfaceTransform.y != 3.0)))\n    {\n      vec4 viewPos = cc_matView * vec4(FSInput_worldPos.xyz, 1.0);\n      float depth = (-viewPos.z - cc_nearFar.x) / cc_nearFar.y;\n      color = vec4(depth, depth, depth, 1.0);\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_WORLD_POS)) && (cc_surfaceTransform.y != 3.0)))\n      color = vec4(FSInput_worldPos.xyz, 1.0);\n  }\n#endif\n#if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_SINGLE\n  bool CCSurfacesDebugViewLightingResult(inout vec4 color, in LightingResult lightingResult)\n  {\n    bool isSRGBColor = false;\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_DIRECT_DIFFUSE)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = lightingResult.directDiffuse * lightingResult.diffuseColorWithLighting;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_DIRECT_SPECULAR)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = lightingResult.directSpecular * lightingResult.specularColorWithLighting * lightingResult.directGF;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_DIRECT_ALL)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = lightingResult.directDiffuse * lightingResult.diffuseColorWithLighting + lightingResult.directSpecular * lightingResult.specularColorWithLighting * lightingResult.directGF;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_ENV_DIFFUSE)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = lightingResult.environmentDiffuse * lightingResult.diffuseColorWithLighting;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_ENV_SPECULAR)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = lightingResult.environmentSpecular * lightingResult.specularColorWithLighting * lightingResult.environmentGF;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_ENV_ALL)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = lightingResult.environmentDiffuse * lightingResult.diffuseColorWithLighting + lightingResult.environmentSpecular * lightingResult.specularColorWithLighting * lightingResult.environmentGF;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_LIGHT_MAP)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = lightingResult.lightmapColor;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_EMISSIVE)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = lightingResult.emissive;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_AO)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = vec3(lightingResult.ao);\n        isSRGBColor = false;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_SHADOW)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = vec3(lightingResult.shadow);\n        isSRGBColor = false;\n    }\n    vec3 fresnel = lightingResult.fresnel;\n    vec3 directTransmitSpecular = vec3(0.0), environmentTransmitSpecular = vec3(0.0);\n    vec3 directTransmitDiffuse = vec3(0.0), environmentTransmitDiffuse = vec3(0.0);\n    vec3 diffuseColorWithLightingTT = vec3(0.0), specularColorWithLighting2ndSpecular = vec3(0.0);\n    vec3 direct2ndSpecular = vec3(0.0), environment2ndSpecular = vec3(0.0);\n  #if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n    directTransmitSpecular = lightingResult.directTransmitSpecular;\n    environmentTransmitSpecular = lightingResult.environmentTransmitSpecular;\n  #endif\n  #if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n    directTransmitDiffuse = lightingResult.directTransmitDiffuse;\n    environmentTransmitDiffuse = lightingResult.environmentTransmitDiffuse;\n  #endif\n  #if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n    direct2ndSpecular = lightingResult.direct2ndSpecular * lightingResult.directGF2ndSpecular;\n    environment2ndSpecular = lightingResult.environment2ndSpecular * lightingResult.environmentGF2ndSpecular;\n    specularColorWithLighting2ndSpecular = lightingResult.specularColorWithLighting2ndSpecular;\n    #if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n      fresnel = lightingResult.environmentSubLayerF;\n    #endif\n  #endif\n  #if CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n    direct2ndSpecular = lightingResult.directSpecularSubLayers;\n    environment2ndSpecular = lightingResult.environmentSpecularSubLayers;\n  #endif\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FRESNEL)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = vec3(fresnel);\n        isSRGBColor = false;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_SPECULAR)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = directTransmitSpecular;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_SPECULAR)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = environmentTransmitSpecular;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_DIFFUSE)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = directTransmitDiffuse;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_DIFFUSE)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = environmentTransmitDiffuse;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_TRANSMIT_ALL)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = directTransmitSpecular + environmentTransmitSpecular + directTransmitDiffuse + environmentTransmitDiffuse;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_DIRECT_2ND_SPECULAR)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = direct2ndSpecular * specularColorWithLighting2ndSpecular;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_ENVIRONMENT_2ND_SPECULAR)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = environment2ndSpecular * specularColorWithLighting2ndSpecular;\n        isSRGBColor = true;\n    }\n    if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_2ND_SPECULAR_ALL)) && (cc_surfaceTransform.y != 3.0)))\n    {\n        color.rgb = (direct2ndSpecular + environment2ndSpecular) * specularColorWithLighting2ndSpecular;\n        isSRGBColor = true;\n    }\n    return isSRGBColor;\n  }\n#endif\n#if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  void CCSurfacesDebugViewCompositeLightingResult(inout LightingResult lightingResult)\n  {\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_DIRECT_DIFFUSE)\n      lightingResult.directDiffuse = vec3(0.0);\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_DIRECT_SPECULAR)\n      lightingResult.directSpecular = vec3(0.0);\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_ENV_DIFFUSE)\n      lightingResult.environmentDiffuse = vec3(0.0);\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_ENV_SPECULAR)\n      lightingResult.environmentSpecular = vec3(0.0);\n  #if CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_TRANSMIT_DIFFUSE)\n      lightingResult.directTransmitDiffuse = lightingResult.environmentTransmitDiffuse = vec3(0.0);\n  #endif\n  #if CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_TRANSMIT_SPECULAR)\n      lightingResult.directTransmitSpecular = lightingResult.environmentTransmitSpecular = vec3(0.0);\n  #endif\n  #if CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_2ND_SPECULAR)\n        lightingResult.direct2ndSpecular = lightingResult.environment2ndSpecular = vec3(0.0);\n  #endif\n  #if CC_SURFACES_LIGHTING_TT\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_TT)\n        lightingResult.directTT = vec3(0.0);\n  #endif\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_EMISSIVE)\n      lightingResult.emissive = vec3(0.0);\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_LIGHT_MAP)\n      lightingResult.lightmapColor = vec3(0.0);\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_SHADOW)\n      lightingResult.shadow = 1.0;\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_AO)\n      lightingResult.ao = 1.0;\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_FRESNEL)\n      lightingResult.fresnel = vec3(1.0);\n  }\n#endif\n#if (CC_PIPELINE_TYPE == 0 || CC_FORCE_FORWARD_SHADING)\n  layout(location = 0) out vec4 fragColorX;\n  void main()  {\n  #if CC_DISABLE_STRUCTURE_IN_FRAGMENT_SHADER\n    float NoL = dot(-cc_mainLitDir.xyz, FSInput_worldNormal.xyz);\n    vec4 color = SurfacesFragmentModifyBaseColorAndTransparency();\n    float fogFactor = 1.0;\n    #if CC_FORWARD_ADD\n      color.rgb = vec3(0.0);\n    #endif\n  #else\n    SurfacesMaterialData surfaceData;\n    CCSurfacesFragmentGetMaterialData(surfaceData);\n    vec2 shadowBias = vec2(0.0);\n    vec3 colDebugCSMLayer = vec3(1.0);\n    #if CC_RECEIVE_SHADOW\n      shadowBias = FSInput_shadowBias;\n      #if !CC_FORWARD_ADD\n        #if CC_USE_DEBUG_VIEW && CC_SURFACES_ENABLE_DEBUG_VIEW\n          if (IS_DEBUG_VIEW_MISC_ENABLE_CSM_LAYER_COLORATION)\n          {\n              vec4 csmPos;\n              vec4 shadowProjDepthInfo, shadowProjInfo;\n              vec3 shadowViewDir0, shadowViewDir1, shadowViewDir2;\n              int csmLayer = -1;\n              csmLayer = CCGetCSMLevel(csmPos, shadowProjDepthInfo, shadowProjInfo, shadowViewDir0, shadowViewDir1, shadowViewDir2, surfaceData.worldPos);\n              bool OutOfRange = csmLayer < 0;\n              if (OutOfRange)\n                  colDebugCSMLayer = vec3(1.0);\n              else if (csmLayer == 0)\n                  colDebugCSMLayer = vec3(1.0, 0.0, 0.0);\n              else if (csmLayer == 1)\n                  colDebugCSMLayer = vec3(0.0, 1.0, 0.0);\n              else if (csmLayer == 2)\n                  colDebugCSMLayer = vec3(0.0, 0.0, 1.0);\n              else if (csmLayer == 3)\n                  colDebugCSMLayer = vec3(0.0, 1.0, 1.0);\n          }\n        #endif\n      #endif\n    #endif\n    float fogFactor = 1.0;\n    #if !CC_FORWARD_ADD\n      #if CC_USE_FOG != 4\n        #if !CC_USE_ACCURATE_FOG\n          fogFactor = FSInput_fogFactor;\n        #else\n          CC_TRANSFER_FOG_BASE(vec4(FSInput_worldPos, 1.0), fogFactor);\n        #endif\n      #endif\n      #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n        if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_FOG) {\n            fogFactor = 1.0;\n        }\n      #endif\n    #endif\n    LightingResult lightingResult;\n    CCSurfacesLighting(lightingResult, surfaceData, shadowBias);\n    #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_SINGLE && CC_SURFACES_ENABLE_DEBUG_VIEW\n      vec4 debugColor = vec4(0.0, 0.0, 0.0, 1.0);\n      float materialTransparency = CCSurfacesShading(surfaceData, lightingResult).a;\n      #if !CC_FORWARD_ADD && !CC_SURFACES_LIGHTING_DISABLE_DIFFUSE\n        CCSurfacesDebugViewMeshData(debugColor);\n        if (CCSurfacesDebugViewSurfaceData(debugColor, surfaceData))\n        {\n          debugColor.a = materialTransparency;\n        }\n        if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_FOG)) && (cc_surfaceTransform.y != 3.0)))\n        {\n          debugColor.rgb = vec3(1.0 - fogFactor);\n        }\n      #endif\n      #if CC_FORWARD_ADD\n        if ((equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_DIRECT_DIFFUSE)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_DIRECT_SPECULAR)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_DIRECT_ALL)) && (cc_surfaceTransform.y != 3.0)) ||\n            (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_DIFFUSE)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_SPECULAR)) && (cc_surfaceTransform.y != 3.0)) || (equalf_mode(cc_debug_view_mode.x, float(CC_SURFACES_DEBUG_VIEW_DIRECT_2ND_SPECULAR)) && (cc_surfaceTransform.y != 3.0)))\n      #endif\n        {\n            if (CCSurfacesDebugViewLightingResult(debugColor, lightingResult))\n            {\n              debugColor.a = materialTransparency;\n              #if !CC_USE_FLOAT_OUTPUT\n                debugColor.rgb = HDRToLDR(debugColor.rgb);\n                debugColor.rgb = LinearToSRGB(debugColor.rgb);\n              #endif\n            }\n        }\n      if (IS_DEBUG_VIEW_ENABLE_WITH_CAMERA) {\n        fragColorX = debugColor;\n        return;\n      }\n    #elif CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n      CCSurfacesDebugViewCompositeLightingResult(lightingResult);\n    #endif\n    vec4 color = CCSurfacesShading(surfaceData, lightingResult);\n    #if CC_USE_DEBUG_VIEW && CC_SURFACES_ENABLE_DEBUG_VIEW\n      if (IS_DEBUG_VIEW_MISC_ENABLE_CSM_LAYER_COLORATION) {\n          color.rgb *= colDebugCSMLayer.rgb;\n      }\n      #if CC_SURFACES_USE_TANGENT_SPACE\n        color = CCSurfacesDebugDisplayInvalidInputData(color, FSInput_worldTangent);\n      #endif\n    #endif\n  #endif\n    #if CC_USE_FOG != 4 && (!CC_USE_FLOAT_OUTPUT || CC_IS_TRANSPARENCY_PASS)\n      #if !CC_FORWARD_ADD\n        #ifdef CC_SURFACES_LIGHTING_MODIFY_FOG\n          color.rgb = CCSurfacesLightingModifyFog(fogFactor, color.rgb, surfaceData, lightingResult);\n        #else\n          CC_APPLY_FOG_BASE(color, fogFactor);\n        #endif\n      #endif\n    #endif\n    #if CC_USE_RGBE_OUTPUT\n      color = packRGBE(color.rgb);\n    #else\n      color = CCSurfacesDebugDisplayInvalidNumber(color);\n      #if !CC_USE_FLOAT_OUTPUT || CC_IS_TRANSPARENCY_PASS\n        color.rgb = HDRToLDR(color.rgb);\n        color.rgb = LinearToSRGB(color.rgb);\n      #endif\n    #endif\n    fragColorX = color;\n  }\n#elif CC_PIPELINE_TYPE == 1\n    layout(location = 0) out vec4 albedoOut;\n    layout(location = 1) out vec4 emissiveOut;\n    layout(location = 2) out vec4 normalOut;\n    void main () {\n      SurfacesMaterialData surfaceData;\n      CCSurfacesFragmentGetMaterialData(surfaceData);\n      albedoOut = CCSurfacesDeferredOutputBaseColor(surfaceData);\n      normalOut = CCSurfacesDeferredOutputNormalMR(surfaceData);\n      emissiveOut = CCSurfacesDeferredOutputEmissiveAO(surfaceData);\n    #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_SINGLE && CC_SURFACES_ENABLE_DEBUG_VIEW\n      vec4 debugColor = vec4(0.0, 0.0, 0.0, 1.0);\n      CCSurfacesDebugViewMeshData(debugColor);\n      CCSurfacesDebugViewSurfaceData(debugColor, surfaceData);\n      if (IS_DEBUG_VIEW_ENABLE_WITH_CAMERA) {\n        albedoOut = debugColor;\n      }\n    #endif\n    }\n#endif"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}, {"name": "CCShadow", "defines": []}, {"name": "CCCSM", "defines": ["CC_SUPPORT_CASCADED_SHADOW_MAP"]}], "samplerTextures": [{"name": "cc_environment", "defines": []}, {"name": "cc_diffuseMap", "defines": ["CC_USE_IBL", "CC_USE_DIFFUSEMAP"]}, {"name": "cc_shadowMap", "defines": ["CC_RECEIVE_SHADOW"]}, {"name": "cc_spotShadowMap", "defines": ["CC_RECEIVE_SHADOW"]}], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": ["!USE_INSTANCING"]}, {"name": "CCMorph", "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCForwardLight", "defines": ["CC_FORWARD_ADD", "CC_ENABLE_CLUSTERED_LIGHT_CULLING"]}, {"name": "CCSH", "defines": ["CC_USE_LIGHT_PROBE", "!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "cc_reflectionProbeCubemap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbePlanarMap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbeDataMap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbeBlendCubemap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_lightingMap", "defines": ["CC_USE_LIGHTMAP", "!CC_FORWARD_ADD"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 93, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 123}}, "defines": [{"name": "USE_TWOSIDE", "type": "boolean"}, {"name": "USE_VERTEX_COLOR", "type": "boolean"}, {"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_REFLECTION_PROBE", "type": "number", "range": [0, 3]}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "default": 0}, {"name": "CC_USE_MORPH", "type": "boolean"}, {"name": "CC_FORWARD_ADD", "type": "boolean"}, {"name": "CC_USE_FOG", "type": "number", "range": [0, 4]}, {"name": "CC_USE_ACCURATE_FOG", "type": "boolean"}, {"name": "CC_MORPH_TARGET_COUNT", "type": "number", "range": [2, 8]}, {"name": "CC_MORPH_TARGET_HAS_POSITION", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_NORMAL", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_TANGENT", "type": "boolean"}, {"name": "CC_MORPH_PRECOMPUTED", "type": "boolean"}, {"name": "CC_USE_REAL_TIME_JOINT_TEXTURE", "type": "boolean"}, {"name": "CC_DISABLE_STRUCTURE_IN_FRAGMENT_SHADER", "type": "number", "range": [0, 1]}, {"name": "CC_PIPELINE_TYPE", "type": "number", "range": [0, 1]}, {"name": "CC_FORCE_FORWARD_SHADING", "type": "boolean"}, {"name": "CC_ENABLE_CLUSTERED_LIGHT_CULLING", "type": "number", "range": [0, 3]}, {"name": "CC_SUPPORT_CASCADED_SHADOW_MAP", "type": "boolean"}, {"name": "CC_USE_IBL", "type": "number", "range": [0, 2]}, {"name": "CC_USE_DIFFUSEMAP", "type": "number", "range": [0, 2]}, {"name": "CC_USE_HDR", "type": "boolean"}, {"name": "CC_USE_DEBUG_VIEW", "type": "number", "range": [0, 3]}, {"name": "CC_TONE_MAPPING_TYPE", "type": "number", "range": [0, 3]}, {"name": "HDR_TONE_MAPPING_ACES", "type": "boolean"}, {"name": "CC_IBL_CONVOLUTED", "type": "boolean"}, {"name": "CC_SHADOWMAP_FORMAT", "type": "number", "range": [0, 3]}, {"name": "CC_SHADOWMAP_USE_LINEAR_DEPTH", "type": "boolean"}, {"name": "CC_DIR_SHADOW_PCF_TYPE", "type": "number", "range": [0, 3]}, {"name": "CC_CASCADED_LAYERS_TRANSITION", "type": "boolean"}, {"name": "CC_LIGHT_MAP_VERSION", "type": "number", "range": [0, 3]}, {"name": "USE_ALBEDO_MAP", "type": "boolean"}, {"name": "ALBEDO_UV", "type": "string", "options": ["v_uv", "v_uv1"]}, {"name": "USE_ALPHA_TEST", "type": "boolean"}, {"name": "ALPHA_TEST_CHANNEL", "type": "string", "options": ["a", "r"]}, {"name": "CC_SURFACES_LIGHTING_DISABLE_DIFFUSE", "type": "boolean"}, {"name": "CC_SURFACES_LIGHTING_DISABLE_SPECULAR", "type": "boolean"}, {"name": "CC_SHADOW_TYPE", "type": "number", "range": [0, 3]}, {"name": "CC_DIR_LIGHT_SHADOW_TYPE", "type": "number", "range": [0, 3]}, {"name": "CC_DISABLE_DIRECTIONAL_LIGHT", "type": "boolean"}, {"name": "CC_USE_FLOAT_OUTPUT", "type": "boolean"}, {"name": "CC_IS_TRANSPARENCY_PASS", "type": "boolean"}, {"name": "CC_USE_RGBE_OUTPUT", "type": "boolean"}]}, {"hash": 2320946369, "name": "../bundleOne/common/effect/surface-effect|shadow-caster-vs|shadow-caster-fs", "blocks": [{"name": "Constants", "stageFlags": 17, "binding": 0, "members": [{"name": "albedo", "type": 16, "count": 1}, {"name": "albedoScaleAnd<PERSON><PERSON>ff", "type": 16, "count": 1}, {"name": "pbrParams", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [{"name": "albedoMap", "type": 28, "count": 1, "stageFlags": 16, "binding": 1, "defines": ["USE_ALBEDO_MAP"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_color", "format": 44, "location": 6, "defines": ["CC_SURFACES_USE_VERTEX_COLOR"]}, {"name": "a_texCoord1", "format": 21, "location": 7, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 16, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 17, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 18, "defines": ["CC_USE_MORPH"]}], "fragColors": [{"name": "fragColorX", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [{"name": "CCLocal", "stageFlags": 17, "tags": {"builtin": "local"}, "members": [{"name": "cc_matWorld", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matWorldIT", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_lightingMapUVParam", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_localShadowBias", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData1", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_reflectionProbeBlendData2", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["!USE_INSTANCING"]}, {"name": "CCMorph", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_displacementWeights", "typename": "vec4", "type": 16, "count": 15, "isArray": true}, {"name": "cc_displacementTextureInfo", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointTextureInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_jointAnimInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "stageFlags": 1, "tags": {"builtin": "local"}, "members": [{"name": "cc_joints", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}], "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCForwardLight", "stageFlags": 16, "tags": {"builtin": "local"}, "members": [{"name": "cc_lightPos", "typename": "vec4", "type": 16, "count": 0, "precision": "highp ", "isArray": true}, {"name": "cc_lightColor", "typename": "vec4", "type": 16, "count": 0, "isArray": true}, {"name": "cc_lightSizeRangeAngle", "typename": "vec4", "type": 16, "count": 0, "isArray": true}, {"name": "cc_lightDir", "typename": "vec4", "type": 16, "count": 0, "isArray": true}, {"name": "cc_lightBoundingSizeVS", "typename": "vec4", "type": 16, "count": 0, "isArray": true}], "defines": ["CC_FORWARD_ADD", "CC_ENABLE_CLUSTERED_LIGHT_CULLING"]}, {"name": "CCSH", "stageFlags": 16, "tags": {"builtin": "local"}, "members": [{"name": "cc_sh_linear_const_r", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_g", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_linear_const_b", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_r", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_g", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_b", "typename": "vec4", "type": 16, "count": 1}, {"name": "cc_sh_quadratic_a", "typename": "vec4", "type": 16, "count": 1}], "defines": ["CC_USE_LIGHT_PROBE", "!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 1, "tags": {"builtin": "local"}, "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "cc_reflectionProbeCubemap", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbePlanarMap", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbeDataMap", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbeBlendCubemap", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_lightingMap", "typename": "sampler2D", "type": 28, "count": 1, "stageFlags": 16, "tags": {"builtin": "local"}, "defines": ["CC_USE_LIGHTMAP", "!CC_FORWARD_ADD"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [{"name": "Constants", "stageFlags": 17, "binding": 0, "members": [{"name": "albedo", "type": 16, "count": 1}, {"name": "albedoScaleAnd<PERSON><PERSON>ff", "type": 16, "count": 1}, {"name": "pbrParams", "type": 16, "count": 1}], "defines": []}], "samplerTextures": [{"name": "albedoMap", "type": 28, "count": 1, "stageFlags": 16, "binding": 1, "defines": ["USE_ALBEDO_MAP"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCShadow", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matLightView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matLightViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_shadowInvProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjDepthInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowProjInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_shadowNFLSInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowWHPBInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowLPNNInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_shadowColor", "typename": "vec4", "type": 16, "count": 1, "precision": "lowp "}, {"name": "cc_planarNDInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCSM", "stageFlags": 16, "tags": {"builtin": "global"}, "members": [{"name": "cc_csmViewDir0", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmViewDir1", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmViewDir2", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmAtlas", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_matCSMViewProj", "typename": "mat4", "type": 25, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmProjDepthInfo", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmProjInfo", "typename": "vec4", "type": 16, "count": 4, "precision": "highp ", "isArray": true}, {"name": "cc_csmSplitsInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}], "defines": ["CC_SUPPORT_CASCADED_SHADOW_MAP"]}], "samplerTextures": [{"name": "cc_environment", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "global"}, "defines": []}, {"name": "cc_diffuseMap", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "global"}, "defines": ["CC_USE_IBL", "CC_USE_DIFFUSEMAP"]}, {"name": "cc_shadowMap", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 16, "tags": {"builtin": "global"}, "defines": ["CC_RECEIVE_SHADOW"]}, {"name": "cc_spotShadowMap", "typename": "sampler2D", "type": 28, "count": 1, "precision": "highp ", "stageFlags": 16, "tags": {"builtin": "global"}, "defines": ["CC_RECEIVE_SHADOW"]}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision highp float;\n#ifndef CC_SURFACES_USE_SECOND_UV\n  #define CC_SURFACES_USE_SECOND_UV 0\n#endif\n#ifndef CC_SURFACES_USE_TANGENT_SPACE\n  #define CC_SURFACES_USE_TANGENT_SPACE 0\n#endif\n#ifndef CC_SURFACES_USE_VERTEX_COLOR\n  #define CC_SURFACES_USE_VERTEX_COLOR 0\n#endif\n#ifndef CC_SURFACES_TRANSFER_LOCAL_POS\n  #define CC_SURFACES_TRANSFER_LOCAL_POS 0\n#endif\n#ifndef CC_SURFACES_TRANSFER_CLIP_POS\n  #define CC_SURFACES_TRANSFER_CLIP_POS 0\n#endif\n#ifndef CC_SURFACES_USE_LIGHT_MAP\n  #ifdef CC_USE_LIGHTMAP\n    #define CC_SURFACES_USE_LIGHT_MAP CC_USE_LIGHTMAP\n  #else\n    #define CC_SURFACES_USE_LIGHT_MAP 0\n  #endif\n#endif\n#ifndef CC_SURFACES_FLIP_UV\n  #define CC_SURFACES_FLIP_UV 0\n#endif\n#ifndef CC_SURFACES_USE_TWO_SIDED\n  #define CC_SURFACES_USE_TWO_SIDED 0\n#endif\n#ifndef CC_SURFACES_USE_REFLECTION_DENOISE\n  #define CC_SURFACES_USE_REFLECTION_DENOISE 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_ANISOTROPIC\n  #define CC_SURFACES_LIGHTING_ANISOTROPIC 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT\n  #define CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT 0\n#endif\n#ifndef CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING\n  #define CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_FRESNEL\n  #define CC_SURFACES_LIGHTING_USE_FRESNEL 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n  #define CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  #define CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT\n  #define CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TRT\n  #define CC_SURFACES_LIGHTING_TRT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR\n  #define CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_SHEEN\n  #define CC_SURFACES_LIGHTING_SHEEN 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_CLEAR_COAT\n  #define CC_SURFACES_LIGHTING_CLEAR_COAT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TT\n  #define CC_SURFACES_LIGHTING_TT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_SSS\n  #define CC_SURFACES_LIGHTING_SSS 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n  #if CC_SURFACES_LIGHTING_TRT || CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR || CC_SURFACES_LIGHTING_SHEEN || CC_SURFACES_LIGHTING_CLEAR_COAT\n    #define CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR 1\n  #endif\n#endif\n#ifndef CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n  #define CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n  #if CC_SURFACES_LIGHTING_CLEAR_COAT\n    #define CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND 1\n  #endif\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n  #define CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND 0\n#endif\n#ifndef CC_SURFACES_ENABLE_DEBUG_VIEW\n  #define CC_SURFACES_ENABLE_DEBUG_VIEW 1\n#endif\n#define CC_USE_SURFACE_SHADER 1\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\n#if CC_SURFACES_USE_TANGENT_SPACE\n  in vec4 a_tangent;\n#endif\n#if CC_SURFACES_USE_VERTEX_COLOR\n  in vec4 a_color;\n#endif\n#if CC_SURFACES_USE_SECOND_UV || CC_USE_LIGHTMAP\n  in vec2 a_texCoord1;\n#endif\n#if CC_USE_SKINNING\n    in vec4 a_joints;\n  in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    in highp vec4 a_jointAnimInfo;\n  #endif\n  in vec4 a_matWorld0;\n  in vec4 a_matWorld1;\n  in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n    in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    in vec4 a_sh_linear_const_r;\n    in vec4 a_sh_linear_const_g;\n    in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    in float a_vertexId;\n#endif\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nout highp vec3 v_worldPos;\nout vec4 v_normal;\nout vec2 v_uv;\n#if CC_SURFACES_USE_VERTEX_COLOR\n  out lowp vec4 v_color;\n#endif\n#if CC_SURFACES_USE_TANGENT_SPACE\n  out mediump vec4 v_tangent;\n#endif\n#if CC_SURFACES_USE_SECOND_UV\n  out mediump vec2 v_uv1;\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  out mediump vec3 v_luv;\n#endif\n#if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n  out mediump vec4 v_shadowBiasAndProbeId;\n#endif\n#if CC_USE_REFLECTION_PROBE && USE_INSTANCING\n  out mediump vec4 v_reflectionProbeData;\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  out mediump float v_fogFactor;\n#endif\n#if CC_SURFACES_TRANSFER_LOCAL_POS\n  out highp vec4 v_localPos;\n#endif\n#if CC_SURFACES_TRANSFER_CLIP_POS\n  out highp vec4 v_clipPos;\n#endif\n#if CC_USE_LIGHT_PROBE\n  #if USE_INSTANCING\n    out mediump vec4 v_sh_linear_const_r;\n    out mediump vec4 v_sh_linear_const_g;\n    out mediump vec4 v_sh_linear_const_b;\n  #endif\n#endif\n#define VSOutput_worldPos v_worldPos\n#define VSOutput_worldNormal v_normal.xyz\n#define VSOutput_faceSideSign v_normal.w\n#define VSOutput_texcoord v_uv\n#if CC_SURFACES_USE_VERTEX_COLOR\n  #define VSOutput_vertexColor v_color\n#endif\n#if CC_SURFACES_USE_TANGENT_SPACE\n  #define VSOutput_worldTangent v_tangent.xyz\n  #define VSOutput_mirrorNormal v_tangent.w\n#endif\n#if CC_SURFACES_USE_SECOND_UV\n  #define VSOutput_texcoord1 v_uv1\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  #define VSOutput_lightMapUV v_luv\n#endif\n#if CC_RECEIVE_SHADOW\n  #define VSOutput_shadowBias v_shadowBiasAndProbeId.xy\n#endif\n#if CC_USE_REFLECTION_PROBE\n  #define VSOutput_reflectionProbeId v_shadowBiasAndProbeId.z\n  #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n    #define VSOutput_reflectionProbeBlendId v_shadowBiasAndProbeId.w\n  #endif\n  #if USE_INSTANCING\n    #define VSOutput_reflectionProbeData v_reflectionProbeData\n  #endif\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  #define VSOutput_fogFactor v_fogFactor\n#endif\n#if CC_SURFACES_TRANSFER_LOCAL_POS\n  #define VSOutput_localPos v_localPos\n#endif\n#if CC_SURFACES_TRANSFER_CLIP_POS\n  #define VSOutput_clipPos v_clipPos\n#endif\nstruct SurfacesStandardVertexIntermediate\n{\n  highp vec4 position;\n  vec3 normal;\n#if CC_SURFACES_USE_TANGENT_SPACE\n  vec4 tangent;\n#endif\n#if CC_SURFACES_USE_VERTEX_COLOR\n  vec4 color;\n#endif\n  vec2 texCoord;\n#if CC_SURFACES_USE_SECOND_UV\n  vec2 texCoord1;\n#endif\n  highp vec4 clipPos;\n  highp vec3 worldPos;\n  vec4 worldNormal;\n  #if CC_SURFACES_USE_TANGENT_SPACE\n    vec3 worldTangent, worldBinormal;\n  #endif\n#if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n  vec4 shadowBiasAndProbeId;\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  float fogFactor;\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  vec3 lightmapUV;\n#endif\n};\n#if CC_USE_MORPH\n    int getVertexId() {\n      return int(a_vertexId);\n    }\n#endif\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nlayout(std140) uniform CCShadow {\n  highp mat4 cc_matLightView;\n  highp mat4 cc_matLightViewProj;\n  highp vec4 cc_shadowInvProjDepthInfo;\n  highp vec4 cc_shadowProjDepthInfo;\n  highp vec4 cc_shadowProjInfo;\n  mediump vec4 cc_shadowNFLSInfo;\n  mediump vec4 cc_shadowWHPBInfo;\n  mediump vec4 cc_shadowLPNNInfo;\n  lowp vec4 cc_shadowColor;\n  mediump vec4 cc_planarNDInfo;\n};\nhighp float decode32 (highp vec4 rgba) {\n  rgba = rgba * 255.0;\n  highp float Sign = 1.0 - (step(128.0, (rgba[3]) + 0.5)) * 2.0;\n  highp float Exponent = 2.0 * (mod(float(int((rgba[3]) + 0.5)), 128.0)) + (step(128.0, (rgba[2]) + 0.5)) - 127.0;\n  highp float Mantissa = (mod(float(int((rgba[2]) + 0.5)), 128.0)) * 65536.0 + rgba[1] * 256.0 + rgba[0] + 8388608.0;\n  return Sign * exp2(Exponent - 23.0) * Mantissa;\n}\n#if !USE_INSTANCING\n  layout(std140) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n#endif\nvoid CCGetWorldMatrixFull(out mat4 matWorld, out mat4 matWorldIT)\n{\n  #if USE_INSTANCING\n    matWorld = mat4(\n      vec4(a_matWorld0.xyz, 0.0),\n      vec4(a_matWorld1.xyz, 0.0),\n      vec4(a_matWorld2.xyz, 0.0),\n      vec4(a_matWorld0.w, a_matWorld1.w, a_matWorld2.w, 1.0)\n    );\n    vec3 scale = 1.0 / vec3(length(a_matWorld0.xyz), length(a_matWorld1.xyz), length(a_matWorld2.xyz));\n    vec3 scale2 = scale * scale;\n    matWorldIT = mat4(\n      vec4(a_matWorld0.xyz * scale2.x, 0.0),\n      vec4(a_matWorld1.xyz * scale2.y, 0.0),\n      vec4(a_matWorld2.xyz * scale2.z, 0.0),\n      vec4(0.0, 0.0, 0.0, 1.0)\n    );\n  #else\n    matWorld = cc_matWorld;\n    matWorldIT = cc_matWorldIT;\n  #endif\n}\n#if CC_USE_MORPH\n  layout(std140) uniform CCMorph {\n    vec4 cc_displacementWeights[15];\n    vec4 cc_displacementTextureInfo;\n  };\n  #if CC_MORPH_TARGET_HAS_POSITION\n    uniform sampler2D cc_PositionDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    uniform sampler2D cc_NormalDisplacements;\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    uniform sampler2D cc_TangentDisplacements;\n  #endif\n  vec2 getPixelLocation(vec2 textureResolution, int pixelIndex) {\n    float pixelIndexF = float(pixelIndex);\n    float x = mod(pixelIndexF, textureResolution.x);\n    float y = floor(pixelIndexF / textureResolution.x);\n    return vec2(x, y);\n  }\n  vec2 getPixelCoordFromLocation(vec2 location, vec2 textureResolution) {\n    return (vec2(location.x, location.y) + .5) / textureResolution;\n  }\n  #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      vec4 fetchVec3ArrayFromTexture(sampler2D tex, int pixelIndex) {\n        ivec2 texSize = textureSize(tex, 0);\n        return texelFetch(tex, ivec2(pixelIndex % texSize.x, pixelIndex / texSize.x), 0);\n      }\n  #else\n    vec4 fetchVec3ArrayFromTexture(sampler2D tex, int elementIndex) {\n      int pixelIndex = elementIndex * 4;\n      vec2 location = getPixelLocation(cc_displacementTextureInfo.xy, pixelIndex);\n      vec2 x = getPixelCoordFromLocation(location + vec2(0.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 y = getPixelCoordFromLocation(location + vec2(1.0, 0.0), cc_displacementTextureInfo.xy);\n      vec2 z = getPixelCoordFromLocation(location + vec2(2.0, 0.0), cc_displacementTextureInfo.xy);\n      return vec4(\n        decode32(texture(tex, x)),\n        decode32(texture(tex, y)),\n        decode32(texture(tex, z)),\n        1.0\n      );\n    }\n  #endif\n  float getDisplacementWeight(int index) {\n    int quot = index / 4;\n    int remainder = index - quot * 4;\n    if (remainder == 0) {\n      return cc_displacementWeights[quot].x;\n    } else if (remainder == 1) {\n      return cc_displacementWeights[quot].y;\n    } else if (remainder == 2) {\n      return cc_displacementWeights[quot].z;\n    } else {\n      return cc_displacementWeights[quot].w;\n    }\n  }\n  vec3 getVec3DisplacementFromTexture(sampler2D tex, int vertexIndex) {\n  #if CC_MORPH_PRECOMPUTED\n    return fetchVec3ArrayFromTexture(tex, vertexIndex).rgb;\n  #else\n    vec3 result = vec3(0, 0, 0);\n    int nVertices = int(cc_displacementTextureInfo.z);\n    for (int iTarget = 0; iTarget < CC_MORPH_TARGET_COUNT; ++iTarget) {\n      result += (fetchVec3ArrayFromTexture(tex, nVertices * iTarget + vertexIndex).rgb * getDisplacementWeight(iTarget));\n    }\n    return result;\n  #endif\n  }\n  #if CC_MORPH_TARGET_HAS_POSITION\n  vec3 getPositionDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_PositionDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n  vec3 getNormalDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_NormalDisplacements, vertexId);\n  }\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n  vec3 getTangentDisplacement(int vertexId) {\n      return getVec3DisplacementFromTexture(cc_TangentDisplacements, vertexId);\n  }\n  #endif\n  void applyMorph (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    int vertexId = getVertexId();\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_NORMAL\n    normal.xyz = normal.xyz + getNormalDisplacement(vertexId);\n  #endif\n  #if CC_MORPH_TARGET_HAS_TANGENT\n    tangent.xyz = tangent.xyz + getTangentDisplacement(vertexId);\n  #endif\n  }\n  void applyMorph (inout vec4 position) {\n  #if CC_MORPH_TARGET_HAS_POSITION\n    position.xyz = position.xyz + getPositionDisplacement(getVertexId());\n  #endif\n  }\n#endif\n#if CC_USE_SKINNING\n  #if CC_USE_BAKED_ANIMATION\n    layout(std140) uniform CCSkinningTexture {\n      highp vec4 cc_jointTextureInfo;\n    };\n    layout(std140) uniform CCSkinningAnimation {\n      highp vec4 cc_jointAnimInfo;\n    };\n    uniform highp sampler2D cc_jointTexture;\n    void CCGetJointTextureCoords(float pixelsPerJoint, float jointIdx, out highp float x, out highp float y, out highp float invSize)\n    {\n      #if USE_INSTANCING\n        highp float temp = pixelsPerJoint * (a_jointAnimInfo.x * a_jointAnimInfo.y + jointIdx) + a_jointAnimInfo.z;\n      #else\n        highp float temp = pixelsPerJoint * (cc_jointAnimInfo.x * cc_jointTextureInfo.y + jointIdx) + cc_jointTextureInfo.z;\n      #endif\n      invSize = cc_jointTextureInfo.w;\n      highp float tempY = floor(temp * invSize);\n      x = floor(temp - tempY * cc_jointTextureInfo.x);\n      y = (tempY + 0.5) * invSize;\n    }\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      uniform highp sampler2D cc_realtimeJoint;\n    #else\n      layout(std140) uniform CCSkinning {\n        highp vec4 cc_joints[CC_JOINT_UNIFORM_CAPACITY * 3];\n      };\n    #endif\n  #endif\n  #if CC_USE_BAKED_ANIMATION\n    #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(3.0, i, x, y, invSize);\n        vec4 v1 = texture(cc_jointTexture, vec2((x + 0.5) * invSize, y));\n        vec4 v2 = texture(cc_jointTexture, vec2((x + 1.5) * invSize, y));\n        vec4 v3 = texture(cc_jointTexture, vec2((x + 2.5) * invSize, y));\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #else\n      mat4 getJointMatrix (float i) {\n        highp float x, y, invSize;\n        CCGetJointTextureCoords(12.0, i, x, y, invSize);\n        vec4 v1 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 0.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 1.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 2.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 3.5) * invSize, y)))\n        );\n        vec4 v2 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 4.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 5.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 6.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 7.5) * invSize, y)))\n        );\n        vec4 v3 = vec4(\n          decode32(texture(cc_jointTexture, vec2((x + 8.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 9.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 10.5) * invSize, y))),\n          decode32(texture(cc_jointTexture, vec2((x + 11.5) * invSize, y)))\n        );\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #else\n    #if CC_USE_REAL_TIME_JOINT_TEXTURE\n      #if CC_DEVICE_SUPPORT_FLOAT_TEXTURE\n        mat4 getJointMatrix (float i) {\n          float x = i;\n          vec4 v1 = texture(cc_realtimeJoint, vec2( x / 256.0, 0.5 / 3.0));\n          vec4 v2 = texture(cc_realtimeJoint, vec2( x / 256.0, 1.5 / 3.0));\n          vec4 v3 = texture(cc_realtimeJoint, vec2( x / 256.0, 2.5 / 3.0));\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #else\n        mat4 getJointMatrix (float i) {\n         float x = 4.0 * i;\n          vec4 v1 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 0.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 0.5 / 3.0)))\n          );\n          vec4 v2 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 1.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 1.5 / 3.0)))\n          );\n          vec4 v3 = vec4(\n            decode32(texture(cc_realtimeJoint, vec2((x + 0.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 1.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 2.5)/ 1024.0, 2.5 / 3.0))),\n            decode32(texture(cc_realtimeJoint, vec2((x + 3.5)/ 1024.0, 2.5 / 3.0)))\n          );\n          return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n        }\n      #endif\n    #else\n      mat4 getJointMatrix (float i) {\n        int idx = int(i);\n        vec4 v1 = cc_joints[idx * 3];\n        vec4 v2 = cc_joints[idx * 3 + 1];\n        vec4 v3 = cc_joints[idx * 3 + 2];\n        return mat4(vec4(v1.xyz, 0.0), vec4(v2.xyz, 0.0), vec4(v3.xyz, 0.0), vec4(v1.w, v2.w, v3.w, 1.0));\n      }\n    #endif\n  #endif\n  mat4 skinMatrix () {\n    vec4 joints = vec4(a_joints);\n    return getJointMatrix(joints.x) * a_weights.x\n         + getJointMatrix(joints.y) * a_weights.y\n         + getJointMatrix(joints.z) * a_weights.z\n         + getJointMatrix(joints.w) * a_weights.w;\n  }\n  void CCSkin (inout vec4 position) {\n    mat4 m = skinMatrix();\n    position = m * position;\n  }\n  void CCSkin (inout vec4 position, inout vec3 normal, inout vec4 tangent) {\n    mat4 m = skinMatrix();\n    position = m * position;\n    normal = (m * vec4(normal, 0.0)).xyz;\n    tangent.xyz = (m * vec4(tangent.xyz, 0.0)).xyz;\n  }\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  #if CC_USE_FOG != 4\n  #endif\n#endif\nlayout(std140) uniform Constants {\n  vec4 albedo;\n  vec4 albedoScaleAndCutoff;\n  vec4 pbrParams;\n};\n#define CC_SURFACES_VERTEX_MODIFY_WORLD_POS\nvec3 SurfacesVertexModifyWorldPos(in SurfacesStandardVertexIntermediate In)\n{\n  return In.worldPos;\n}\n#define CC_SURFACES_VERTEX_MODIFY_WORLD_NORMAL\n#define CC_SURFACES_VERTEX_MODIFY_UV\nvoid SurfacesVertexModifyUV(inout SurfacesStandardVertexIntermediate In)\n{\n}\n#ifndef CC_SURFACES_VERTEX_MODIFY_LOCAL_POS\nvec3 SurfacesVertexModifyLocalPos(in SurfacesStandardVertexIntermediate In)\n{\n  return vec3(In.position.xyz);\n}\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_LOCAL_NORMAL\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_LOCAL_TANGENT\n  #if CC_SURFACES_USE_TANGENT_SPACE\n  #endif\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_LOCAL_SHARED_DATA\nvoid SurfacesVertexModifyLocalSharedData(inout SurfacesStandardVertexIntermediate In)\n{\n}\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_WORLD_POS\nvec3 SurfacesVertexModifyWorldPos(in SurfacesStandardVertexIntermediate In)\n{\n  return In.worldPos;\n}\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_CLIP_POS\nvec4 SurfacesVertexModifyClipPos(in SurfacesStandardVertexIntermediate In)\n{\n  return In.clipPos;\n}\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_UV\nvoid SurfacesVertexModifyUV(inout SurfacesStandardVertexIntermediate In)\n{\n}\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_WORLD_NORMAL\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_SHADOW_BIAS\n#endif\n#ifndef CC_SURFACES_VERTEX_MODIFY_SHARED_DATA\nvoid SurfacesVertexModifySharedData(inout SurfacesStandardVertexIntermediate In)\n{\n}\n#endif\nvoid CCSurfacesVertexInput(out SurfacesStandardVertexIntermediate In)\n{\n  In.position = vec4(a_position, 1.0);\n  In.normal = a_normal;\n#if CC_SURFACES_USE_TANGENT_SPACE\n  In.tangent = a_tangent;\n#endif\n#if CC_SURFACES_USE_VERTEX_COLOR\n  In.color = a_color;\n#endif\n  In.texCoord = a_texCoord;\n#if CC_SURFACES_USE_SECOND_UV\n  In.texCoord1 = a_texCoord1;\n#endif\n}\nvoid CCSurfacesVertexOutput(in SurfacesStandardVertexIntermediate In)\n{\n  gl_Position = In.clipPos;\n  VSOutput_worldNormal = In.worldNormal.xyz;\n  VSOutput_faceSideSign = In.worldNormal.w;\n  VSOutput_worldPos = In.worldPos;\n#if CC_SURFACES_USE_TANGENT_SPACE\n  VSOutput_worldTangent = In.worldTangent.xyz;\n  VSOutput_mirrorNormal = In.tangent.w > 0.0 ? 1.0 : -1.0;\n#endif\n#if CC_SURFACES_USE_VERTEX_COLOR\n  VSOutput_vertexColor = In.color;\n#endif\n  VSOutput_texcoord = In.texCoord;\n#if CC_SURFACES_USE_SECOND_UV\n  VSOutput_texcoord1 = In.texCoord1;\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  VSOutput_fogFactor = In.fogFactor;\n#endif\n#if CC_RECEIVE_SHADOW\n  VSOutput_shadowBias = In.shadowBiasAndProbeId.xy;\n#endif\n#if CC_USE_REFLECTION_PROBE\n  VSOutput_reflectionProbeId = In.shadowBiasAndProbeId.z;\n  #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n    VSOutput_reflectionProbeBlendId = In.shadowBiasAndProbeId.w;\n  #endif\n  #if USE_INSTANCING\n    v_reflectionProbeData = a_reflectionProbeData;\n  #endif\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  VSOutput_lightMapUV = In.lightmapUV;\n#endif\n#if CC_SURFACES_TRANSFER_LOCAL_POS\n  VSOutput_localPos = In.position;\n#endif\n#if CC_SURFACES_TRANSFER_CLIP_POS\n  VSOutput_clipPos = In.clipPos;\n#endif\n#if CC_USE_LIGHT_PROBE\n  #if USE_INSTANCING\n    v_sh_linear_const_r = a_sh_linear_const_r;\n    v_sh_linear_const_g = a_sh_linear_const_g;\n    v_sh_linear_const_b = a_sh_linear_const_b;\n  #endif\n#endif\n}\nvoid CCSurfacesVertexAnimation(inout SurfacesStandardVertexIntermediate In)\n{\nvec4 temp = vec4(0.0);\n#if CC_USE_MORPH\n  #if CC_SURFACES_USE_TANGENT_SPACE\n    applyMorph(In.position, In.normal, In.tangent);\n  #else\n    applyMorph(In.position, In.normal, temp);\n  #endif\n#endif\n#if CC_USE_SKINNING\n  #if CC_SURFACES_USE_TANGENT_SPACE\n    CCSkin(In.position, In.normal, In.tangent);\n  #else\n    CCSkin(In.position, In.normal, temp);\n  #endif\n#endif\n}\nvoid CCSurfacesVertexWorldTransform(inout SurfacesStandardVertexIntermediate In)\n{\n    mat4 matWorld, matWorldIT;\n    CCGetWorldMatrixFull(matWorld, matWorldIT);\n    In.worldPos = (matWorld * In.position).xyz;\n    In.worldNormal.xyz = normalize((matWorldIT * vec4(In.normal.xyz, 0.0)).xyz);\n    #if CC_SURFACES_USE_TANGENT_SPACE\n      In.worldTangent = normalize((matWorld * vec4(In.tangent.xyz, 0.0)).xyz);\n      In.worldBinormal = cross(In.worldNormal.xyz, In.worldTangent) * In.tangent.w;\n    #endif\n}\nvoid CCSurfacesVertexTransformUV(inout SurfacesStandardVertexIntermediate In)\n{\n  #if CC_SURFACES_FLIP_UV\n    In.texCoord = cc_cameraPos.w > 1.0 ? vec2(In.texCoord.x, 1.0 - In.texCoord.y) : In.texCoord;\n    #if CC_SURFACES_USE_SECOND_UV\n      In.texCoord1 = cc_cameraPos.w > 1.0 ? vec2(In.texCoord1.x, 1.0 - In.texCoord1.y) : In.texCoord1;\n    #endif\n  #endif\n}\nout highp vec2 v_clip_depth;\nvoid main()\n{\n  SurfacesStandardVertexIntermediate In;\n  CCSurfacesVertexInput(In);\n  CCSurfacesVertexAnimation(In);\n  In.position.xyz = SurfacesVertexModifyLocalPos(In);\n  SurfacesVertexModifyLocalSharedData(In);\n  CCSurfacesVertexWorldTransform(In);\n  In.worldPos = SurfacesVertexModifyWorldPos(In);\n  In.clipPos = cc_matLightViewProj * vec4(In.worldPos, 1.0);\n  In.clipPos = SurfacesVertexModifyClipPos(In);\n  SurfacesVertexModifyUV(In);\n  SurfacesVertexModifySharedData(In);\n  CCSurfacesVertexTransformUV(In);\n  CCSurfacesVertexOutput(In);\n  v_clip_depth = In.clipPos.zw;\n}", "frag": "\nprecision highp float;\n#ifndef CC_SURFACES_USE_SECOND_UV\n  #define CC_SURFACES_USE_SECOND_UV 0\n#endif\n#ifndef CC_SURFACES_USE_TANGENT_SPACE\n  #define CC_SURFACES_USE_TANGENT_SPACE 0\n#endif\n#ifndef CC_SURFACES_USE_VERTEX_COLOR\n  #define CC_SURFACES_USE_VERTEX_COLOR 0\n#endif\n#ifndef CC_SURFACES_TRANSFER_LOCAL_POS\n  #define CC_SURFACES_TRANSFER_LOCAL_POS 0\n#endif\n#ifndef CC_SURFACES_TRANSFER_CLIP_POS\n  #define CC_SURFACES_TRANSFER_CLIP_POS 0\n#endif\n#ifndef CC_SURFACES_USE_LIGHT_MAP\n  #ifdef CC_USE_LIGHTMAP\n    #define CC_SURFACES_USE_LIGHT_MAP CC_USE_LIGHTMAP\n  #else\n    #define CC_SURFACES_USE_LIGHT_MAP 0\n  #endif\n#endif\n#ifndef CC_SURFACES_FLIP_UV\n  #define CC_SURFACES_FLIP_UV 0\n#endif\n#ifndef CC_SURFACES_USE_TWO_SIDED\n  #define CC_SURFACES_USE_TWO_SIDED 0\n#endif\n#ifndef CC_SURFACES_USE_REFLECTION_DENOISE\n  #define CC_SURFACES_USE_REFLECTION_DENOISE 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_ANISOTROPIC\n  #define CC_SURFACES_LIGHTING_ANISOTROPIC 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT\n  #define CC_SURFACES_LIGHTING_ANISOTROPIC_ENVCONVOLUTION_COUNT 0\n#endif\n#ifndef CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING\n  #define CC_SURFACES_USE_LEGACY_COMPATIBLE_LIGHTING 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_FRESNEL\n  #define CC_SURFACES_LIGHTING_USE_FRESNEL 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR\n  #define CC_SURFACES_LIGHTING_TRANSMIT_SPECULAR 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE\n  #define CC_SURFACES_LIGHTING_TRANSMIT_DIFFUSE 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT\n  #define CC_SURFACES_LIGHTING_USE_SHADOWMAP_TRANSMIT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TRT\n  #define CC_SURFACES_LIGHTING_TRT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR\n  #define CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_SHEEN\n  #define CC_SURFACES_LIGHTING_SHEEN 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_CLEAR_COAT\n  #define CC_SURFACES_LIGHTING_CLEAR_COAT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_TT\n  #define CC_SURFACES_LIGHTING_TT 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_SSS\n  #define CC_SURFACES_LIGHTING_SSS 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n  #if CC_SURFACES_LIGHTING_TRT || CC_SURFACES_LIGHTING_DUAL_LOBE_SPECULAR || CC_SURFACES_LIGHTING_SHEEN || CC_SURFACES_LIGHTING_CLEAR_COAT\n    #define CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR 1\n  #endif\n#endif\n#ifndef CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR\n  #define CC_SURFACES_LIGHTING_2ND_LAYER_SPECULAR 0\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n  #if CC_SURFACES_LIGHTING_CLEAR_COAT\n    #define CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND 1\n  #endif\n#endif\n#ifndef CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND\n  #define CC_SURFACES_LIGHTING_USE_MULTIPLE_LAYER_BLEND 0\n#endif\n#ifndef CC_SURFACES_ENABLE_DEBUG_VIEW\n  #define CC_SURFACES_ENABLE_DEBUG_VIEW 1\n#endif\n#define CC_USE_SURFACE_SHADER 1\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nin highp vec3 v_worldPos;\nin vec4 v_normal;\nin vec2 v_uv;\n#if CC_SURFACES_USE_VERTEX_COLOR\n  in lowp vec4 v_color;\n#endif\n#if CC_SURFACES_USE_TANGENT_SPACE\n  in mediump vec4 v_tangent;\n#endif\n#if CC_SURFACES_USE_SECOND_UV\n  in mediump vec2 v_uv1;\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  in mediump vec3 v_luv;\n#endif\n#if CC_RECEIVE_SHADOW || CC_USE_REFLECTION_PROBE\n  in mediump vec4 v_shadowBiasAndProbeId;\n#endif\n#if CC_USE_REFLECTION_PROBE && USE_INSTANCING\n  in mediump vec4 v_reflectionProbeData;\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  in mediump float v_fogFactor;\n#endif\n#if CC_SURFACES_TRANSFER_LOCAL_POS\n  in highp vec4 v_localPos;\n#endif\n#if CC_SURFACES_TRANSFER_CLIP_POS\n  in highp vec4 v_clipPos;\n#endif\n#if CC_USE_LIGHT_PROBE\n  #if USE_INSTANCING\n    in mediump vec4 v_sh_linear_const_r;\n    in mediump vec4 v_sh_linear_const_g;\n    in mediump vec4 v_sh_linear_const_b;\n  #endif\n#endif\n#define FSInput_worldPos v_worldPos\n#define FSInput_worldNormal v_normal.xyz\n#define FSInput_faceSideSign v_normal.w\n#define FSInput_texcoord v_uv\n#if CC_SURFACES_USE_VERTEX_COLOR\n  #define FSInput_vertexColor v_color\n#else\n  #define FSInput_vertexColor vec4(1.0)\n#endif\n#if CC_SURFACES_USE_TANGENT_SPACE\n  #define FSInput_worldTangent v_tangent.xyz\n  #define FSInput_mirrorNormal v_tangent.w\n#else\n  #define FSInput_worldTangent vec3(1.0, 1.0, 1.0)\n  #define FSInput_mirrorNormal 1.0\n#endif\n#if CC_SURFACES_USE_SECOND_UV\n  #define FSInput_texcoord1 v_uv1\n#else\n  #define FSInput_texcoord1 vec2(0.0, 0.0)\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  #define FSInput_lightMapUV v_luv\n#endif\n#if CC_RECEIVE_SHADOW\n  #define FSInput_shadowBias v_shadowBiasAndProbeId.xy\n#endif\n#if CC_USE_REFLECTION_PROBE\n  #define FSInput_reflectionProbeId v_shadowBiasAndProbeId.z\n  #if CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND || CC_USE_REFLECTION_PROBE == REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX\n    #define FSInput_reflectionProbeBlendId v_shadowBiasAndProbeId.w\n  #endif\n  #if USE_INSTANCING\n    #define FSInput_reflectionProbeData v_reflectionProbeData\n  #endif\n#endif\n#if CC_USE_FOG != 4 && !CC_USE_ACCURATE_FOG\n  #define FSInput_fogFactor v_fogFactor\n#endif\n#if CC_SURFACES_TRANSFER_LOCAL_POS\n  #define FSInput_localPos v_localPos\n#endif\n#if CC_SURFACES_TRANSFER_CLIP_POS\n  #define FSInput_clipPos v_clipPos\n#endif\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_COLOR 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_NORMAL CC_SURFACES_DEBUG_VIEW_VERTEX_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_TANGENT CC_SURFACES_DEBUG_VIEW_VERTEX_NORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_WORLD_POS CC_SURFACES_DEBUG_VIEW_VERTEX_TANGENT + 1\n#define CC_SURFACES_DEBUG_VIEW_VERTEX_MIRROR CC_SURFACES_DEBUG_VIEW_WORLD_POS + 1\n#define CC_SURFACES_DEBUG_VIEW_FACE_SIDE CC_SURFACES_DEBUG_VIEW_VERTEX_MIRROR + 1\n#define CC_SURFACES_DEBUG_VIEW_UV0 CC_SURFACES_DEBUG_VIEW_FACE_SIDE + 1\n#define CC_SURFACES_DEBUG_VIEW_UV1 CC_SURFACES_DEBUG_VIEW_UV0 + 1\n#define CC_SURFACES_DEBUG_VIEW_UVLIGHTMAP CC_SURFACES_DEBUG_VIEW_UV1 + 1\n#define CC_SURFACES_DEBUG_VIEW_PROJ_DEPTH CC_SURFACES_DEBUG_VIEW_UVLIGHTMAP + 1\n#define CC_SURFACES_DEBUG_VIEW_LINEAR_DEPTH CC_SURFACES_DEBUG_VIEW_PROJ_DEPTH + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL CC_SURFACES_DEBUG_VIEW_LINEAR_DEPTH + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT CC_SURFACES_DEBUG_VIEW_FRAGMENT_NORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL CC_SURFACES_DEBUG_VIEW_FRAGMENT_TANGENT + 1\n#define CC_SURFACES_DEBUG_VIEW_BASE_COLOR CC_SURFACES_DEBUG_VIEW_FRAGMENT_BINORMAL + 1\n#define CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR CC_SURFACES_DEBUG_VIEW_BASE_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR CC_SURFACES_DEBUG_VIEW_DIFFUSE_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSPARENCY CC_SURFACES_DEBUG_VIEW_SPECULAR_COLOR + 1\n#define CC_SURFACES_DEBUG_VIEW_METALLIC CC_SURFACES_DEBUG_VIEW_TRANSPARENCY + 1\n#define CC_SURFACES_DEBUG_VIEW_ROUGHNESS CC_SURFACES_DEBUG_VIEW_METALLIC + 1\n#define CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY CC_SURFACES_DEBUG_VIEW_ROUGHNESS + 1\n#define CC_SURFACES_DEBUG_VIEW_IOR CC_SURFACES_DEBUG_VIEW_SPECULAR_INTENSITY + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_DIFFUSE CC_SURFACES_DEBUG_VIEW_IOR + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_SPECULAR CC_SURFACES_DEBUG_VIEW_DIRECT_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_ALL CC_SURFACES_DEBUG_VIEW_DIRECT_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_DIFFUSE CC_SURFACES_DEBUG_VIEW_DIRECT_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_SPECULAR CC_SURFACES_DEBUG_VIEW_ENV_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_ENV_ALL CC_SURFACES_DEBUG_VIEW_ENV_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_EMISSIVE CC_SURFACES_DEBUG_VIEW_ENV_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_LIGHT_MAP CC_SURFACES_DEBUG_VIEW_EMISSIVE + 1\n#define CC_SURFACES_DEBUG_VIEW_SHADOW CC_SURFACES_DEBUG_VIEW_LIGHT_MAP + 1\n#define CC_SURFACES_DEBUG_VIEW_AO CC_SURFACES_DEBUG_VIEW_SHADOW + 1\n#define CC_SURFACES_DEBUG_VIEW_FRESNEL CC_SURFACES_DEBUG_VIEW_AO + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_DIFFUSE CC_SURFACES_DEBUG_VIEW_FRESNEL + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_DIFFUSE CC_SURFACES_DEBUG_VIEW_TRANSMIT_DIRECT_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_DIFFUSE + 1\n#define CC_SURFACES_DEBUG_VIEW_TRANSMIT_ALL CC_SURFACES_DEBUG_VIEW_TRANSMIT_ENV_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_DIRECT_2ND_SPECULAR CC_SURFACES_DEBUG_VIEW_TRANSMIT_ALL + 1\n#define CC_SURFACES_DEBUG_VIEW_ENVIRONMENT_2ND_SPECULAR CC_SURFACES_DEBUG_VIEW_DIRECT_2ND_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_2ND_SPECULAR_ALL CC_SURFACES_DEBUG_VIEW_ENVIRONMENT_2ND_SPECULAR + 1\n#define CC_SURFACES_DEBUG_VIEW_FOG CC_SURFACES_DEBUG_VIEW_2ND_SPECULAR_ALL + 1\n#define IS_DEBUG_VIEW_ENABLE_WITH_CAMERA (cc_surfaceTransform.y != 3.0)\n#define IS_DEBUG_VIEW_LIGHTING_ENABLE_WITH_ALBEDO (UnpackBitFromFloat(cc_debug_view_mode.w, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_MISC_ENABLE_CSM_LAYER_COLORATION (UnpackBitFromFloat(cc_debug_view_mode.w, 7) && IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_DIRECT_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.y, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_DIRECT_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.y, 1) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_ENV_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.y, 2) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_ENV_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.y, 3) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_EMISSIVE (UnpackBitFromFloat(cc_debug_view_mode.y, 4) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_LIGHT_MAP (UnpackBitFromFloat(cc_debug_view_mode.y, 5) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_SHADOW (UnpackBitFromFloat(cc_debug_view_mode.y, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_AO (UnpackBitFromFloat(cc_debug_view_mode.y, 7) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_NORMAL_MAP (UnpackBitFromFloat(cc_debug_view_mode.z, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_FOG (UnpackBitFromFloat(cc_debug_view_mode.z, 1) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TONE_MAPPING (UnpackBitFromFloat(cc_debug_view_mode.z, 2) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION (UnpackBitFromFloat(cc_debug_view_mode.z, 3) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_FRESNEL (UnpackBitFromFloat(cc_debug_view_mode.z, 4) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TRANSMIT_DIFFUSE (UnpackBitFromFloat(cc_debug_view_mode.z, 5) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TRANSMIT_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.z, 6) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_2ND_SPECULAR (UnpackBitFromFloat(cc_debug_view_mode.z, 7) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#define IS_DEBUG_VIEW_COMPOSITE_ENABLE_TT (UnpackBitFromFloat(cc_debug_view_mode.w, 0) || !IS_DEBUG_VIEW_ENABLE_WITH_CAMERA)\n#if (CC_PIPELINE_TYPE == 0 || CC_FORCE_FORWARD_SHADING)\n  #if CC_FORWARD_ADD\n    #if CC_PIPELINE_TYPE == 0\n      #define LIGHTS_PER_PASS 1\n    #else\n      #define LIGHTS_PER_PASS 10\n    #endif\n    #if CC_ENABLE_CLUSTERED_LIGHT_CULLING == 0\n    layout(std140) uniform CCForwardLight {\n      highp vec4 cc_lightPos[LIGHTS_PER_PASS];\n      vec4 cc_lightColor[LIGHTS_PER_PASS];\n      vec4 cc_lightSizeRangeAngle[LIGHTS_PER_PASS];\n      vec4 cc_lightDir[LIGHTS_PER_PASS];\n      vec4 cc_lightBoundingSizeVS[LIGHTS_PER_PASS];\n    };\n    #endif\n  #endif\n#endif\n#if CC_USE_LIGHT_PROBE\n  #if !USE_INSTANCING\n    layout(std140) uniform CCSH {\n      vec4 cc_sh_linear_const_r;\n      vec4 cc_sh_linear_const_g;\n      vec4 cc_sh_linear_const_b;\n      vec4 cc_sh_quadratic_r;\n      vec4 cc_sh_quadratic_g;\n      vec4 cc_sh_quadratic_b;\n      vec4 cc_sh_quadratic_a;\n    };\n  #endif\n#endif\nlayout(std140) uniform CCShadow {\n  highp mat4 cc_matLightView;\n  highp mat4 cc_matLightViewProj;\n  highp vec4 cc_shadowInvProjDepthInfo;\n  highp vec4 cc_shadowProjDepthInfo;\n  highp vec4 cc_shadowProjInfo;\n  mediump vec4 cc_shadowNFLSInfo;\n  mediump vec4 cc_shadowWHPBInfo;\n  mediump vec4 cc_shadowLPNNInfo;\n  lowp vec4 cc_shadowColor;\n  mediump vec4 cc_planarNDInfo;\n};\n#if CC_SUPPORT_CASCADED_SHADOW_MAP\n  layout(std140) uniform CCCSM {\n    highp vec4 cc_csmViewDir0[4];\n    highp vec4 cc_csmViewDir1[4];\n    highp vec4 cc_csmViewDir2[4];\n    highp vec4 cc_csmAtlas[4];\n    highp mat4 cc_matCSMViewProj[4];\n    highp vec4 cc_csmProjDepthInfo[4];\n    highp vec4 cc_csmProjInfo[4];\n    highp vec4 cc_csmSplitsInfo;\n  };\n#endif\nuniform samplerCube cc_environment;\n#if CC_USE_IBL\n  #if CC_USE_DIFFUSEMAP\n    uniform samplerCube cc_diffuseMap;\n  #endif\n#endif\n#if CC_USE_REFLECTION_PROBE\n  uniform samplerCube cc_reflectionProbeCubemap;\n  uniform sampler2D cc_reflectionProbePlanarMap;\n  uniform sampler2D cc_reflectionProbeDataMap;\n  uniform samplerCube cc_reflectionProbeBlendCubemap;\n#endif\nvec4 packDepthToRGBA (float depth) {\n  vec4 ret = vec4(1.0, 255.0, 65025.0, 16581375.0) * depth;\n  ret = fract(ret);\n  ret -= vec4(ret.yzw, 0.0) / 255.0;\n  return ret;\n}\n#define UnpackBitFromFloat(value, bit) (mod(floor(value / pow(10.0, float(bit))), 10.0) > 0.0)\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nfloat GetLinearDepthFromViewSpace(vec3 viewPos, float near, float far) {\n  float dist = length(viewPos);\n  return (dist - near) / (far - near);\n}\n#if CC_SUPPORT_CASCADED_SHADOW_MAP\n#endif\nfloat CCGetLinearDepth(vec3 worldPos, float viewSpaceBias) {\n\tvec4 viewPos = cc_matLightView * vec4(worldPos.xyz, 1.0);\n  viewPos.z += viewSpaceBias;\n\treturn GetLinearDepthFromViewSpace(viewPos.xyz, cc_shadowNFLSInfo.x, cc_shadowNFLSInfo.y);\n}\nfloat CCGetLinearDepth(vec3 worldPos) {\n\treturn CCGetLinearDepth(worldPos, 0.0);\n}\n#if CC_RECEIVE_SHADOW\n  uniform highp sampler2D cc_shadowMap;\n  uniform highp sampler2D cc_spotShadowMap;\n  #if CC_SUPPORT_CASCADED_SHADOW_MAP\n  #else\n  #endif\n#endif\n#if CC_USE_FOG != 4\n#endif\n#if CC_USE_LIGHT_PROBE\n  #if CC_USE_LIGHT_PROBE\n  #endif\n#endif\n#if CC_USE_REFLECTION_PROBE\n  layout(std140) uniform CCLocal {\n    highp mat4 cc_matWorld;\n    highp mat4 cc_matWorldIT;\n    highp vec4 cc_lightingMapUVParam;\n    highp vec4 cc_localShadowBias;\n    highp vec4 cc_reflectionProbeData1;\n    highp vec4 cc_reflectionProbeData2;\n    highp vec4 cc_reflectionProbeBlendData1;\n    highp vec4 cc_reflectionProbeBlendData2;\n  };\n#endif\n#if CC_USE_LIGHTMAP && !CC_FORWARD_ADD\n  uniform sampler2D cc_lightingMap;\n#endif\nlayout(std140) uniform Constants {\n  vec4 albedo;\n  vec4 albedoScaleAndCutoff;\n  vec4 pbrParams;\n};\n#if USE_ALBEDO_MAP\n  uniform sampler2D albedoMap;\n#endif\n#if USE_ALPHA_TEST\n#endif\n#define CC_SURFACES_FRAGMENT_MODIFY_BASECOLOR_AND_TRANSPARENCY\n#define CC_SURFACES_FRAGMENT_ALPHA_CLIP_ONLY\nvoid SurfacesFragmentAlphaClipOnly()\n{\n  #if USE_ALPHA_TEST\n    float alpha = albedo.ALPHA_TEST_CHANNEL;\n    #if USE_VERTEX_COLOR\n      alpha *= FSInput_vertexColor.a;\n    #endif\n    #if USE_ALBEDO_MAP\n      alpha = texture(albedoMap, ALBEDO_UV).ALPHA_TEST_CHANNEL;\n    #endif\n    if (alpha < albedoScaleAndCutoff.w) discard;\n  #endif\n}\n#define CC_SURFACES_FRAGMENT_MODIFY_WORLD_NORMAL\n#define CC_SURFACES_FRAGMENT_MODIFY_EMISSIVE\n#define CC_SURFACES_FRAGMENT_MODIFY_PBRPARAMS\nin highp vec2 v_clip_depth;\nlayout(location = 0) out vec4 fragColorX;\nvoid main () {\n#ifdef CC_SURFACES_FRAGMENT_ALPHA_CLIP_ONLY\n  SurfacesFragmentAlphaClipOnly();\n#endif\n  highp float clipDepth = v_clip_depth.x / v_clip_depth.y * 0.5 + 0.5;\n  #if CC_SHADOWMAP_USE_LINEAR_DEPTH\n    if (IS_SPOT_LIGHT(cc_shadowLPNNInfo.x)) {\n      clipDepth = CCGetLinearDepth(FSInput_worldPos.xyz);\n    }\n  #endif\n  #if CC_SHADOWMAP_FORMAT == 1\n    fragColorX = packDepthToRGBA(clipDepth);\n  #else\n    fragColorX = vec4(clipDepth, 1.0, 1.0, 1.0);\n  #endif\n}"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}, {"name": "CCShadow", "defines": []}, {"name": "CCCSM", "defines": ["CC_SUPPORT_CASCADED_SHADOW_MAP"]}], "samplerTextures": [{"name": "cc_environment", "defines": []}, {"name": "cc_diffuseMap", "defines": ["CC_USE_IBL", "CC_USE_DIFFUSEMAP"]}, {"name": "cc_shadowMap", "defines": ["CC_RECEIVE_SHADOW"]}, {"name": "cc_spotShadowMap", "defines": ["CC_RECEIVE_SHADOW"]}], "buffers": [], "images": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": ["!USE_INSTANCING"]}, {"name": "CCMorph", "defines": ["CC_USE_MORPH"]}, {"name": "CCSkinningTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinningAnimation", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "CCSkinning", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "!CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "CCForwardLight", "defines": ["CC_FORWARD_ADD", "CC_ENABLE_CLUSTERED_LIGHT_CULLING"]}, {"name": "CCSH", "defines": ["CC_USE_LIGHT_PROBE", "!USE_INSTANCING"]}], "samplerTextures": [{"name": "cc_PositionDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_POSITION"]}, {"name": "cc_NormalDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_NORMAL"]}, {"name": "cc_TangentDisplacements", "defines": ["CC_USE_MORPH", "CC_MORPH_TARGET_HAS_TANGENT"]}, {"name": "cc_jointTexture", "defines": ["CC_USE_SKINNING", "CC_USE_BAKED_ANIMATION"]}, {"name": "cc_realtimeJoint", "defines": ["CC_USE_SKINNING", "!CC_USE_BAKED_ANIMATION", "CC_USE_REAL_TIME_JOINT_TEXTURE"]}, {"name": "cc_reflectionProbeCubemap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbePlanarMap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbeDataMap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_reflectionProbeBlendCubemap", "defines": ["CC_USE_REFLECTION_PROBE"]}, {"name": "cc_lightingMap", "defines": ["CC_USE_LIGHTMAP", "!CC_FORWARD_ADD"]}], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 93, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 123}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_REFLECTION_PROBE", "type": "number", "range": [0, 3]}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean", "default": 0}, {"name": "CC_USE_MORPH", "type": "boolean"}, {"name": "CC_FORWARD_ADD", "type": "boolean"}, {"name": "CC_USE_FOG", "type": "number", "range": [0, 4]}, {"name": "CC_USE_ACCURATE_FOG", "type": "boolean"}, {"name": "CC_MORPH_TARGET_COUNT", "type": "number", "range": [2, 8]}, {"name": "CC_MORPH_TARGET_HAS_POSITION", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_NORMAL", "type": "boolean"}, {"name": "CC_MORPH_TARGET_HAS_TANGENT", "type": "boolean"}, {"name": "CC_MORPH_PRECOMPUTED", "type": "boolean"}, {"name": "CC_USE_REAL_TIME_JOINT_TEXTURE", "type": "boolean"}, {"name": "CC_DISABLE_STRUCTURE_IN_FRAGMENT_SHADER", "type": "number", "range": [0, 1]}, {"name": "CC_PIPELINE_TYPE", "type": "number", "range": [0, 1]}, {"name": "CC_FORCE_FORWARD_SHADING", "type": "boolean"}, {"name": "CC_ENABLE_CLUSTERED_LIGHT_CULLING", "type": "number", "range": [0, 3]}, {"name": "CC_SUPPORT_CASCADED_SHADOW_MAP", "type": "boolean"}, {"name": "CC_USE_IBL", "type": "number", "range": [0, 2]}, {"name": "CC_USE_DIFFUSEMAP", "type": "number", "range": [0, 2]}, {"name": "USE_ALBEDO_MAP", "type": "boolean"}, {"name": "ALBEDO_UV", "type": "string", "options": ["v_uv", "v_uv1"]}, {"name": "USE_ALPHA_TEST", "type": "boolean"}, {"name": "ALPHA_TEST_CHANNEL", "type": "string", "options": ["a", "r"]}, {"name": "USE_VERTEX_COLOR", "type": "boolean"}, {"name": "CC_SHADOWMAP_USE_LINEAR_DEPTH", "type": "boolean"}, {"name": "CC_SHADOWMAP_FORMAT", "type": "number", "range": [0, 3]}]}], [{"name": "opaque", "passes": [{"program": "../bundleOne/common/effect/surface-effect|standard-vs|standard-fs", "properties": {"mainTexture": {"value": "grey", "type": 28, "handleInfo": ["albedoMap", 0, 28]}, "mainColor": {"linear": true, "type": 16, "value": [1, 1, 1, 1], "handleInfo": ["albedo", 0, 16]}, "albedoScale": {"type": 15, "value": [1, 1, 1], "handleInfo": ["albedoScaleAnd<PERSON><PERSON>ff", 0, 15]}, "alphaThreshold": {"type": 13, "value": [0.5], "handleInfo": ["albedoScaleAnd<PERSON><PERSON>ff", 3, 13]}, "roughness": {"type": 13, "value": [0.8], "handleInfo": ["pbrParams", 1, 13]}, "metallic": {"type": 13, "value": [0.6], "handleInfo": ["pbrParams", 2, 13]}, "albedoMap": {"type": 28, "value": "grey"}, "albedo": {"type": 16, "value": [1, 1, 1, 1]}, "albedoScaleAndCutoff": {"type": 16, "value": [1, 1, 1, 0.5]}, "pbrParams": {"type": 16, "value": [0, 0.8, 0.6, 0]}}}, {"phase": "forward-add", "propertyIndex": 0, "program": "../bundleOne/common/effect/surface-effect|standard-vs|standard-fs", "embeddedMacros": {"CC_FORWARD_ADD": true}, "blendState": {"targets": [{"blend": true, "blendSrc": 1, "blendDst": 1, "blendSrcAlpha": 0, "blendDstAlpha": 1}]}, "depthStencilState": {"depthFunc": 2, "depthTest": true, "depthWrite": false}}, {"phase": "shadow-caster", "propertyIndex": 0, "program": "../bundleOne/common/effect/surface-effect|shadow-caster-vs|shadow-caster-fs", "rasterizerState": {"cullMode": 1}, "properties": {"mainColor": {"type": 16, "value": [1, 1, 1, 1], "handleInfo": ["albedo", 0, 16]}, "albedoScale": {"type": 15, "value": [1, 1, 1], "handleInfo": ["albedoScaleAnd<PERSON><PERSON>ff", 0, 15]}, "alphaThreshold": {"type": 13, "value": [0.5], "handleInfo": ["albedoScaleAnd<PERSON><PERSON>ff", 3, 13]}, "mainTexture": {"value": "grey", "type": 28, "handleInfo": ["albedoMap", 0, 28]}, "albedo": {"type": 16, "value": [1, 1, 1, 1]}, "albedoScaleAndCutoff": {"type": 16, "value": [1, 1, 1, 0.5]}, "albedoMap": {"type": 28, "value": "grey"}}}]}, {"name": "transparent", "passes": [{"program": "../bundleOne/common/effect/surface-effect|standard-vs|standard-fs", "embeddedMacros": {"CC_FORCE_FORWARD_SHADING": true}, "blendState": {"targets": [{"blend": true, "blendSrc": 2, "blendDst": 4, "blendDstAlpha": 4}]}, "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"mainTexture": {"value": "grey", "type": 28, "handleInfo": ["albedoMap", 0, 28]}, "mainColor": {"linear": true, "type": 16, "value": [1, 1, 1, 1], "handleInfo": ["albedo", 0, 16]}, "albedoScale": {"type": 15, "value": [1, 1, 1], "handleInfo": ["albedoScaleAnd<PERSON><PERSON>ff", 0, 15]}, "alphaThreshold": {"type": 13, "value": [0.5], "handleInfo": ["albedoScaleAnd<PERSON><PERSON>ff", 3, 13]}, "roughness": {"type": 13, "value": [0.8], "handleInfo": ["pbrParams", 1, 13]}, "metallic": {"type": 13, "value": [0.6], "handleInfo": ["pbrParams", 2, 13]}, "albedoMap": {"type": 28, "value": "grey"}, "albedo": {"type": 16, "value": [1, 1, 1, 1]}, "albedoScaleAndCutoff": {"type": 16, "value": [1, 1, 1, 0.5]}, "pbrParams": {"type": 16, "value": [0, 0.8, 0.6, 0]}}}, {"phase": "forward-add", "propertyIndex": 0, "program": "../bundleOne/common/effect/surface-effect|standard-vs|standard-fs", "embeddedMacros": {"CC_FORWARD_ADD": true}, "blendState": {"targets": [{"blend": true, "blendSrc": 1, "blendDst": 1, "blendSrcAlpha": 0, "blendDstAlpha": 1}]}, "depthStencilState": {"depthFunc": 2, "depthTest": true, "depthWrite": false}}, {"phase": "shadow-caster", "propertyIndex": 0, "program": "../bundleOne/common/effect/surface-effect|shadow-caster-vs|shadow-caster-fs", "rasterizerState": {"cullMode": 1}, "properties": {"mainColor": {"type": 16, "value": [1, 1, 1, 1], "handleInfo": ["albedo", 0, 16]}, "albedoScale": {"type": 15, "value": [1, 1, 1], "handleInfo": ["albedoScaleAnd<PERSON><PERSON>ff", 0, 15]}, "alphaThreshold": {"type": 13, "value": [0.5], "handleInfo": ["albedoScaleAnd<PERSON><PERSON>ff", 3, 13]}, "mainTexture": {"value": "grey", "type": 28, "handleInfo": ["albedoMap", 0, 28]}, "albedo": {"type": 16, "value": [1, 1, 1, 1]}, "albedoScaleAndCutoff": {"type": 16, "value": [1, 1, 1, 0.5]}, "albedoMap": {"type": 28, "value": "grey"}}}]}]]], 0, 0, [], [], []]