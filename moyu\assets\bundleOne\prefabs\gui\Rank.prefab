[{"__type__": "cc.Prefab", "_name": "Rank", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Rank", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 12}, {"__id__": 186}, {"__id__": 209}], "_active": true, "_components": [{"__id__": 336}, {"__id__": 338}, {"__id__": 340}], "_prefab": {"__id__": 342}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "142393a8-2641-4775-a79c-cbfe1bd9e5ca", "__expectedType__": "cc.Prefab"}, "fileId": "a0daVw8DRLi6ToMaTA0VS2", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "4bd7+FLBlMqJQRLHhjl3Px", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 5}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_name"], "value": "mask"}, {"__type__": "cc.TargetInfo", "localID": ["a0daVw8DRLi6ToMaTA0VS2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 11}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 750, "height": 1334}}, {"__type__": "cc.TargetInfo", "localID": ["77N2cid5pKDpXplRH/AWEU"]}, {"__type__": "cc.Node", "_name": "contenArea", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}], "_active": true, "_components": [{"__id__": 204}, {"__id__": 206}], "_prefab": {"__id__": 208}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "rankBg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 14}, {"__id__": 83}, {"__id__": 123}], "_active": true, "_components": [{"__id__": 180}, {"__id__": 182}, {"__id__": 184}], "_prefab": {"__id__": 203}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "rankTitle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 15}, {"__id__": 36}, {"__id__": 57}], "_active": true, "_components": [{"__id__": 78}, {"__id__": 80}], "_prefab": {"__id__": 82}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 462.043, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 14}, "_prefab": {"__id__": 16}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 15}, "asset": {"__uuid__": "954f23b2-188c-4f23-b579-229a1820a818", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 17}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "66tDuSzb9JUY+pds5ynjaU", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 18}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}, {"__id__": 23}, {"__id__": 25}, {"__id__": 27}, {"__id__": 28}, {"__id__": 29}, {"__id__": 30}, {"__id__": 31}, {"__id__": 32}, {"__id__": 34}, {"__id__": 35}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_name"], "value": "rank"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -164.703125, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_string"], "value": "Rank"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 26}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 56.3125, "height": 36.76}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_fontSize"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_actualFontSize"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 249, "g": 242, "b": 211, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_isBold"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_lineHeight"], "value": 26}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 33}, "propertyPath": ["_dataID"], "value": "Rank"}, {"__type__": "cc.TargetInfo", "localID": ["807dKXf5tHrJtEdIFnHMo0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_enableOutline"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 24}, "propertyPath": ["_outlineColor"], "value": {"__type__": "cc.Color", "r": 134, "g": 99, "b": 64, "a": 255}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 14}, "_prefab": {"__id__": 37}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 36}, "asset": {"__uuid__": "954f23b2-188c-4f23-b579-229a1820a818", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 38}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "0eE8kQCfBGnYr7TC1BIqQ/", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 39}, {"__id__": 41}, {"__id__": 42}, {"__id__": 43}, {"__id__": 44}, {"__id__": 46}, {"__id__": 48}, {"__id__": 49}, {"__id__": 50}, {"__id__": 51}, {"__id__": 52}, {"__id__": 53}, {"__id__": 55}, {"__id__": 56}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 40}, "propertyPath": ["_name"], "value": "name"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 40}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -2.859375, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 40}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 40}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_string"], "value": "Name"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 67.375, "height": 36.76}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_fontSize"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_actualFontSize"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 249, "g": 242, "b": 211, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_lineHeight"], "value": 26}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_isBold"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 54}, "propertyPath": ["_dataID"], "value": "Name"}, {"__type__": "cc.TargetInfo", "localID": ["807dKXf5tHrJtEdIFnHMo0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_enableOutline"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 45}, "propertyPath": ["_outlineColor"], "value": {"__type__": "cc.Color", "r": 134, "g": 99, "b": 64, "a": 255}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 14}, "_prefab": {"__id__": 58}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 57}, "asset": {"__uuid__": "954f23b2-188c-4f23-b579-229a1820a818", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 59}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e8LBP6AKtNjIRyctlFuR37", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 60}, {"__id__": 62}, {"__id__": 63}, {"__id__": 64}, {"__id__": 65}, {"__id__": 67}, {"__id__": 69}, {"__id__": 70}, {"__id__": 71}, {"__id__": 72}, {"__id__": 73}, {"__id__": 74}, {"__id__": 76}, {"__id__": 77}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 61}, "propertyPath": ["_name"], "value": "score"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 61}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 161.84375, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 61}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 61}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_string"], "value": "Score"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 68}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 62.03125, "height": 36.76}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_fontSize"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_actualFontSize"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 249, "g": 242, "b": 211, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_lineHeight"], "value": 26}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_isBold"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 75}, "propertyPath": ["_dataID"], "value": "Score"}, {"__type__": "cc.TargetInfo", "localID": ["807dKXf5tHrJtEdIFnHMo0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_enableOutline"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 66}, "propertyPath": ["_outlineColor"], "value": {"__type__": "cc.Color", "r": 134, "g": 99, "b": 64, "a": 255}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 79}, "_contentSize": {"__type__": "cc.Size", "width": 405.71875, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bLlGA4khEt7BMYF6Z+JN8"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 81}, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 10, "_paddingRight": 10, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 100, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16CO3vVxNFnre6pOrilD6I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10jbW62INGqJ4BANj+JK4q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 84}, {"__id__": 102}], "_active": true, "_components": [{"__id__": 116}, {"__id__": 118}, {"__id__": 99}, {"__id__": 120}], "_prefab": {"__id__": 122}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "scrollBar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 83}, "_children": [{"__id__": 85}], "_active": true, "_components": [{"__id__": 91}, {"__id__": 93}, {"__id__": 95}, {"__id__": 97}], "_prefab": {"__id__": 115}, "_lpos": {"__type__": "cc.Vec3", "x": 302.479, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 84}, "_children": [], "_active": true, "_components": [{"__id__": 86}, {"__id__": 88}], "_prefab": {"__id__": 90}, "_lpos": {"__type__": "cc.Vec3", "x": -11, "y": -31.25, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 85}, "_enabled": true, "__prefab": {"__id__": 87}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 156.25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c25THWmcpLqLEt+h8o48lO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 85}, "_enabled": true, "__prefab": {"__id__": 89}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_spriteFrame": {"__uuid__": "afc47931-f066-46b0-90be-9fe61f213428@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bb3Wn5785IdoVjMrupx+fg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d2Xi/EWblO3pahS1m/vRcT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 84}, "_enabled": true, "__prefab": {"__id__": 92}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 804}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58SD2PHP9GjIFfPclg5ukV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 84}, "_enabled": true, "__prefab": {"__id__": 94}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_spriteFrame": {"__uuid__": "ffb88a8f-af62-48f4-8f1d-3cb606443a43@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdjE8UuElGXIjwhw2F1wWO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 84}, "_enabled": true, "__prefab": {"__id__": 96}, "_alignFlags": 37, "_target": null, "_left": 0, "_right": 47.521000000000015, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 250, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73SM+Sz4FP0Jw955dv7oFj"}, {"__type__": "<PERSON>.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 84}, "_enabled": true, "__prefab": {"__id__": 98}, "_scrollView": {"__id__": 99}, "_handle": {"__id__": 88}, "_direction": 1, "_enableAutoHide": false, "_autoHideTime": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4erbe/bs9JlJJrjgSKwm2S"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 100}, "bounceDuration": 0.23, "brake": 0.75, "elastic": true, "inertia": true, "horizontal": false, "vertical": true, "cancelInnerEvents": true, "scrollEvents": [], "_content": {"__id__": 101}, "_horizontalScrollBar": null, "_verticalScrollBar": {"__id__": 97}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23qpqHsCNHKZMXqzxVIHRO"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 102}, "_children": [], "_active": true, "_components": [{"__id__": 110}, {"__id__": 112}], "_prefab": {"__id__": 114}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 83}, "_children": [{"__id__": 101}], "_active": true, "_components": [{"__id__": 103}, {"__id__": 105}, {"__id__": 107}], "_prefab": {"__id__": 109}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 11.971, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 102}, "_enabled": true, "__prefab": {"__id__": 104}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 800}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bZwnHtfhLJK6Sb5ZQP61J"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 102}, "_enabled": true, "__prefab": {"__id__": 106}, "_type": 0, "_inverted": false, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81fTqedRVOJaRUIUiVBJ1b"}, {"__type__": "cc.Graphics", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 102}, "_enabled": true, "__prefab": {"__id__": 108}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_lineWidth": 1, "_strokeColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_lineJoin": 2, "_lineCap": 0, "_fillColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 0}, "_miterLimit": 10, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2c8nBV6IpAXLs7vi5Br7xN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9eq626edtDrIXo1jSfPe0a", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 111}, "_contentSize": {"__type__": "cc.Size", "width": 560, "height": -8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7KQWj2GRIjLpeFdn+fPIw"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 113}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 8, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b22V7J62hLQ40yWOEvYqAS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b4hbaw3KBAc7ymVmStfof1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7bpd7XnaRI5oVoaa5QhpNV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 117}, "_contentSize": {"__type__": "cc.Size", "width": 700, "height": 804}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdKhaDalNJuqRAmc2ADRVU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 119}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": null, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5f96mcq4RHG7h34oWeO4ff"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": {"__id__": 121}, "_alignFlags": 45, "_target": null, "_left": -50, "_right": -50, "_top": 100, "_bottom": 140, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f2EG8TWehDzJ3BUsr83WHn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "131gr9LwtBCIWLb3n4fgBs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "selfRankBg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 124}, {"__id__": 143}, {"__id__": 156}], "_active": true, "_components": [{"__id__": 173}, {"__id__": 175}, {"__id__": 177}], "_prefab": {"__id__": 179}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -443.757, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 123}, "_prefab": {"__id__": 125}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 124}, "asset": {"__uuid__": "8728975f-5233-40d6-9a87-08626aba6287", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 126}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "deUMvnXEtOwp3OI+7rhauT", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 127}, {"__id__": 129}, {"__id__": 130}, {"__id__": 131}, {"__id__": 132}, {"__id__": 134}, {"__id__": 136}, {"__id__": 137}, {"__id__": 138}, {"__id__": 139}, {"__id__": 140}, {"__id__": 141}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 128}, "propertyPath": ["_name"], "value": "noRank"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 128}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -21.514, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 128}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 128}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 133}, "propertyPath": ["_string"], "value": "VMLabelLanguage_White"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 135}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 75.1953125, "height": 32.76}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 133}, "propertyPath": ["_fontSize"], "value": 22}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 133}, "propertyPath": ["_actualFontSize"], "value": 22}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 133}, "propertyPath": ["_isBold"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 133}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 249, "g": 242, "b": 211, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 133}, "propertyPath": ["_lineHeight"], "value": 26}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 142}, "propertyPath": ["_dataID"], "value": "NoRank"}, {"__type__": "cc.TargetInfo", "localID": ["09i06HqvBP2ZaQ4Pk+p3Mt"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 123}, "_prefab": {"__id__": 144}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 143}, "asset": {"__uuid__": "97e6505d-4fd1-46e0-a7c6-cae90c965816", "__expectedType__": "cc.Prefab"}, "fileId": "acbcPqpZpPo5/a/YKQdN4X", "instance": {"__id__": 145}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e7BJhp2+5GeLXw4ZZ0ZoA3", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 146}, {"__id__": 148}, {"__id__": 150}, {"__id__": 152}, {"__id__": 154}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 147}, "propertyPath": ["_name"], "value": "selfRankCell"}, {"__type__": "cc.TargetInfo", "localID": ["acbcPqpZpPo5/a/YKQdN4X"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 149}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -21.513999999999953, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["acbcPqpZpPo5/a/YKQdN4X"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 151}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["acbcPqpZpPo5/a/YKQdN4X"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 153}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["acbcPqpZpPo5/a/YKQdN4X"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 155}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 560, "height": 90}}, {"__type__": "cc.TargetInfo", "localID": ["46Rpbz0eJJiaodu6kJAN0/"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 123}, "_prefab": {"__id__": 157}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 156}, "asset": {"__uuid__": "8728975f-5233-40d6-9a87-08626aba6287", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 158}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "a3vgIykpZEj6+yvsX6/wU+", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 159}, {"__id__": 161}, {"__id__": 162}, {"__id__": 163}, {"__id__": 164}, {"__id__": 166}, {"__id__": 168}, {"__id__": 169}, {"__id__": 170}, {"__id__": 171}, {"__id__": 172}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 160}, "propertyPath": ["_name"], "value": "selfLbl"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 160}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 44.952, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 160}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 160}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_string"], "value": "VMLabelLanguage_White"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 167}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 267.28125, "height": 32.76}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_fontSize"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_actualFontSize"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_isBold"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 249, "g": 242, "b": 211, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_lineHeight"], "value": 26}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 174}, "_contentSize": {"__type__": "cc.Size", "width": 560, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58WrjXGTNH2YI0L0L6mCeH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 176}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f97288fe-88cd-4afa-ae9f-726e00639f98@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dbYU1ZzhDLIDfJ889zuMh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 178}, "_alignFlags": 20, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 13.242999999999995, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a9yypQnJ9KtJhdfCOrhcr2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0eCbBiAwlLfq53b1tY7b6L", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 181}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 1044}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04vPGIAwdBwrnbZlZbsLxn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 183}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "eb4b3812-eabc-4735-9185-a9f27ae7e561@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9rRUd6D5IuotcHs5X8Zao"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 185}, "watchPath": "*.rankType", "foreachChildMode": false, "condition": 0, "foreachChildType": 0, "valueA": 0, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 186}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4KBNtJpVENoyjCDYlND2o"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 187}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 186}, "asset": {"__uuid__": "80a6dff8-dc06-4f9d-a2c8-03170daa1ddd", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 188}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "023NJUrCRNrarxwUFlne9t", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 189}], "propertyOverrides": [{"__id__": 193}, {"__id__": 195}, {"__id__": 196}, {"__id__": 197}, {"__id__": 198}, {"__id__": 199}, {"__id__": 201}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 190}, "components": [{"__id__": 191}]}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 186}}, "node": {"__id__": 186}, "_enabled": true, "__prefab": {"__id__": 192}, "_alignFlags": 21, "_target": null, "_left": 0, "_right": 0, "_top": 684.818, "_bottom": 611.3820000000001, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 50.4, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05gUWjXPxAB5FzLo9zyU9X"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 194}, "propertyPath": ["_name"], "value": "lblLoading"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 194}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -36.71799999999996, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 194}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 194}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 194}, "propertyPath": ["_active"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 200}, "propertyPath": ["_string"], "value": ""}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 202}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 58.40625, "height": 37.799999999999955}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a4Lq5lA71FOp6S6jZeyy3o", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 205}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1+W3Ms5dLGpm8s8KDEl3x"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 207}, "_alignFlags": 0, "_target": null, "_left": 490, "_right": 490, "_top": 910, "_bottom": 910, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10k8NTtQFGb4StfF5ozUNh"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0cbo3bj7dJ17cTA4AjdN3A", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "topButtom", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 210}, {"__id__": 271}, {"__id__": 318}], "_active": true, "_components": [{"__id__": 333}], "_prefab": {"__id__": 335}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 519.478, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 209}, "_prefab": {"__id__": 211}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 210}, "asset": {"__uuid__": "62da37bb-2377-4788-8c7d-bec59bdca136", "__expectedType__": "cc.Prefab"}, "fileId": "30Ahna/hFADJL5JEyiP8Kc", "instance": {"__id__": 212}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "c8XaePX/JCfrYC9vf5hxiX", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 213}, {"__id__": 218}], "propertyOverrides": [{"__id__": 226}, {"__id__": 228}, {"__id__": 230}, {"__id__": 232}, {"__id__": 234}, {"__id__": 236}, {"__id__": 238}, {"__id__": 240}, {"__id__": 242}, {"__id__": 244}, {"__id__": 246}, {"__id__": 248}, {"__id__": 250}, {"__id__": 252}, {"__id__": 254}, {"__id__": 256}, {"__id__": 258}, {"__id__": 260}, {"__id__": 262}, {"__id__": 264}, {"__id__": 266}, {"__id__": 268}, {"__id__": 269}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 214}, "components": [{"__id__": 215}]}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 210}}, "node": {"__id__": 216}, "_enabled": true, "__prefab": {"__id__": 217}, "templateMode": false, "watchPath": "", "labelType": "cc.Label", "watchPathArr": [], "_dataID": "Country", "_id": ""}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.CompPrefabInfo", "fileId": "8a9ylzPmhKvaTkpCuD0Qhx"}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 219}, "components": [{"__id__": 220}, {"__id__": 222}, {"__id__": 224}]}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 210}}, "node": {"__id__": 210}, "_enabled": true, "__prefab": {"__id__": 221}, "watchPath": "*.rankType", "foreachChildMode": false, "condition": 0, "foreachChildType": 0, "valueA": 2, "valueB": 0, "valueAction": 6, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 210}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11Z1/2vEpML4nIYtDzB9Gg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 210}}, "node": {"__id__": 210}, "_enabled": true, "__prefab": {"__id__": 223}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8bZ7oFRsVGup2xkZRBhwY+"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 210}}, "node": {"__id__": 210}, "_enabled": true, "__prefab": {"__id__": 225}, "watchPath": "*.rankType", "foreachChildMode": false, "condition": 1, "foreachChildType": 0, "valueA": 2, "valueB": 0, "valueAction": 3, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 216}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9fSPLR1cVBTq21rZP8N97M"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 227}, "propertyPath": ["_name"], "value": "countryBtn"}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 229}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -167.161, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 231}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 233}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 235}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 140, "height": 64}}, {"__type__": "cc.TargetInfo", "localID": ["b2FpfgEc1Py4FLQrPbDjfF"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 237}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "9986dc05-8103-4203-a055-57a05a3d5458@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["daO3pUYmlKVIgsdc588k+9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 239}, "propertyPath": ["_sizeMode"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["daO3pUYmlKVIgsdc588k+9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 241}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 249, "g": 242, "b": 211, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 243}, "propertyPath": ["_string"], "value": "Country"}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 245}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 91.8515625, "height": 36.76}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 247}, "propertyPath": ["_transition"], "value": 2}, {"__type__": "cc.TargetInfo", "localID": ["43UvszQV9LRKWvhBXHpprD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 249}, "propertyPath": ["_target"], "value": {"__id__": 210}}, {"__type__": "cc.TargetInfo", "localID": ["43UvszQV9LRKWvhBXHpprD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 251}, "propertyPath": ["_disabledSprite"], "value": {"__uuid__": "b6ab13fb-ec46-43de-abe4-e93e07292fcd@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["43UvszQV9LRKWvhBXHpprD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 253}, "propertyPath": ["_hoverSprite"], "value": {"__uuid__": "9986dc05-8103-4203-a055-57a05a3d5458@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["43UvszQV9LRKWvhBXHpprD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 255}, "propertyPath": ["_pressedSprite"], "value": {"__uuid__": "9986dc05-8103-4203-a055-57a05a3d5458@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["43UvszQV9LRKWvhBXHpprD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 257}, "propertyPath": ["_normalSprite"], "value": {"__uuid__": "9986dc05-8103-4203-a055-57a05a3d5458@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["43UvszQV9LRKWvhBXHpprD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 259}, "propertyPath": ["_enableOutline"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 261}, "propertyPath": ["_outlineColor"], "value": {"__type__": "cc.Color", "r": 100, "g": 75, "b": 52, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 263}, "propertyPath": ["_fontSize"], "value": 26}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 265}, "propertyPath": ["_actualFontSize"], "value": 26}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 267}, "propertyPath": ["_lineHeight"], "value": 26}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 267}, "propertyPath": ["_isBold"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 270}, "propertyPath": ["_name"], "value": "VMcountry"}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 209}, "_prefab": {"__id__": 272}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 271}, "asset": {"__uuid__": "62da37bb-2377-4788-8c7d-bec59bdca136", "__expectedType__": "cc.Prefab"}, "fileId": "30Ahna/hFADJL5JEyiP8Kc", "instance": {"__id__": 273}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "105dd9ZV9J37OOMKpXskx0", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 274}, {"__id__": 283}], "propertyOverrides": [{"__id__": 287}, {"__id__": 289}, {"__id__": 290}, {"__id__": 291}, {"__id__": 292}, {"__id__": 294}, {"__id__": 296}, {"__id__": 297}, {"__id__": 299}, {"__id__": 300}, {"__id__": 302}, {"__id__": 304}, {"__id__": 305}, {"__id__": 306}, {"__id__": 307}, {"__id__": 308}, {"__id__": 309}, {"__id__": 310}, {"__id__": 311}, {"__id__": 312}, {"__id__": 313}, {"__id__": 314}, {"__id__": 315}, {"__id__": 317}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 275}, "components": [{"__id__": 276}, {"__id__": 278}, {"__id__": 280}]}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 271}}, "node": {"__id__": 271}, "_enabled": true, "__prefab": {"__id__": 277}, "watchPath": "*.rankType", "foreachChildMode": false, "condition": 0, "foreachChildType": 0, "valueA": 3, "valueB": 0, "valueAction": 6, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 271}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65GJeFsK9FRrDHZ7UDXT4k"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 271}}, "node": {"__id__": 271}, "_enabled": true, "__prefab": {"__id__": 279}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 1, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0fzf6ObcBCEo8NhYMrACw1"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 271}}, "node": {"__id__": 271}, "_enabled": true, "__prefab": {"__id__": 281}, "watchPath": "*.rankType", "foreachChildMode": false, "condition": 1, "foreachChildType": 0, "valueA": 3, "valueB": 0, "valueAction": 3, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 195, "g": 153, "b": 112, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 282}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e8ynout0lATbv/m/RL/s1K"}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 284}, "components": [{"__id__": 285}]}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 271}}, "node": {"__id__": 282}, "_enabled": true, "__prefab": {"__id__": 286}, "templateMode": false, "watchPath": "", "labelType": "cc.Label", "watchPathArr": [], "_dataID": "World", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aevStwc6xHvaePqmWEIIlK"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 288}, "propertyPath": ["_name"], "value": "worldBtn"}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 288}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -19.245, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 288}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 288}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 293}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 140, "height": 64}}, {"__type__": "cc.TargetInfo", "localID": ["b2FpfgEc1Py4FLQrPbDjfF"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 295}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "9986dc05-8103-4203-a055-57a05a3d5458@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["daO3pUYmlKVIgsdc588k+9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 295}, "propertyPath": ["_sizeMode"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 298}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 249, "g": 242, "b": 211, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 298}, "propertyPath": ["_string"], "value": "World"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 301}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 71.1328125, "height": 36.76}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 303}, "propertyPath": ["_transition"], "value": 2}, {"__type__": "cc.TargetInfo", "localID": ["43UvszQV9LRKWvhBXHpprD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 303}, "propertyPath": ["_target"], "value": {"__id__": 271}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 303}, "propertyPath": ["_normalSprite"], "value": {"__uuid__": "9986dc05-8103-4203-a055-57a05a3d5458@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 303}, "propertyPath": ["_pressedSprite"], "value": {"__uuid__": "9986dc05-8103-4203-a055-57a05a3d5458@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 303}, "propertyPath": ["_hoverSprite"], "value": {"__uuid__": "9986dc05-8103-4203-a055-57a05a3d5458@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 303}, "propertyPath": ["_disabledSprite"], "value": {"__uuid__": "b6ab13fb-ec46-43de-abe4-e93e07292fcd@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 298}, "propertyPath": ["_enableOutline"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 298}, "propertyPath": ["_outlineColor"], "value": {"__type__": "cc.Color", "r": 100, "g": 75, "b": 52, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 298}, "propertyPath": ["_fontSize"], "value": 26}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 298}, "propertyPath": ["_actualFontSize"], "value": 26}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 298}, "propertyPath": ["_lineHeight"], "value": 26}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 298}, "propertyPath": ["_isBold"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 316}, "propertyPath": ["_name"], "value": "vmWorld"}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 295}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 249, "g": 242, "b": 211, "a": 255}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 209}, "_prefab": {"__id__": 319}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 318}, "asset": {"__uuid__": "381309c0-851c-4f2a-8789-4a693f87ef38", "__expectedType__": "cc.Prefab"}, "fileId": "46L362HwxAyYxM0TWgugL+", "instance": {"__id__": 320}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "0fQw7EDY9M37WljUEq8lER", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 321}], "propertyOverrides": [{"__id__": 325}, {"__id__": 327}, {"__id__": 329}, {"__id__": 331}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 322}, "components": [{"__id__": 323}]}, {"__type__": "cc.TargetInfo", "localID": ["46L362HwxAyYxM0TWgugL+"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 318}}, "node": {"__id__": 318}, "_enabled": true, "__prefab": {"__id__": 324}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": -20.520000000000003, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a6hWOMj/pICa0p3jx6GjoF"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 326}, "propertyPath": ["_name"], "value": "closeBtn"}, {"__type__": "cc.TargetInfo", "localID": ["46L362HwxAyYxM0TWgugL+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 328}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 271.21, "y": -18.020000000000003, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["46L362HwxAyYxM0TWgugL+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 330}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["46L362HwxAyYxM0TWgugL+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 332}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["46L362HwxAyYxM0TWgugL+"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 209}, "_enabled": true, "__prefab": {"__id__": 334}, "_contentSize": {"__type__": "cc.Size", "width": 330, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22Xhyh37RC5Z5anFnCj0AW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "98eDM1cbdCsrir3h8SBggm", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 337}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77N2cid5pKDpXplRH/AWEU"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 339}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 2, "_originalHeight": 2, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63zNQq8NlBQ5QWzOJ4Kgjs"}, {"__type__": "5b916sxTpdNP7k/xIXZ9qrs", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 341}, "scrollView": {"__id__": 99}, "rankCell": {"__uuid__": "97e6505d-4fd1-46e0-a7c6-cae90c965816", "__expectedType__": "cc.Prefab"}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbMwYJv65JhbUmjGn1VW72"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a0daVw8DRLi6ToMaTA0VS2", "instance": null, "targetOverrides": [{"__id__": 343}, {"__id__": 345}, {"__id__": 347}, {"__id__": 349}], "nestedPrefabInstanceRoots": [{"__id__": 318}, {"__id__": 271}, {"__id__": 210}, {"__id__": 186}, {"__id__": 156}, {"__id__": 143}, {"__id__": 124}, {"__id__": 57}, {"__id__": 36}, {"__id__": 15}, {"__id__": 2}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 276}, "sourceInfo": null, "propertyPath": [], "target": {"__id__": 271}, "targetInfo": {"__id__": 344}}, {"__type__": "cc.TargetInfo", "localID": ["65GJeFsK9FRrDHZ7UDXT4k"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 224}, "sourceInfo": null, "propertyPath": ["watchNodes", "0"], "target": {"__id__": 210}, "targetInfo": {"__id__": 346}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 280}, "sourceInfo": null, "propertyPath": [], "target": {"__id__": 271}, "targetInfo": {"__id__": 348}}, {"__type__": "cc.TargetInfo", "localID": ["e8ynout0lATbv/m/RL/s1K"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 280}, "sourceInfo": null, "propertyPath": ["watchNodes", "0"], "target": {"__id__": 271}, "targetInfo": {"__id__": 350}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}]