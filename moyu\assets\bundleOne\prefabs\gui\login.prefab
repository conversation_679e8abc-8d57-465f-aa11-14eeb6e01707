[{"__type__": "cc.Prefab", "_name": "login", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "login", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 12}], "_active": true, "_components": [{"__id__": 437}, {"__id__": 439}, {"__id__": 441}, {"__id__": 443}], "_prefab": {"__id__": 577}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "142393a8-2641-4775-a79c-cbfe1bd9e5ca", "__expectedType__": "cc.Prefab"}, "fileId": "a0daVw8DRLi6ToMaTA0VS2", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "6cH5XhLHlJb5HbIIaNX3/i", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 5}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_name"], "value": "mask"}, {"__type__": "cc.TargetInfo", "localID": ["a0daVw8DRLi6ToMaTA0VS2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 11}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 750, "height": 1334}}, {"__type__": "cc.TargetInfo", "localID": ["77N2cid5pKDpXplRH/AWEU"]}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}, {"__id__": 207}], "_active": true, "_components": [{"__id__": 430}, {"__id__": 432}, {"__id__": 434}], "_prefab": {"__id__": 436}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "registerNode", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 14}, {"__id__": 168}], "_active": false, "_components": [{"__id__": 191}, {"__id__": 193}, {"__id__": 195}, {"__id__": 197}, {"__id__": 200}, {"__id__": 203}], "_prefab": {"__id__": 206}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 15}, {"__id__": 23}, {"__id__": 43}, {"__id__": 63}, {"__id__": 136}], "_active": true, "_components": [{"__id__": 163}, {"__id__": 165}], "_prefab": {"__id__": 167}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -13.900000000000006, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 14}, "_prefab": {"__id__": 16}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 15}, "asset": {"__uuid__": "c56d55bf-1f78-414f-a500-a1ff71150c7e", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 17}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "5b9lrIiFJI5ZotGpBAGSqP", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 18}, {"__id__": 20}, {"__id__": 21}, {"__id__": 22}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_name"], "value": "regAccountEditBoxNode"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -16.7109375, "y": 118.9, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 19}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 14}, "_prefab": {"__id__": 24}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 23}, "asset": {"__uuid__": "c56d55bf-1f78-414f-a500-a1ff71150c7e", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 25}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "85wpl8wmhPPYgmJpsTUOv5", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 26}, {"__id__": 28}, {"__id__": 29}, {"__id__": 30}, {"__id__": 31}, {"__id__": 33}, {"__id__": 35}, {"__id__": 37}, {"__id__": 39}, {"__id__": 41}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_name"], "value": "regPassEditBoxNode"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -16.7109375, "y": 58.900000000000006, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 27}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 32}, "propertyPath": ["_dataID"], "value": "password"}, {"__type__": "cc.TargetInfo", "localID": ["eaXK79jL1DLI2yR06q+Pwy", "807dKXf5tHrJtEdIFnHMo0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 34}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 118.34375, "height": 37.8}}, {"__type__": "cc.TargetInfo", "localID": ["eaXK79jL1DLI2yR06q+Pwy", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 36}, "propertyPath": ["_dataID"], "value": "password_tips"}, {"__type__": "cc.TargetInfo", "localID": ["e34fdXI8pErIudzokGEhEC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 38}, "propertyPath": ["_dataID"], "value": "password_tips"}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "800tdHtlJM2IVdpueUwzRq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 40}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 93.515625, "height": 27.72}}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 42}, "propertyPath": ["_name"], "value": "passwordMsg"}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 14}, "_prefab": {"__id__": 44}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 43}, "asset": {"__uuid__": "c56d55bf-1f78-414f-a500-a1ff71150c7e", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 45}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "74lYjpGkJIwIxH5cnZRGy/", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 46}, {"__id__": 48}, {"__id__": 49}, {"__id__": 50}, {"__id__": 51}, {"__id__": 53}, {"__id__": 55}, {"__id__": 57}, {"__id__": 59}, {"__id__": 61}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_name"], "value": "regPassEditBoxNode2"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -16.7109375, "y": -1.0999999999999943, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_dataID"], "value": "password_again"}, {"__type__": "cc.TargetInfo", "localID": ["e34fdXI8pErIudzokGEhEC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 54}, "propertyPath": ["_dataID"], "value": "repeat_password"}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "800tdHtlJM2IVdpueUwzRq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 56}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 113.4375, "height": 27.72}}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_dataID"], "value": "repeat_password"}, {"__type__": "cc.TargetInfo", "localID": ["eaXK79jL1DLI2yR06q+Pwy", "807dKXf5tHrJtEdIFnHMo0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 60}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 211.75, "height": 37.8}}, {"__type__": "cc.TargetInfo", "localID": ["eaXK79jL1DLI2yR06q+Pwy", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 62}, "propertyPath": ["_name"], "value": "passwordMsg2"}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "cc.Node", "_name": "buttonNode", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 14}, "_children": [{"__id__": 64}, {"__id__": 95}], "_active": true, "_components": [{"__id__": 133}], "_prefab": {"__id__": 135}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -71.1, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 63}, "_prefab": {"__id__": 65}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 64}, "asset": {"__uuid__": "62da37bb-2377-4788-8c7d-bec59bdca136", "__expectedType__": "cc.Prefab"}, "fileId": "30Ahna/hFADJL5JEyiP8Kc", "instance": {"__id__": 66}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "c3BaD5fEJPwKTCDGqayVk2", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 67}, {"__id__": 72}], "propertyOverrides": [{"__id__": 76}, {"__id__": 78}, {"__id__": 79}, {"__id__": 80}, {"__id__": 81}, {"__id__": 83}, {"__id__": 85}, {"__id__": 87}, {"__id__": 88}, {"__id__": 89}, {"__id__": 91}, {"__id__": 93}, {"__id__": 94}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 68}, "components": [{"__id__": 69}]}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 64}}, "node": {"__id__": 70}, "_enabled": true, "__prefab": {"__id__": 71}, "templateMode": false, "watchPath": "", "labelType": "cc.Label", "watchPathArr": [], "_dataID": "confirm_register", "_id": ""}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.CompPrefabInfo", "fileId": "91YLLzSvhHr5ZfxoEExPFG"}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 73}, "components": [{"__id__": 74}]}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 64}}, "node": {"__id__": 64}, "_enabled": true, "__prefab": {"__id__": 75}, "_alignFlags": 8, "_target": null, "_left": 60, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "07TjJyIEBLLbpR42DbmG5Z"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_name"], "value": "confirmRegButton"}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -105, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 77}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 82}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 2.725999999999999, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 84}, "propertyPath": ["_fontSize"], "value": 22}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 86}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 154.171875, "height": 50.4}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 84}, "propertyPath": ["_actualFontSize"], "value": 22}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 84}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 111, "g": 130, "b": 231, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 90}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 120, "height": 50}}, {"__type__": "cc.TargetInfo", "localID": ["b2FpfgEc1Py4FLQrPbDjfF"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 92}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "22b95aa5-1672-4636-92c3-74cd59faec22@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["daO3pUYmlKVIgsdc588k+9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 92}, "propertyPath": ["_sizeMode"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 84}, "propertyPath": ["_string"], "value": "login_button"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 63}, "_prefab": {"__id__": 96}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 95}, "asset": {"__uuid__": "62da37bb-2377-4788-8c7d-bec59bdca136", "__expectedType__": "cc.Prefab"}, "fileId": "30Ahna/hFADJL5JEyiP8Kc", "instance": {"__id__": 97}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "ba8btBVWlN8rtzdW1lzRh/", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 98}, {"__id__": 103}], "propertyOverrides": [{"__id__": 109}, {"__id__": 111}, {"__id__": 112}, {"__id__": 113}, {"__id__": 114}, {"__id__": 116}, {"__id__": 118}, {"__id__": 120}, {"__id__": 121}, {"__id__": 122}, {"__id__": 124}, {"__id__": 126}, {"__id__": 127}, {"__id__": 128}, {"__id__": 130}, {"__id__": 132}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 99}, "components": [{"__id__": 100}]}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 95}}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 102}, "templateMode": false, "watchPath": "", "labelType": "cc.Label", "watchPathArr": [], "_dataID": "back_login", "_id": ""}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7G/+Sb19JFpwDqpkEyJXY"}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 104}, "components": [{"__id__": 105}, {"__id__": 107}]}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 95}}, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 106}, "_alignFlags": 32, "_target": null, "_left": 0, "_right": 60, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffT/MdSLFBTrVPl7VC2TE7"}, {"__type__": "7d2a4voaOJJGJZRWFPG6Bk7", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 95}}, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 108}, "watchPath": "*.is<PERSON><PERSON><PERSON>", "valueClamp": true, "valueClampMode": 2, "valueMin": 0, "valueMax": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86fT4q6yJIwI2OxNSqxphW"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 110}, "propertyPath": ["_name"], "value": "back<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 110}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 105, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 110}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 110}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 115}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 2.725999999999999, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 117}, "propertyPath": ["_fontSize"], "value": 22}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 119}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 103.8125, "height": 50.4}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 117}, "propertyPath": ["_actualFontSize"], "value": 22}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 117}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 111, "g": 130, "b": 231, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 120, "height": 50}}, {"__type__": "cc.TargetInfo", "localID": ["b2FpfgEc1Py4FLQrPbDjfF"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "22b95aa5-1672-4636-92c3-74cd59faec22@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["daO3pUYmlKVIgsdc588k+9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 117}, "propertyPath": ["_string"], "value": "register"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 125}, "propertyPath": ["_sizeMode"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 129}, "propertyPath": ["clickEvents", "length"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["43UvszQV9LRKWvhBXHpprD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 129}, "propertyPath": ["clickEvents", "0"], "value": {"__id__": 131}}, {"__type__": "cc.ClickEvent", "target": {"__id__": 95}, "component": "", "_componentId": "7d2a4voaOJJGJZRWFPG6Bk7", "handler": "vSub", "customEventData": "1"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 129}, "propertyPath": ["_target"], "value": {"__id__": 95}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 63}, "_enabled": true, "__prefab": {"__id__": 134}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73DPhbu79G7rYCIgWu4Klz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "04hwQPIxVPTrcSZzjXTOg2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 14}, "_prefab": {"__id__": 137}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 136}, "asset": {"__uuid__": "80a6dff8-dc06-4f9d-a2c8-03170daa1ddd", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 138}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "96r+Y8PRBOia4qSHCljRm5", "prefabRootNode": {"__id__": 1}, "mountedChildren": [{"__id__": 139}], "mountedComponents": [], "propertyOverrides": [{"__id__": 149}, {"__id__": 151}, {"__id__": 152}, {"__id__": 153}, {"__id__": 154}, {"__id__": 156}, {"__id__": 158}, {"__id__": 160}, {"__id__": 161}, {"__id__": 162}], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 140}, "nodes": [{"__id__": 141}]}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "cc.Node", "_name": "googleButton1", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 136}}, "_parent": {"__id__": 136}, "_children": [], "_active": true, "_components": [{"__id__": 142}, {"__id__": 144}, {"__id__": 146}], "_prefab": {"__id__": 148}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -35, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 143}, "_contentSize": {"__type__": "cc.Size", "width": 29, "height": 29}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7IniUOrJMn4OVhP+zXD/h"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 145}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bbff4e54-a225-4a69-8fb9-9494c3619153@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aEXlNDuBHi6RspGfG9A9K"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 141}, "_enabled": true, "__prefab": {"__id__": 147}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 141}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54AXdvYh5IBZHP2nTJTrtv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eberLAwtlAiYb1q0OLYgTd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 150}, "propertyPath": ["_name"], "value": "otherway"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 150}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -130, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 150}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 150}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 155}, "propertyPath": ["_dataID"], "value": "other_login_way"}, {"__type__": "cc.TargetInfo", "localID": ["48BGi+JnJOKpaEdqvfVVS1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 157}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 129.8671875, "height": 37.8}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 159}, "propertyPath": ["_string"], "value": "other_login_way"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 159}, "propertyPath": ["_fontSize"], "value": 18}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 159}, "propertyPath": ["_actualFontSize"], "value": 18}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 150}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 164}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 297.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91jAhidJNMIo6xClycKcfL"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": {"__id__": 166}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 80, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a0ZJjEDO1IPKL2XAxy/LPH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1dFKxTwv5P9oI3z7DvI1lp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 13}, "_children": [{"__id__": 169}], "_active": true, "_components": [{"__id__": 184}, {"__id__": 186}, {"__id__": 188}], "_prefab": {"__id__": 190}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 178.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 168}, "_prefab": {"__id__": 170}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 169}, "asset": {"__uuid__": "8728975f-5233-40d6-9a87-08626aba6287", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 171}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "44CyLpHnhFKIxOby/HO+WK", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 172}, {"__id__": 174}, {"__id__": 175}, {"__id__": 176}, {"__id__": 177}, {"__id__": 179}, {"__id__": 181}, {"__id__": 182}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 173}, "propertyPath": ["_name"], "value": "title"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 173}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 173}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 173}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 178}, "propertyPath": ["_enableOutline"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 180}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 133.6875, "height": 54.4}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 178}, "propertyPath": ["_string"], "value": "Settings"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 183}, "propertyPath": ["_dataID"], "value": "register"}, {"__type__": "cc.TargetInfo", "localID": ["09i06HqvBP2ZaQ4Pk+p3Mt"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 168}, "_enabled": true, "__prefab": {"__id__": 185}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62Qj49XppOXJIqSfy8Q0m2"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 168}, "_enabled": true, "__prefab": {"__id__": 187}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "120862de-b58c-449f-9258-e8eb1063d993@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91M27zGNdDwbtFVhycwtUp"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 168}, "_enabled": true, "__prefab": {"__id__": 189}, "_alignFlags": 17, "_target": null, "_left": 318.5, "_right": 315.5, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 116, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0d60y6oRNJRalPCLkC95ok"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "abejEqSatFoIWRKtvApQuu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 192}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 430}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53ALStLT1DtLO+NxNvn/jJ"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 194}, "_alignFlags": 18, "_target": null, "_left": 0, "_right": 0, "_top": 788.5405000000001, "_bottom": 445.4595, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "55DySH7ulNiJV2zNa9Cjqb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 196}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1e585b0c-568f-46d8-8cb9-d0132174ed09@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3Hb3cXxVE9br/v3IR03nK"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 198}, "watchPath": "*.isSafeAccount", "foreachChildMode": false, "condition": 0, "foreachChildType": 0, "valueA": 0, "valueB": 0, "valueAction": 1, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 199}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "089bzZzS5Agq5DkwyVm0pA"}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 201}, "watchPath": "*.isSafePassword", "foreachChildMode": false, "condition": 0, "foreachChildType": 0, "valueA": 0, "valueB": 0, "valueAction": 1, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 202}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b2eYvABXlLNbL1WUAkUwMW"}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 204}, "watchPath": "*.isPasswordMatch", "foreachChildMode": false, "condition": 0, "foreachChildType": 0, "valueA": 0, "valueB": 0, "valueAction": 1, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 205}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15yqvxD+xA9boemiPclS70"}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26Qy8kmPtNCLF1vyA1FKNw", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "loginNode", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 208}, {"__id__": 396}], "_active": true, "_components": [{"__id__": 423}, {"__id__": 425}, {"__id__": 427}], "_prefab": {"__id__": 429}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 24.283999999999992, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 207}, "_children": [{"__id__": 209}, {"__id__": 229}, {"__id__": 258}, {"__id__": 364}], "_active": true, "_components": [{"__id__": 391}, {"__id__": 393}], "_prefab": {"__id__": 395}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -7.187000000000012, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 208}, "_prefab": {"__id__": 210}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 209}, "asset": {"__uuid__": "c56d55bf-1f78-414f-a500-a1ff71150c7e", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 211}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "d0Iji3M5BAbLz5cdn3Y8A8", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 212}, {"__id__": 214}, {"__id__": 216}, {"__id__": 218}, {"__id__": 220}, {"__id__": 222}, {"__id__": 224}, {"__id__": 226}, {"__id__": 228}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 213}, "propertyPath": ["_name"], "value": "loginAccount"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 215}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 91.125, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 217}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 219}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 221}, "propertyPath": ["_name"], "value": "loginErrMsg"}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 223}, "propertyPath": ["_dataID"], "value": "login_fail"}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "800tdHtlJM2IVdpueUwzRq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 225}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 58.********, "height": 27.72}}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 227}, "propertyPath": ["_dataID"], "value": "account_placeholder"}, {"__type__": "cc.TargetInfo", "localID": ["e34fdXI8pErIudzokGEhEC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 221}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 208}, "_prefab": {"__id__": 230}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 229}, "asset": {"__uuid__": "c56d55bf-1f78-414f-a500-a1ff71150c7e", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 231}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "634xbzy75PRJnOkPOSxX3u", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 232}, {"__id__": 234}, {"__id__": 235}, {"__id__": 236}, {"__id__": 237}, {"__id__": 239}, {"__id__": 241}, {"__id__": 243}, {"__id__": 245}, {"__id__": 247}, {"__id__": 249}, {"__id__": 251}, {"__id__": 253}, {"__id__": 254}, {"__id__": 256}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 233}, "propertyPath": ["_name"], "value": "loginPassword"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 233}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 28.9, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 233}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 233}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 238}, "propertyPath": ["_dataID"], "value": "password"}, {"__type__": "cc.TargetInfo", "localID": ["eaXK79jL1DLI2yR06q+Pwy", "807dKXf5tHrJtEdIFnHMo0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 240}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 118.34375, "height": 37.8}}, {"__type__": "cc.TargetInfo", "localID": ["eaXK79jL1DLI2yR06q+Pwy", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 242}, "propertyPath": ["_dataID"], "value": "password_placeholder"}, {"__type__": "cc.TargetInfo", "localID": ["e34fdXI8pErIudzokGEhEC"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 244}, "propertyPath": ["_dataID"], "value": "password_tips"}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "800tdHtlJM2IVdpueUwzRq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 246}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 93.515625, "height": 27.72}}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 248}, "propertyPath": ["_name"], "value": "accountsg"}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 250}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 376.578125, "height": 64.45}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 252}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -140, "y": -13.***************, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["eaXK79jL1DLI2yR06q+Pwy", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 248}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 255}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -15.36, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["ddOfelTBZE1rggF7WlbAIs"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 257}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 250, "height": 59.974999999999994}}, {"__type__": "cc.TargetInfo", "localID": ["b8YzmdduxAio6kjkLI8vm8"]}, {"__type__": "cc.Node", "_name": "buttonNode", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 208}, "_children": [{"__id__": 259}, {"__id__": 290}, {"__id__": 328}], "_active": true, "_components": [{"__id__": 359}, {"__id__": 361}], "_prefab": {"__id__": 363}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -43.32500000000001, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 258}, "_prefab": {"__id__": 260}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 259}, "asset": {"__uuid__": "62da37bb-2377-4788-8c7d-bec59bdca136", "__expectedType__": "cc.Prefab"}, "fileId": "30Ahna/hFADJL5JEyiP8Kc", "instance": {"__id__": 261}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "ac+bRd6uxGJoe+IpsGyyaQ", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 262}, {"__id__": 267}], "propertyOverrides": [{"__id__": 271}, {"__id__": 273}, {"__id__": 274}, {"__id__": 275}, {"__id__": 276}, {"__id__": 278}, {"__id__": 280}, {"__id__": 282}, {"__id__": 283}, {"__id__": 284}, {"__id__": 286}, {"__id__": 288}, {"__id__": 289}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 263}, "components": [{"__id__": 264}]}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 259}}, "node": {"__id__": 265}, "_enabled": true, "__prefab": {"__id__": 266}, "templateMode": false, "watchPath": "", "labelType": "cc.Label", "watchPathArr": [], "_dataID": "login_button", "_id": ""}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.CompPrefabInfo", "fileId": "91YLLzSvhHr5ZfxoEExPFG"}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 268}, "components": [{"__id__": 269}]}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 259}}, "node": {"__id__": 259}, "_enabled": true, "__prefab": {"__id__": 270}, "_alignFlags": 8, "_target": null, "_left": 38, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeapftaz5BgI1+fsgRuNiC"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 272}, "propertyPath": ["_name"], "value": "loginButton"}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 272}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -127, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 272}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 272}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 277}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 2.725999999999999, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 279}, "propertyPath": ["_fontSize"], "value": 22}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 281}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 119.625, "height": 50.4}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 279}, "propertyPath": ["_actualFontSize"], "value": 22}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 279}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 111, "g": 130, "b": 231, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 285}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 120, "height": 50}}, {"__type__": "cc.TargetInfo", "localID": ["b2FpfgEc1Py4FLQrPbDjfF"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 287}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "22b95aa5-1672-4636-92c3-74cd59faec22@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["daO3pUYmlKVIgsdc588k+9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 287}, "propertyPath": ["_sizeMode"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 279}, "propertyPath": ["_string"], "value": "login_button"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 258}, "_prefab": {"__id__": 291}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 290}, "asset": {"__uuid__": "62da37bb-2377-4788-8c7d-bec59bdca136", "__expectedType__": "cc.Prefab"}, "fileId": "30Ahna/hFADJL5JEyiP8Kc", "instance": {"__id__": 292}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "a1moknUJlGnL65AOZvhQB0", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 293}, {"__id__": 298}], "propertyOverrides": [{"__id__": 304}, {"__id__": 306}, {"__id__": 307}, {"__id__": 308}, {"__id__": 309}, {"__id__": 311}, {"__id__": 313}, {"__id__": 315}, {"__id__": 316}, {"__id__": 317}, {"__id__": 319}, {"__id__": 321}, {"__id__": 322}, {"__id__": 323}, {"__id__": 325}, {"__id__": 327}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 294}, "components": [{"__id__": 295}]}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 290}}, "node": {"__id__": 296}, "_enabled": true, "__prefab": {"__id__": 297}, "templateMode": false, "watchPath": "", "labelType": "cc.Label", "watchPathArr": [], "_dataID": "register", "_id": ""}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7G/+Sb19JFpwDqpkEyJXY"}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 299}, "components": [{"__id__": 300}, {"__id__": 302}]}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 290}}, "node": {"__id__": 290}, "_enabled": true, "__prefab": {"__id__": 301}, "_alignFlags": 16, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 3, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "73Hkkg3JRKrLRVnGMqxRWn"}, {"__type__": "7d2a4voaOJJGJZRWFPG6Bk7", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 290}}, "node": {"__id__": 290}, "_enabled": true, "__prefab": {"__id__": 303}, "watchPath": "*.is<PERSON><PERSON><PERSON>", "valueClamp": true, "valueClampMode": 2, "valueMin": 0, "valueMax": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ferJPr/xtMNYKv/BqVZFmM"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 305}, "propertyPath": ["_name"], "value": "registerButton"}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 305}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 3, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 305}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 305}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 310}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 2.725999999999999, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 312}, "propertyPath": ["_fontSize"], "value": 22}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 314}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 71.328125, "height": 50.4}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 312}, "propertyPath": ["_actualFontSize"], "value": 22}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 312}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 111, "g": 130, "b": 231, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 318}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 120, "height": 50}}, {"__type__": "cc.TargetInfo", "localID": ["b2FpfgEc1Py4FLQrPbDjfF"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 320}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "22b95aa5-1672-4636-92c3-74cd59faec22@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["daO3pUYmlKVIgsdc588k+9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 312}, "propertyPath": ["_string"], "value": "register"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 320}, "propertyPath": ["_sizeMode"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 324}, "propertyPath": ["clickEvents", "length"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["43UvszQV9LRKWvhBXHpprD"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 324}, "propertyPath": ["clickEvents", "0"], "value": {"__id__": 326}}, {"__type__": "cc.ClickEvent", "target": {"__id__": 290}, "component": "", "_componentId": "7d2a4voaOJJGJZRWFPG6Bk7", "handler": "vAdd", "customEventData": "1"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 324}, "propertyPath": ["_target"], "value": {"__id__": 290}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 258}, "_prefab": {"__id__": 329}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 328}, "asset": {"__uuid__": "62da37bb-2377-4788-8c7d-bec59bdca136", "__expectedType__": "cc.Prefab"}, "fileId": "30Ahna/hFADJL5JEyiP8Kc", "instance": {"__id__": 330}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "2c/mYp1c1JqoZcuObrurp9", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 331}, {"__id__": 336}], "propertyOverrides": [{"__id__": 340}, {"__id__": 342}, {"__id__": 343}, {"__id__": 344}, {"__id__": 345}, {"__id__": 347}, {"__id__": 349}, {"__id__": 351}, {"__id__": 352}, {"__id__": 353}, {"__id__": 355}, {"__id__": 357}, {"__id__": 358}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 332}, "components": [{"__id__": 333}]}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 328}}, "node": {"__id__": 334}, "_enabled": true, "__prefab": {"__id__": 335}, "templateMode": false, "watchPath": "", "labelType": "cc.Label", "watchPathArr": [], "_dataID": "login_guest", "_id": ""}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7G/+Sb19JFpwDqpkEyJXY"}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 337}, "components": [{"__id__": 338}]}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 328}}, "node": {"__id__": 328}, "_enabled": true, "__prefab": {"__id__": 339}, "_alignFlags": 32, "_target": null, "_left": 0, "_right": 32, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3XhKv58VOi4vrQptxjRba"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 341}, "propertyPath": ["_name"], "value": "loginGuestButton"}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 341}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 133, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 341}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 341}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 346}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 2.725999999999999, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 348}, "propertyPath": ["_fontSize"], "value": 22}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 350}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 109.9140625, "height": 50.4}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 348}, "propertyPath": ["_actualFontSize"], "value": 22}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 348}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 216, "g": 65, "b": 107, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 354}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 120, "height": 50}}, {"__type__": "cc.TargetInfo", "localID": ["b2FpfgEc1Py4FLQrPbDjfF"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 356}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "22b95aa5-1672-4636-92c3-74cd59faec22@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["daO3pUYmlKVIgsdc588k+9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 348}, "propertyPath": ["_string"], "value": "login_guest"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 356}, "propertyPath": ["_sizeMode"], "value": 0}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 258}, "_enabled": true, "__prefab": {"__id__": 360}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "974ighhdRF4bpCSC8J7wEd"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 258}, "_enabled": true, "__prefab": {"__id__": 362}, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 38, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 10, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1fTrxsgG1FWJ4zdjStClba"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "70BT4StMlEsoDheY+TOddu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 208}, "_prefab": {"__id__": 365}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 364}, "asset": {"__uuid__": "80a6dff8-dc06-4f9d-a2c8-03170daa1ddd", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 366}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e0YF1EG7VIn6/1FWZrzM9Z", "prefabRootNode": {"__id__": 1}, "mountedChildren": [{"__id__": 367}], "mountedComponents": [], "propertyOverrides": [{"__id__": 377}, {"__id__": 379}, {"__id__": 380}, {"__id__": 381}, {"__id__": 382}, {"__id__": 384}, {"__id__": 386}, {"__id__": 388}, {"__id__": 389}, {"__id__": 390}], "removedComponents": []}, {"__type__": "cc.MountedChildrenInfo", "targetInfo": {"__id__": 368}, "nodes": [{"__id__": 369}]}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "cc.Node", "_name": "googleButton2", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 364}}, "_parent": {"__id__": 364}, "_children": [], "_active": true, "_components": [{"__id__": 370}, {"__id__": 372}, {"__id__": 374}], "_prefab": {"__id__": 376}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -45, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 369}, "_enabled": true, "__prefab": {"__id__": 371}, "_contentSize": {"__type__": "cc.Size", "width": 29, "height": 29}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7IniUOrJMn4OVhP+zXD/h"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 369}, "_enabled": true, "__prefab": {"__id__": 373}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bbff4e54-a225-4a69-8fb9-9494c3619153@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aEXlNDuBHi6RspGfG9A9K"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 369}, "_enabled": true, "__prefab": {"__id__": 375}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 369}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54AXdvYh5IBZHP2nTJTrtv"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "eberLAwtlAiYb1q0OLYgTd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 378}, "propertyPath": ["_name"], "value": "otherway"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 378}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -102.225, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 378}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 378}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 383}, "propertyPath": ["_dataID"], "value": "other_login_way"}, {"__type__": "cc.TargetInfo", "localID": ["48BGi+JnJOKpaEdqvfVVS1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 385}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 129.8671875, "height": 37.8}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 387}, "propertyPath": ["_string"], "value": "other_login_way"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 387}, "propertyPath": ["_fontSize"], "value": 18}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 387}, "propertyPath": ["_actualFontSize"], "value": 18}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 378}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 392}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 242.25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dmNebGE9B/YKhU7D46R5G"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 208}, "_enabled": true, "__prefab": {"__id__": 394}, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 76.06200000000001, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1aTjdyWBGmpHwRvBsbWlZ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "894SERsltI45hByEmMa91b", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 207}, "_children": [{"__id__": 397}], "_active": true, "_components": [{"__id__": 416}, {"__id__": 418}, {"__id__": 420}], "_prefab": {"__id__": 422}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 153.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 396}, "_prefab": {"__id__": 398}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 397}, "asset": {"__uuid__": "8728975f-5233-40d6-9a87-08626aba6287", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 399}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "b6EypWCVlMda/goZdetqsG", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 400}, {"__id__": 402}, {"__id__": 404}, {"__id__": 406}, {"__id__": 408}, {"__id__": 410}, {"__id__": 412}, {"__id__": 414}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 401}, "propertyPath": ["_name"], "value": "title"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 403}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 405}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 407}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 409}, "propertyPath": ["_enableOutline"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 411}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 209.78125, "height": 54.4}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 413}, "propertyPath": ["_string"], "value": "Settings"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 415}, "propertyPath": ["_dataID"], "value": "login_game"}, {"__type__": "cc.TargetInfo", "localID": ["09i06HqvBP2ZaQ4Pk+p3Mt"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 396}, "_enabled": true, "__prefab": {"__id__": 417}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3eVXTyo71M4aXvsA1Zi316"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 396}, "_enabled": true, "__prefab": {"__id__": 419}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "120862de-b58c-449f-9258-e8eb1063d993@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7fWpa8ylpGzIeCj1XkLZje"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 396}, "_enabled": true, "__prefab": {"__id__": 421}, "_alignFlags": 17, "_target": null, "_left": 318.5, "_right": 315.5, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 116, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88zq088pJDaLMmhcvBSOQ9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9eZwfu8E9Da4BWz79aiIJP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 424}, "_contentSize": {"__type__": "cc.Size", "width": 450, "height": 380}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4HrnCI3JCpJLrBrM0xHMm"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 426}, "_alignFlags": 18, "_target": null, "_left": 0, "_right": 0, "_top": 788.5405000000001, "_bottom": 445.4595, "_horizontalCenter": 0, "_verticalCenter": 24.283999999999992, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3nU+me0hFl4bVYXAaltmW"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 207}, "_enabled": true, "__prefab": {"__id__": 428}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1e585b0c-568f-46d8-8cb9-d0132174ed09@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34e4QdZBRNprNvke31ZIZJ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "73Tql2xnNJ8KUCDd54Drkg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 431}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "46BH2zQaZDHqZUETi982R9"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 433}, "watchPath": "*.is<PERSON><PERSON><PERSON>", "foreachChildMode": false, "condition": 0, "foreachChildType": 0, "valueA": 1, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 13}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1eFFnrDp1I/4XMtg46FfzV"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 435}, "watchPath": "*.is<PERSON><PERSON><PERSON>", "foreachChildMode": false, "condition": 0, "foreachChildType": 0, "valueA": 0, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 207}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7altCVp2hEc6fF0FD/mPhl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "40U5xWXqpAaI7r+VZtuTbs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 438}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1hVvKAKJAsY8YKb40A5oY"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 440}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48O7mvCMxPQ4BoitAgcnAc"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 442}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1080, "_originalHeight": 1920, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9y1hDPe5P24tzvKVZtU1V"}, {"__type__": "4e091gilDVKO7MagP5GRpoa", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 444}, "loginNode": {"__id__": 207}, "registerNode": {"__id__": 13}, "loginAccountEBox": {"__id__": 445}, "loginPasswordEBox": {"__id__": 472}, "regAccountEBox": {"__id__": 499}, "regPasswordEBox": {"__id__": 525}, "regPasswordAgainEBox": {"__id__": 551}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "212kEVGDJGvbTS/ufre90c"}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 446}, "_enabled": true, "__prefab": {"__id__": 471}, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_textLabel": {"__id__": 455}, "_placeholderLabel": {"__id__": 461}, "_returnType": 0, "_string": "", "_tabIndex": 0, "_backgroundImage": {"__uuid__": "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_inputFlag": 5, "_inputMode": 6, "_maxLength": 8, "_id": ""}, {"__type__": "cc.Node", "_name": "EditBox", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 447}, "_children": [{"__id__": 452}, {"__id__": 458}], "_active": true, "_components": [{"__id__": 466}, {"__id__": 468}, {"__id__": 445}], "_prefab": {"__id__": 470}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -15.36, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 209}, "_children": [{"__id__": 448}, {"__id__": 446}], "_active": true, "_components": [{"__id__": 449}], "_prefab": {"__id__": 451}, "_lpos": {"__type__": "cc.Vec3", "x": 63.2890625, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 447}, "_enabled": true, "__prefab": {"__id__": 450}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8YzmdduxAio6kjkLI8vm8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69q9sWWqJA97+Y5p9TX9uk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 446}, "_children": [], "_active": false, "_components": [{"__id__": 453}, {"__id__": 455}], "_prefab": {"__id__": 457}, "_lpos": {"__type__": "cc.Vec3", "x": -123, "y": 14.6275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 454}, "_contentSize": {"__type__": "cc.Size", "width": 248, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bEfqhR8lOTZtnQhHBoMMX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 452}, "_enabled": true, "__prefab": {"__id__": 456}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71wbpeDSRJJIR6SalXggaP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bXdpKVOFLVZlwE9ooS0gn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 446}, "_children": [], "_active": true, "_components": [{"__id__": 459}, {"__id__": 461}, {"__id__": 463}], "_prefab": {"__id__": 465}, "_lpos": {"__type__": "cc.Vec3", "x": -123, "y": 14.6275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 458}, "_enabled": true, "__prefab": {"__id__": 460}, "_contentSize": {"__type__": "cc.Size", "width": 248, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65PABYbBFD+oKiMMB1/8CQ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 458}, "_enabled": true, "__prefab": {"__id__": 462}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_string": "account_placeholder", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28jx8A8HJIlpFtWwyDq5Bf"}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 458}, "_enabled": true, "__prefab": {"__id__": 464}, "templateMode": false, "watchPath": "", "labelType": "cc.Label", "watchPathArr": [], "_dataID": "account_placeholder", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e34fdXI8pErIudzokGEhEC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afI3gpV49F66OArF8TDqq2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 446}, "_enabled": true, "__prefab": {"__id__": 467}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ae2ep09FLHoObQPI3gzpL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 446}, "_enabled": true, "__prefab": {"__id__": 469}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dD7VWwtxHhbP6eNxeH7O8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddOfelTBZE1rggF7WlbAIs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.CompPrefabInfo", "fileId": "916eopZOJCC6M9n2sVZLmV"}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 473}, "_enabled": true, "__prefab": {"__id__": 498}, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_textLabel": {"__id__": 482}, "_placeholderLabel": {"__id__": 488}, "_returnType": 0, "_string": "", "_tabIndex": 0, "_backgroundImage": {"__uuid__": "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_inputFlag": 5, "_inputMode": 6, "_maxLength": 8, "_id": ""}, {"__type__": "cc.Node", "_name": "EditBox", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 474}, "_children": [{"__id__": 479}, {"__id__": 485}], "_active": true, "_components": [{"__id__": 493}, {"__id__": 495}, {"__id__": 472}], "_prefab": {"__id__": 497}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -15.36, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 229}, "_children": [{"__id__": 475}, {"__id__": 473}], "_active": true, "_components": [{"__id__": 476}], "_prefab": {"__id__": 478}, "_lpos": {"__type__": "cc.Vec3", "x": 63.2890625, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 474}, "_enabled": true, "__prefab": {"__id__": 477}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 59.974999999999994}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8YzmdduxAio6kjkLI8vm8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69q9sWWqJA97+Y5p9TX9uk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 473}, "_children": [], "_active": false, "_components": [{"__id__": 480}, {"__id__": 482}], "_prefab": {"__id__": 484}, "_lpos": {"__type__": "cc.Vec3", "x": -123, "y": 14.6275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 479}, "_enabled": true, "__prefab": {"__id__": 481}, "_contentSize": {"__type__": "cc.Size", "width": 248, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bEfqhR8lOTZtnQhHBoMMX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 479}, "_enabled": true, "__prefab": {"__id__": 483}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71wbpeDSRJJIR6SalXggaP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bXdpKVOFLVZlwE9ooS0gn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 473}, "_children": [], "_active": true, "_components": [{"__id__": 486}, {"__id__": 488}, {"__id__": 490}], "_prefab": {"__id__": 492}, "_lpos": {"__type__": "cc.Vec3", "x": -123, "y": 14.6275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 485}, "_enabled": true, "__prefab": {"__id__": 487}, "_contentSize": {"__type__": "cc.Size", "width": 248, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65PABYbBFD+oKiMMB1/8CQ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 485}, "_enabled": true, "__prefab": {"__id__": 489}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_string": "password_placeholder", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28jx8A8HJIlpFtWwyDq5Bf"}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 485}, "_enabled": true, "__prefab": {"__id__": 491}, "templateMode": false, "watchPath": "", "labelType": "cc.Label", "watchPathArr": [], "_dataID": "password_placeholder", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e34fdXI8pErIudzokGEhEC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afI3gpV49F66OArF8TDqq2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 473}, "_enabled": true, "__prefab": {"__id__": 494}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ae2ep09FLHoObQPI3gzpL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 473}, "_enabled": true, "__prefab": {"__id__": 496}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dD7VWwtxHhbP6eNxeH7O8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddOfelTBZE1rggF7WlbAIs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.CompPrefabInfo", "fileId": "916eopZOJCC6M9n2sVZLmV"}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 524}, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_textLabel": {"__id__": 508}, "_placeholderLabel": {"__id__": 514}, "_returnType": 0, "_string": "", "_tabIndex": 0, "_backgroundImage": {"__uuid__": "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_inputFlag": 5, "_inputMode": 6, "_maxLength": 8, "_id": ""}, {"__type__": "cc.Node", "_name": "EditBox", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 501}, "_children": [{"__id__": 505}, {"__id__": 511}], "_active": true, "_components": [{"__id__": 519}, {"__id__": 521}, {"__id__": 499}], "_prefab": {"__id__": 523}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -15.36, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 15}, "_children": [{"__id__": 199}, {"__id__": 500}], "_active": true, "_components": [{"__id__": 502}], "_prefab": {"__id__": 504}, "_lpos": {"__type__": "cc.Vec3", "x": 63.2890625, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 501}, "_enabled": true, "__prefab": {"__id__": 503}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8YzmdduxAio6kjkLI8vm8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69q9sWWqJA97+Y5p9TX9uk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 500}, "_children": [], "_active": false, "_components": [{"__id__": 506}, {"__id__": 508}], "_prefab": {"__id__": 510}, "_lpos": {"__type__": "cc.Vec3", "x": -123, "y": 14.6275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 505}, "_enabled": true, "__prefab": {"__id__": 507}, "_contentSize": {"__type__": "cc.Size", "width": 248, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bEfqhR8lOTZtnQhHBoMMX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 505}, "_enabled": true, "__prefab": {"__id__": 509}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71wbpeDSRJJIR6SalXggaP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bXdpKVOFLVZlwE9ooS0gn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 500}, "_children": [], "_active": true, "_components": [{"__id__": 512}, {"__id__": 514}, {"__id__": 516}], "_prefab": {"__id__": 518}, "_lpos": {"__type__": "cc.Vec3", "x": -123, "y": 14.6275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 511}, "_enabled": true, "__prefab": {"__id__": 513}, "_contentSize": {"__type__": "cc.Size", "width": 248, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65PABYbBFD+oKiMMB1/8CQ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 511}, "_enabled": true, "__prefab": {"__id__": 515}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_string": "username_tips", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28jx8A8HJIlpFtWwyDq5Bf"}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 511}, "_enabled": true, "__prefab": {"__id__": 517}, "templateMode": false, "watchPath": "", "labelType": "cc.Label", "watchPathArr": [], "_dataID": "username_tips", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e34fdXI8pErIudzokGEhEC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afI3gpV49F66OArF8TDqq2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 520}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ae2ep09FLHoObQPI3gzpL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 500}, "_enabled": true, "__prefab": {"__id__": 522}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dD7VWwtxHhbP6eNxeH7O8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddOfelTBZE1rggF7WlbAIs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.CompPrefabInfo", "fileId": "916eopZOJCC6M9n2sVZLmV"}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 526}, "_enabled": true, "__prefab": {"__id__": 550}, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_textLabel": {"__id__": 534}, "_placeholderLabel": {"__id__": 540}, "_returnType": 0, "_string": "", "_tabIndex": 0, "_backgroundImage": {"__uuid__": "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_inputFlag": 5, "_inputMode": 6, "_maxLength": 8, "_id": ""}, {"__type__": "cc.Node", "_name": "EditBox", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 527}, "_children": [{"__id__": 531}, {"__id__": 537}], "_active": true, "_components": [{"__id__": 545}, {"__id__": 547}, {"__id__": 525}], "_prefab": {"__id__": 549}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -15.36, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 23}, "_children": [{"__id__": 202}, {"__id__": 526}], "_active": true, "_components": [{"__id__": 528}], "_prefab": {"__id__": 530}, "_lpos": {"__type__": "cc.Vec3", "x": 63.2890625, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 527}, "_enabled": true, "__prefab": {"__id__": 529}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8YzmdduxAio6kjkLI8vm8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69q9sWWqJA97+Y5p9TX9uk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 526}, "_children": [], "_active": false, "_components": [{"__id__": 532}, {"__id__": 534}], "_prefab": {"__id__": 536}, "_lpos": {"__type__": "cc.Vec3", "x": -123, "y": 14.6275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 531}, "_enabled": true, "__prefab": {"__id__": 533}, "_contentSize": {"__type__": "cc.Size", "width": 248, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bEfqhR8lOTZtnQhHBoMMX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 531}, "_enabled": true, "__prefab": {"__id__": 535}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71wbpeDSRJJIR6SalXggaP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bXdpKVOFLVZlwE9ooS0gn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 526}, "_children": [], "_active": true, "_components": [{"__id__": 538}, {"__id__": 540}, {"__id__": 542}], "_prefab": {"__id__": 544}, "_lpos": {"__type__": "cc.Vec3", "x": -123, "y": 14.6275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 537}, "_enabled": true, "__prefab": {"__id__": 539}, "_contentSize": {"__type__": "cc.Size", "width": 248, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65PABYbBFD+oKiMMB1/8CQ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 537}, "_enabled": true, "__prefab": {"__id__": 541}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_string": "username_tips", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28jx8A8HJIlpFtWwyDq5Bf"}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 537}, "_enabled": true, "__prefab": {"__id__": 543}, "templateMode": false, "watchPath": "", "labelType": "cc.Label", "watchPathArr": [], "_dataID": "password_tips", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e34fdXI8pErIudzokGEhEC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afI3gpV49F66OArF8TDqq2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 526}, "_enabled": true, "__prefab": {"__id__": 546}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ae2ep09FLHoObQPI3gzpL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 526}, "_enabled": true, "__prefab": {"__id__": 548}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dD7VWwtxHhbP6eNxeH7O8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddOfelTBZE1rggF7WlbAIs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.CompPrefabInfo", "fileId": "916eopZOJCC6M9n2sVZLmV"}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 552}, "_enabled": true, "__prefab": {"__id__": 576}, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_textLabel": {"__id__": 560}, "_placeholderLabel": {"__id__": 566}, "_returnType": 0, "_string": "", "_tabIndex": 0, "_backgroundImage": {"__uuid__": "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_inputFlag": 5, "_inputMode": 6, "_maxLength": 8, "_id": ""}, {"__type__": "cc.Node", "_name": "EditBox", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 553}, "_children": [{"__id__": 557}, {"__id__": 563}], "_active": true, "_components": [{"__id__": 571}, {"__id__": 573}, {"__id__": 551}], "_prefab": {"__id__": 575}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -15.36, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 43}, "_children": [{"__id__": 205}, {"__id__": 552}], "_active": true, "_components": [{"__id__": 554}], "_prefab": {"__id__": 556}, "_lpos": {"__type__": "cc.Vec3", "x": 63.2890625, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 553}, "_enabled": true, "__prefab": {"__id__": 555}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8YzmdduxAio6kjkLI8vm8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "69q9sWWqJA97+Y5p9TX9uk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 552}, "_children": [], "_active": false, "_components": [{"__id__": 558}, {"__id__": 560}], "_prefab": {"__id__": 562}, "_lpos": {"__type__": "cc.Vec3", "x": -123, "y": 14.6275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 557}, "_enabled": true, "__prefab": {"__id__": 559}, "_contentSize": {"__type__": "cc.Size", "width": 248, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bEfqhR8lOTZtnQhHBoMMX"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 557}, "_enabled": true, "__prefab": {"__id__": 561}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 40, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71wbpeDSRJJIR6SalXggaP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "2bXdpKVOFLVZlwE9ooS0gn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 552}, "_children": [], "_active": true, "_components": [{"__id__": 564}, {"__id__": 566}, {"__id__": 568}], "_prefab": {"__id__": 570}, "_lpos": {"__type__": "cc.Vec3", "x": -123, "y": 14.6275, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 563}, "_enabled": true, "__prefab": {"__id__": 565}, "_contentSize": {"__type__": "cc.Size", "width": 248, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65PABYbBFD+oKiMMB1/8CQ"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 563}, "_enabled": true, "__prefab": {"__id__": 567}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_string": "username_tips", "_horizontalAlign": 0, "_verticalAlign": 1, "_actualFontSize": 20, "_fontSize": 20, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 1, "_enableWrapText": false, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "28jx8A8HJIlpFtWwyDq5Bf"}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 563}, "_enabled": true, "__prefab": {"__id__": 569}, "templateMode": false, "watchPath": "", "labelType": "cc.Label", "watchPathArr": [], "_dataID": "password_again", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e34fdXI8pErIudzokGEhEC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "afI3gpV49F66OArF8TDqq2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 552}, "_enabled": true, "__prefab": {"__id__": 572}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 29.255}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7ae2ep09FLHoObQPI3gzpL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 552}, "_enabled": true, "__prefab": {"__id__": 574}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "bd1bcaba-bd7d-4a71-b143-997c882383e4@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dD7VWwtxHhbP6eNxeH7O8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ddOfelTBZE1rggF7WlbAIs", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.CompPrefabInfo", "fileId": "916eopZOJCC6M9n2sVZLmV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "22ahoiOOVHK7EfzrB8wn/z", "instance": null, "targetOverrides": [{"__id__": 578}, {"__id__": 580}, {"__id__": 582}, {"__id__": 584}, {"__id__": 586}, {"__id__": 588}, {"__id__": 590}, {"__id__": 592}, {"__id__": 594}, {"__id__": 596}], "nestedPrefabInstanceRoots": [{"__id__": 397}, {"__id__": 364}, {"__id__": 328}, {"__id__": 290}, {"__id__": 259}, {"__id__": 229}, {"__id__": 209}, {"__id__": 169}, {"__id__": 136}, {"__id__": 95}, {"__id__": 64}, {"__id__": 43}, {"__id__": 23}, {"__id__": 15}, {"__id__": 2}]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["loginAccountEBox"], "target": {"__id__": 209}, "targetInfo": {"__id__": 579}}, {"__type__": "cc.TargetInfo", "localID": ["916eopZOJCC6M9n2sVZLmV"]}, {"__type__": "cc.TargetOverrideInfo", "source": null, "sourceInfo": null, "propertyPath": ["loginPasswordEBox"], "target": {"__id__": 229}, "targetInfo": {"__id__": 581}}, {"__type__": "cc.TargetInfo", "localID": ["916eopZOJCC6M9n2sVZLmV"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 443}, "sourceInfo": null, "propertyPath": ["regPasswordEBox"], "target": {"__id__": 23}, "targetInfo": {"__id__": 583}}, {"__type__": "cc.TargetInfo", "localID": ["916eopZOJCC6M9n2sVZLmV"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 443}, "sourceInfo": null, "propertyPath": ["regAccountEBox"], "target": {"__id__": 15}, "targetInfo": {"__id__": 585}}, {"__type__": "cc.TargetInfo", "localID": ["916eopZOJCC6M9n2sVZLmV"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 443}, "sourceInfo": null, "propertyPath": ["regPasswordAgainEBox"], "target": {"__id__": 43}, "targetInfo": {"__id__": 587}}, {"__type__": "cc.TargetInfo", "localID": ["916eopZOJCC6M9n2sVZLmV"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 443}, "sourceInfo": null, "propertyPath": ["loginAccountEBox"], "target": {"__id__": 209}, "targetInfo": {"__id__": 589}}, {"__type__": "cc.TargetInfo", "localID": ["916eopZOJCC6M9n2sVZLmV"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 443}, "sourceInfo": null, "propertyPath": ["loginPasswordEBox"], "target": {"__id__": 229}, "targetInfo": {"__id__": 591}}, {"__type__": "cc.TargetInfo", "localID": ["916eopZOJCC6M9n2sVZLmV"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 197}, "sourceInfo": null, "propertyPath": ["watchNodes", "0"], "target": {"__id__": 15}, "targetInfo": {"__id__": 593}}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 200}, "sourceInfo": null, "propertyPath": ["watchNodes", "0"], "target": {"__id__": 23}, "targetInfo": {"__id__": 595}}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 203}, "sourceInfo": null, "propertyPath": ["watchNodes", "0"], "target": {"__id__": 43}, "targetInfo": {"__id__": 597}}, {"__type__": "cc.TargetInfo", "localID": ["09sQr/0RFDc7MTGVUn+MyB", "f05XX5jrpEOYwv6lCoUIav"]}]