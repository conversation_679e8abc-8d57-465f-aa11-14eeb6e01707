[1, ["62C2vzA2lFYIN/KiwAtzwm", "12Y9dMgWdJKJGmTiZyQR9H@2e76e"], ["node", "_mesh", "root", "data"], [["cc.Node", ["_name", "_components", "_prefab", "_lpos", "_children", "_parent", "_lscale"], 2, 9, 4, 5, 2, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_name", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.PlaneCollider", ["node", "__prefab"], 3, 1, 4], ["cc.RigidBody", ["_group", "_type", "node", "__prefab"], 1, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["c9fa7SRcolAo45K/fmQMjla", ["node", "__prefab"], 3, 1, 4]], [[3, 0, 2], [7, 0, 1, 2, 3, 4, 5, 5], [1, 0, 2], [0, 0, 4, 1, 2, 3, 2], [0, 0, 5, 1, 2, 3, 6, 2], [2, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 3], [5, 0, 1, 1], [6, 0, 1, 2, 3, 3], [8, 0, 1, 1]], [[2, "createNode"], [3, "createNode", [-3], [[9, -2, [0, "e7nNSL4ltL9qhQffGFuz/l"]]], [1, "3a5gaXletMQLMRIt4+D9nJ", null, null, null, -1, 0], [1, 0, 4, 0]], [4, "Plane", 1, [[5, "Plane<ModelComponent>", -4, [0, "7ceEVDXvRF67vtdc/szkM5"], [0], [6, true, true], 1], [7, -5, [0, "4fgfb/viJBco/d47OKE+nw"]], [8, 32, 2, -6, [0, "0b63HQ381FypK0Bs+qKpRu"]]], [1, "045sKyxHtJM6bFYnhWVXOY", null, null, null, 1, 0], [1, 0, -2, 0], [1, 100, 1, 100]]], 0, [0, 2, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 3, 1, 6], [0, 0], [-1, 1], [0, 1]]