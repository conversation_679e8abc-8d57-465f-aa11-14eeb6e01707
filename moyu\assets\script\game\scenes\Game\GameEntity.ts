import { Collider, instantiate, Mesh<PERSON><PERSON>er, Node, Prefab, RigidBody, Vec3, Vec4 } from 'cc';
import { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';
import { ecs } from '../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';
import { PropType, SceneType, UsePropArgs } from '../../../tsrpc/protocols/base';
import { smc } from '../../common/SingletonModuleComp';

import { EventMessage } from '../../../../../extensions/oops-plugin-framework/assets/core/common/event/EventMessage';

import { ClientConst } from '../../common/ClientConst';

import { SceneItemType } from '../../../tsrpc/protocols/base';
import { GameEvent } from '../../common/Enum';
import { simpleLoader } from '../../common/loader/SimpleLoadingManager';
import { ItemEntity } from '../../item/ItemEntity';
import { ItemSceneViewComp } from '../../item/view/ItemSceneViewComp';
import { WallSceneViewComp } from '../../prefab/WallSceneViewComp';
import { GameSceneViewComp } from '../../sceneMgr/vIew/GameSceneViewComp';
import { BaseSceneEntity } from '../BaseSceneEntity';
import { GameModelComp } from './model/GameModelComp';
import { GameUIViewComp } from './view/GameUIViewComp';

// 🏗️ 统一管理器导入
import { configManager } from '../../managers/ConfigManager';
import { GameState, UnifiedGameManager } from '../../managers/UnifiedGameManager';

// 🎮 业务逻辑管理器导入
import { ModuleUtil } from '../../../../../extensions/oops-plugin-framework/assets/module/common/ModuleUtil';
import { UIID } from '../../common/config/GameUIConfig';
import { gameAudioManager } from '../../managers/AudioManager';
import { levelProgressManager } from '../../managers/LevelProgressManager';

// 🎯 导入触摸交互管理器
import { CollectionSlotManager } from '../../managers/CollectionSlotManager';
import { GameResultManager } from '../../managers/GameResultManager';
import { ItemInteractionManager } from '../../managers/ItemInteractionManager';
import { ItemInteractionManager as ItemInteractionManagerComp } from './ItemInteractionManager';

/**
 * 游戏实体 - 简化版
 * <AUTHOR>
 * @version 3.0.0 - 统一管理器架构 + 消融生成效果 + 完整触摸系统
 */
@ecs.register(`GameEntity`)
export class GameEntity extends BaseSceneEntity {
    // 🎯 核心组件
    GameModel!: GameModelComp;
    GameUIView!: GameUIViewComp;
    GameSceneView!: GameSceneViewComp;
    WallSceneView!: WallSceneViewComp;

    // 🏗️ 统一管理器
    public gameManager!: UnifiedGameManager;

    // 🎯 触摸交互管理器
    public interactionManager!: ItemInteractionManager;

    // 🎯 游戏结果管理器
    private gameResultManager!: GameResultManager;

    // 🎭 简化版消融系统 - 只用一个标志位
    private isDissolveAnimating: boolean = false;

    // 🎯 预加载状态管理
    private hardModeResourcesLoaded: boolean = false;

    /**
     * 🚀 实体初始化
     */
    protected init() {
        this.add(GameModelComp);
        this.listenEvent();
        // 延迟初始化gameManager，确保异步完成
        this.initializeGameManager();

        // 🎯 初始化触摸交互管理器
        this.interactionManager = new ItemInteractionManager(this);

        // 🎯 初始化游戏结果管理器
        this.gameResultManager = new GameResultManager(this);
    }

    /**
     * 🎮 初始化统一游戏管理器
     */
    private async initializeGameManager(): Promise<void> {
        try {
            // 确保ConfigManager已初始化
            await configManager.initialize();

            // 创建统一管理器
            this.gameManager = new UnifiedGameManager(this);

            // 设置状态回调
            this.setupStateCallbacks();

            oops.log.logBusiness('🎮 统一游戏管理器初始化完成');
        } catch (error) {
            oops.log.logError('❌ 游戏管理器初始化失败:', error);
        }
    }

    /**
     * 🎯 设置状态回调
     */
    private setupStateCallbacks(): void {
        this.gameManager.onStateChange(GameState.SimpleMode, () => {
            this.setupSimpleMode();
            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态
        });

        this.gameManager.onStateChange(GameState.HardMode, () => {
            this.setupHardMode();
            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态
        });

        this.gameManager.onStateChange(GameState.Win, () => {
            this.handleGameWin();
            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态
        });

        this.gameManager.onStateChange(GameState.GameOver, () => {
            this.handleGameFail();
            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态
        });

        this.gameManager.onStateChange(GameState.Paused, () => {
            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态
        });

        this.gameManager.onStateChange(GameState.Loading, () => {
            this.updateGameStateInViewModel(); // 🎯 更新ViewModel状态
        });
    }

    /**
     * 🎯 更新ViewModel中的游戏状态数据
     * 供VMState组件使用
     */
    public updateGameStateInViewModel(): void {
        const currentState = this.gameManager.getCurrentState();

        // 更新vmdata中的游戏状态
        this.GameModel.vmdata.gameState = currentState;
        this.GameModel.vmdata.isSimpleMode = this.gameManager.isSimpleMode() ? 1 : 0;
        this.GameModel.vmdata.isHardMode = this.gameManager.isHardMode() ? 1 : 0;
        this.GameModel.vmdata.isGameOver = this.gameManager.isGameOver() ? 1 : 0;
        this.GameModel.vmdata.isWin = this.gameManager.isWin() ? 1 : 0;
        this.GameModel.vmdata.isPaused = this.gameManager.isPaused() ? 1 : 0;
        this.GameModel.vmdata.isPlaying =
            this.gameManager.isSimpleMode() || this.gameManager.isHardMode() ? 1 : 0;

        oops.log.logBusiness(`🎯 ViewModel游戏状态已更新: ${currentState}`);
    }

    /**
     * ⚡ 设置简单模式
     */
    private setupSimpleMode(): void {
        const levelConfig = this.gameManager.getCurrentLevelConfig();
        if (levelConfig) {
            this.gameManager.setRefillStrategy({
                adjustByPerformance: false,
                adjustByTime: false,
                minInterval: levelConfig.spawnInterval || 3000,
                maxInterval: (levelConfig.spawnInterval || 3000) * 1.5,
                difficultyMultiplier: 1.0,
            });
        }
        gameAudioManager.playSimpleGameMusic();

        // 🎯 简单模式开始时，后台预加载困难模式资源
        this.startPreloadingHardModeInBackground();
    }

    /**
     * 🔥 设置困难模式
     */
    private setupHardMode(): void {
        const levelConfig = this.gameManager.getCurrentLevelConfig();
        if (levelConfig) {
            this.gameManager.setRefillStrategy({
                adjustByPerformance: true,
                adjustByTime: true,
                minInterval: levelConfig.spawnInterval || 2000,
                maxInterval: (levelConfig.spawnInterval || 2000) * 2,
                difficultyMultiplier: levelProgressManager.getDifficultyMultiplier(
                    smc.role.getPassIndex()
                ),
            });
        }
        gameAudioManager.playHardGameMusic();

        // 🎯 进入困难模式时完成新手引导（统一新手状态管理）
        if (smc.role.isNewPlayer()) {
            oops.log.logBusiness('🎓 进入困难模式，检测到新手玩家，开始完成新手引导...');
            oops.log.logBusiness(
                `🔍 引导前状态: isNewPlayer = ${smc.role.RoleModel?.userGameData?.isNewPlayer}`
            );

            smc.role
                .completeNewPlayerGuide()
                .then(success => {
                    if (success) {
                        oops.log.logBusiness('✅ 新手引导完成成功');
                        oops.log.logBusiness(
                            `🔍 引导后状态: isNewPlayer = ${smc.role.RoleModel?.userGameData?.isNewPlayer}`
                        );
                    } else {
                        oops.log.logError('❌ 新手引导完成失败');
                    }
                })
                .catch(error => {
                    oops.log.logError('❌ 完成新手引导异常:', error);
                });
        } else {
            oops.log.logBusiness('ℹ️ 进入困难模式，玩家已完成新手引导，跳过新手状态更新');
        }

        // 💰 困难模式开始时扣除挑战次数并记录挑战数据
        this.costChallengeAttempt()
            .then(success => {
                if (success) {
                    oops.log.logBusiness('✅ 困难模式挑战次数扣除成功');
                } else {
                    oops.log.logBusiness('❌ 困难模式挑战次数扣除失败');
                }
            })
            .catch(error => {
                oops.log.logError('❌ 困难模式挑战次数扣除异常:', error);
            });

        // 🎯 困难模式开始后开始预加载后续需要的资源
        this.startPreloadingEndGameResources();
    }

    /**
     * 🏆 处理游戏胜利
     */
    private async handleGameWin(): Promise<void> {
        oops.log.logBusiness('🏆 游戏胜利处理开始');

        // 🎯 委托给GameResultManager统一处理
        await this.gameResultManager.handleGameWin();
    }

    /**
     * 💥 处理游戏失败
     */
    private async handleGameFail(): Promise<void> {
        oops.log.logBusiness('💥 游戏失败处理开始');

        // 🎯 委托给GameResultManager统一处理
        await this.gameResultManager.handleGameFail();
    }

    /**
     * 🎯 简单模式开始时后台预加载困难模式资源
     */
    private startPreloadingHardModeInBackground(): void {
        // 🎯 异步后台预加载，不阻塞游戏进行，不显示Loading UI
        this.ensureHardModeResourcesPreloaded(false).catch(error => {
            oops.log.logError('❌ 困难模式资源后台预加载失败:', error);
            // 预加载失败时不设置标记，让胜利时再次尝试
        });
    }

    /**
     * 🎯 统一的困难模式资源预加载方法
     * @param showLoading 是否显示Loading界面
     */
    private async ensureHardModeResourcesPreloaded(showLoading: boolean = false): Promise<void> {
        const levelConfig = this.gameManager.getCurrentLevelConfig();
        if (!levelConfig?.hardModeItems || levelConfig.hardModeItems.length === 0) {
            oops.log.logBusiness('🎯 无困难模式道具需要预加载');
            this.hardModeResourcesLoaded = true;
            return;
        }

        if (this.hardModeResourcesLoaded) {
            oops.log.logBusiness('🎯 困难模式资源已预加载，跳过');
            return;
        }

        if (showLoading) {
            oops.gui.open(UIID.Loading);
        }

        try {
            const uniqueItemNames = Array.from(
                new Set(levelConfig.hardModeItems.map(item => item.name))
            );
            // 🎯 使用配置化的物品路径
            const itemPrefabPath = this.getItemPrefabPath();
            const prefabPaths = uniqueItemNames.map(name => `${itemPrefabPath}${name}`);

            await simpleLoader.loadPrefabs(prefabPaths);
            this.hardModeResourcesLoaded = true;
            oops.log.logBusiness(`✅ 困难模式道具预加载完成: ${uniqueItemNames.length} 种道具`);
        } catch (error) {
            oops.log.logError('❌ 困难模式资源预加载失败:', error);
            throw error;
        } finally {
            if (showLoading) {
                oops.gui.remove(UIID.Loading);
            }
        }
    }

    /**
     * 🎯 开始预加载游戏结束相关资源
     * 在困难模式开始时调用，后台预加载GameResult界面和Hall资源
     */
    private startPreloadingEndGameResources(): void {
        oops.log.logBusiness('🎯 困难模式开始，后台预加载结束游戏资源');

        // 预加载GameResult界面资源
        this.preloadGameResultResources();

        // 预加载Hall大厅资源
        this.preloadHallResources();
    }

    /**
     * 🎮 预加载GameResult界面资源
     */
    private async preloadGameResultResources(): Promise<void> {
        try {
            // 预加载GameResult预制体
            await oops.res.loadAsync('prefabs/commonPrefabs/GameResult', Prefab);
            oops.log.logBusiness('✅ GameResult界面资源预加载完成');
        } catch (error) {
            oops.log.logWarn('⚠️ GameResult界面资源预加载失败:', error);
        }
    }

    /**
     * 🏛️ 预加载Hall大厅资源
     */
    private async preloadHallResources(): Promise<void> {
        try {
            // 使用SimpleSceneManager的预加载方法
            const success = await smc.sceneMgr.preloadScene(SceneType.Hall);
            if (success) {
                oops.log.logBusiness('✅ Hall大厅资源预加载完成');
            } else {
                oops.log.logWarn('⚠️ Hall大厅资源预加载失败');
            }
        } catch (error) {
            oops.log.logWarn('⚠️ Hall大厅资源预加载失败:', error);
        }
    }

    /**
     * 🙈 游戏隐藏事件处理
     */
    onHide() {
        this.gameManager?.setInputEnabled(false);
    }

    /**
     * 👂 注册事件监听
     */
    listenEvent() {
        oops.message.on(EventMessage.GAME_HIDE, this.onHide, this);
        oops.message.on(GameEvent.UseProp, this.onUseProp, this);
    }

    /**
     * 🔇 注销事件监听
     */
    unListenEvent() {
        oops.message.off(EventMessage.GAME_HIDE, this.onHide, this);
        oops.message.off(GameEvent.UseProp, this.onUseProp, this);
    }

    /**
     * 👆 选择物品 - 恢复完整逻辑
     */
    chooseItem(item: Node) {
        const inputEnabled = this.gameManager.isInputEnabled();
        const dissolveAnimating = this.isDissolveAnimating;

        // 🎯 详细状态检查日志
        oops.log.logBusiness(
            `🎯 选择物品 ${item.name}: 输入启用=${inputEnabled}, 消融动画=${dissolveAnimating}`
        );

        if (!inputEnabled) {
            oops.log.logBusiness('🚫 输入被禁用，无法选择物品');
            return;
        }

        // 检查消融动画是否完成
        if (dissolveAnimating) {
            oops.log.logBusiness('🎭 消融动画进行中，无法选择物品');
            return;
        }

        // 委托给触摸交互管理器处理
        this.interactionManager.chooseItem(item);
    }

    /**
     * 🎯 选择额外区物品
     */
    chooseExtraItem(item: Node) {
        if (!this.gameManager.isInputEnabled()) return;

        this.interactionManager.chooseExtraItem(item);
    }

    /**
     * 💥 销毁实体
     */
    destroy(): void {
        console.log('🗑️ GameEntity销毁开始');

        // 🧹 清理收集槽和所有物品（防止场景切换时物品残留）
        if (this.GameModel) {
            console.log('🧹 场景切换时清理收集槽和物品');
            this.GameModel.clear();
        }

        // 🎯 清理交互管理器
        if (this.interactionManager) {
            this.interactionManager.destroy();
        }

        // 🎮 清理游戏管理器
        this.gameManager?.destroy();

        // 🎯 清理游戏结果管理器
        if (this.gameResultManager) {
            this.gameResultManager.destroy();
        }

        // 🔇 取消事件监听
        this.unListenEvent();

        // 🧹 调用父类销毁
        super.destroy();

        console.log('✅ GameEntity销毁完成');
    }

    /**
     * 🏗️ 加载UI和场景
     */
    async loadUIAndScene(index?: number, onEssentialLoaded?: () => void): Promise<void> {
        const level = index || 1; // 默认关卡1
        try {
            oops.log.logBusiness(`🎮 开始加载游戏场景，关卡: ${level}`);

            // 🔧 等待gameManager初始化完成
            await this.waitForGameManagerReady();

            // 🎓 如果是新手玩家，引导
            if (smc.role?.isNewPlayer()) {
                try {
                    await smc.guide.load();
                    oops.log.logBusiness('✅ 新手引导系统加载完成');
                } catch (error) {
                    oops.log.logError('❌ 引导系统加载失败:', error);
                    // 即使引导系统加载失败，也继续游戏流程
                }
            }

            await this.gameManager.loadLevel(level, onEssentialLoaded);
        } catch (error) {
            oops.log.logError(`❌ 关卡 ${level} 加载失败:`, error);
            throw error;
        }
    }

    /**
     * 🔧 等待gameManager准备就绪
     */
    private async waitForGameManagerReady(): Promise<void> {
        const maxWaitTime = 5000; // 最多等待5秒
        const checkInterval = 50; // 每50ms检查一次
        let waitedTime = 0;

        while (!this.gameManager && waitedTime < maxWaitTime) {
            await new Promise(resolve => setTimeout(resolve, checkInterval));
            waitedTime += checkInterval;
        }

        if (!this.gameManager) {
            throw new Error('GameManager初始化超时');
        }
    }

    /**
     * 🚨 紧急创建地板（当WallSceneView未加载时的备用方案）
     */
    private createEmergencyFloor() {
        oops.log.logWarn('🚨 启动紧急地板创建程序...');

        // 创建一个简单的地板节点
        const floorNode = new Node('EmergencyFloor');
        floorNode.parent = oops.game.root;
        floorNode.setPosition(0, -10, 0);
        floorNode.setScale(100, 1, 100);

        // 添加刚体组件
        const rigidBody = floorNode.addComponent(RigidBody);
        rigidBody.type = RigidBody.Type.STATIC;
        rigidBody.enabled = true;

        // 添加碰撞体组件
        const collider = floorNode.addComponent(Collider);
        collider.enabled = true;

        oops.log.logBusiness('✅ 紧急地板创建完成: 位置Y=-10, 尺寸100x100');
    }

    /**
     * 🏗️ 异步加载游戏场景（wallSceneView已包含在gameSceneView中）
     */
    async loadWallAndScene() {
        try {
            oops.log.logBusiness('🏗️ 开始加载游戏场景组件...');

            // 加载游戏场景视图（现在包含了wallSceneView子节点）
            oops.log.logBusiness(`📦 加载GameSceneView: ${this.GameModel.gameSceneViewPrefab}`);
            await ModuleUtil.addViewAsync(
                this,
                GameSceneViewComp,
                oops.game.root,
                this.GameModel.gameSceneViewPrefab
            );
            oops.log.logBusiness('✅ GameSceneView加载完成');

            // 🔧 确保GameSceneView的ent属性正确设置
            if (this.GameSceneView) {
                this.GameSceneView.ent = this;
                oops.log.logBusiness('✅ GameSceneView.ent属性已设置');

                // 🎯 在GameSceneView节点上添加触摸交互管理器组件
                if (!this.GameSceneView.node.getComponent(ItemInteractionManagerComp)) {
                    this.GameSceneView.node.addComponent(ItemInteractionManagerComp);
                    oops.log.logBusiness('✅ ItemInteractionManager组件已添加到GameSceneView');
                }
            } else {
                oops.log.logError('❌ GameSceneView组件未正确加载');
            }

            // 🔧 优化：从GameSceneView中获取WallSceneView子组件
            oops.log.logBusiness('🔍 从GameSceneView中查找WallSceneView组件...');
            const wallSceneNode = this.GameSceneView.node.getChildByName('wallSceneView');
            if (wallSceneNode) {
                const wallSceneComp = wallSceneNode.getComponent(WallSceneViewComp);
                if (wallSceneComp) {
                    // 手动注册WallSceneView组件到实体
                    this.WallSceneView = wallSceneComp;
                    this.WallSceneView.ent = this;
                    oops.log.logBusiness('✅ WallSceneView组件已从GameSceneView中获取');
                } else {
                    oops.log.logWarn('⚠️ wallSceneView节点中未找到WallSceneViewComp组件');
                }
            } else {
                oops.log.logWarn('⚠️ GameSceneView中未找到wallSceneView子节点');
            }

            oops.log.logBusiness('🎉 所有游戏场景组件加载完成');
        } catch (error) {
            oops.log.logError('❌ 游戏场景组件加载失败:', error);
            throw error;
        }
    }

    /**
     * 🎬 加载场景
     */
    loadScene() {
        this.loadWallAndScene().then(() => {
            setTimeout(() => {
                this.startGame();
            }, 300);
        });
    }

    /**
     * 🗑️ 销毁物品
     */
    destroyItem(itemEntity: ItemEntity) {
        // 🎯 安全检查：确保itemEntity和ItemModel存在
        if (!itemEntity || !itemEntity.ItemModel) {
            oops.log.logWarn(`⚠️ destroyItem: itemEntity或ItemModel为空`);
            return;
        }

        // 🎯 在销毁前释放PickBox（双重保险）
        if (itemEntity.ItemModel.pickBox) {
            itemEntity.ItemModel.pickBox.freeItem();
            oops.log.logBusiness(`📤 destroyItem时释放PickBox: ${itemEntity.ItemModel.itemId}`);
        }

        const itemId = itemEntity.ItemModel.itemId;
        if (itemId !== undefined && itemId !== null) {
            this.GameModel.allItemEntitys.delete(itemId);
        }

        itemEntity.destroy();
    }

    /**
     * 🔍 检查阈值触发
     */
    async checkThreshold() {
        this.gameManager.checkAndRefillItems();
    }

    /**
     * 🎯 在指定位置创建物品 - 简化版，适配重新设计的预制体
     */
    createItemOnPos(
        bornPos: Vec3,
        itemName: string,
        index: number,
        randomRotation: Boolean = true
    ) {
        const levelConfig = this.gameManager.getCurrentLevelConfig();
        if (!levelConfig) {
            oops.log.logError(`关卡配置未找到，无法创建物品: ${itemName}`);
            return;
        }

        const itemConfigs = this.gameManager.isSimpleMode()
            ? levelConfig.easyModeItems
            : levelConfig.hardModeItems;

        const foundItem = itemConfigs.find(item => item.name === itemName);
        if (!foundItem) {
            oops.log.logError(`物品配置未找到，无法创建物品: ${itemName}`);
            return;
        }

        // 创建ECS实体并预先初始化
        const itemEntity = ecs.getEntity<ItemEntity>(ItemEntity);
        itemEntity.ItemModel.itemId = index;
        itemEntity.ItemModel.itemType = SceneItemType.Foods;
        itemEntity.ItemModel.touching = false;

        // 🎯 设置缩放参数
        itemEntity.ItemModel.startScale = new Vec3(
            foundItem.startScale,
            foundItem.startScale,
            foundItem.startScale
        );
        itemEntity.ItemModel.pickScale = new Vec3(
            foundItem.pickScale,
            foundItem.pickScale,
            foundItem.pickScale
        );

        // 创建物品节点 - 支持从配置获取路径
        const itemPath = this.getItemPrefabPathFromConfig() + itemName;
        const prefab = oops.res.get(itemPath, Prefab);

        if (prefab) {
            const itemNode = instantiate(prefab);
            itemNode.setPosition(bornPos);

            // 🎯 保存预制体的原始旋转方向
            itemEntity.ItemModel.startRotation = itemNode.rotation.clone();

            // 然后才设置随机旋转（用于掉落效果）
            if (randomRotation) {
                const randomY = Math.random() * 360;
                itemNode.setRotationFromEuler(0, randomY, 0);
            }

            // 🎯 应用初始缩放到节点
            if (itemEntity.ItemModel.startScale) {
                itemNode.setScale(itemEntity.ItemModel.startScale);
            }

            oops.game.root.addChild(itemNode);

            // 获取或添加ItemSceneViewComp组件
            let itemSceneViewComp = itemNode.getComponent(ItemSceneViewComp);
            if (!itemSceneViewComp) {
                itemSceneViewComp = itemNode.addComponent(ItemSceneViewComp);
            }

            // 建立ECS关联
            itemSceneViewComp.ent = itemEntity;
            itemSceneViewComp.ItemModel = itemEntity.ItemModel;

            // 设置渲染器
            const itemMeshRenderer = itemNode.getComponent(MeshRenderer);
            if (itemMeshRenderer) {
                itemEntity.ItemModel.meshRenderer = itemMeshRenderer;
            }

            // 初始化组件
            if (typeof itemSceneViewComp.initializeComponent === 'function') {
                itemSceneViewComp.initializeComponent();
            }

            // 检查消融效果
            const meshRenderer = itemNode.getComponent(MeshRenderer);

            if (this.interactionManager && !this.interactionManager.sharedMaterial) {
                this.interactionManager.sharedMaterial = meshRenderer?.sharedMaterial;
                oops.log.logBusiness('✅ 消融材质已设置，等待统一启动');
            }

            // 添加到管理器
            this.GameModel.allItemEntitys.set(index, itemEntity);
            this.GameModel.allItemsToPick.set(index, itemNode);
        }
    }

    /**
     * 🎨 执行消融动画 - 性能优化版本
     */
    private runDissolveAnimation(duration: number): void {
        // 🚀 性能优化：降低更新频率到30fps，减少GPU状态切换
        const updateInterval = 1000 / 30; // 30fps足够流畅
        let elapsedTime = 0;
        let lastProgress = -1; // 避免重复设置相同值

        const updateTimer = setInterval(() => {
            elapsedTime += updateInterval / 1000;

            if (elapsedTime >= duration) {
                // 动画完成，设置最终状态
                this.interactionManager.sharedMaterial.setProperty(
                    'dissolveParams',
                    new Vec4(1.0, 0.1, 0.0, 0.0)
                );

                // 清理定时器并完成动画
                clearInterval(updateTimer);
                this.completeDissolveAndEnableInput();
                return;
            }

            // 计算当前进度并应用缓动
            const progress = elapsedTime / duration;
            const easedProgress = this.easeInOutCubic(progress);

            // 🚀 性能优化：只在进度有明显变化时才更新材质
            const progressDiff = Math.abs(easedProgress - lastProgress);
            if (progressDiff > 0.01) {
                // 1%的变化阈值
                this.interactionManager.sharedMaterial.setProperty(
                    'dissolveParams',
                    new Vec4(easedProgress, 0.1, 0.0, 0.0)
                );
                lastProgress = easedProgress;
            }
        }, updateInterval);
    }

    /**
     * 🎨 三次方缓动函数
     */
    private easeInOutCubic(t: number): number {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    /**
     * 🎉 完成消融动画并启用输入 - 统一入口
     */
    private completeDissolveAndEnableInput(): void {
        this.isDissolveAnimating = false;
        this.gameManager.setInputEnabled(true);
        oops.log.logBusiness('🎉 消融动画完成！游戏正式开始，可以触摸选择物品了！');
    }

    /**
     * 🎯 统一处理输入启用 - 检查是否需要消融动画
     */
    private handleInputEnabling(): void {
        // 🔍 检查是否有共享材质（通过材质属性判断是否需要消融动画）
        if (this.interactionManager?.sharedMaterial) {
            oops.log.logBusiness('🎬 检测到消融材质，启动消融动画');
            this.startDissolveAnimation();
        } else {
            // 🎯 没有消融动画，延迟一下直接启用输入（

            setTimeout(() => {
                this.completeDissolveAndEnableInput();
            }, 0.5 * 1000);
            oops.log.logBusiness('🎯 无消融材质，将延迟启用输入');
        }
    }

    /**
     * 🎬 启动消融动画 - 统一入口
     */
    private startDissolveAnimation(): void {
        const dissolveStartDelay = 0.2; // 延迟0.2秒开始消融
        const dissolveDuration = ClientConst.dissoveCreatedDuration || 1.5; // 消融总时长

        // 设置标志位
        this.isDissolveAnimating = true;

        // 设置初始消融参数：完全透明
        this.interactionManager.sharedMaterial.setProperty(
            'dissolveParams',
            new Vec4(0, 0.1, 0.0, 0.0)
        );

        // 延迟启动消融动画
        setTimeout(() => {
            oops.log.logBusiness(`🎬 开始消融动画，持续时间: ${dissolveDuration}秒`);
            this.runDissolveAnimation(dissolveDuration);
        }, dissolveStartDelay * 1000);
    }

    /**
     * 🎮 加载游戏UI
     */
    loadGameUI() {
        const uic = {
            onAdded: (node: Node, params: any) => {
                // 🔗 绑定UI组件到实体
                const comp = node.getComponent(GameUIViewComp)!;
                this.add(comp);
            },
        };
        oops.gui.open(this.GameModel.gameUIID, null, uic);
    }

    /**
     * 🎮 加载游戏UI并执行回调
     */
    loadGameUIWithCallback(onUIReady: () => void) {
        const uic = {
            onAdded: (node: Node, params: any) => {
                // 🔗 绑定UI组件到实体
                const comp = node.getComponent(GameUIViewComp)!;
                this.add(comp);

                // 执行回调
                onUIReady();
            },
        };
        oops.gui.open(this.GameModel.gameUIID, null, uic);
    }

    // =================== 公共API ===================

    public getCurrentGameState(): GameState {
        return this.gameManager.getCurrentState();
    }

    public isSimpleMode(): boolean {
        return this.gameManager.isSimpleMode();
    }

    public isHardMode(): boolean {
        return this.gameManager.isHardMode();
    }

    /**
     * 🎯 判断之前是否为困难模式（用于Win/GameOver状态时的模式判断）
     */
    public wasPreviouslyHardMode(): boolean {
        return this.gameManager.wasPreviouslyHardMode();
    }

    /**
     * 🎯 判断之前是否为简单模式（用于Win/GameOver状态时的模式判断）
     */
    public wasPreviouslySimpleMode(): boolean {
        return this.gameManager.wasPreviouslySimpleMode();
    }

    /**
     * 🎯 获取当前关卡索引（支持测试关卡）
     */
    public getCurrentLevelIndex(): number {
        return this.gameManager?.getCurrentLevelIndex() || 1;
    }

    /**
     * 🎯 获取当前关卡的物品预制体路径（公共方法）
     */
    public getItemPrefabPath(): string {
        return this.getItemPrefabPathFromConfig();
    }

    /**
     * 🎯 获取当前关卡的物品预制体路径
     */
    private getItemPrefabPathFromConfig(): string {
        const levelConfig = this.gameManager.getCurrentLevelConfig();
        if (!levelConfig) {
            oops.log.logWarn(`⚠️ 关卡配置不存在，使用默认物品路径`);
            return ClientConst.itemPrefabPaths; // 兜底默认路径
        }

        const configuredPath = levelConfig.itemPrefabPaths;

        // 确保路径以斜杠结尾
        const path = configuredPath.endsWith('/') ? configuredPath : configuredPath + '/';
        return path;
    }

    public checkAndRefillItems(touchedItem?: Node): void {
        this.gameManager.checkAndRefillItems(touchedItem);
    }

    /**
     * 🎯 检查输入是否启用（供引导系统使用）
     */
    public isInputEnabled(): boolean {
        return this.gameManager?.isInputEnabled() ?? false;
    }

    async doGameFail() {
        this.gameManager.changeState(GameState.GameOver);
    }

    async doGameWin() {
        this.gameManager.changeState(GameState.Win);
    }

    beforeStartGame() {
        // 🔧 调试：检查所有组件的加载状态
        oops.log.logBusiness('🔍 检查游戏组件加载状态:');
        oops.log.logBusiness(`- WallSceneView: ${this.WallSceneView ? '✅ 已加载' : '❌ 未加载'}`);
        oops.log.logBusiness(`- GameSceneView: ${this.GameSceneView ? '✅ 已加载' : '❌ 未加载'}`);
        oops.log.logBusiness(`- GameUIView: ${this.GameUIView ? '✅ 已加载' : '❌ 未加载'}`);

        // 🔧 强制调用墙体创建，即使组件未正确加载
        if (this.WallSceneView?.beforeStartGame) {
            oops.log.logBusiness(`🏗️ 调用WallSceneView.beforeStartGame()`);
            this.WallSceneView.beforeStartGame();
        } else {
            oops.log.logWarn('⚠️ WallSceneView组件未加载或beforeStartGame方法不存在');
            // 🔧 尝试手动创建基础地板
            this.createEmergencyFloor();
        }

        if (this.GameSceneView?.beforeStartGame) {
            oops.log.logBusiness('🎮 调用GameSceneView.beforeStartGame()');
            this.GameSceneView.beforeStartGame();

            // 🎯 直接初始化收集槽管理器
            if (this.interactionManager) {
                this.interactionManager.slotManager = new CollectionSlotManager(this.GameSceneView);
            }
        } else {
            oops.log.logWarn('⚠️ GameSceneView组件未加载');
        }

        if (this.GameUIView?.beforeStartGame) {
            oops.log.logBusiness('🖥️ 调用GameUIView.beforeStartGame()');
            this.GameUIView.beforeStartGame();
        } else {
            oops.log.logWarn('⚠️ GameUIView组件未加载');
        }

        this.restartFromSimpleMode();
    }

    /**
     * 🔄 从简单模式重新开始游戏
     */
    public restartFromSimpleMode(): void {
        // 🎯 强制设置为简单模式（状态回调会自动调用setupSimpleMode）
        this.gameManager.changeState(GameState.SimpleMode);

        // 🎮 开始游戏
        this.startGame();
    }

    async startGame() {
        // 🧹 统一清理游戏状态（避免重复清理）
        this.GameModel.clear();

        // 🧹 清理其他状态
        if (this.interactionManager) {
            this.interactionManager.sharedMaterial = null!;
        }
        this.isDissolveAnimating = false;
        this.hardModeResourcesLoaded = false;

        // 🎯 根据当前游戏状态初始化对应的道具（状态由外部设置，不在这里改变）
        if (this.isSimpleMode()) {
            this.gameManager.initializeEasyModeItems();
        } else if (this.isHardMode()) {
            this.gameManager.initializeHardModeItems();
        }

        // 🎯 统一创建道具（UnifiedGameManager会根据模式和新手状态选择策略）
        this.gameManager.createInitialItemsInScene();

        // 🎯 初始时禁用输入，然后统一处理输入启用
        this.gameManager.setInputEnabled(false);

        // 🎯 统一处理输入启用：检查是否需要消融动画
        this.handleInputEnabling();
    }

    /**
     * 🎯 检查游戏结果
     */
    checkResult() {
        // 🔍 输出当前状态调试信息
        const collectItemsCount = this.GameModel.collectItems.length;
        const allItemsToPickCount = this.GameModel.allItemsToPick.size;
        const gameMode = this.isSimpleMode() ? '简单模式' : '困难模式';

        oops.log.logBusiness(
            `🔍 检查游戏结果 [${gameMode}]: 收集槽=${collectItemsCount}, 场景物品=${allItemsToPickCount}`
        );

        // 检查是否所有物品都被收集
        if (collectItemsCount === 0 && allItemsToPickCount === 0) {
            // 游戏胜利
            oops.log.logBusiness(`🎉 ${gameMode}胜利！所有物品已收集`);
            this.gameManager.changeState(GameState.Win);
            return;
        }

        // 检查是否收集区已满 - 但要考虑三消可能性
        if (collectItemsCount >= 7) {
            // 🔍 检查是否有三消的可能性
            if (this.hasPossibleMerge()) {
                oops.log.logBusiness('🎯 收集区已满，但检测到可能的三消，等待三消完成...');
                // 有三消可能性，不立即结束游戏，等待三消逻辑处理
                return;
            }

            // 没有三消可能性，游戏失败
            oops.log.logBusiness('💥 游戏失败！收集区已满且无法三消');
            this.gameManager.changeState(GameState.GameOver);
        }
    }

    /**
     * 🔍 检查是否有可能的三消
     */
    private hasPossibleMerge(): boolean {
        if (!this.interactionManager?.slotManager) {
            return false;
        }

        // 统计每种物品的数量
        const itemCounts: { [key: string]: number } = {};
        for (const item of this.GameModel.collectItems) {
            const name = item.name;
            itemCounts[name] = (itemCounts[name] || 0) + 1;
        }

        // 检查是否有任何物品数量>=3
        for (const count of Object.values(itemCounts)) {
            if (count >= 3) {
                return true;
            }
        }

        return false;
    }

    onUseProp(_?: any, args?: UsePropArgs) {
        console.log('使用道具:', args);
    }

    handleUseProp(_?: any, args?: UsePropArgs) {
        console.log('使用道具:', args);
    }

    tryUseProp(usePropArgs: UsePropArgs, toastMsg = true): boolean {
        return true;
    }

    /**
     * 🔄 处理简单模式到困难模式的切换
     * 由GameResultManager调用，处理模式切换的完整流程
     */
    public async handleSimpleModeToHardMode(): Promise<void> {
        // 🎯 禁用输入，防止玩家操作
        this.gameManager.setInputEnabled(false);

        // 🧹 确保Guide系统被完全清理
        this.ensureGuideSystemCleanup();

        // 🎬 确保困难模式资源已预加载（显示Loading UI如果需要）
        await this.ensureHardModeResourcesPreloaded(true);

        // 🧹 统一清理游戏状态（避免重复清理）
        this.GameModel.clear();

        // 🧹 清理其他状态
        if (this.interactionManager) {
            this.interactionManager.sharedMaterial = null!;
        }
        this.isDissolveAnimating = false;

        // 🔄 切换到困难模式（状态回调会自动调用setupHardMode）
        this.gameManager.changeState(GameState.HardMode);

        // 🎯 初始化困难模式道具数组
        this.gameManager.initializeHardModeItems();

        // 🎯 创建困难模式道具
        this.gameManager.createInitialItemsInScene();

        // 🎯 处理输入启用
        this.handleInputEnabling();

        oops.log.logBusiness('✅ 困难模式切换完成，重复游戏流程开始');
    }

    /**
     * 🧹 确保Guide系统被完全清理
     */
    private ensureGuideSystemCleanup(): void {
        try {
            // 🎯 清理Guide系统
            if (smc.guide && smc.guide.GuideModel) {
                oops.log.logBusiness('🧹 清理Guide系统');
                smc.guide.GuideModel.reset();
            }

            // 🎯 补充清理GuideView3DItemComp（Guide系统没有清理的部分）
            if (this.GameSceneView && this.GameSceneView.node) {
                const guideItemComponents = this.GameSceneView.node.getComponentsInChildren(
                    'GuideView3DItem' as any
                );
                for (const comp of guideItemComponents) {
                    if (comp && comp.node && comp.node.isValid) {
                        oops.log.logBusiness(`🧹 清理3D引导组件: ${comp.node.name}`);
                        comp.node.destroy();
                    }
                }
            }

            oops.log.logBusiness('✅ Guide系统清理完成');
        } catch (error) {
            oops.log.logWarn('⚠️ 清理Guide系统时出错:', error);
        }
    }

    /**
     * 💰 扣除挑战次数并记录挑战数据
     * @returns Promise<boolean> 是否成功扣除
     */
    private async costChallengeAttempt(): Promise<boolean> {
        try {
            // 🔄 使用Role的updateProp方法扣除挑战次数（会自动调用服务端API记录数据）
            const success = await smc.role.updateProp(
                PropType.PropsDayLeftCount,
                -1,
                'start_hard_mode'
            );

            if (success) {
                oops.log.logBusiness('✅ 困难模式挑战次数已扣除，挑战数据已记录');
                return true;
            } else {
                oops.log.logBusiness('❌ 挑战次数扣除失败');
                return false;
            }
        } catch (error) {
            oops.log.logError('❌ 扣除挑战次数异常:', error);
            return false;
        }
    }
}

export class EcsSceneSystem extends ecs.System {
    constructor() {
        super();
    }
}
