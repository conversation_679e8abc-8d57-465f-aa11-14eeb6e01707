[{"__type__": "cc.Prefab", "_name": "hallRankUICell", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "hallRankUICell", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 17}], "_prefab": {"__id__": 19}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 116.562, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "8728975f-5233-40d6-9a87-08626aba6287", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "8eCNJYQF5Ck6oLAgJvxVQU", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 5}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}, {"__id__": 10}, {"__id__": 12}, {"__id__": 13}, {"__id__": 14}, {"__id__": 15}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_name"], "value": "VMLabelLanguage_White"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 11}, "propertyPath": ["templateMode"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["09i06HqvBP2ZaQ4Pk+p3Mt"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 11}, "propertyPath": ["watchPathArr", "length"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 11}, "propertyPath": ["watchPathArr", "0"], "value": ""}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 11}, "propertyPath": ["_dataID"], "value": "HallRankLabel"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 16}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 248.4375, "height": 50.4}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 18}, "_contentSize": {"__type__": "cc.Size", "width": 260.15625, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ehE0yjgpOdpGShi2Ty+Al"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "26iaepJgFCqKV3l5WUmOuO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 2}]}]