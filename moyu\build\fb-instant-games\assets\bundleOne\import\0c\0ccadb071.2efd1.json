[1, ["d2lw3KtrZG9aD626X+kq2a", "bdc/VAaXlMH57gR5XTjf+v@eeaea"], ["node", "root", "data", "_mesh"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11]], [[3, 0, 2], [0, 0, 2], [1, 0, 1, 2, 2], [2, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 3, 4, 3], [7, 0, 1, 2, 3, 4, 5, 5], [8, 0, 1, 2, 3]], [[[[1, "面包_8"], [2, "面包_8", [[3, 4, -2, [0, "4b88KkVElPC7ZlANJPjCf4"]], [4, 1, -3, [0, "7e2zvi3RhINbaAk/cIiiXH"], [0], [5, true, true], 1], [6, 0.35281963646411896, 0.4014894664287567, -4, [0, "0d+sUjZHVCHbYNofK8o7WD"], [1, -0.03426990658044815, 0.421771, -0.013825580477714539]]], [7, "51xjCfaW5J3qut8SGbZLxa", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]], [[[8, ".bin", 2294082655, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 117360, "length": 21132, "count": 10566, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 117360, "count": 2445, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.34658536314964294, 0.0005022621480748057, -0.34110504388809204], "maxPosition", 8, [1, 0.3900887966156006, 0.9157850742340088, 0.3191150724887848]]], -1], 0, 0, [], [], []]]]