[1, ["14f21396d@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [[[{"name": "标记", "rect": {"x": 371, "y": 854, "width": 25, "height": 31}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 25, "height": 31}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "shijian_bg", "rect": {"x": 272, "y": 645, "width": 240, "height": 93}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 240, "height": 93}, "rotated": true, "capInsets": [103, 52, 100, 35], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "下底板", "rect": {"x": 452, "y": 394, "width": 256, "height": 50}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 256, "height": 50}, "rotated": true, "capInsets": [126, 28, 130, 20], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "t1图标底", "rect": {"x": 412, "y": 854, "width": 72, "height": 72}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 72, "height": 72}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "0小按钮", "rect": {"x": 174, "y": 619, "width": 158, "height": 68}, "offset": {"x": 0, "y": 0.5}, "originalSize": {"width": 158, "height": 69}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "重置", "rect": {"x": 371, "y": 709, "width": 139, "height": 116}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 139, "height": 116}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "t底", "rect": {"x": 62, "y": 615, "width": 141, "height": 106}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 141, "height": 106}, "rotated": true, "capInsets": [57, 44, 63, 46], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "灯光", "rect": {"x": 3, "y": 3, "width": 285, "height": 222}, "offset": {"x": -0.5, "y": -1}, "originalSize": {"width": 290, "height": 226}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "暂停", "rect": {"x": 272, "y": 891, "width": 36, "height": 46}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 36, "height": 46}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "小底已通过", "rect": {"x": 324, "y": 891, "width": 32, "height": 38}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 38}, "rotated": true, "capInsets": [12, 14, 13, 15], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "震动", "rect": {"x": 217, "y": 231, "width": 63, "height": 55}, "offset": {"x": -0.5, "y": 0.5}, "originalSize": {"width": 70, "height": 70}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "松鼠", "rect": {"x": 62, "y": 231, "width": 134, "height": 149}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 134, "height": 149}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "绿色按钮", "rect": {"x": 3, "y": 808, "width": 263, "height": 113}, "offset": {"x": 0, "y": 1.5}, "originalSize": {"width": 263, "height": 116}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "亮", "rect": {"x": 469, "y": 158, "width": 53, "height": 39}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 53, "height": 39}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "红", "rect": {"x": 272, "y": 376, "width": 263, "height": 113}, "offset": {"x": 0, "y": 1.5}, "originalSize": {"width": 263, "height": 116}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "投影", "rect": {"x": 469, "y": 3, "width": 149, "height": 39}, "offset": {"x": -0.5, "y": 0.5}, "originalSize": {"width": 150, "height": 40}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "亮1", "rect": {"x": 469, "y": 217, "width": 53, "height": 39}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 53, "height": 39}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "+", "rect": {"x": 62, "y": 762, "width": 32, "height": 32}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "暗", "rect": {"x": 469, "y": 276, "width": 53, "height": 39}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 53, "height": 39}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "失败底", "rect": {"x": 294, "y": 3, "width": 367, "height": 169}, "offset": {"x": -0.5, "y": 0}, "originalSize": {"width": 368, "height": 169}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "箭头", "rect": {"x": 452, "y": 656, "width": 44, "height": 49}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 44, "height": 49}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "时钟", "rect": {"x": 207, "y": 571, "width": 42, "height": 42}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 42, "height": 42}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "小底未通过", "rect": {"x": 368, "y": 891, "width": 32, "height": 38}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 32, "height": 38}, "rotated": true, "capInsets": [13, 15, 14, 18], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "选择", "rect": {"x": 207, "y": 527, "width": 49, "height": 38}, "offset": {"x": -0.5, "y": 0}, "originalSize": {"width": 50, "height": 40}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "数值底", "rect": {"x": 207, "y": 477, "width": 44, "height": 45}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 44, "height": 45}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "guanbianniu", "rect": {"x": 217, "y": 355, "width": 48, "height": 47}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 48, "height": 47}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "凑齐", "rect": {"x": 62, "y": 371, "width": 139, "height": 116}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 139, "height": 116}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "音乐", "rect": {"x": 217, "y": 292, "width": 57, "height": 58}, "offset": {"x": -0.5, "y": 0}, "originalSize": {"width": 70, "height": 70}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "音效", "rect": {"x": 207, "y": 408, "width": 63, "height": 50}, "offset": {"x": -0.5, "y": 0}, "originalSize": {"width": 70, "height": 70}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "移出", "rect": {"x": 62, "y": 493, "width": 139, "height": 116}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 139, "height": 116}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "邀请有礼 底", "rect": {"x": 391, "y": 376, "width": 327, "height": 55}, "offset": {"x": -0.5, "y": 0.5}, "originalSize": {"width": 328, "height": 56}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "进度底", "rect": {"x": 3, "y": 231, "width": 571, "height": 53}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 571, "height": 53}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "暗1", "rect": {"x": 469, "y": 335, "width": 53, "height": 39}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 53, "height": 39}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]]]