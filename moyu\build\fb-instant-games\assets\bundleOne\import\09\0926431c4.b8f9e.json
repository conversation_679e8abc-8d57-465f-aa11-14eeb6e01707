[1, ["56oY0PFUVKRJA95WSWzdXD", "e28dJLVXJC3YFObxU5gPQx@b8ada"], ["node", "root", "data", "_mesh"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", [], 3], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["<PERSON>.<PERSON><PERSON><PERSON>", ["_radius", "_height", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11]], [[3, 0, 2], [7, 0, 1, 2, 3, 4, 3], [0, 0, 2], [1, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 5, 2], [4, 1], [5, 0, 1, 2, 2], [6, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5], [9, 0, 1, 2, 3]], [[[[2, "大蒜"], [3, "大蒜", [[4, 1, -2, [0, "33Si9LNTdETYSQFoU11pmB"], [0], [5], 1], [6, 4, -3, [0, "5aeuI6GrpI/4MTfbkRJ0Ej"]], [7, 0.085, 0.689, -4, [0, "6etljdRpxJ4IKdJb8/6f3I"], [1, 0.04, 0.14732910692691803, -0.01635456085205078]], [1, 0.422, 0.203, -5, [0, "31ugu1VNdJPKjvlvVDXDrj"], [1, 0.006435215473175049, -0.04, -0.01635456085205078]], [1, 0.349, 0.372, -6, [0, "80ZwSox2hE34jlaJS97HH9"], [1, 0.006435215473175049, -0.01, -0.01635456085205078]], [1, 0.203, 0.534, -7, [0, "98GHSJS81H6YyO1iBxlI0e"], [1, 0.006435215473175049, 0.01, -0.01635456085205078]]], [8, "3cacQAlMpNNo0uLrGg2MiE", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 7], [0, 0], [-1, 3], [0, 1]], [[[9, ".bin", 2869196629, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 26880, "length": 4488, "count": 2244, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 26880, "count": 420, "stride": 64}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}, {"name": "a_texCoord1", "format": 21, "isNormalized": false}, {"name": "a_texCoord2", "format": 21, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.4234117865562439, -0.2767798602581024, -0.4429178535938263], "maxPosition", 8, [1, 0.436282217502594, 0.5714380741119385, 0.41020873188972473]]], -1], 0, 0, [], [], []]]]