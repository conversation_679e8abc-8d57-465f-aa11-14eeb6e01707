[1, ["14I5OoJkFHdaecy/4b2eXK", "5dZJ5VKmVMn4WQldoN7+vA@f9941", "0fJ5VVbx1E4IlMUiRX0Fbb", "38EwnAhRxPKoeJSmk/h+84"], ["node", "root", "_parent", "asset", "data", "_spriteFrame"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_parent", "_lpos", "_components", "_children"], -1, 4, 1, 5, 9, 9], ["cc.Node", ["_name", "_layer", "_children", "_components", "_prefab", "_parent", "_lpos"], 1, 2, 9, 4, 1, 5], ["cc.Sprite", ["node", "__prefab", "_spriteFrame"], 3, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node", "__prefab"], 0, 1, 4], ["e0958oXX3VN7IhkdBJWFRYF", ["node", "__prefab"], 3, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab"], 2, 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isBold", "node", "__prefab", "_color"], -1, 1, 4, 5]], [[8, 0, 2], [4, 0, 1, 2, 3, 4, 5, 5], [15, 0, 1, 2, 2], [7, 0, 1, 2, 1], [0, 2, 3, 5, 4, 3], [6, 0, 1, 2, 3, 4, 5, 4], [12, 0, 2], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3], [3, 0, 2], [0, 0, 1, 5, 4, 6, 3], [0, 0, 1, 5, 7, 4, 6, 3], [1, 0, 1, 5, 2, 3, 4, 6, 3], [2, 0, 1, 1], [0, 0, 1, 8, 4, 3], [0, 0, 1, 5, 4, 3], [0, 0, 1, 5, 7, 4, 3], [1, 0, 1, 2, 3, 4, 3], [5, 0, 1, 2, 3, 4, 5, 4], [9, 0, 1, 2, 3, 4, 4], [10, 0, 1, 1], [2, 0, 1, 2, 1], [11, 0, 1, 2, 2], [16, 0, 1, 2, 3, 4, 5, 6, 5]], [[[[9, "create<PERSON><PERSON>er"], [14, "create<PERSON><PERSON>er", 524288, [[10, "layerOne", 524288, -3, [1, "61L6JcjNtJBpO5LVyWhaak", null, null, null, -2, 0], [1, 0, 10, 0]], [15, "layerTwo", 524288, -5, [1, "cfyq6RP/FC9INyHs1A38hL", null, null, null, -4, 0]], [10, "layerTree", 524288, -7, [1, "cd8KjzBSlIb7UuBvMIEN8C", null, null, null, -6, 0], [1, 0, -10, 0]]], [1, "afQdJYzHlJS4e09arjozPb", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 1, 1, 0, 2, 1, 0, 1, 1, 0, 2, 1, 0, 1, 1, 0, 2, 1, 0, 4, 1, 7], [], [], []], [[[9, "revive"], [17, "revive", 33554432, [-8, -9], [[3, -5, [0, "c0cp8XlKBJOILLPAv66S6x"], [5, 750, 1334]], [19, 45, 100, 100, -6, [0, "6ekLu4c1FA0bUftvRoWF0+"]], [20, -7, [0, "eb6khvnudLVJI2etW53i3H"]]], [18, "cerIilZb1NcrnFzz7o2GEh", null, null, -4, 0, [-1, -2, -3]]], [12, "contentArea", 33554432, 1, [-11, -12, -13, -14], [[3, -10, [0, "692Y5rkj9PPanWAmVlp0s5"], [5, 558, 565]]], [1, "c6YUzDtZBCVpPFmbBRNnyd", null, null, null, 1, 0], [1, 0, 95.054, 0]], [12, "videoBtn", 33554432, 2, [-18, -19], [[3, -15, [0, "0aW35um+ZB8bIDCIvn+QC9"], [5, 173, 118]], [13, -16, [0, "0eSJhw2XROmYHwggTXI91L"]], [22, 3, -17, [0, "70PJ+xpbdPKYj57pSoAZ1k"]]], [1, "2dyVCQQrlGCbF/cM1vrkRU", null, null, null, 1, 0], [1, 3.662, -248.01, 0]], [6, ["a0daVw8DRLi6ToMaTA0VS2"]], [6, ["afQdJYzHlJS4e09arjozPb"]], [6, ["46L362HwxAyYxM0TWgugL+"]], [4, 0, {}, 1, [5, "a0daVw8DRLi6ToMaTA0VS2", null, null, -20, [7, "131+Y0z11M5Y54N99JB0X6", 1, [[8, "mask", ["_name"], 4], [2, ["_lpos"], 4, [1, 0, 0, 0]], [2, ["_lrot"], 4, [3, 0, 0, 0, 1]], [2, ["_euler"], 4, [1, 0, 0, 0]]]], 0]], [16, "bg", 33554432, 2, [[3, -21, [0, "0bSrWSLwBOvqIIHkguBJ/a"], [5, 735, 737]], [21, -22, [0, "d7XuP27wFA2ZKYBN+XPpPp"], 1]], [1, "9dLEhw+RtLLYrdo62R9mkQ", null, null, null, 1, 0]], [11, "img_heart", 33554432, 2, [[3, -23, [0, "8dMTGIInZHEodOHEGgaBYf"], [5, 304, 304]], [13, -24, [0, "baZpevnERKJbtDgMYEwOvR"]]], [1, "451XfakNRKupx5OzRUiwAE", null, null, null, 1, 0], [1, -4, 74.413, 0]], [11, "btnTips", 33554432, 3, [[3, -25, [0, "1awULQqohBxKBz2uHCLOEF"], [5, 241.796875, 50.4]], [23, "免费获得1次复活", 32, 32, true, -26, [0, "bay/TvVRtF34eViSnBptxY"], [4, 4286219621]]], [1, "24dyIPKvFGT7giUjUmudVl", null, null, null, 1, 0], [1, 0, 87.446, 0]], [4, 0, {}, 3, [5, "afQdJYzHlJS4e09arjozPb", null, null, -27, [7, "cfZeuPnqpLYpE6Dtzl2qcp", 1, [[8, "create<PERSON><PERSON>er", ["_name"], 5], [2, ["_lpos"], 5, [1, 0, 0, 0]], [2, ["_lrot"], 5, [3, 0, 0, 0, 1]], [2, ["_euler"], 5, [1, 0, 0, 0]]]], 2]], [4, 0, {}, 2, [5, "46L362HwxAyYxM0TWgugL+", null, null, -28, [7, "1fppMbMAROeprvTKNSYN7y", 1, [[8, "closeBtn", ["_name"], 6], [2, ["_lpos"], 6, [1, 322.556, 330.842, 0]], [2, ["_lrot"], 6, [3, 0, 0, 0, 1]], [2, ["_euler"], 6, [1, 0, 0, 0]]]], 3]]], 0, [0, -1, 12, 0, -2, 11, 0, -3, 7, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 7, 0, -2, 2, 0, 0, 2, 0, -1, 8, 0, -2, 9, 0, -3, 3, 0, -4, 12, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 10, 0, -2, 11, 0, 1, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 1, 11, 0, 1, 12, 0, 4, 1, 28], [0, 0, 0, 0], [3, 5, 3, 3], [0, 1, 2, 3]]]]