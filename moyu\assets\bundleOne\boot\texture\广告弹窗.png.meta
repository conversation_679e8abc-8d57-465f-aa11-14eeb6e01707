{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "5d649e55-2a65-4c9f-8590-95da0defebc0", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "5d649e55-2a65-4c9f-8590-95da0defebc0@6c48a", "displayName": "广告弹窗", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0, "isUuid": true, "imageUuidOrDatabaseUri": "5d649e55-2a65-4c9f-8590-95da0defebc0", "visible": false}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "5d649e55-2a65-4c9f-8590-95da0defebc0@f9941", "displayName": "广告弹窗", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 460, "height": 461, "rawWidth": 460, "rawHeight": 461, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-230, -230.5, 0, 230, -230.5, 0, -230, 230.5, 0, 230, 230.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 461, 460, 461, 0, 0, 460, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-230, -230.5, 0], "maxPos": [230, 230.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "5d649e55-2a65-4c9f-8590-95da0defebc0@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"hasAlpha": true, "type": "sprite-frame", "fixAlphaTransparencyArtifacts": false, "redirect": "5d649e55-2a65-4c9f-8590-95da0defebc0@6c48a"}}