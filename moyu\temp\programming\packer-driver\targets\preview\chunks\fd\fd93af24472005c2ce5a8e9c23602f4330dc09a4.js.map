{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/tsrpc/models/ShareConfig.ts"], "names": ["getCurrentEnvironment", "Environment", "PRODUCTION_FACEBOOK", "validateConfig", "config", "ShareConfig", "serverUrl", "clientUrl", "console", "error", "mongoUrl", "mongoDbName", "enableFacebookSDK", "facebookAppId", "printConfigInfo", "log", "environment", "platform", "port", "isProduction", "Platform", "PRODUCTION_FACEBOOK_CONFIG", "FACEBOOK", "gamePort", "gameServerUrl", "enableCors", "cors<PERSON><PERSON><PERSON>", "logLevel", "enableAnalytics", "maxPlayersPerRoom", "gameSettings", "enableRanking", "enableRewards", "enableSocialFeatures", "https", "gate", "httpPort", "json", "security", "CONFIG_MAP", "DEVELOPMENT", "FACEBOOK_MOCK", "PRODUCTION_PERSONAL"], "mappings": ";;;;;AA+CA;AACA,WAASA,qBAAT,GAA8C;AAC1C;AACA,WAAOC,WAAW,CAACC,mBAAnB;AACH,G,CAED;;;AA8CA;AACO,WAASC,cAAT,GAAmC;AACtC,QAAMC,MAAM,GAAGC,WAAf;;AAEA,QAAI,CAACD,MAAM,CAACE,SAAR,IAAqB,CAACF,MAAM,CAACG,SAAjC,EAA4C;AACxCC,MAAAA,OAAO,CAACC,KAAR,CAAc,oCAAd;AACA,aAAO,KAAP;AACH;;AAED,QAAI,CAACL,MAAM,CAACM,QAAR,IAAoB,CAACN,MAAM,CAACO,WAAhC,EAA6C;AACzCH,MAAAA,OAAO,CAACC,KAAR,CAAc,wCAAd;AACA,aAAO,KAAP;AACH;;AAED,QAAIL,MAAM,CAACQ,iBAAP,IAA4B,CAACR,MAAM,CAACS,aAAxC,EAAuD;AACnDL,MAAAA,OAAO,CAACC,KAAR,CAAc,6CAAd;AACA,aAAO,KAAP;AACH;;AAED,WAAO,IAAP;AACH,G,CAED;;;AACO,WAASK,eAAT,GAAiC;AACpCN,IAAAA,OAAO,CAACO,GAAR,CAAY,mDAAZ;AACAP,IAAAA,OAAO,CAACO,GAAR,mBAA4BV,WAAW,CAACW,WAAxC;AACAR,IAAAA,OAAO,CAACO,GAAR,gBAAyBV,WAAW,CAACY,QAArC;AACAT,IAAAA,OAAO,CAACO,GAAR,kBAA2BV,WAAW,CAACC,SAAvC;AACAE,IAAAA,OAAO,CAACO,GAAR,kBAA2BV,WAAW,CAACE,SAAvC;AACAC,IAAAA,OAAO,CAACO,GAAR,gBAAyBV,WAAW,CAACM,WAArC;AACAH,IAAAA,OAAO,CAACO,GAAR,YAAqBV,WAAW,CAACa,IAAjC;AACAV,IAAAA,OAAO,CAACO,GAAR,kBAA2BV,WAAW,CAACc,YAAvC;AACAX,IAAAA,OAAO,CAACO,GAAR,oBAA6BV,WAAW,CAACO,iBAAzC;AACAJ,IAAAA,OAAO,CAACO,GAAR,uBAAgCV,WAAW,CAACQ,aAA5C;AACAL,IAAAA,OAAO,CAACO,GAAR,CAAY,oDAAZ;AACH;;;oBAlCeZ,c;qBAsBAW;;;;;;;;;;;;AA1HhB;AACA;6BAEYb,W,0BAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;eAAAA,W;;;0BAOAmB,Q,0BAAAA,Q;AAAAA,QAAAA,Q;AAAAA,QAAAA,Q;eAAAA,Q;;;AA4CNC,MAAAA,0B,GAA2C;AAC7CL,QAAAA,WAAW,EAAEf,WAAW,CAACC,mBADoB;AAE7Ce,QAAAA,QAAQ,EAAEG,QAAQ,CAACE,QAF0B;AAG7ChB,QAAAA,SAAS,EAAE,oCAHkC;AAI7CC,QAAAA,SAAS,EAAE,gCAJkC;AAK7CG,QAAAA,QAAQ,EAAE,kCALmC;AAM7CC,QAAAA,WAAW,EAAE,eANgC;AAO7CO,QAAAA,IAAI,EAAE,IAPuC;AAQ7CK,QAAAA,QAAQ,EAAE,IARmC;AAS7CC,QAAAA,aAAa,EAAE,yCAT8B;AAU7CL,QAAAA,YAAY,EAAE,IAV+B;AAW7CM,QAAAA,UAAU,EAAE,IAXiC;AAY7CC,QAAAA,WAAW,EAAE,CACT,uBADS,EAET,0BAFS,EAGT,iCAHS,CAZgC;AAiB7CC,QAAAA,QAAQ,EAAE,MAjBmC;AAkB7Cf,QAAAA,iBAAiB,EAAE,IAlB0B;AAmB7CgB,QAAAA,eAAe,EAAE,IAnB4B;AAoB7CC,QAAAA,iBAAiB,EAAE,CApB0B;AAqB7CC,QAAAA,YAAY,EAAE;AACVC,UAAAA,aAAa,EAAE,IADL;AAEVC,UAAAA,aAAa,EAAE,IAFL;AAGVC,UAAAA,oBAAoB,EAAE;AAHZ,SArB+B;AA0B7C;AACAC,QAAAA,KAAK,EAAE,IA3BsC;AA4B7CC,QAAAA,IAAI,EAAE,oBA5BuC;AA6B7CC,QAAAA,QAAQ,EAAE,IA7BmC;AA8B7CC,QAAAA,IAAI,EAAE,IA9BuC;AA+B7CC,QAAAA,QAAQ,EAAE;AA/BmC,O,EAkCjD;;AACMC,MAAAA,U,GAAgD;AAClD,SAACtC,WAAW,CAACuC,WAAb,GAA2BnB,0BADuB;AACK;AACvD,SAACpB,WAAW,CAACwC,aAAb,GAA6BpB,0BAFqB;AAEO;AACzD,SAACpB,WAAW,CAACyC,mBAAb,GAAmCrB,0BAHe;AAGa;AAC/D,SAACpB,WAAW,CAACC,mBAAb,GAAmCmB;AAJe,O,EAOtD;;6BACahB,W,GAA4BgB,0B;;yBAuC1BhB,W", "sourcesContent": ["// ShareConfig.ts - master-facebook分支配置 (Facebook生产环境)\n// 根据分支自动确定环境配置，简化部署流程\n\nexport enum Environment {\n    DEVELOPMENT = 'development',\n    FACEBOOK_MOCK = 'facebook_mock',\n    PRODUCTION_PERSONAL = 'production_personal',\n    PRODUCTION_FACEBOOK = 'production_facebook',\n}\n\nexport enum Platform {\n    PERSONAL = 'personal',\n    FACEBOOK = 'facebook',\n}\n\nexport interface ServerConfig {\n    environment: Environment;\n    platform: Platform;\n    serverUrl: string;\n    clientUrl: string;\n    mongoUrl: string;\n    mongoDbName: string;\n    port: number;\n    gamePort: number;\n    gameServerUrl: string;\n    isProduction: boolean;\n    enableCors: boolean;\n    corsOrigins: string[];\n    logLevel: 'debug' | 'info' | 'warn' | 'error';\n    enableFacebookSDK: boolean;\n    facebookAppId?: string;\n    enableAnalytics: boolean;\n    maxPlayersPerRoom: number;\n    gameSettings: {\n        enableRanking: boolean;\n        enableRewards: boolean;\n        enableSocialFeatures: boolean;\n    };\n\n    // 兼容旧客户端代码的属性 - 纯HTTP架构\n    https: boolean;\n    gate: string;\n    httpPort: number;\n    json: boolean;\n    security: boolean;\n}\n\n// 根据当前分支确定环境\nfunction getCurrentEnvironment(): Environment {\n    // master-facebook 分支固定返回 PRODUCTION_FACEBOOK 环境\n    return Environment.PRODUCTION_FACEBOOK;\n}\n\n// 🔧 master-facebook分支专用：Facebook生产环境配置\nconst PRODUCTION_FACEBOOK_CONFIG: ServerConfig = {\n    environment: Environment.PRODUCTION_FACEBOOK,\n    platform: Platform.FACEBOOK,\n    serverUrl: 'https://idlefun.press/api/facebook',\n    clientUrl: 'https://idlefun.press/facebook',\n    mongoUrl: 'mongodb://mongodb-facebook:27017',\n    mongoDbName: 'moyu_facebook',\n    port: 3002,\n    gamePort: 3003,\n    gameServerUrl: 'https://idlefun.press/api/facebook/game',\n    isProduction: true,\n    enableCors: true,\n    corsOrigins: [\n        'https://idlefun.press',\n        'https://www.facebook.com',\n        'https://developers.facebook.com',\n    ],\n    logLevel: 'warn',\n    enableFacebookSDK: true,\n    enableAnalytics: true,\n    maxPlayersPerRoom: 8,\n    gameSettings: {\n        enableRanking: true,\n        enableRewards: true,\n        enableSocialFeatures: true,\n    },\n    // 兼容属性\n    https: true,\n    gate: 'idlefun.press:3002',\n    httpPort: 3002,\n    json: true,\n    security: true,\n};\n\n// 环境配置映射\nconst CONFIG_MAP: Record<Environment, ServerConfig> = {\n    [Environment.DEVELOPMENT]: PRODUCTION_FACEBOOK_CONFIG, // 简化\n    [Environment.FACEBOOK_MOCK]: PRODUCTION_FACEBOOK_CONFIG, // 简化\n    [Environment.PRODUCTION_PERSONAL]: PRODUCTION_FACEBOOK_CONFIG, // 简化\n    [Environment.PRODUCTION_FACEBOOK]: PRODUCTION_FACEBOOK_CONFIG,\n};\n\n// 获取当前配置 - master-facebook分支专用Facebook生产配置\nexport const ShareConfig: ServerConfig = PRODUCTION_FACEBOOK_CONFIG;\n\n// 配置验证\nexport function validateConfig(): boolean {\n    const config = ShareConfig;\n\n    if (!config.serverUrl || !config.clientUrl) {\n        console.error('Missing required URL configuration');\n        return false;\n    }\n\n    if (!config.mongoUrl || !config.mongoDbName) {\n        console.error('Missing required MongoDB configuration');\n        return false;\n    }\n\n    if (config.enableFacebookSDK && !config.facebookAppId) {\n        console.error('Facebook SDK enabled but no App ID provided');\n        return false;\n    }\n\n    return true;\n}\n\n// 打印当前配置信息\nexport function printConfigInfo(): void {\n    console.log('=== ShareConfig Information (master-facebook) ===');\n    console.log(`Environment: ${ShareConfig.environment}`);\n    console.log(`Platform: ${ShareConfig.platform}`);\n    console.log(`Server URL: ${ShareConfig.serverUrl}`);\n    console.log(`Client URL: ${ShareConfig.clientUrl}`);\n    console.log(`Database: ${ShareConfig.mongoDbName}`);\n    console.log(`Port: ${ShareConfig.port}`);\n    console.log(`Production: ${ShareConfig.isProduction}`);\n    console.log(`Facebook SDK: ${ShareConfig.enableFacebookSDK}`);\n    console.log(`Facebook App ID: ${ShareConfig.facebookAppId}`);\n    console.log('==================================================');\n}\n\nexport default ShareConfig;\n"]}