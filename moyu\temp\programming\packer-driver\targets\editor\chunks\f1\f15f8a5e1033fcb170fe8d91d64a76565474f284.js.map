{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/initialize/view/InitialViewComp.ts"], "names": ["_decorator", "tween", "UIOpacity", "Vec3", "DEBUG", "DEV", "oops", "<PERSON><PERSON><PERSON><PERSON>", "ecs", "CCComp", "Platform", "ShareConfig", "SceneType", "ClientConst", "GameStorageConfig", "StorageTestUtils", "smc", "ccclass", "property", "InitialViewComp", "register", "waitComplete", "loadComplete", "currentProgress", "start", "platform", "FACEBOOK", "log", "logBusiness", "PERSONAL", "updateLoadingProgress", "playWaitAnimation", "startFullInitialization", "logoOpacity", "node", "getChildByName", "getComponent", "opacity", "to", "position", "call", "enterGame", "progress", "message", "detail", "window", "updateProgress", "bundleResult", "config<PERSON><PERSON><PERSON>", "Promise", "all", "loadBundleAndUIResources", "loadConfigurationData", "loadUserDataInBackground", "gui", "setOpenFailure", "onOpenFailure", "error", "handleInitializationError", "ensureUserDataLoaded", "log<PERSON>arn", "createTemporaryUserData", "tempUserData", "key", "userName", "index", "isNewPlayer", "propUseData", "recordData", "isTemporary", "role", "RoleModel", "res", "loadBundle", "Error", "simpleLoader", "tasks", "name", "dirs", "priority", "bundle", "result", "loadTasks", "logError", "languageResult", "loadDirAsync", "loadLanguage", "language", "storage", "get", "Language", "setLanguage", "alwaysNewPlayerTest", "forceNewPlayerState", "userGameData", "isLikelyNewPlayer", "SSOToken", "LoginViewComp", "loginSuccess", "doFacebookLogin", "existingToken", "tokenInfo", "getTokenInfo", "<PERSON><PERSON><PERSON><PERSON>", "expiredTime", "Date", "now", "创建时间", "createdTime", "toLocaleString", "过期时间", "当前时间", "剩余时间", "Math", "round", "loadData", "remove", "SSOTokenInfo", "tempLoginComp", "guestLoginSuccess", "loginGuestButton", "success", "showLoading", "sceneMgr", "switchToScene", "Foods", "closeLoadingUI", "bind", "Hall", "undefined", "toast", "ent", "includes", "retryInitialization", "setTimeout", "notifyHTMLLoadingComplete", "<PERSON><PERSON><PERSON><PERSON>", "notifyFacebookComplete", "<PERSON><PERSON><PERSON>", "setLoadingProgress", "tokenInfoStr", "JSON", "parse", "reset"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AAE9BC,MAAAA,K,UAAAA,K;AAAOC,MAAAA,G,UAAAA,G;;AACPC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;;AACVC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,iB,iBAAAA,iB;AAAmBC,MAAAA,gB,iBAAAA,gB;;AACnBC,MAAAA,G,kBAAAA,G;;;;;;;;;AAET;OASM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBlB,U;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;iCAGamB,e,WAFZF,OAAO,CAAC,iBAAD,C,UACP;AAAA;AAAA,sBAAIG,QAAJ,CAAa,aAAb,EAA4B,KAA5B,C,+BADD,MAEaD,eAFb;AAAA;AAAA,4BAE4C;AAAA;AAAA;AAAA,eAChCE,YADgC,GACR,KADQ;AAAA,eAEhCC,YAFgC,GAER,KAFQ;AAAA,eAGhCC,eAHgC,GAGN,CAHM;AAAA;;AAKxCC,QAAAA,KAAK,GAAG;AACJ;AACA,cAAI;AAAA;AAAA,0CAAYC,QAAZ,KAAyB;AAAA;AAAA,oCAASC,QAAtC,EAAgD;AAC5C;AAAA;AAAA,8BAAKC,GAAL,CAASC,WAAT,CAAqB,wBAArB;AACH,WAFD,MAEO,IAAI;AAAA;AAAA,0CAAYH,QAAZ,KAAyB;AAAA;AAAA,oCAASI,QAAtC,EAAgD;AACnD;AAAA;AAAA,8BAAKF,GAAL,CAASC,WAAT,CAAqB,kBAArB;AACA,iBAAKE,qBAAL,CAA2B,EAA3B,EAA+B,SAA/B,EAA0C,WAA1C;AACH,WAHM,MAGA;AACH;AACA,iBAAKC,iBAAL;AACH;;AAED,eAAKC,uBAAL;AACH;AAED;;;AACQD,QAAAA,iBAAiB,GAAG;AAAA;;AACxB,cAAIE,WAAW,4BAAG,KAAKC,IAAL,CAAUC,cAAV,CAAyB,MAAzB,CAAH,qBAAG,sBAAkCC,YAAlC,CAA+ClC,SAA/C,CAAlB;;AACA,cAAI+B,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAACI,OAAZ,GAAsB,EAAtB;AACApC,YAAAA,KAAK,CAACgC,WAAW,CAACC,IAAb,CAAL,CACKI,EADL,CACQ,CADR,EACW;AAAED,cAAAA,OAAO,EAAE,GAAX;AAAgBE,cAAAA,QAAQ,EAAE,IAAIpC,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAA1B,aADX,EAEKqC,IAFL,CAEU,MAAM;AACR,mBAAKnB,YAAL,GAAoB,IAApB;;AACA,kBAAI,KAAKC,YAAT,EAAuB;AACnB,qBAAKmB,SAAL;AACH;AACJ,aAPL,EAQKjB,KARL;AASH,WAXD,MAWO;AACH,iBAAKH,YAAL,GAAoB,IAApB;AACH;AACJ;AAED;;;AACQS,QAAAA,qBAAqB,CAACY,QAAD,EAAmBC,OAAnB,EAAoCC,MAApC,EAAqD;AAC9E,eAAKrB,eAAL,GAAuBmB,QAAvB;;AAEA,cAAIG,MAAM,CAACC,cAAP,IAAyB,OAAOD,MAAM,CAACC,cAAd,KAAiC,UAA9D,EAA0E;AACtED,YAAAA,MAAM,CAACC,cAAP,CAAsBJ,QAAtB,EAAgCC,OAAhC,EAAyCC,MAAzC;AACA;AAAA;AAAA,8BAAKjB,GAAL,CAASC,WAAT,CAAsB,YAAWc,QAAS,OAAMC,OAAQ,EAAxD;AACH;AACJ;AAED;;;AACqC,cAAvBX,uBAAuB,GAAG;AACpC,cAAI;AACA;AACA,kBAAM,CAACe,YAAD,EAAeC,YAAf,IAA+B,MAAMC,OAAO,CAACC,GAAR,CAAY,CACnD;AACA,iBAAKC,wBAAL,EAFmD,EAGnD;AACA,iBAAKC,qBAAL,EAJmD,CAAZ,CAA3C,CAFA,CASA;;AACA,iBAAKC,wBAAL,GAVA,CAYA;;AACA;AAAA;AAAA,8BAAKC,GAAL,CAASC,cAAT,CAAwB,KAAKC,aAA7B;AAEA,iBAAKlC,YAAL,GAAoB,IAApB,CAfA,CAiBA;;AACA,gBACI;AAAA;AAAA,4CAAYG,QAAZ,KAAyB;AAAA;AAAA,sCAASC,QAAlC,IACA;AAAA;AAAA,4CAAYD,QAAZ,KAAyB;AAAA;AAAA,sCAASI,QAFtC,EAGE;AACE,mBAAKY,SAAL;AACH,aALD,MAKO,IAAI,KAAKpB,YAAT,EAAuB;AAC1B,mBAAKoB,SAAL;AACH;AACJ,WA1BD,CA0BE,OAAOgB,KAAP,EAAc;AACZ,iBAAKC,yBAAL,CAA+BD,KAA/B;AACH;AACJ;AAED;;;AACsC,cAAxBJ,wBAAwB,GAAG;AACrC,cAAI;AACA,kBAAM,KAAKM,oBAAL,EAAN;AACA;AAAA;AAAA,8BAAKhC,GAAL,CAASC,WAAT,CAAqB,cAArB;AACH,WAHD,CAGE,OAAO6B,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAK9B,GAAL,CAASiC,OAAT,CAAiB,yBAAjB,EAA4CH,KAA5C,EADY,CAEZ;AACH;AACJ;AAED;;;AACQI,QAAAA,uBAAuB,GAAS;AACpC,cAAI;AACA;AACA,kBAAMC,YAAY,GAAG;AACjBC,cAAAA,GAAG,EAAE,CADY;AAEjBC,cAAAA,QAAQ,EAAE,YAFO;AAGjBC,cAAAA,KAAK,EAAE,CAHU;AAIjBC,cAAAA,WAAW,EAAE,IAJI;AAKjBC,cAAAA,WAAW,EAAE,EALI;AAMjBC,cAAAA,UAAU,EAAE,EANK;AAOjB;AACAC,cAAAA,WAAW,EAAE;AARI,aAArB,CAFA,CAaA;;AACA,gBAAI,CAAC;AAAA;AAAA,4BAAIC,IAAJ,CAASC,SAAd,EAAyB;AACrB;AACA;AAAA;AAAA,gCAAK5C,GAAL,CAASC,WAAT,CAAqB,sBAArB;AACH;;AAED;AAAA;AAAA,8BAAKD,GAAL,CAASC,WAAT,CAAqB,uBAArB;AACH,WApBD,CAoBE,OAAO6B,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAK9B,GAAL,CAASiC,OAAT,CAAiB,gBAAjB,EAAmCH,KAAnC;AACH;AACJ;AAED;;;AACsC,cAAxBN,wBAAwB,GAAkB;AACpD;AAAA;AAAA,4BAAKxB,GAAL,CAASC,WAAT,CAAqB,2BAArB;;AAEA,cAAI;AACA;AACA,gBAAI;AACA,oBAAM;AAAA;AAAA,gCAAK4C,GAAL,CAASC,UAAT,CAAoB,WAApB,CAAN;AACA;AAAA;AAAA,gCAAK9C,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH,aAHD,CAGE,OAAO6B,KAAP,EAAc;AACZ,oBAAM,IAAIiB,KAAJ,CAAW,gCAA+BjB,KAAM,EAAhD,CAAN;AACH,aAPD,CASA;;;AACA,kBAAM;AAAEkB,cAAAA;AAAF,gBAAmB,wCAAzB,CAVA,CAYA;;AACA,kBAAMC,KAAK,GAAG,CACV;AACIC,cAAAA,IAAI,EAAE,UADV;AAEIC,cAAAA,IAAI,EAAE,CAAC,MAAD,CAFV;AAGIC,cAAAA,QAAQ,EAAE,MAHd;AAIIC,cAAAA,MAAM,EAAE;AAJZ,aADU,CAAd;AASA,kBAAMC,MAAM,GAAG,MAAMN,YAAY,CAACO,SAAb,CAAuBN,KAAvB,CAArB;;AAEA,gBAAI,CAACK,MAAL,EAAa;AACT,oBAAM,IAAIP,KAAJ,CAAU,YAAV,CAAN;AACH;;AAED;AAAA;AAAA,8BAAK/C,GAAL,CAASC,WAAT,CAAqB,qBAArB;AACH,WA7BD,CA6BE,OAAO6B,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAK9B,GAAL,CAASwD,QAAT,CAAkB,oBAAlB,EAAwC1B,KAAxC;AACA,kBAAMA,KAAN;AACH;AACJ;AAED;;;AACmC,cAArBL,qBAAqB,GAAkB;AACjD;AAAA;AAAA,4BAAKzB,GAAL,CAASC,WAAT,CAAqB,kBAArB;;AAEA,cAAI;AACA;AACA,kBAAM,CAACoB,YAAD,EAAeoC,cAAf,IAAiC,MAAMnC,OAAO,CAACC,GAAR,CAAY,CACrD;AAAA;AAAA,sCAASmC,YAAT,EADqD,EAErD,KAAKC,YAAL,EAFqD,CAAZ,CAA7C;;AAKA,gBAAI,CAACtC,YAAL,EAAmB;AACf,oBAAM,IAAI0B,KAAJ,CAAU,SAAV,CAAN;AACH;;AAED;AAAA;AAAA,8BAAK/C,GAAL,CAASC,WAAT,CAAqB,cAArB;AACH,WAZD,CAYE,OAAO6B,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAK9B,GAAL,CAASwD,QAAT,CAAkB,eAAlB,EAAmC1B,KAAnC;AACA,kBAAMA,KAAN;AACH;AACJ;AAED;;;AAC0B,cAAZ6B,YAAY,GAAkB;AACxC,cAAIC,QAAQ,GAAG;AAAA;AAAA,4BAAKC,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,sDAAkBC,QAAnC,EAA6C,IAA7C,CAAf;AACAH,UAAAA,QAAQ,GAAG,IAAX;AACA,gBAAM;AAAA;AAAA,4BAAKA,QAAL,CAAcI,WAAd,CAA0BJ,QAA1B,CAAN;AACH;AAED;;;AACkC,cAApB5B,oBAAoB,GAAkB;AAChD;AACA,cAAI;AAAA;AAAA,0CAAYiC,mBAAZ,KAAoCvF,GAAG,IAAID,KAA3C,CAAJ,EAAuD;AACnD;AAAA;AAAA,sDAAiByF,mBAAjB;AACA;AAAA;AAAA,8BAAKlE,GAAL,CAASC,WAAT,CAAqB,qBAArB;AACH,WAL+C,CAOhD;;;AACA,cAAI;AAAA;AAAA,0BAAI0C,IAAJ,CAASC,SAAT,IAAsB;AAAA;AAAA,0BAAID,IAAJ,CAASC,SAAT,CAAmBuB,YAA7C,EAA2D;AACvD;AAAA;AAAA,8BAAKnE,GAAL,CAASC,WAAT,CAAqB,gBAArB;AACA;AACH,WAX+C,CAahD;;;AACA,gBAAMmE,iBAAiB,GAAG,CAAC;AAAA;AAAA,4BAAKP,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,sDAAkBO,QAAnC,CAA3B;;AACA,cAAID,iBAAJ,EAAuB;AACnB;AAAA;AAAA,8BAAKpE,GAAL,CAASC,WAAT,CAAqB,wBAArB,EADmB,CAEnB;;AACA,iBAAKiC,uBAAL;AACA;AACH;;AAED;AAAA;AAAA,4BAAKlC,GAAL,CAASC,WAAT,CAAqB,gBAArB,EAtBgD,CAwBhD;;AACA,cAAI;AAAA;AAAA,0CAAYH,QAAZ,KAAyB;AAAA;AAAA,oCAASC,QAAtC,EAAgD;AAC5C;AACA;AAAA;AAAA,8BAAKC,GAAL,CAASC,WAAT,CAAqB,yBAArB;;AAEA,gBAAI;AACA,oBAAM;AAAEqE,gBAAAA;AAAF,kBAAoB,wCAA1B;AACA,oBAAMC,YAAY,GAAG,MAAMD,aAAa,CAACE,eAAd,EAA3B;;AAEA,kBAAID,YAAJ,EAAkB;AACd;AAAA;AAAA,kCAAKvE,GAAL,CAASC,WAAT,CAAqB,kBAArB;AACH,eAFD,MAEO;AACH;AAAA;AAAA,kCAAKD,GAAL,CAASiC,OAAT,CAAiB,4BAAjB;AACH;AACJ,aATD,CASE,OAAOH,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAK9B,GAAL,CAASwD,QAAT,CAAkB,mBAAlB,EAAuC1B,KAAvC;AACH;AACJ,WAhBD,MAgBO,IAAI;AAAA;AAAA,0CAAYhC,QAAZ,KAAyB;AAAA;AAAA,oCAASI,QAAtC,EAAgD;AACnD;AACA;AAAA;AAAA,8BAAKF,GAAL,CAASC,WAAT,CAAqB,qBAArB;;AAEA,gBAAI;AACA,oBAAM;AAAEqE,gBAAAA;AAAF,kBAAoB,wCAA1B,CADA,CAGA;;AACA,oBAAMG,aAAa,GAAG;AAAA;AAAA,gCAAKZ,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,0DAAkBO,QAAnC,CAAtB;AACA,oBAAMK,SAAS,GAAG,KAAKC,YAAL,EAAlB;AACA,kBAAIC,WAAW,GAAG,KAAlB;;AAEA,kBAAIH,aAAa,IAAIC,SAArB,EAAgC;AAC5B;AACA,oBAAIA,SAAS,CAACG,WAAV,GAAwBC,IAAI,CAACC,GAAL,EAA5B,EAAwC;AACpC;AAAA;AAAA,oCAAK/E,GAAL,CAASiC,OAAT,CAAiB,uBAAjB,EAA0C;AACtC+C,oBAAAA,IAAI,EAAE,IAAIF,IAAJ,CAASJ,SAAS,CAACO,WAAnB,EAAgCC,cAAhC,EADgC;AAEtCC,oBAAAA,IAAI,EAAE,IAAIL,IAAJ,CAASJ,SAAS,CAACG,WAAnB,EAAgCK,cAAhC,EAFgC;AAGtCE,oBAAAA,IAAI,EAAE,IAAIN,IAAJ,GAAWI,cAAX;AAHgC,mBAA1C;AAKAN,kBAAAA,WAAW,GAAG,IAAd;AACH,iBAPD,MAOO;AACH;AAAA;AAAA,oCAAK5E,GAAL,CAASC,WAAT,CAAqB,+BAArB,EAAsD;AAClDoF,oBAAAA,IAAI,EACAC,IAAI,CAACC,KAAL,CACI,CAACb,SAAS,CAACG,WAAV,GAAwBC,IAAI,CAACC,GAAL,EAAzB,KAAwC,KAAK,EAAL,GAAU,IAAlD,CADJ,IAEI;AAJ0C,mBAAtD,EADG,CAQH;;AACA,sBAAI;AACA,0BAAM;AAAA;AAAA,oCAAIpC,IAAJ,CAAS6C,QAAT,EAAN;;AAEA,wBAAI;AAAA;AAAA,oCAAI7C,IAAJ,CAASC,SAAT,IAAsB;AAAA;AAAA,oCAAID,IAAJ,CAASC,SAAT,CAAmBuB,YAA7C,EAA2D;AACvD;AAAA;AAAA,wCAAKnE,GAAL,CAASC,WAAT,CAAqB,oBAArB;AACA,6BAFuD,CAE/C;AACX,qBAHD,MAGO;AACH2E,sBAAAA,WAAW,GAAG,IAAd;AACH;AACJ,mBATD,CASE,OAAO9C,KAAP,EAAc;AACZ;AAAA;AAAA,sCAAK9B,GAAL,CAASiC,OAAT,CAAiB,sBAAjB,EAAyCH,KAAzC;AACA8C,oBAAAA,WAAW,GAAG,IAAd;AACH;AACJ;AACJ,eAhCD,MAgCO;AACH;AAAA;AAAA,kCAAK5E,GAAL,CAASC,WAAT,CAAqB,wBAArB;AACA2E,gBAAAA,WAAW,GAAG,IAAd;AACH,eA3CD,CA6CA;;;AACA,kBAAIA,WAAJ,EAAiB;AACb;AAAA;AAAA,kCAAK5E,GAAL,CAASC,WAAT,CAAqB,cAArB,EADa,CAGb;;AACA,oBAAIwE,aAAJ,EAAmB;AACf;AAAA;AAAA,oCAAKZ,OAAL,CAAa4B,MAAb,CAAoB;AAAA;AAAA,8DAAkBpB,QAAtC;AACA;AAAA;AAAA,oCAAKR,OAAL,CAAa4B,MAAb,CAAoB;AAAA;AAAA,8DAAkBC,YAAtC;AACA;AAAA;AAAA,oCAAK1F,GAAL,CAASC,WAAT,CAAqB,0BAArB;AACH,iBARY,CAUb;;;AACA,sBAAM0F,aAAa,GAAG,IAAIrB,aAAJ,EAAtB;AACA,sBAAMsB,iBAAiB,GAAG,MAAMD,aAAa,CAACE,gBAAd,EAAhC;;AAEA,oBAAID,iBAAJ,EAAuB;AACnB;AAAA;AAAA,oCAAK5F,GAAL,CAASC,WAAT,CAAqB,UAArB,EADmB,CAGnB;;AACA,wBAAM;AAAA;AAAA,kCAAI0C,IAAJ,CAAS6C,QAAT,EAAN;;AAEA,sBAAI;AAAA;AAAA,kCAAI7C,IAAJ,CAASC,SAAT,IAAsB;AAAA;AAAA,kCAAID,IAAJ,CAASC,SAAT,CAAmBuB,YAA7C,EAA2D;AACvD;AAAA;AAAA,sCAAKnE,GAAL,CAASC,WAAT,CAAqB,YAArB;AACH,mBAFD,MAEO;AACH;AAAA;AAAA,sCAAKD,GAAL,CAASiC,OAAT,CAAiB,kBAAjB;AACH;AACJ,iBAXD,MAWO;AACH;AAAA;AAAA,oCAAKjC,GAAL,CAASiC,OAAT,CAAiB,WAAjB;AACH;AACJ;AACJ,aA3ED,CA2EE,OAAOH,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAK9B,GAAL,CAASwD,QAAT,CAAkB,aAAlB,EAAiC1B,KAAjC;AACH;AACJ,WAlFM,MAkFA;AACH;AACA,kBAAM;AAAA;AAAA,4BAAIa,IAAJ,CAAS6C,QAAT,EAAN;AACH,WA9H+C,CAgIhD;;;AACA,cAAI;AAAA;AAAA,0BAAI7C,IAAJ,CAASC,SAAT,IAAsB;AAAA;AAAA,0BAAID,IAAJ,CAASC,SAAT,CAAmBuB,YAA7C,EAA2D;AACvD;AAAA;AAAA,8BAAKnE,GAAL,CAASC,WAAT,CAAqB,cAArB;AACH,WAFD,MAEO;AACH;AAAA;AAAA,8BAAKD,GAAL,CAASiC,OAAT,CAAiB,uBAAjB;AACH;AACJ;AAED;;;AACuB,cAATnB,SAAS,GAAkB;AACrC,cAAI;AACA;AAAA;AAAA,8BAAKd,GAAL,CAASC,WAAT,CAAqB,cAArB,EADA,CAGA;;AACA,gBAAIsC,WAAW,GAAG,KAAlB;;AAEA,gBAAI;AAAA;AAAA,4BAAII,IAAJ,CAASC,SAAT,IAAsB;AAAA;AAAA,4BAAID,IAAJ,CAASC,SAAT,CAAmBuB,YAA7C,EAA2D;AACvD5B,cAAAA,WAAW,GAAG;AAAA;AAAA,8BAAII,IAAJ,CAASJ,WAAT,EAAd;AACA;AAAA;AAAA,gCAAKvC,GAAL,CAASC,WAAT,CAAsB,mBAAkBsC,WAAY,EAApD;AACH,aAHD,MAGO;AACH;AACAA,cAAAA,WAAW,GAAG,IAAd;AACA;AAAA;AAAA,gCAAKvC,GAAL,CAASiC,OAAT,CAAiB,oBAAjB;AACH;;AAED;AAAA;AAAA,8BAAKjC,GAAL,CAASC,WAAT,CAAsB,QAAOsC,WAAW,GAAG,MAAH,GAAY,IAAK,IAAzD;AAEA,gBAAIuD,OAAO,GAAG,KAAd;AACA,gBAAIC,WAAW,GAAG,IAAlB,CAlBA,CAoBA;;AACA,gBACI;AAAA;AAAA,4CAAYjG,QAAZ,KAAyB;AAAA;AAAA,sCAASC,QAAlC,IACA;AAAA;AAAA,4CAAYD,QAAZ,KAAyB;AAAA;AAAA,sCAASI,QAFtC,EAGE;AACE6F,cAAAA,WAAW,GAAG,KAAd;AACH;;AAED,gBAAIxD,WAAJ,EAAiB;AACb;AACA;AAAA;AAAA,gCAAKvC,GAAL,CAASC,WAAT,CACK,MAAK;AAAA;AAAA,8CAAYH,QAAZ,KAAyB;AAAA;AAAA,wCAASC,QAAlC,GAA6C,UAA7C,GAA0D;AAAA;AAAA,8CAAYD,QAAZ,KAAyB;AAAA;AAAA,wCAASI,QAAlC,GAA6C,IAA7C,GAAoD,EAAG,oBAD3H;AAIA4F,cAAAA,OAAO,GAAG,MAAM;AAAA;AAAA,8BAAIE,QAAJ,CAAaC,aAAb,CACZ;AAAA;AAAA,0CAAUC,KADE,EAEZ,CAFY,EAGZH,WAHY,EAIZ,KAAKI,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAJY,CAAhB;AAMH,aAZD,MAYO;AACH;AACA;AAAA;AAAA,gCAAKpG,GAAL,CAASC,WAAT,CACK,MAAK;AAAA;AAAA,8CAAYH,QAAZ,KAAyB;AAAA;AAAA,wCAASC,QAAlC,GAA6C,UAA7C,GAA0D;AAAA;AAAA,8CAAYD,QAAZ,KAAyB;AAAA;AAAA,wCAASI,QAAlC,GAA6C,IAA7C,GAAoD,EAAG,kBAD3H;AAGA4F,cAAAA,OAAO,GAAG,MAAM;AAAA;AAAA,8BAAIE,QAAJ,CAAaC,aAAb,CACZ;AAAA;AAAA,0CAAUI,IADE,EAEZC,SAFY,EAGZP,WAHY,EAIZ,KAAKI,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAJY,CAAhB;AAMH;;AAED,gBAAIN,OAAJ,EAAa;AACT;AAAA;AAAA,gCAAK9F,GAAL,CAASC,WAAT,CAAsB,MAAKsC,WAAW,GAAG,IAAH,GAAU,IAAK,QAArD;AACH,aAFD,MAEO;AACH,oBAAM,IAAIQ,KAAJ,CAAW,GAAER,WAAW,GAAG,IAAH,GAAU,IAAK,QAAvC,CAAN;AACH;AACJ,WA1DD,CA0DE,OAAOT,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAK9B,GAAL,CAASwD,QAAT,CAAkB,YAAlB,EAAgC1B,KAAhC,EADY,CAGZ;;AACA,iBAAK3B,qBAAL,CAA2B,CAA3B,EAA8B,QAA9B,EAAwC,SAAxC;AAEA;AAAA;AAAA,8BAAKwB,GAAL,CAAS4E,KAAT,CAAe,gBAAf,EANY,CAQZ;;AACA,iBAAKC,GAAL,CAASf,MAAT,CAAgBjG,eAAhB;AACH;AACJ;AAED;;;AACQuC,QAAAA,yBAAyB,CAACD,KAAD,EAAmB;AAChD;AAAA;AAAA,4BAAK9B,GAAL,CAASwD,QAAT,CAAkB,YAAlB,EAAgC1B,KAAhC,EADgD,CAGhD;;AACA,eAAK3B,qBAAL,CAA2B,CAA3B,EAA8B,OAA9B,EAAuC2B,KAAK,CAACd,OAAN,IAAiB,MAAxD,EAJgD,CAMhD;;AACA,cAAIc,KAAK,CAACd,OAAN,CAAcyF,QAAd,CAAuB,WAAvB,CAAJ,EAAyC;AACrC;AAAA;AAAA,8BAAK9E,GAAL,CAAS4E,KAAT,CAAe,mBAAf,EADqC,CAErC;;AACA,iBAAKC,GAAL,CAASf,MAAT,CAAgBjG,eAAhB;AACA,mBAJqC,CAI7B;AACX,WAZ+C,CAchD;;;AACA;AAAA;AAAA,4BAAKmC,GAAL,CAAS4E,KAAT,CAAe,gBAAf;AACA,eAAKG,mBAAL;AACH;AAED;;;AACQA,QAAAA,mBAAmB,GAAS;AAChCC,UAAAA,UAAU,CAAC,MAAM;AACb;AAAA;AAAA,8BAAK3G,GAAL,CAASC,WAAT,CAAqB,eAArB;AACA,iBAAKL,eAAL,GAAuB,CAAvB;AACA,iBAAKO,qBAAL,CAA2B,CAA3B,EAA8B,SAA9B,EAAyC,SAAzC;AACA,iBAAKE,uBAAL;AACH,WALS,EAKP,IALO,CAAV;AAMH;AAED;;;AACQuG,QAAAA,yBAAyB,GAAS;AACtC;AAAA;AAAA,4BAAK5G,GAAL,CAASC,WAAT,CAAqB,yBAArB;;AAEA,cAAI;AACA;AACA,gBAAIiB,MAAM,CAACC,cAAP,IAAyB,OAAOD,MAAM,CAACC,cAAd,KAAiC,UAA9D,EAA0E;AACtED,cAAAA,MAAM,CAACC,cAAP,CAAsB,GAAtB,EAA2B,MAA3B;AACA;AAAA;AAAA,gCAAKnB,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH,aAHD,MAGO;AACH;AAAA;AAAA,gCAAKD,GAAL,CAASC,WAAT,CAAqB,sCAArB;AACH,aAPD,CASA;;;AACA,gBAAIiB,MAAM,CAAC2F,UAAP,IAAqB,OAAO3F,MAAM,CAAC2F,UAAd,KAA6B,UAAtD,EAAkE;AAC9D3F,cAAAA,MAAM,CAAC2F,UAAP;AACA;AAAA;AAAA,gCAAK7G,GAAL,CAASC,WAAT,CAAqB,cAArB;AACH,aAHD,MAGO;AACH;AAAA;AAAA,gCAAKD,GAAL,CAASC,WAAT,CAAqB,kCAArB;AACH;AACJ,WAhBD,CAgBE,OAAO6B,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAK9B,GAAL,CAASiC,OAAT,CAAiB,qBAAjB,EAAwCH,KAAxC;AACH;AACJ;AAED;;;AACQqE,QAAAA,cAAc,GAAS;AAC3B;AACA,cACI;AAAA;AAAA,0CAAYrG,QAAZ,KAAyB;AAAA;AAAA,oCAASC,QAAlC,IACA;AAAA;AAAA,0CAAYD,QAAZ,KAAyB;AAAA;AAAA,oCAASI,QAFtC,EAGE;AACE,iBAAK0G,yBAAL;AACH,WAP0B,CAS3B;;;AACA,cAAI;AAAA;AAAA,0CAAY9G,QAAZ,KAAyB;AAAA;AAAA,oCAASC,QAAtC,EAAgD;AAC5C,iBAAK+G,sBAAL;AACH;;AAED;AAAA;AAAA,4BAAK9G,GAAL,CAASC,WAAT,CAAqB,YAArB,EAd2B,CAgB3B;;AACA,eAAKuG,GAAL,CAASf,MAAT,CAAgBjG,eAAhB;AACH;AAED;;;AACQsH,QAAAA,sBAAsB,GAAS;AACnC,cAAI5F,MAAM,CAAC6F,SAAP,IAAoB7F,MAAM,CAAC6F,SAAP,CAAiBC,kBAAzC,EAA6D;AACzD9F,YAAAA,MAAM,CAAC6F,SAAP,CAAiBC,kBAAjB,CAAoC,GAApC;AACA;AAAA;AAAA,8BAAKhH,GAAL,CAASC,WAAT,CAAqB,6BAArB;AACH;AACJ;AAED;;;AACA4B,QAAAA,aAAa,GAAS;AAClB;AAAA;AAAA,4BAAK7B,GAAL,CAASwD,QAAT,CAAkB,UAAlB;AACH;AAED;;;AACQmB,QAAAA,YAAY,GAAuE;AACvF,cAAI;AACA,kBAAMsC,YAAY,GAAG;AAAA;AAAA,8BAAKpD,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,wDAAkB4B,YAAnC,CAArB;;AACA,gBAAIuB,YAAJ,EAAkB;AACd,qBAAOC,IAAI,CAACC,KAAL,CAAWF,YAAX,CAAP;AACH;AACJ,WALD,CAKE,OAAOnF,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAK9B,GAAL,CAASiC,OAAT,CAAiB,iBAAjB,EAAoCH,KAApC;AACH;;AACD,iBAAO,IAAP;AACH;;AAEDsF,QAAAA,KAAK,GAAS;AACV,eAAK1H,YAAL,GAAoB,KAApB;AACA,eAAKC,YAAL,GAAoB,KAApB;AACA,eAAKC,eAAL,GAAuB,CAAvB;AACH;;AAxfuC,O", "sourcesContent": ["import { _decorator, tween, UIOpacity, Vec3 } from 'cc';\n\nimport { DEBUG, DEV } from 'cc/env';\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { JsonUtil } from '../../../../../extensions/oops-plugin-framework/assets/core/utils/JsonUtil';\nimport { ecs } from '../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';\nimport { CCComp } from '../../../../../extensions/oops-plugin-framework/assets/module/common/CCComp';\nimport { Platform, ShareConfig } from '../../../tsrpc/models/ShareConfig';\nimport { SceneType } from '../../../tsrpc/protocols/base';\nimport { ClientConst } from '../../common/ClientConst';\nimport { GameStorageConfig, StorageTestUtils } from '../../common/config/GameStorageConfig';\nimport { smc } from '../../common/SingletonModuleComp';\n\n// 扩展window类型\ndeclare global {\n    interface Window {\n        updateProgress?: (progress: number, message?: string, detail?: string) => void;\n        hideLoader?: () => void;\n        FBInstant?: any;\n    }\n}\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 游戏完整初始化组件 - 自定义加载版本\n *\n * 职责：\n * - UI动画播放\n * - Bundle验证和加载\n * - 配置数据加载（配置表 + 语言包）\n * - 用户数据加载\n * - 进入游戏流程\n *\n * 优化：\n * - 所有初始化逻辑集中管理\n * - Bundle加载失败直接报错\n * - 配置表和语言包并行加载\n * - 支持自定义加载界面（Facebook + 个人平台）\n */\n@ccclass('InitialViewComp')\**************('InitialView', false)\nexport class InitialViewComp extends CCComp {\n    private waitComplete: boolean = false;\n    private loadComplete: boolean = false;\n    private currentProgress: number = 0;\n\n    start() {\n        // 🚀 统一使用自定义加载界面，跳过Cocos内置动画\n        if (ShareConfig.platform === Platform.FACEBOOK) {\n            oops.log.logBusiness('🚀 Facebook环境: 自定义加载模式');\n        } else if (ShareConfig.platform === Platform.PERSONAL) {\n            oops.log.logBusiness('🚀 个人环境: 自定义加载模式');\n            this.updateLoadingProgress(50, '游戏引擎已启动', '个人环境初始化完成');\n        } else {\n            // 其他环境：使用传统动画\n            this.playWaitAnimation();\n        }\n\n        this.startFullInitialization();\n    }\n\n    /** 🎬 播放等待动画（传统环境） */\n    private playWaitAnimation() {\n        let logoOpacity = this.node.getChildByName('logo')?.getComponent(UIOpacity);\n        if (logoOpacity) {\n            logoOpacity.opacity = 50;\n            tween(logoOpacity.node)\n                .to(1, { opacity: 255, position: new Vec3(0, 0, 0) })\n                .call(() => {\n                    this.waitComplete = true;\n                    if (this.loadComplete) {\n                        this.enterGame();\n                    }\n                })\n                .start();\n        } else {\n            this.waitComplete = true;\n        }\n    }\n\n    /** 🎯 更新加载进度 */\n    private updateLoadingProgress(progress: number, message: string, detail?: string) {\n        this.currentProgress = progress;\n\n        if (window.updateProgress && typeof window.updateProgress === 'function') {\n            window.updateProgress(progress, message, detail);\n            oops.log.logBusiness(`🎯 加载进度: ${progress}% - ${message}`);\n        }\n    }\n\n    /** 🚀 完整初始化流程 - 优化版本 */\n    private async startFullInitialization() {\n        try {\n            // 🚀 优化：并行执行可独立的初始化任务\n            const [bundleResult, configResult] = await Promise.all([\n                // 1️⃣ 验证和加载Bundle + 基础UI资源\n                this.loadBundleAndUIResources(),\n                // 2️⃣ 加载配置数据（配置表 + 语言包）\n                this.loadConfigurationData(),\n            ]);\n\n            // 3️⃣ 用户数据加载可以在后台进行，不阻塞UI显示\n            this.loadUserDataInBackground();\n\n            // 设置窗口打开失败事件\n            oops.gui.setOpenFailure(this.onOpenFailure);\n\n            this.loadComplete = true;\n\n            // 🚀 自定义加载环境直接进入游戏\n            if (\n                ShareConfig.platform === Platform.FACEBOOK ||\n                ShareConfig.platform === Platform.PERSONAL\n            ) {\n                this.enterGame();\n            } else if (this.waitComplete) {\n                this.enterGame();\n            }\n        } catch (error) {\n            this.handleInitializationError(error);\n        }\n    }\n\n    /** 🔄 后台加载用户数据，不阻塞游戏启动 */\n    private async loadUserDataInBackground() {\n        try {\n            await this.ensureUserDataLoaded();\n            oops.log.logBusiness('✅ 用户数据后台加载完成');\n        } catch (error) {\n            oops.log.logWarn('⚠️ 用户数据后台加载失败，但不影响游戏启动:', error);\n            // 用户数据加载失败时，可以在游戏中再次尝试\n        }\n    }\n\n    /** 🚀 创建临时用户数据，用于快速启动 */\n    private createTemporaryUserData(): void {\n        try {\n            // 创建最小化的临时用户数据\n            const tempUserData = {\n                key: 0,\n                userName: 'TempPlayer',\n                index: 0,\n                isNewPlayer: true,\n                propUseData: {},\n                recordData: {},\n                // 标记为临时数据\n                isTemporary: true,\n            };\n\n            // 如果Role模块还没有初始化，先初始化\n            if (!smc.role.RoleModel) {\n                // 这里可能需要初始化RoleModel，但为了快速启动，我们先跳过\n                oops.log.logBusiness('🚀 Role模块未初始化，将在后台完成');\n            }\n\n            oops.log.logBusiness('✅ 临时用户数据创建完成，游戏可以快速启动');\n        } catch (error) {\n            oops.log.logWarn('⚠️ 临时用户数据创建失败:', error);\n        }\n    }\n\n    /** 1️⃣ 验证和加载Bundle + 基础UI资源 */\n    private async loadBundleAndUIResources(): Promise<void> {\n        oops.log.logBusiness('🔍 开始验证Bundle和加载基础UI资源...');\n\n        try {\n            // 🚀 直接验证bundleOne是否存在，不存在就报错\n            try {\n                await oops.res.loadBundle('bundleOne');\n                oops.log.logBusiness('✅ bundleOne加载成功');\n            } catch (error) {\n                throw new Error(`❌ 关键Bundle 'bundleOne' 加载失败: ${error}`);\n            }\n\n            // 🚀 延迟导入，减少初始bundle大小\n            const { simpleLoader } = await import('../../common/loader/SimpleLoadingManager');\n\n            // 🎯 加载基础UI资源\n            const tasks = [\n                {\n                    name: '加载基础UI资源',\n                    dirs: ['boot'],\n                    priority: 'high',\n                    bundle: 'bundleOne',\n                },\n            ];\n\n            const result = await simpleLoader.loadTasks(tasks);\n\n            if (!result) {\n                throw new Error('基础UI资源加载失败');\n            }\n\n            oops.log.logBusiness('✅ Bundle和基础UI资源加载完成');\n        } catch (error) {\n            oops.log.logError('❌ Bundle和UI资源加载失败:', error);\n            throw error;\n        }\n    }\n\n    /** 2️⃣ 加载配置数据资源（配置表 + 语言包） */\n    private async loadConfigurationData(): Promise<void> {\n        oops.log.logBusiness('🔄 开始加载配置数据资源...');\n\n        try {\n            // 🚀 优化：配置表和语言包可以并行加载\n            const [configResult, languageResult] = await Promise.all([\n                JsonUtil.loadDirAsync(),\n                this.loadLanguage(),\n            ]);\n\n            if (!configResult) {\n                throw new Error('配置表加载失败');\n            }\n\n            oops.log.logBusiness('✅ 配置数据资源加载完成');\n        } catch (error) {\n            oops.log.logError('❌ 配置数据资源加载失败:', error);\n            throw error;\n        }\n    }\n\n    /** 加载语言包 */\n    private async loadLanguage(): Promise<void> {\n        let language = oops.storage.get(GameStorageConfig.Language, 'en');\n        language = 'en';\n        await oops.language.setLanguage(language);\n    }\n\n    /** 3️⃣ 确保用户数据已加载 - 优化版本 */\n    private async ensureUserDataLoaded(): Promise<void> {\n        // 🧪 测试开关：控制数据清空行为\n        if (ClientConst.alwaysNewPlayerTest && (DEV || DEBUG)) {\n            StorageTestUtils.forceNewPlayerState();\n            oops.log.logBusiness('🎓 [测试模式] 强制设置为新手状态');\n        }\n\n        // 检查是否已有用户数据\n        if (smc.role.RoleModel && smc.role.RoleModel.userGameData) {\n            oops.log.logBusiness('✅ 用户数据已存在，跳过加载');\n            return;\n        }\n\n        // 🚀 快速启动模式：对于新手玩家，可以延迟加载用户数据\n        const isLikelyNewPlayer = !oops.storage.get(GameStorageConfig.SSOToken);\n        if (isLikelyNewPlayer) {\n            oops.log.logBusiness('🚀 检测到可能是新手玩家，启用快速启动模式');\n            // 创建临时用户数据，允许游戏快速启动\n            this.createTemporaryUserData();\n            return;\n        }\n\n        oops.log.logBusiness('🔄 开始加载用户数据...');\n\n        // 🔧 根据平台选择不同的登录方式\n        if (ShareConfig.platform === Platform.FACEBOOK) {\n            // Facebook环境：执行Facebook自动登录\n            oops.log.logBusiness('🔐 Facebook环境：执行自动登录...');\n\n            try {\n                const { LoginViewComp } = await import('./LoginViewComp');\n                const loginSuccess = await LoginViewComp.doFacebookLogin();\n\n                if (loginSuccess) {\n                    oops.log.logBusiness('✅ Facebook自动登录成功');\n                } else {\n                    oops.log.logWarn('⚠️ Facebook自动登录失败，继续尝试加载数据');\n                }\n            } catch (error) {\n                oops.log.logError('❌ Facebook自动登录异常:', error);\n            }\n        } else if (ShareConfig.platform === Platform.PERSONAL) {\n            // 个人环境：使用LoginViewComp的游客登录\n            oops.log.logBusiness('🔐 个人环境：执行游客自动登录...');\n\n            try {\n                const { LoginViewComp } = await import('./LoginViewComp');\n\n                // 🔍 先检查token过期时间，再尝试加载数据\n                const existingToken = oops.storage.get(GameStorageConfig.SSOToken);\n                const tokenInfo = this.getTokenInfo();\n                let needRelogin = false;\n\n                if (existingToken && tokenInfo) {\n                    // 检查token是否过期\n                    if (tokenInfo.expiredTime < Date.now()) {\n                        oops.log.logWarn('⏰ SSO Token已过期，需要重新登录', {\n                            创建时间: new Date(tokenInfo.createdTime).toLocaleString(),\n                            过期时间: new Date(tokenInfo.expiredTime).toLocaleString(),\n                            当前时间: new Date().toLocaleString(),\n                        });\n                        needRelogin = true;\n                    } else {\n                        oops.log.logBusiness('🔑 检测到有效SSO Token，尝试加载用户数据...', {\n                            剩余时间:\n                                Math.round(\n                                    (tokenInfo.expiredTime - Date.now()) / (60 * 60 * 1000)\n                                ) + '小时',\n                        });\n\n                        // 尝试加载用户数据以验证token有效性\n                        try {\n                            await smc.role.loadData();\n\n                            if (smc.role.RoleModel && smc.role.RoleModel.userGameData) {\n                                oops.log.logBusiness('✅ 用户数据加载成功，token有效');\n                                return; // 成功加载，直接返回\n                            } else {\n                                needRelogin = true;\n                            }\n                        } catch (error) {\n                            oops.log.logWarn('⚠️ token验证失败，需要重新登录:', error);\n                            needRelogin = true;\n                        }\n                    }\n                } else {\n                    oops.log.logBusiness('🔍 未检测到有效token或token信息');\n                    needRelogin = true;\n                }\n\n                // 如果需要重新登录\n                if (needRelogin) {\n                    oops.log.logBusiness('🔄 执行游客登录...');\n\n                    // 清除旧token和token信息\n                    if (existingToken) {\n                        oops.storage.remove(GameStorageConfig.SSOToken);\n                        oops.storage.remove(GameStorageConfig.SSOTokenInfo);\n                        oops.log.logBusiness('🗑️ 已清除无效的SSO Token和相关信息');\n                    }\n\n                    // 创建临时LoginViewComp实例进行游客登录\n                    const tempLoginComp = new LoginViewComp();\n                    const guestLoginSuccess = await tempLoginComp.loginGuestButton();\n\n                    if (guestLoginSuccess) {\n                        oops.log.logBusiness('✅ 游客登录成功');\n\n                        // 重新加载用户数据\n                        await smc.role.loadData();\n\n                        if (smc.role.RoleModel && smc.role.RoleModel.userGameData) {\n                            oops.log.logBusiness('✅ 用户数据加载成功');\n                        } else {\n                            oops.log.logWarn('⚠️ 游客登录后用户数据加载失败');\n                        }\n                    } else {\n                        oops.log.logWarn('⚠️ 游客登录失败');\n                    }\n                }\n            } catch (error) {\n                oops.log.logError('❌ 游客登录流程异常:', error);\n            }\n        } else {\n            // 其他平台，直接尝试加载数据\n            await smc.role.loadData();\n        }\n\n        // 最终检查结果\n        if (smc.role.RoleModel && smc.role.RoleModel.userGameData) {\n            oops.log.logBusiness('✅ 用户数据最终加载成功');\n        } else {\n            oops.log.logWarn('⚠️ 用户数据最终加载失败，但继续进入游戏');\n        }\n    }\n\n    /** 🎮 进入游戏流程 */\n    private async enterGame(): Promise<void> {\n        try {\n            oops.log.logBusiness('🎓 开始进入游戏...');\n\n            // 🔍 确保role数据已正确加载，避免ViewModel访问失败\n            let isNewPlayer = false;\n\n            if (smc.role.RoleModel && smc.role.RoleModel.userGameData) {\n                isNewPlayer = smc.role.isNewPlayer();\n                oops.log.logBusiness(`🎯 用户数据有效，新手状态: ${isNewPlayer}`);\n            } else {\n                // 如果没有用户数据，默认当作新手处理\n                isNewPlayer = true;\n                oops.log.logWarn('⚠️ 用户数据缺失，默认当作新手处理');\n            }\n\n            oops.log.logBusiness(`🎮 进入${isNewPlayer ? '新手游戏' : '大厅'}场景`);\n\n            let success = false;\n            let showLoading = true;\n\n            // 🎯 使用自定义加载界面的平台不显示Cocos默认加载\n            if (\n                ShareConfig.platform === Platform.FACEBOOK ||\n                ShareConfig.platform === Platform.PERSONAL\n            ) {\n                showLoading = false;\n            }\n\n            if (isNewPlayer) {\n                // 新手玩家：进入Foods场景\n                oops.log.logBusiness(\n                    `🆕 ${ShareConfig.platform === Platform.FACEBOOK ? 'Facebook' : ShareConfig.platform === Platform.PERSONAL ? '个人' : ''}新手玩家：进入游戏场景（Foods）`\n                );\n\n                success = await smc.sceneMgr.switchToScene(\n                    SceneType.Foods,\n                    1,\n                    showLoading,\n                    this.closeLoadingUI.bind(this)\n                );\n            } else {\n                // 老玩家：进入大厅场景\n                oops.log.logBusiness(\n                    `👤 ${ShareConfig.platform === Platform.FACEBOOK ? 'Facebook' : ShareConfig.platform === Platform.PERSONAL ? '个人' : ''}老玩家：进入大厅场景（Hall）`\n                );\n                success = await smc.sceneMgr.switchToScene(\n                    SceneType.Hall,\n                    undefined,\n                    showLoading,\n                    this.closeLoadingUI.bind(this)\n                );\n            }\n\n            if (success) {\n                oops.log.logBusiness(`🎮 ${isNewPlayer ? '游戏' : '大厅'}场景加载成功`);\n            } else {\n                throw new Error(`${isNewPlayer ? '游戏' : '大厅'}场景加载失败`);\n            }\n        } catch (error) {\n            oops.log.logError('🔥 进入游戏失败:', error);\n\n            // 更新加载界面显示错误\n            this.updateLoadingProgress(0, '进入游戏失败', '请刷新页面重试');\n\n            oops.gui.toast('进入游戏失败，请刷新页面重试');\n\n            // 🎯 失败时也要移除组件，避免内存泄漏\n            this.ent.remove(InitialViewComp);\n        }\n    }\n\n    /** 🚨 错误处理 */\n    private handleInitializationError(error: any): void {\n        oops.log.logError('❌ 游戏初始化失败:', error);\n\n        // 更新加载界面显示错误\n        this.updateLoadingProgress(0, '初始化失败', error.message || '未知错误');\n\n        // 🚨 如果是Bundle加载失败，显示更明确的错误信息\n        if (error.message.includes('bundleOne')) {\n            oops.gui.toast('❌ 游戏资源包缺失，请检查资源配置');\n            // 🎯 严重错误时移除组件，避免内存泄漏\n            this.ent.remove(InitialViewComp);\n            return; // 不重试，直接失败\n        }\n\n        // 其他错误可以重试\n        oops.gui.toast('初始化失败，请检查网络后重试');\n        this.retryInitialization();\n    }\n\n    /** 🔧 初始化重试机制 */\n    private retryInitialization(): void {\n        setTimeout(() => {\n            oops.log.logBusiness('🔄 尝试重新初始化...');\n            this.currentProgress = 0;\n            this.updateLoadingProgress(0, '正在重试...', '重新初始化游戏');\n            this.startFullInitialization();\n        }, 2000);\n    }\n\n    /** 通知HTML加载完成 */\n    private notifyHTMLLoadingComplete(): void {\n        oops.log.logBusiness('🚪 通知HTML加载完成，关闭自定义加载界面');\n\n        try {\n            // 更新进度到100%\n            if (window.updateProgress && typeof window.updateProgress === 'function') {\n                window.updateProgress(100, '加载完成');\n                oops.log.logBusiness('✅ HTML进度更新为100%');\n            } else {\n                oops.log.logBusiness('ℹ️ window.updateProgress 方法不存在（正常情况）');\n            }\n\n            // 隐藏加载界面\n            if (window.hideLoader && typeof window.hideLoader === 'function') {\n                window.hideLoader();\n                oops.log.logBusiness('✅ 自定义加载界面已隐藏');\n            } else {\n                oops.log.logBusiness('ℹ️ window.hideLoader 方法不存在（正常情况）');\n            }\n        } catch (error) {\n            oops.log.logWarn('⚠️ 通知HTML加载完成时出现异常:', error);\n        }\n    }\n\n    /** 关闭加载界面 */\n    private closeLoadingUI(): void {\n        // 🎯 自定义加载界面的平台都调用HTML通知\n        if (\n            ShareConfig.platform === Platform.FACEBOOK ||\n            ShareConfig.platform === Platform.PERSONAL\n        ) {\n            this.notifyHTMLLoadingComplete();\n        }\n\n        // Facebook生产环境额外通知\n        if (ShareConfig.platform === Platform.FACEBOOK) {\n            this.notifyFacebookComplete();\n        }\n\n        oops.log.logBusiness('🎉 游戏启动完成！');\n\n        // 移除自己\n        this.ent.remove(InitialViewComp);\n    }\n\n    /** 通知Facebook完成 */\n    private notifyFacebookComplete(): void {\n        if (window.FBInstant && window.FBInstant.setLoadingProgress) {\n            window.FBInstant.setLoadingProgress(100);\n            oops.log.logBusiness('📊 Facebook SDK: 进度已设置为100%');\n        }\n    }\n\n    /** 窗口打开失败回调 */\n    onOpenFailure(): void {\n        oops.log.logError('❌ 窗口打开失败');\n    }\n\n    /** 获取token信息 */\n    private getTokenInfo(): { token: string; expiredTime: number; createdTime: number } | null {\n        try {\n            const tokenInfoStr = oops.storage.get(GameStorageConfig.SSOTokenInfo);\n            if (tokenInfoStr) {\n                return JSON.parse(tokenInfoStr);\n            }\n        } catch (error) {\n            oops.log.logWarn('⚠️ 解析token信息失败:', error);\n        }\n        return null;\n    }\n\n    reset(): void {\n        this.waitComplete = false;\n        this.loadComplete = false;\n        this.currentProgress = 0;\n    }\n}\n"]}