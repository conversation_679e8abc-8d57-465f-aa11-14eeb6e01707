{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/initialize/view/InitialViewComp.ts"], "names": ["_decorator", "tween", "UIOpacity", "Vec3", "DEBUG", "DEV", "oops", "<PERSON><PERSON><PERSON><PERSON>", "ecs", "CCComp", "GameConst", "Platform", "ShareConfig", "SceneType", "ClientConst", "GameStorageConfig", "StorageTestUtils", "smc", "ccclass", "property", "InitialViewComp", "register", "waitComplete", "loadComplete", "currentProgress", "startTime", "start", "Date", "now", "log", "logBusiness", "platform", "FACEBOOK", "PERSONAL", "updateLoadingProgress", "playWaitAnimation", "startFullInitialization", "logoOpacity", "node", "getChildByName", "getComponent", "opacity", "to", "position", "call", "enterGame", "progress", "message", "detail", "window", "updateProgress", "alwaysNewPlayerTest", "forceNewPlayerState", "Promise", "all", "loadBundleAndUIResources", "loadConfigurationData", "quickUserDataInitialization", "gui", "setOpenFailure", "onOpenFailure", "error", "handleInitializationError", "isNewPlayer", "determineNewPlayerStatus", "newPlayerFastStart", "initializeBasicRoleData", "loadFullUserDataInBackground", "ensureUserDataLoaded", "log<PERSON>arn", "createNewPlayerDefaultProps", "currentTime", "newPlayerProps", "amount", "propType", "propId", "desc", "getTime", "lastResetTime", "lastUpdateTime", "移出道具", "提示道具", "洗牌道具", "挑战次数", "setTimeout", "forceCompleteUserDataLoad", "dispatchEvent", "tempUserData", "role", "RoleModel", "userGameData", "isTemporaryData", "performCompleteLogin", "LoginViewComp", "loginSuccess", "doFacebookLogin", "logError", "loadData", "isNewFromRole", "hasLoginRecord", "storage", "get", "SSOToken", "userDumpKey", "get<PERSON>son", "UserDumpKey", "String", "basicUserData", "key", "guuid", "googleUuid", "facebookId", "userName", "nick<PERSON><PERSON>", "sex", "createtime", "openid", "platformType", "avatar", "avatarId", "countryCode", "passTimes", "index", "currCountryPassTimes", "lastChangeCountryTime", "selfCountryRank", "propUseData", "recordData", "isGuest", "lastStep", "updateUserData", "propCount", "Object", "keys", "length", "res", "loadBundle", "Error", "simpleLoader", "tasks", "name", "dirs", "priority", "bundle", "result", "loadTasks", "config<PERSON><PERSON><PERSON>", "loadDirAsync", "loadLanguage", "language", "Language", "setLanguage", "userData", "existingToken", "tokenInfo", "getTokenInfo", "<PERSON><PERSON><PERSON><PERSON>", "expiredTime", "创建时间", "createdTime", "toLocaleString", "过期时间", "当前时间", "剩余时间", "Math", "round", "remove", "SSOTokenInfo", "tempLoginComp", "guestLoginSuccess", "loginGuestButton", "success", "showLoading", "sceneMgr", "switchToScene", "Foods", "closeLoadingUI", "bind", "Hall", "undefined", "toast", "ent", "includes", "retryInitialization", "notifyHTMLLoadingComplete", "<PERSON><PERSON><PERSON><PERSON>", "endTime", "totalTime", "notifyFacebookComplete", "performance", "<PERSON><PERSON><PERSON>", "setLoadingProgress", "tokenInfoStr", "JSON", "parse", "reset"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;;AAE9BC,MAAAA,K,UAAAA,K;AAAOC,MAAAA,G,UAAAA,G;;AACPC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;;AACVC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,iB,kBAAAA,iB;AAAmBC,MAAAA,gB,kBAAAA,gB;;AACnBC,MAAAA,G,kBAAAA,G;;;;;;;;;AAET;OASM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBnB,U;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;iCAGaoB,e,WAFZF,OAAO,CAAC,iBAAD,C,UACP;AAAA;AAAA,sBAAIG,QAAJ,CAAa,aAAb,EAA4B,KAA5B,C,+BADD,MAEaD,eAFb;AAAA;AAAA,4BAE4C;AAAA;AAAA;AAAA,eAChCE,YADgC,GACR,KADQ;AAAA,eAEhCC,YAFgC,GAER,KAFQ;AAAA,eAGhCC,eAHgC,GAGN,CAHM;AAAA,eAIhCC,SAJgC,GAIZ,CAJY;AAAA;;AAMxCC,QAAAA,KAAK,GAAG;AACJ;AACA,eAAKD,SAAL,GAAiBE,IAAI,CAACC,GAAL,EAAjB;AACA;AAAA;AAAA,4BAAKC,GAAL,CAASC,WAAT,CAAqB,YAArB,EAAmC;AAAEL,YAAAA,SAAS,EAAE,KAAKA;AAAlB,WAAnC,EAHI,CAKJ;;AACA,cAAI;AAAA;AAAA,0CAAYM,QAAZ,KAAyB;AAAA;AAAA,oCAASC,QAAtC,EAAgD;AAC5C;AAAA;AAAA,8BAAKH,GAAL,CAASC,WAAT,CAAqB,wBAArB;AACH,WAFD,MAEO,IAAI;AAAA;AAAA,0CAAYC,QAAZ,KAAyB;AAAA;AAAA,oCAASE,QAAtC,EAAgD;AACnD;AAAA;AAAA,8BAAKJ,GAAL,CAASC,WAAT,CAAqB,kBAArB;AACA,iBAAKI,qBAAL,CAA2B,EAA3B,EAA+B,SAA/B,EAA0C,WAA1C;AACH,WAHM,MAGA;AACH;AACA,iBAAKC,iBAAL;AACH;;AAED,eAAKC,uBAAL;AACH;AAED;;;AACQD,QAAAA,iBAAiB,GAAG;AAAA;;AACxB,cAAIE,WAAW,4BAAG,KAAKC,IAAL,CAAUC,cAAV,CAAyB,MAAzB,CAAH,qBAAG,sBAAkCC,YAAlC,CAA+CtC,SAA/C,CAAlB;;AACA,cAAImC,WAAJ,EAAiB;AACbA,YAAAA,WAAW,CAACI,OAAZ,GAAsB,EAAtB;AACAxC,YAAAA,KAAK,CAACoC,WAAW,CAACC,IAAb,CAAL,CACKI,EADL,CACQ,CADR,EACW;AAAED,cAAAA,OAAO,EAAE,GAAX;AAAgBE,cAAAA,QAAQ,EAAE,IAAIxC,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf;AAA1B,aADX,EAEKyC,IAFL,CAEU,MAAM;AACR,mBAAKtB,YAAL,GAAoB,IAApB;;AACA,kBAAI,KAAKC,YAAT,EAAuB;AACnB,qBAAKsB,SAAL;AACH;AACJ,aAPL,EAQKnB,KARL;AASH,WAXD,MAWO;AACH,iBAAKJ,YAAL,GAAoB,IAApB;AACH;AACJ;AAED;;;AACQY,QAAAA,qBAAqB,CAACY,QAAD,EAAmBC,OAAnB,EAAoCC,MAApC,EAAqD;AAC9E,eAAKxB,eAAL,GAAuBsB,QAAvB;;AAEA,cAAIG,MAAM,CAACC,cAAP,IAAyB,OAAOD,MAAM,CAACC,cAAd,KAAiC,UAA9D,EAA0E;AACtED,YAAAA,MAAM,CAACC,cAAP,CAAsBJ,QAAtB,EAAgCC,OAAhC,EAAyCC,MAAzC;AACA;AAAA;AAAA,8BAAKnB,GAAL,CAASC,WAAT,CAAsB,YAAWgB,QAAS,OAAMC,OAAQ,EAAxD;AACH;AACJ;AAED;;;AACqC,cAAvBX,uBAAuB,GAAG;AACpC,cAAI;AACA;AACA,gBAAI;AAAA;AAAA,4CAAYe,mBAAZ,KAAoC9C,GAAG,IAAID,KAA3C,CAAJ,EAAuD;AACnD;AAAA;AAAA,wDAAiBgD,mBAAjB;AACA;AAAA;AAAA,gCAAKvB,GAAL,CAASC,WAAT,CAAqB,qBAArB;AACH,aALD,CAOA;;;AACA,kBAAMuB,OAAO,CAACC,GAAR,CAAY,CACd;AACA,iBAAKC,wBAAL,EAFc,EAGd;AACA,iBAAKC,qBAAL,EAJc,CAAZ,CAAN,CARA,CAeA;;AACA,kBAAM,KAAKC,2BAAL,EAAN,CAhBA,CAkBA;;AACA;AAAA;AAAA,8BAAKC,GAAL,CAASC,cAAT,CAAwB,KAAKC,aAA7B;AAEA,iBAAKrC,YAAL,GAAoB,IAApB,CArBA,CAuBA;;AACA,gBACI;AAAA;AAAA,4CAAYQ,QAAZ,KAAyB;AAAA;AAAA,sCAASC,QAAlC,IACA;AAAA;AAAA,4CAAYD,QAAZ,KAAyB;AAAA;AAAA,sCAASE,QAFtC,EAGE;AACE,mBAAKY,SAAL;AACH,aALD,MAKO,IAAI,KAAKvB,YAAT,EAAuB;AAC1B,mBAAKuB,SAAL;AACH;AACJ,WAhCD,CAgCE,OAAOgB,KAAP,EAAc;AACZ,iBAAKC,yBAAL,CAA+BD,KAA/B;AACH;AACJ;AAED;;;AACyC,cAA3BJ,2BAA2B,GAAkB;AACvD,cAAI;AACA;AACA,kBAAMM,WAAW,GAAG,KAAKC,wBAAL,EAApB,CAFA,CAIA;;AACA,gBAAID,WAAW,IAAI;AAAA;AAAA,wCAAUE,kBAA7B,EAAiD;AAC7C;AAAA;AAAA,gCAAKpC,GAAL,CAASC,WAAT,CAAqB,eAArB;AACA,oBAAM,KAAKoC,uBAAL,EAAN,CAF6C,CAI7C;;AACA,mBAAKC,4BAAL;AACH,aAND,MAMO;AACH;AACA,oBAAM,KAAKC,oBAAL,EAAN;AACH;AACJ,WAfD,CAeE,OAAOP,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKhC,GAAL,CAASwC,OAAT,CAAiB,iBAAjB,EAAoCR,KAApC,EADY,CAEZ;;AACA,kBAAM,KAAKK,uBAAL,EAAN;AACH;AACJ;AAED;;;AACQI,QAAAA,2BAA2B,GAA2B;AAC1D,gBAAMC,WAAW,GAAG,IAAI5C,IAAJ,EAApB,CAD0D,CAG1D;;AACA,gBAAM6C,cAAc,GAAG;AACnB;AACA,eAAG;AACCC,cAAAA,MAAM,EAAE,CADT;AAECC,cAAAA,QAAQ,EAAE,CAFX;AAGCC,cAAAA,MAAM,EAAE,CAHT;AAICC,cAAAA,IAAI,EAAE,MAJP;AAKCC,cAAAA,OAAO,EAAEN,WALV;AAMCO,cAAAA,aAAa,EAAEP,WANhB;AAOCQ,cAAAA,cAAc,EAAER;AAPjB,aAFgB;AAWnB;AACA,eAAG;AACCE,cAAAA,MAAM,EAAE,CADT;AAECC,cAAAA,QAAQ,EAAE,CAFX;AAGCC,cAAAA,MAAM,EAAE,CAHT;AAICC,cAAAA,IAAI,EAAE,MAJP;AAKCC,cAAAA,OAAO,EAAEN,WALV;AAMCO,cAAAA,aAAa,EAAEP,WANhB;AAOCQ,cAAAA,cAAc,EAAER;AAPjB,aAZgB;AAqBnB;AACA,eAAG;AACCE,cAAAA,MAAM,EAAE,CADT;AAECC,cAAAA,QAAQ,EAAE,CAFX;AAGCC,cAAAA,MAAM,EAAE,CAHT;AAICC,cAAAA,IAAI,EAAE,QAJP;AAKCC,cAAAA,OAAO,EAAEN,WALV;AAMCO,cAAAA,aAAa,EAAEP,WANhB;AAOCQ,cAAAA,cAAc,EAAER;AAPjB,aAtBgB;AA+BnB;AACA,eAAG;AACCE,cAAAA,MAAM,EAAE,IADT;AAECC,cAAAA,QAAQ,EAAE,CAFX;AAGCC,cAAAA,MAAM,EAAE,CAHT;AAICC,cAAAA,IAAI,EAAE,UAJP;AAKCC,cAAAA,OAAO,EAAEN,WALV;AAMCO,cAAAA,aAAa,EAAEP,WANhB;AAOCQ,cAAAA,cAAc,EAAER;AAPjB;AAhCgB,WAAvB;AA2CA;AAAA;AAAA,4BAAK1C,GAAL,CAASC,WAAT,CAAqB,cAArB,EAAqC;AACjCkD,YAAAA,IAAI,EAAER,cAAc,CAAC,CAAD,CAAd,CAAkBC,MADS;AAEjCQ,YAAAA,IAAI,EAAET,cAAc,CAAC,CAAD,CAAd,CAAkBC,MAFS;AAGjCS,YAAAA,IAAI,EAAEV,cAAc,CAAC,CAAD,CAAd,CAAkBC,MAHS;AAIjCU,YAAAA,IAAI,EAAEX,cAAc,CAAC,CAAD,CAAd,CAAkBC;AAJS,WAArC;AAOA,iBAAOD,cAAP;AACH;AAED;;;AACQL,QAAAA,4BAA4B,GAAS;AACzC;AACAiB,UAAAA,UAAU,CAAC,YAAY;AACnB,gBAAI;AACA;AAAA;AAAA,gCAAKvD,GAAL,CAASC,WAAT,CAAqB,oBAArB,EADA,CAGA;;AACA,oBAAM,KAAKuD,yBAAL,EAAN;AAEA;AAAA;AAAA,gCAAKxD,GAAL,CAASC,WAAT,CAAqB,cAArB,EANA,CAQA;;AACA;AAAA;AAAA,gCAAKiB,OAAL,CAAauC,aAAb,CAA2B,gBAA3B;AACH,aAVD,CAUE,OAAOzB,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKhC,GAAL,CAASwC,OAAT,CAAiB,gBAAjB,EAAmCR,KAAnC;AACH;AACJ,WAdS,EAcP,GAdO,CAAV;AAeH;AAED;;;AACuC,cAAzBwB,yBAAyB,GAAkB;AACrD,cAAI;AAAA;;AACA;AACA,kBAAME,YAAY,sBAAG;AAAA;AAAA,4BAAIC,IAAJ,CAASC,SAAZ,qBAAG,gBAAoBC,YAAzC;;AACA,gBAAIH,YAAJ,EAAkB;AACd;AACCA,cAAAA,YAAD,CAAsBI,eAAtB,GAAwC,IAAxC;AACH,aAND,CAQA;;;AACA,kBAAM,KAAKC,oBAAL,EAAN;AACH,WAVD,CAUE,OAAO/B,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKhC,GAAL,CAASwC,OAAT,CAAiB,cAAjB,EAAiCR,KAAjC;AACA,kBAAMA,KAAN;AACH;AACJ;AAED;;;AACkC,cAApB+B,oBAAoB,GAAkB;AAChD;AAAA;AAAA,4BAAK/D,GAAL,CAASC,WAAT,CAAqB,kBAArB,EADgD,CAGhD;;AACA,cAAI;AAAA;AAAA,0CAAYC,QAAZ,KAAyB;AAAA;AAAA,oCAASC,QAAtC,EAAgD;AAC5C;AACA;AAAA;AAAA,8BAAKH,GAAL,CAASC,WAAT,CAAqB,yBAArB;;AAEA,gBAAI;AACA,oBAAM;AAAE+D,gBAAAA;AAAF,kBAAoB,wCAA1B;AACA,oBAAMC,YAAY,GAAG,MAAMD,aAAa,CAACE,eAAd,EAA3B;;AAEA,kBAAID,YAAJ,EAAkB;AACd;AAAA;AAAA,kCAAKjE,GAAL,CAASC,WAAT,CAAqB,kBAArB;AACH,eAFD,MAEO;AACH;AAAA;AAAA,kCAAKD,GAAL,CAASwC,OAAT,CAAiB,4BAAjB;AACH;AACJ,aATD,CASE,OAAOR,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKhC,GAAL,CAASmE,QAAT,CAAkB,mBAAlB,EAAuCnC,KAAvC;AACH;AACJ,WAhBD,MAgBO;AACH;AACA;AAAA;AAAA,8BAAKhC,GAAL,CAASC,WAAT,CAAqB,gBAArB;;AAEA,gBAAI;AACA;AACA,oBAAM;AAAA;AAAA,8BAAI0D,IAAJ,CAASS,QAAT,EAAN;AACA;AAAA;AAAA,gCAAKpE,GAAL,CAASC,WAAT,CAAqB,YAArB;AACH,aAJD,CAIE,OAAO+B,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKhC,GAAL,CAASwC,OAAT,CAAiB,cAAjB,EAAiCR,KAAjC;AACA,oBAAMA,KAAN;AACH;AACJ;AACJ;AAED;;;AACQG,QAAAA,wBAAwB,GAAY;AACxC;AACA,cAAI;AAAA;AAAA,0BAAIwB,IAAJ,CAASC,SAAT,IAAsB;AAAA;AAAA,0BAAID,IAAJ,CAASC,SAAT,CAAmBC,YAA7C,EAA2D;AACvD,kBAAMQ,aAAa,GAAG;AAAA;AAAA,4BAAIV,IAAJ,CAASzB,WAAT,EAAtB;AACA;AAAA;AAAA,8BAAKlC,GAAL,CAASC,WAAT,CAAsB,oBAAmBoE,aAAc,EAAvD;AACA,mBAAOA,aAAP;AACH,WANuC,CAQxC;;;AACA,gBAAMC,cAAc,GAAG,CAAC,CAAC;AAAA;AAAA,4BAAKC,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,sDAAkBC,QAAnC,CAAzB;;AACA,cAAIH,cAAJ,EAAoB;AAChB;AAAA;AAAA,8BAAKtE,GAAL,CAASC,WAAT,CAAqB,mBAArB;AACA,mBAAO,KAAP;AACH,WAbuC,CAexC;;;AACA,gBAAMyE,WAAW,GAAG;AAAA;AAAA,4BAAKH,OAAL,CAAaI,OAAb,CAAqB;AAAA;AAAA,sDAAkBC,WAAvC,EAAoD,IAApD,CAApB;;AACA,cAAIF,WAAW,IAAIG,MAAM,CAACH,WAAD,CAAN,KAAwB,GAA3C,EAAgD;AAC5C;AAAA;AAAA,8BAAK1E,GAAL,CAASC,WAAT,CAAqB,qBAArB;AACA,mBAAO,KAAP;AACH,WApBuC,CAsBxC;;;AACA;AAAA;AAAA,4BAAKD,GAAL,CAASC,WAAT,CAAqB,sBAArB;AACA,iBAAO,IAAP;AACH;AAED;;;AACqC,cAAvBoC,uBAAuB,GAAkB;AACnD,cAAI;AACA;AAAA;AAAA,8BAAKrC,GAAL,CAASC,WAAT,CAAqB,qBAArB,EADA,CAGA;;AACA,kBAAMyC,WAAW,GAAG,IAAI5C,IAAJ,EAApB;AACA,kBAAMgF,aAAa,GAAG;AAClB;AACAC,cAAAA,GAAG,EAAE,CAFa;AAGlBC,cAAAA,KAAK,EAAE,EAHW;AAIlBC,cAAAA,UAAU,EAAE,EAJM;AAKlBC,cAAAA,UAAU,EAAE,EALM;AAMlBC,cAAAA,QAAQ,EAAE,EANQ;AAOlBC,cAAAA,QAAQ,EAAE,EAPQ;AAQlBC,cAAAA,GAAG,EAAE,CARa;AAQV;AACRC,cAAAA,UAAU,EAAE5C,WATM;AAUlB6C,cAAAA,MAAM,EAAE,EAVU;AAWlBrF,cAAAA,QAAQ,EAAE,KAXQ;AAYlBsF,cAAAA,YAAY,EAAE,KAZI;AAalBC,cAAAA,MAAM,EAAE,EAbU;AAclBC,cAAAA,QAAQ,EAAE,CAdQ;AAelBC,cAAAA,WAAW,EAAE,OAfK;AAiBlB;AACAC,cAAAA,SAAS,EAAE,CAlBO;AAmBlBC,cAAAA,KAAK,EAAE,CAnBW;AAoBlBC,cAAAA,oBAAoB,EAAE,CApBJ;AAqBlBC,cAAAA,qBAAqB,EAAErD,WArBL;AAsBlBsD,cAAAA,eAAe,EAAE,CAtBC;AAwBlB;AACAC,cAAAA,WAAW,EAAE,KAAKxD,2BAAL,EAzBK;AA0BlByD,cAAAA,UAAU,EAAE,EA1BM;AA4BlB;AACAhE,cAAAA,WAAW,EAAE,IA7BK;AA8BlBiE,cAAAA,OAAO,EAAE,IA9BS;AA+BlBC,cAAAA,QAAQ,EAAE;AA/BQ,aAAtB,CALA,CAuCA;AACA;;AACA,gBAAI;AAAA;AAAA,4BAAIzC,IAAJ,IAAY;AAAA;AAAA,4BAAIA,IAAJ,CAAS0C,cAAzB,EAAyC;AACrC;AAAA;AAAA,8BAAI1C,IAAJ,CAAS0C,cAAT,CAAwBvB,aAAxB;AACA;AAAA;AAAA,gCAAK9E,GAAL,CAASC,WAAT,CAAqB,6BAArB;AACH,aAHD,MAGO;AACH;AAAA;AAAA,gCAAKD,GAAL,CAASwC,OAAT,CAAiB,+BAAjB;AACH;;AAED;AAAA;AAAA,8BAAKxC,GAAL,CAASC,WAAT,CAAqB,kBAArB,EAAyC;AACrCiC,cAAAA,WAAW,EAAE4C,aAAa,CAAC5C,WADU;AAErCoE,cAAAA,SAAS,EAAEC,MAAM,CAACC,IAAP,CAAY1B,aAAa,CAACmB,WAA1B,EAAuCQ;AAFb,aAAzC;AAIH,WApDD,CAoDE,OAAOzE,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKhC,GAAL,CAASwC,OAAT,CAAiB,mBAAjB,EAAsCR,KAAtC;AACH;AACJ;AAED;;;AACsC,cAAxBN,wBAAwB,GAAkB;AACpD;AAAA;AAAA,4BAAK1B,GAAL,CAASC,WAAT,CAAqB,2BAArB;;AAEA,cAAI;AACA;AACA,gBAAI;AACA,oBAAM;AAAA;AAAA,gCAAKyG,GAAL,CAASC,UAAT,CAAoB,WAApB,CAAN;AACA;AAAA;AAAA,gCAAK3G,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH,aAHD,CAGE,OAAO+B,KAAP,EAAc;AACZ,oBAAM,IAAI4E,KAAJ,CAAW,gCAA+B5E,KAAM,EAAhD,CAAN;AACH,aAPD,CASA;;;AACA,kBAAM;AAAE6E,cAAAA;AAAF,gBAAmB,wCAAzB,CAVA,CAYA;;AACA,kBAAMC,KAAK,GAAG,CACV;AACIC,cAAAA,IAAI,EAAE,UADV;AAEIC,cAAAA,IAAI,EAAE,CAAC,MAAD,CAFV;AAGIC,cAAAA,QAAQ,EAAE,MAHd;AAIIC,cAAAA,MAAM,EAAE;AAJZ,aADU,CAAd;AASA,kBAAMC,MAAM,GAAG,MAAMN,YAAY,CAACO,SAAb,CAAuBN,KAAvB,CAArB;;AAEA,gBAAI,CAACK,MAAL,EAAa;AACT,oBAAM,IAAIP,KAAJ,CAAU,YAAV,CAAN;AACH;;AAED;AAAA;AAAA,8BAAK5G,GAAL,CAASC,WAAT,CAAqB,qBAArB;AACH,WA7BD,CA6BE,OAAO+B,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKhC,GAAL,CAASmE,QAAT,CAAkB,oBAAlB,EAAwCnC,KAAxC;AACA,kBAAMA,KAAN;AACH;AACJ;AAED;;;AACmC,cAArBL,qBAAqB,GAAkB;AACjD;AAAA;AAAA,4BAAK3B,GAAL,CAASC,WAAT,CAAqB,kBAArB;;AAEA,cAAI;AACA;AACA,kBAAM,CAACoH,YAAD,IAAiB,MAAM7F,OAAO,CAACC,GAAR,CAAY,CACrC;AAAA;AAAA,sCAAS6F,YAAT,EADqC,EAErC,KAAKC,YAAL,EAFqC,CAAZ,CAA7B;;AAKA,gBAAI,CAACF,YAAL,EAAmB;AACf,oBAAM,IAAIT,KAAJ,CAAU,SAAV,CAAN;AACH;;AAED;AAAA;AAAA,8BAAK5G,GAAL,CAASC,WAAT,CAAqB,cAArB;AACH,WAZD,CAYE,OAAO+B,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKhC,GAAL,CAASmE,QAAT,CAAkB,eAAlB,EAAmCnC,KAAnC;AACA,kBAAMA,KAAN;AACH;AACJ;AAED;;;AAC0B,cAAZuF,YAAY,GAAkB;AACxC,cAAIC,QAAQ,GAAG;AAAA;AAAA,4BAAKjD,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,sDAAkBiD,QAAnC,EAA6C,IAA7C,CAAf;AACAD,UAAAA,QAAQ,GAAG,IAAX;AACA;AAAA;AAAA,4BAAKA,QAAL,CAAcE,WAAd,CAA0BF,QAA1B;AACH;AAED;;;AACkC,cAApBjF,oBAAoB,GAAkB;AAAA;;AAChD;AACA,gBAAMoF,QAAQ,uBAAG;AAAA;AAAA,0BAAIhE,IAAJ,CAASC,SAAZ,qBAAG,iBAAoBC,YAArC;;AACA,cAAI8D,QAAQ,IAAI,CAAEA,QAAD,CAAkB7D,eAAnC,EAAoD;AAChD;AAAA;AAAA,8BAAK9D,GAAL,CAASC,WAAT,CAAqB,gBAArB;AACA;AACH;;AAED,cAAK0H,QAAL,YAAKA,QAAD,CAAmB7D,eAAvB,EAAwC;AACpC;AAAA;AAAA,8BAAK9D,GAAL,CAASC,WAAT,CAAqB,wBAArB;AACH,WAFD,MAEO;AACH;AAAA;AAAA,8BAAKD,GAAL,CAASC,WAAT,CAAqB,oBAArB;AACH,WAZ+C,CAchD;;;AACA,cAAI;AAAA;AAAA,0CAAYC,QAAZ,KAAyB;AAAA;AAAA,oCAASC,QAAtC,EAAgD;AAC5C;AACA;AAAA;AAAA,8BAAKH,GAAL,CAASC,WAAT,CAAqB,yBAArB;;AAEA,gBAAI;AACA,oBAAM;AAAE+D,gBAAAA;AAAF,kBAAoB,wCAA1B;AACA,oBAAMC,YAAY,GAAG,MAAMD,aAAa,CAACE,eAAd,EAA3B;;AAEA,kBAAID,YAAJ,EAAkB;AACd;AAAA;AAAA,kCAAKjE,GAAL,CAASC,WAAT,CAAqB,kBAArB;AACH,eAFD,MAEO;AACH;AAAA;AAAA,kCAAKD,GAAL,CAASwC,OAAT,CAAiB,4BAAjB;AACH;AACJ,aATD,CASE,OAAOR,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKhC,GAAL,CAASmE,QAAT,CAAkB,mBAAlB,EAAuCnC,KAAvC;AACH;AACJ,WAhBD,MAgBO,IAAI;AAAA;AAAA,0CAAY9B,QAAZ,KAAyB;AAAA;AAAA,oCAASE,QAAtC,EAAgD;AACnD;AAEA,gBAAI;AACA,oBAAM;AAAE4D,gBAAAA;AAAF,kBAAoB,wCAA1B,CADA,CAGA;;AACA,oBAAM4D,aAAa,GAAG;AAAA;AAAA,gCAAKrD,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,0DAAkBC,QAAnC,CAAtB;AACA,oBAAMoD,SAAS,GAAG,KAAKC,YAAL,EAAlB;AACA,kBAAIC,WAAW,GAAG,KAAlB;;AAEA,kBAAIH,aAAa,IAAIC,SAArB,EAAgC;AAC5B;AACA,oBAAIA,SAAS,CAACG,WAAV,GAAwBlI,IAAI,CAACC,GAAL,EAA5B,EAAwC;AACpC;AAAA;AAAA,oCAAKC,GAAL,CAASwC,OAAT,CAAiB,uBAAjB,EAA0C;AACtCyF,oBAAAA,IAAI,EAAE,IAAInI,IAAJ,CAAS+H,SAAS,CAACK,WAAnB,EAAgCC,cAAhC,EADgC;AAEtCC,oBAAAA,IAAI,EAAE,IAAItI,IAAJ,CAAS+H,SAAS,CAACG,WAAnB,EAAgCG,cAAhC,EAFgC;AAGtCE,oBAAAA,IAAI,EAAE,IAAIvI,IAAJ,GAAWqI,cAAX;AAHgC,mBAA1C;AAKAJ,kBAAAA,WAAW,GAAG,IAAd;AACH,iBAPD,MAOO;AACH;AAAA;AAAA,oCAAK/H,GAAL,CAASC,WAAT,CAAqB,+BAArB,EAAsD;AAClDqI,oBAAAA,IAAI,EACAC,IAAI,CAACC,KAAL,CACI,CAACX,SAAS,CAACG,WAAV,GAAwBlI,IAAI,CAACC,GAAL,EAAzB,KAAwC,KAAK,EAAL,GAAU,IAAlD,CADJ,IAEI;AAJ0C,mBAAtD,EADG,CAQH;;AACA,sBAAI;AACA,0BAAM;AAAA;AAAA,oCAAI4D,IAAJ,CAASS,QAAT,EAAN;;AAEA,wBAAI;AAAA;AAAA,oCAAIT,IAAJ,CAASC,SAAT,IAAsB;AAAA;AAAA,oCAAID,IAAJ,CAASC,SAAT,CAAmBC,YAA7C,EAA2D;AACvD;AAAA;AAAA,wCAAK7D,GAAL,CAASC,WAAT,CAAqB,oBAArB;AACA,6BAFuD,CAE/C;AACX,qBAHD,MAGO;AACH8H,sBAAAA,WAAW,GAAG,IAAd;AACH;AACJ,mBATD,CASE,OAAO/F,KAAP,EAAc;AACZ;AAAA;AAAA,sCAAKhC,GAAL,CAASwC,OAAT,CAAiB,sBAAjB,EAAyCR,KAAzC;AACA+F,oBAAAA,WAAW,GAAG,IAAd;AACH;AACJ;AACJ,eAhCD,MAgCO;AACH;AAAA;AAAA,kCAAK/H,GAAL,CAASC,WAAT,CAAqB,wBAArB;AACA8H,gBAAAA,WAAW,GAAG,IAAd;AACH,eA3CD,CA6CA;;;AACA,kBAAIA,WAAJ,EAAiB;AACb;AAAA;AAAA,kCAAK/H,GAAL,CAASC,WAAT,CAAqB,cAArB,EADa,CAGb;;AACA,oBAAI2H,aAAJ,EAAmB;AACf;AAAA;AAAA,oCAAKrD,OAAL,CAAakE,MAAb,CAAoB;AAAA;AAAA,8DAAkBhE,QAAtC;AACA;AAAA;AAAA,oCAAKF,OAAL,CAAakE,MAAb,CAAoB;AAAA;AAAA,8DAAkBC,YAAtC;AACA;AAAA;AAAA,oCAAK1I,GAAL,CAASC,WAAT,CAAqB,0BAArB;AACH,iBARY,CAUb;;;AACA,sBAAM0I,aAAa,GAAG,IAAI3E,aAAJ,EAAtB;AACA,sBAAM4E,iBAAiB,GAAG,MAAMD,aAAa,CAACE,gBAAd,EAAhC;;AAEA,oBAAID,iBAAJ,EAAuB;AACnB;AAAA;AAAA,oCAAK5I,GAAL,CAASC,WAAT,CAAqB,UAArB,EADmB,CAGnB;;AACA,wBAAM;AAAA;AAAA,kCAAI0D,IAAJ,CAASS,QAAT,EAAN;;AAEA,sBAAI;AAAA;AAAA,kCAAIT,IAAJ,CAASC,SAAT,IAAsB;AAAA;AAAA,kCAAID,IAAJ,CAASC,SAAT,CAAmBC,YAA7C,EAA2D;AACvD;AAAA;AAAA,sCAAK7D,GAAL,CAASC,WAAT,CAAqB,YAArB;AACH,mBAFD,MAEO;AACH;AAAA;AAAA,sCAAKD,GAAL,CAASwC,OAAT,CAAiB,kBAAjB;AACH;AACJ,iBAXD,MAWO;AACH;AAAA;AAAA,oCAAKxC,GAAL,CAASwC,OAAT,CAAiB,WAAjB;AACH;AACJ;AACJ,aA3ED,CA2EE,OAAOR,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKhC,GAAL,CAASmE,QAAT,CAAkB,aAAlB,EAAiCnC,KAAjC;AACH;AACJ,WAjFM,MAiFA;AACH;AACA,kBAAM;AAAA;AAAA,4BAAI2B,IAAJ,CAASS,QAAT,EAAN;AACH,WAnH+C,CAqHhD;;;AACA,cAAI;AAAA;AAAA,0BAAIT,IAAJ,CAASC,SAAT,IAAsB;AAAA;AAAA,0BAAID,IAAJ,CAASC,SAAT,CAAmBC,YAA7C,EAA2D;AACvD;AAAA;AAAA,8BAAK7D,GAAL,CAASC,WAAT,CAAqB,cAArB;AACH,WAFD,MAEO;AACH;AAAA;AAAA,8BAAKD,GAAL,CAASwC,OAAT,CAAiB,uBAAjB;AACH;AACJ;AAED;;;AACuB,cAATxB,SAAS,GAAkB;AACrC,cAAI;AACA;AAAA;AAAA,8BAAKhB,GAAL,CAASC,WAAT,CAAqB,cAArB,EADA,CAGA;;AACA,gBAAIiC,WAAW,GAAG,KAAKC,wBAAL,EAAlB;AAEA;AAAA;AAAA,8BAAKnC,GAAL,CAASC,WAAT,CAAsB,gBAAeiC,WAAY,EAAjD;AACA;AAAA;AAAA,8BAAKlC,GAAL,CAASC,WAAT,CAAsB,QAAOiC,WAAW,GAAG,MAAH,GAAY,IAAK,IAAzD;AAEA,gBAAI4G,OAAO,GAAG,KAAd;AACA,gBAAIC,WAAW,GAAG,IAAlB,CAVA,CAYA;;AACA,gBACI;AAAA;AAAA,4CAAY7I,QAAZ,KAAyB;AAAA;AAAA,sCAASC,QAAlC,IACA;AAAA;AAAA,4CAAYD,QAAZ,KAAyB;AAAA;AAAA,sCAASE,QAFtC,EAGE;AACE2I,cAAAA,WAAW,GAAG,KAAd;AACH;;AAED,gBAAI7G,WAAJ,EAAiB;AACb;AAAA;AAAA,gCAAKlC,GAAL,CAASC,WAAT,CACK,MAAK;AAAA;AAAA,8CAAYC,QAAZ,KAAyB;AAAA;AAAA,wCAASC,QAAlC,GAA6C,UAA7C,GAA0D;AAAA;AAAA,8CAAYD,QAAZ,KAAyB;AAAA;AAAA,wCAASE,QAAlC,GAA6C,IAA7C,GAAoD,EAAG,oBAD3H;AAIA0I,cAAAA,OAAO,GAAG,MAAM;AAAA;AAAA,8BAAIE,QAAJ,CAAaC,aAAb,CACZ;AAAA;AAAA,0CAAUC,KADE,EAEZ,CAFY,EAGZH,WAHY,EAIZ,KAAKI,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAJY,CAAhB;AAMH,aAXD,MAWO;AACH;AACA;AAAA;AAAA,gCAAKpJ,GAAL,CAASC,WAAT,CACK,MAAK;AAAA;AAAA,8CAAYC,QAAZ,KAAyB;AAAA;AAAA,wCAASC,QAAlC,GAA6C,UAA7C,GAA0D;AAAA;AAAA,8CAAYD,QAAZ,KAAyB;AAAA;AAAA,wCAASE,QAAlC,GAA6C,IAA7C,GAAoD,EAAG,kBAD3H;AAGA0I,cAAAA,OAAO,GAAG,MAAM;AAAA;AAAA,8BAAIE,QAAJ,CAAaC,aAAb,CACZ;AAAA;AAAA,0CAAUI,IADE,EAEZC,SAFY,EAGZP,WAHY,EAIZ,KAAKI,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAJY,CAAhB;AAMH;;AAED,gBAAIN,OAAJ,EAAa;AACT;AAAA;AAAA,gCAAK9I,GAAL,CAASC,WAAT,CAAsB,MAAKiC,WAAW,GAAG,IAAH,GAAU,IAAK,QAArD;AACH,aAFD,MAEO;AACH,oBAAM,IAAI0E,KAAJ,CAAW,GAAE1E,WAAW,GAAG,IAAH,GAAU,IAAK,QAAvC,CAAN;AACH;AACJ,WAjDD,CAiDE,OAAOF,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKhC,GAAL,CAASmE,QAAT,CAAkB,YAAlB,EAAgCnC,KAAhC,EADY,CAGZ;;AACA,iBAAK3B,qBAAL,CAA2B,CAA3B,EAA8B,QAA9B,EAAwC,SAAxC;AAEA;AAAA;AAAA,8BAAKwB,GAAL,CAAS0H,KAAT,CAAe,gBAAf,EANY,CAQZ;;AACA,iBAAKC,GAAL,CAASf,MAAT,CAAgBlJ,eAAhB;AACH;AACJ;AAED;;;AACQ0C,QAAAA,yBAAyB,CAACD,KAAD,EAAmB;AAChD;AAAA;AAAA,4BAAKhC,GAAL,CAASmE,QAAT,CAAkB,YAAlB,EAAgCnC,KAAhC,EADgD,CAGhD;;AACA,eAAK3B,qBAAL,CAA2B,CAA3B,EAA8B,OAA9B,EAAuC2B,KAAK,CAACd,OAAN,IAAiB,MAAxD,EAJgD,CAMhD;;AACA,cAAIc,KAAK,CAACd,OAAN,CAAcuI,QAAd,CAAuB,WAAvB,CAAJ,EAAyC;AACrC;AAAA;AAAA,8BAAK5H,GAAL,CAAS0H,KAAT,CAAe,mBAAf,EADqC,CAErC;;AACA,iBAAKC,GAAL,CAASf,MAAT,CAAgBlJ,eAAhB;AACA,mBAJqC,CAI7B;AACX,WAZ+C,CAchD;;;AACA;AAAA;AAAA,4BAAKsC,GAAL,CAAS0H,KAAT,CAAe,gBAAf;AACA,eAAKG,mBAAL;AACH;AAED;;;AACQA,QAAAA,mBAAmB,GAAS;AAChCnG,UAAAA,UAAU,CAAC,MAAM;AACb;AAAA;AAAA,8BAAKvD,GAAL,CAASC,WAAT,CAAqB,eAArB;AACA,iBAAKN,eAAL,GAAuB,CAAvB;AACA,iBAAKU,qBAAL,CAA2B,CAA3B,EAA8B,SAA9B,EAAyC,SAAzC;AACA,iBAAKE,uBAAL;AACH,WALS,EAKP,IALO,CAAV;AAMH;AAED;;;AACQoJ,QAAAA,yBAAyB,GAAS;AACtC;AAAA;AAAA,4BAAK3J,GAAL,CAASC,WAAT,CAAqB,yBAArB;;AAEA,cAAI;AACA;AACA,gBAAImB,MAAM,CAACC,cAAP,IAAyB,OAAOD,MAAM,CAACC,cAAd,KAAiC,UAA9D,EAA0E;AACtED,cAAAA,MAAM,CAACC,cAAP,CAAsB,GAAtB,EAA2B,MAA3B;AACA;AAAA;AAAA,gCAAKrB,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH,aAHD,MAGO;AACH;AAAA;AAAA,gCAAKD,GAAL,CAASC,WAAT,CAAqB,sCAArB;AACH,aAPD,CASA;;;AACA,gBAAImB,MAAM,CAACwI,UAAP,IAAqB,OAAOxI,MAAM,CAACwI,UAAd,KAA6B,UAAtD,EAAkE;AAC9DxI,cAAAA,MAAM,CAACwI,UAAP;AACA;AAAA;AAAA,gCAAK5J,GAAL,CAASC,WAAT,CAAqB,cAArB;AACH,aAHD,MAGO;AACH;AAAA;AAAA,gCAAKD,GAAL,CAASC,WAAT,CAAqB,kCAArB;AACH;AACJ,WAhBD,CAgBE,OAAO+B,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKhC,GAAL,CAASwC,OAAT,CAAiB,qBAAjB,EAAwCR,KAAxC;AACH;AACJ;AAED;;;AACQmH,QAAAA,cAAc,GAAS;AAC3B;AACA,gBAAMU,OAAO,GAAG/J,IAAI,CAACC,GAAL,EAAhB;AACA,gBAAM+J,SAAS,GAAGD,OAAO,GAAG,KAAKjK,SAAjC,CAH2B,CAK3B;;AACA,cACI;AAAA;AAAA,0CAAYM,QAAZ,KAAyB;AAAA;AAAA,oCAASC,QAAlC,IACA;AAAA;AAAA,0CAAYD,QAAZ,KAAyB;AAAA;AAAA,oCAASE,QAFtC,EAGE;AACE,iBAAKuJ,yBAAL;AACH,WAX0B,CAa3B;;;AACA,cAAI;AAAA;AAAA,0CAAYzJ,QAAZ,KAAyB;AAAA;AAAA,oCAASC,QAAtC,EAAgD;AAC5C,iBAAK4J,sBAAL;AACH;;AAED;AAAA;AAAA,4BAAK/J,GAAL,CAASC,WAAT,CAAqB,YAArB,EAAmC;AAC/B6J,YAAAA,SAAS,EAAG,GAAEA,SAAU,IADO;AAE/BE,YAAAA,WAAW,EAAEF,SAAS,GAAG,IAAZ,GAAmB,IAAnB,GAA0BA,SAAS,GAAG,IAAZ,GAAmB,IAAnB,GAA0B;AAFlC,WAAnC,EAlB2B,CAuB3B;;AACA,eAAKN,GAAL,CAASf,MAAT,CAAgBlJ,eAAhB;AACH;AAED;;;AACQwK,QAAAA,sBAAsB,GAAS;AACnC,cAAI3I,MAAM,CAAC6I,SAAP,IAAoB7I,MAAM,CAAC6I,SAAP,CAAiBC,kBAAzC,EAA6D;AACzD9I,YAAAA,MAAM,CAAC6I,SAAP,CAAiBC,kBAAjB,CAAoC,GAApC;AACA;AAAA;AAAA,8BAAKlK,GAAL,CAASC,WAAT,CAAqB,6BAArB;AACH;AACJ;AAED;;;AACA8B,QAAAA,aAAa,GAAS;AAClB;AAAA;AAAA,4BAAK/B,GAAL,CAASmE,QAAT,CAAkB,UAAlB;AACH;AAED;;;AACQ2D,QAAAA,YAAY,GAAuE;AACvF,cAAI;AACA,kBAAMqC,YAAY,GAAG;AAAA;AAAA,8BAAK5F,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,wDAAkBkE,YAAnC,CAArB;;AACA,gBAAIyB,YAAJ,EAAkB;AACd,qBAAOC,IAAI,CAACC,KAAL,CAAWF,YAAX,CAAP;AACH;AACJ,WALD,CAKE,OAAOnI,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKhC,GAAL,CAASwC,OAAT,CAAiB,iBAAjB,EAAoCR,KAApC;AACH;;AACD,iBAAO,IAAP;AACH;;AAEDsI,QAAAA,KAAK,GAAS;AACV,eAAK7K,YAAL,GAAoB,KAApB;AACA,eAAKC,YAAL,GAAoB,KAApB;AACA,eAAKC,eAAL,GAAuB,CAAvB;AACH;;AAnsBuC,O", "sourcesContent": ["import { _decorator, tween, UIOpacity, Vec3 } from 'cc';\n\nimport { DEBUG, DEV } from 'cc/env';\nimport { oops } from '../../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { JsonUtil } from '../../../../../extensions/oops-plugin-framework/assets/core/utils/JsonUtil';\nimport { ecs } from '../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';\nimport { CCComp } from '../../../../../extensions/oops-plugin-framework/assets/module/common/CCComp';\nimport { GameConst } from '../../../tsrpc/models/GameConst';\nimport { Platform, ShareConfig } from '../../../tsrpc/models/ShareConfig';\nimport { SceneType } from '../../../tsrpc/protocols/base';\nimport { ClientConst } from '../../common/ClientConst';\nimport { GameStorageConfig, StorageTestUtils } from '../../common/config/GameStorageConfig';\nimport { smc } from '../../common/SingletonModuleComp';\n\n// 扩展window类型\ndeclare global {\n    interface Window {\n        updateProgress?: (progress: number, message?: string, detail?: string) => void;\n        hideLoader?: () => void;\n        FBInstant?: any;\n    }\n}\n\nconst { ccclass, property } = _decorator;\n\n/**\n * 游戏完整初始化组件 - 自定义加载版本\n *\n * 职责：\n * - UI动画播放\n * - Bundle验证和加载\n * - 配置数据加载（配置表 + 语言包）\n * - 用户数据加载\n * - 进入游戏流程\n *\n * 优化：\n * - 所有初始化逻辑集中管理\n * - Bundle加载失败直接报错\n * - 配置表和语言包并行加载\n * - 支持自定义加载界面（Facebook + 个人平台）\n */\n@ccclass('InitialViewComp')\**************('InitialView', false)\nexport class InitialViewComp extends CCComp {\n    private waitComplete: boolean = false;\n    private loadComplete: boolean = false;\n    private currentProgress: number = 0;\n    private startTime: number = 0;\n\n    start() {\n        // 🚀 记录启动开始时间\n        this.startTime = Date.now();\n        oops.log.logBusiness('🚀 游戏初始化开始', { startTime: this.startTime });\n\n        // 🚀 统一使用自定义加载界面，跳过Cocos内置动画\n        if (ShareConfig.platform === Platform.FACEBOOK) {\n            oops.log.logBusiness('🚀 Facebook环境: 自定义加载模式');\n        } else if (ShareConfig.platform === Platform.PERSONAL) {\n            oops.log.logBusiness('🚀 个人环境: 自定义加载模式');\n            this.updateLoadingProgress(50, '游戏引擎已启动', '个人环境初始化完成');\n        } else {\n            // 其他环境：使用传统动画\n            this.playWaitAnimation();\n        }\n\n        this.startFullInitialization();\n    }\n\n    /** 🎬 播放等待动画（传统环境） */\n    private playWaitAnimation() {\n        let logoOpacity = this.node.getChildByName('logo')?.getComponent(UIOpacity);\n        if (logoOpacity) {\n            logoOpacity.opacity = 50;\n            tween(logoOpacity.node)\n                .to(1, { opacity: 255, position: new Vec3(0, 0, 0) })\n                .call(() => {\n                    this.waitComplete = true;\n                    if (this.loadComplete) {\n                        this.enterGame();\n                    }\n                })\n                .start();\n        } else {\n            this.waitComplete = true;\n        }\n    }\n\n    /** 🎯 更新加载进度 */\n    private updateLoadingProgress(progress: number, message: string, detail?: string) {\n        this.currentProgress = progress;\n\n        if (window.updateProgress && typeof window.updateProgress === 'function') {\n            window.updateProgress(progress, message, detail);\n            oops.log.logBusiness(`🎯 加载进度: ${progress}% - ${message}`);\n        }\n    }\n\n    /** 🚀 完整初始化流程 - 重构版本 */\n    private async startFullInitialization() {\n        try {\n            // 🧪 测试开关：控制数据清空行为\n            if (ClientConst.alwaysNewPlayerTest && (DEV || DEBUG)) {\n                StorageTestUtils.forceNewPlayerState();\n                oops.log.logBusiness('🎓 [测试模式] 强制设置为新手状态');\n            }\n\n            // 🚀 优化：并行执行可独立的初始化任务\n            await Promise.all([\n                // 1️⃣ 验证和加载Bundle + 基础UI资源\n                this.loadBundleAndUIResources(),\n                // 2️⃣ 加载配置数据（配置表 + 语言包）\n                this.loadConfigurationData(),\n            ]);\n\n            // 3️⃣ 尝试快速用户数据初始化\n            await this.quickUserDataInitialization();\n\n            // 设置窗口打开失败事件\n            oops.gui.setOpenFailure(this.onOpenFailure);\n\n            this.loadComplete = true;\n\n            // 🚀 自定义加载环境直接进入游戏\n            if (\n                ShareConfig.platform === Platform.FACEBOOK ||\n                ShareConfig.platform === Platform.PERSONAL\n            ) {\n                this.enterGame();\n            } else if (this.waitComplete) {\n                this.enterGame();\n            }\n        } catch (error) {\n            this.handleInitializationError(error);\n        }\n    }\n\n    /** � 快速用户数据初始化 */\n    private async quickUserDataInitialization(): Promise<void> {\n        try {\n            // 🔍 首先尝试快速加载现有用户数据\n            const isNewPlayer = this.determineNewPlayerStatus();\n\n            // 🚀 如果没有现有数据，启用快速启动模式\n            if (isNewPlayer && GameConst.newPlayerFastStart) {\n                oops.log.logBusiness('🚀 启用新手快速启动模式');\n                await this.initializeBasicRoleData();\n\n                // 🔄 在后台继续完整的用户数据加载\n                this.loadFullUserDataInBackground();\n            } else {\n                // 🔄 传统模式：完整加载用户数据\n                await this.ensureUserDataLoaded();\n            }\n        } catch (error) {\n            oops.log.logWarn('⚠️ 快速用户数据初始化失败:', error);\n            // 失败时创建基础数据，确保游戏可以启动\n            await this.initializeBasicRoleData();\n        }\n    }\n\n    /** 🎁 为新手玩家创建默认道具数据 */\n    private createNewPlayerDefaultProps(): { [key: number]: any } {\n        const currentTime = new Date();\n\n        // 🎯 新手玩家默认道具配置\n        const newPlayerProps = {\n            // 1. 移出道具 - 新手给3个\n            1: {\n                amount: 3,\n                propType: 1,\n                propId: 1,\n                desc: '移出道具',\n                getTime: currentTime,\n                lastResetTime: currentTime,\n                lastUpdateTime: currentTime,\n            },\n            // 2. 提示道具 - 新手给3个\n            2: {\n                amount: 3,\n                propType: 2,\n                propId: 2,\n                desc: '提示道具',\n                getTime: currentTime,\n                lastResetTime: currentTime,\n                lastUpdateTime: currentTime,\n            },\n            // 3. 重新洗牌道具 - 新手给2个\n            3: {\n                amount: 2,\n                propType: 3,\n                propId: 3,\n                desc: '重新洗牌道具',\n                getTime: currentTime,\n                lastResetTime: currentTime,\n                lastUpdateTime: currentTime,\n            },\n            // 7. 每日挑战剩余次数 - 新手给足够的次数\n            7: {\n                amount: 9999,\n                propType: 7,\n                propId: 7,\n                desc: '每日挑战剩余次数',\n                getTime: currentTime,\n                lastResetTime: currentTime,\n                lastUpdateTime: currentTime,\n            },\n        };\n\n        oops.log.logBusiness('🎁 新手默认道具配置:', {\n            移出道具: newPlayerProps[1].amount,\n            提示道具: newPlayerProps[2].amount,\n            洗牌道具: newPlayerProps[3].amount,\n            挑战次数: newPlayerProps[7].amount,\n        });\n\n        return newPlayerProps;\n    }\n\n    /** 🔄 后台加载完整用户数据 */\n    private loadFullUserDataInBackground(): void {\n        // 使用setTimeout确保不阻塞当前流程\n        setTimeout(async () => {\n            try {\n                oops.log.logBusiness('🔄 开始后台加载完整用户数据...');\n\n                // 🚀 强制执行完整的登录流程，不跳过\n                await this.forceCompleteUserDataLoad();\n\n                oops.log.logBusiness('✅ 后台用户数据加载完成');\n\n                // 触发数据更新事件，通知游戏其他模块\n                oops.message.dispatchEvent('UserDataLoaded');\n            } catch (error) {\n                oops.log.logWarn('⚠️ 后台用户数据加载失败:', error);\n            }\n        }, 100);\n    }\n\n    /** 🔄 强制执行完整的用户数据加载流程 */\n    private async forceCompleteUserDataLoad(): Promise<void> {\n        try {\n            // 🔄 临时清除基础数据标记，强制执行完整登录\n            const tempUserData = smc.role.RoleModel?.userGameData;\n            if (tempUserData) {\n                // 标记这是临时数据，需要完整登录\n                (tempUserData as any).isTemporaryData = true;\n            }\n\n            // 🔄 执行完整的登录流程\n            await this.performCompleteLogin();\n        } catch (error) {\n            oops.log.logWarn('⚠️ 强制完整登录失败:', error);\n            throw error;\n        }\n    }\n\n    /** 🔐 执行完整的登录流程 */\n    private async performCompleteLogin(): Promise<void> {\n        oops.log.logBusiness('🔐 开始执行完整登录流程...');\n\n        // 🔧 根据平台选择不同的登录方式\n        if (ShareConfig.platform === Platform.FACEBOOK) {\n            // Facebook环境：执行Facebook自动登录\n            oops.log.logBusiness('🔐 Facebook环境：执行自动登录...');\n\n            try {\n                const { LoginViewComp } = await import('./LoginViewComp');\n                const loginSuccess = await LoginViewComp.doFacebookLogin();\n\n                if (loginSuccess) {\n                    oops.log.logBusiness('✅ Facebook自动登录成功');\n                } else {\n                    oops.log.logWarn('⚠️ Facebook自动登录失败，继续尝试加载数据');\n                }\n            } catch (error) {\n                oops.log.logError('❌ Facebook自动登录异常:', error);\n            }\n        } else {\n            // 🔐 其他环境：执行标准登录流程\n            oops.log.logBusiness('🔐 执行标准登录流程...');\n\n            try {\n                // 调用Role模块的数据加载方法\n                await smc.role.loadData();\n                oops.log.logBusiness('✅ 标准登录流程完成');\n            } catch (error) {\n                oops.log.logWarn('⚠️ 标准登录流程失败:', error);\n                throw error;\n            }\n        }\n    }\n\n    /** � 智能判断新手状态 */\n    private determineNewPlayerStatus(): boolean {\n        // 1. 优先使用Role模块的判断\n        if (smc.role.RoleModel && smc.role.RoleModel.userGameData) {\n            const isNewFromRole = smc.role.isNewPlayer();\n            oops.log.logBusiness(`🎯 Role模块判断新手状态: ${isNewFromRole}`);\n            return isNewFromRole;\n        }\n\n        // 2. 检查本地存储的登录记录\n        const hasLoginRecord = !!oops.storage.get(GameStorageConfig.SSOToken);\n        if (hasLoginRecord) {\n            oops.log.logBusiness('🔍 检测到登录记录，判定为老玩家');\n            return false;\n        }\n\n        // 3. 检查是否有其他用户数据痕迹\n        const userDumpKey = oops.storage.getJson(GameStorageConfig.UserDumpKey, null);\n        if (userDumpKey && String(userDumpKey) !== '0') {\n            oops.log.logBusiness('🔍 检测到用户数据痕迹，判定为老玩家');\n            return false;\n        }\n\n        // 4. 默认判定为新手\n        oops.log.logBusiness('🆕 无任何用户数据痕迹，判定为新手玩家');\n        return true;\n    }\n\n    /** �🚀 初始化基础Role数据，确保道具系统可用 */\n    private async initializeBasicRoleData(): Promise<void> {\n        try {\n            oops.log.logBusiness('🚀 开始初始化基础Role数据...');\n\n            // 创建完整的基础用户数据结构\n            const currentTime = new Date();\n            const basicUserData = {\n                // 基础标识\n                key: 0,\n                guuid: '',\n                googleUuid: '',\n                facebookId: '',\n                userName: '',\n                nickName: '',\n                sex: 1, // SexType.None\n                createtime: currentTime,\n                openid: '',\n                platform: 'web',\n                platformType: 'web',\n                avatar: '',\n                avatarId: 0,\n                countryCode: 'Other',\n\n                // 游戏进度\n                passTimes: 0,\n                index: 0,\n                currCountryPassTimes: 0,\n                lastChangeCountryTime: currentTime,\n                selfCountryRank: 0,\n\n                // 道具和记录数据\n                propUseData: this.createNewPlayerDefaultProps(),\n                recordData: {},\n\n                // 新手和游客状态\n                isNewPlayer: true,\n                isGuest: true,\n                lastStep: 0,\n            };\n\n            // 🎯 使用Role模块的updateUserData方法来设置数据\n            // 这样可以确保数据完整性和触发相关事件\n            if (smc.role && smc.role.updateUserData) {\n                smc.role.updateUserData(basicUserData as any);\n                oops.log.logBusiness('✅ 基础用户数据已通过updateUserData设置');\n            } else {\n                oops.log.logWarn('⚠️ Role模块或updateUserData方法不可用');\n            }\n\n            oops.log.logBusiness('🚀 基础Role数据初始化完成', {\n                isNewPlayer: basicUserData.isNewPlayer,\n                propCount: Object.keys(basicUserData.propUseData).length,\n            });\n        } catch (error) {\n            oops.log.logWarn('⚠️ 基础Role数据初始化失败:', error);\n        }\n    }\n\n    /** 1️⃣ 验证和加载Bundle + 基础UI资源 */\n    private async loadBundleAndUIResources(): Promise<void> {\n        oops.log.logBusiness('🔍 开始验证Bundle和加载基础UI资源...');\n\n        try {\n            // 🚀 直接验证bundleOne是否存在，不存在就报错\n            try {\n                await oops.res.loadBundle('bundleOne');\n                oops.log.logBusiness('✅ bundleOne加载成功');\n            } catch (error) {\n                throw new Error(`❌ 关键Bundle 'bundleOne' 加载失败: ${error}`);\n            }\n\n            // 🚀 延迟导入，减少初始bundle大小\n            const { simpleLoader } = await import('../../common/loader/SimpleLoadingManager');\n\n            // 🎯 加载基础UI资源\n            const tasks = [\n                {\n                    name: '加载基础UI资源',\n                    dirs: ['boot'],\n                    priority: 'high',\n                    bundle: 'bundleOne',\n                },\n            ];\n\n            const result = await simpleLoader.loadTasks(tasks);\n\n            if (!result) {\n                throw new Error('基础UI资源加载失败');\n            }\n\n            oops.log.logBusiness('✅ Bundle和基础UI资源加载完成');\n        } catch (error) {\n            oops.log.logError('❌ Bundle和UI资源加载失败:', error);\n            throw error;\n        }\n    }\n\n    /** 2️⃣ 加载配置数据资源（配置表 + 语言包） */\n    private async loadConfigurationData(): Promise<void> {\n        oops.log.logBusiness('🔄 开始加载配置数据资源...');\n\n        try {\n            // 🚀 优化：配置表和语言包可以并行加载\n            const [configResult] = await Promise.all([\n                JsonUtil.loadDirAsync(),\n                this.loadLanguage(),\n            ]);\n\n            if (!configResult) {\n                throw new Error('配置表加载失败');\n            }\n\n            oops.log.logBusiness('✅ 配置数据资源加载完成');\n        } catch (error) {\n            oops.log.logError('❌ 配置数据资源加载失败:', error);\n            throw error;\n        }\n    }\n\n    /** 加载语言包 */\n    private async loadLanguage(): Promise<void> {\n        let language = oops.storage.get(GameStorageConfig.Language, 'en');\n        language = 'en';\n        oops.language.setLanguage(language);\n    }\n\n    /** 3️⃣ 确保用户数据已加载 - 完整登录流程 */\n    private async ensureUserDataLoaded(): Promise<void> {\n        // 检查是否已有用户数据，但排除临时数据\n        const userData = smc.role.RoleModel?.userGameData;\n        if (userData && !(userData as any).isTemporaryData) {\n            oops.log.logBusiness('✅ 用户数据已存在，跳过加载');\n            return;\n        }\n\n        if ((userData as any)?.isTemporaryData) {\n            oops.log.logBusiness('🔄 检测到临时数据，执行完整登录流程...');\n        } else {\n            oops.log.logBusiness('🔄 开始完整用户数据加载流程...');\n        }\n\n        // 🔧 根据平台选择不同的登录方式\n        if (ShareConfig.platform === Platform.FACEBOOK) {\n            // Facebook环境：执行Facebook自动登录\n            oops.log.logBusiness('🔐 Facebook环境：执行自动登录...');\n\n            try {\n                const { LoginViewComp } = await import('./LoginViewComp');\n                const loginSuccess = await LoginViewComp.doFacebookLogin();\n\n                if (loginSuccess) {\n                    oops.log.logBusiness('✅ Facebook自动登录成功');\n                } else {\n                    oops.log.logWarn('⚠️ Facebook自动登录失败，继续尝试加载数据');\n                }\n            } catch (error) {\n                oops.log.logError('❌ Facebook自动登录异常:', error);\n            }\n        } else if (ShareConfig.platform === Platform.PERSONAL) {\n            //\n\n            try {\n                const { LoginViewComp } = await import('./LoginViewComp');\n\n                // 🔍 先检查token过期时间，再尝试加载数据\n                const existingToken = oops.storage.get(GameStorageConfig.SSOToken);\n                const tokenInfo = this.getTokenInfo();\n                let needRelogin = false;\n\n                if (existingToken && tokenInfo) {\n                    // 检查token是否过期\n                    if (tokenInfo.expiredTime < Date.now()) {\n                        oops.log.logWarn('⏰ SSO Token已过期，需要重新登录', {\n                            创建时间: new Date(tokenInfo.createdTime).toLocaleString(),\n                            过期时间: new Date(tokenInfo.expiredTime).toLocaleString(),\n                            当前时间: new Date().toLocaleString(),\n                        });\n                        needRelogin = true;\n                    } else {\n                        oops.log.logBusiness('🔑 检测到有效SSO Token，尝试加载用户数据...', {\n                            剩余时间:\n                                Math.round(\n                                    (tokenInfo.expiredTime - Date.now()) / (60 * 60 * 1000)\n                                ) + '小时',\n                        });\n\n                        // 尝试加载用户数据以验证token有效性\n                        try {\n                            await smc.role.loadData();\n\n                            if (smc.role.RoleModel && smc.role.RoleModel.userGameData) {\n                                oops.log.logBusiness('✅ 用户数据加载成功，token有效');\n                                return; // 成功加载，直接返回\n                            } else {\n                                needRelogin = true;\n                            }\n                        } catch (error) {\n                            oops.log.logWarn('⚠️ token验证失败，需要重新登录:', error);\n                            needRelogin = true;\n                        }\n                    }\n                } else {\n                    oops.log.logBusiness('🔍 未检测到有效token或token信息');\n                    needRelogin = true;\n                }\n\n                // 如果需要重新登录\n                if (needRelogin) {\n                    oops.log.logBusiness('🔄 执行游客登录...');\n\n                    // 清除旧token和token信息\n                    if (existingToken) {\n                        oops.storage.remove(GameStorageConfig.SSOToken);\n                        oops.storage.remove(GameStorageConfig.SSOTokenInfo);\n                        oops.log.logBusiness('🗑️ 已清除无效的SSO Token和相关信息');\n                    }\n\n                    // 创建临时LoginViewComp实例进行游客登录\n                    const tempLoginComp = new LoginViewComp();\n                    const guestLoginSuccess = await tempLoginComp.loginGuestButton();\n\n                    if (guestLoginSuccess) {\n                        oops.log.logBusiness('✅ 游客登录成功');\n\n                        // 重新加载用户数据\n                        await smc.role.loadData();\n\n                        if (smc.role.RoleModel && smc.role.RoleModel.userGameData) {\n                            oops.log.logBusiness('✅ 用户数据加载成功');\n                        } else {\n                            oops.log.logWarn('⚠️ 游客登录后用户数据加载失败');\n                        }\n                    } else {\n                        oops.log.logWarn('⚠️ 游客登录失败');\n                    }\n                }\n            } catch (error) {\n                oops.log.logError('❌ 游客登录流程异常:', error);\n            }\n        } else {\n            // 其他平台，直接尝试加载数据\n            await smc.role.loadData();\n        }\n\n        // 最终检查结果\n        if (smc.role.RoleModel && smc.role.RoleModel.userGameData) {\n            oops.log.logBusiness('✅ 用户数据最终加载成功');\n        } else {\n            oops.log.logWarn('⚠️ 用户数据最终加载失败，但继续进入游戏');\n        }\n    }\n\n    /** 🎮 进入游戏流程 - 优化版本 */\n    private async enterGame(): Promise<void> {\n        try {\n            oops.log.logBusiness('🎓 开始进入游戏...');\n\n            // 🔍 智能判断新手状态\n            let isNewPlayer = this.determineNewPlayerStatus();\n\n            oops.log.logBusiness(`🎯 最终判定新手状态: ${isNewPlayer}`);\n            oops.log.logBusiness(`🎮 进入${isNewPlayer ? '新手游戏' : '大厅'}场景`);\n\n            let success = false;\n            let showLoading = true;\n\n            // 🎯 使用自定义加载界面的平台不显示Cocos默认加载\n            if (\n                ShareConfig.platform === Platform.FACEBOOK ||\n                ShareConfig.platform === Platform.PERSONAL\n            ) {\n                showLoading = false;\n            }\n\n            if (isNewPlayer) {\n                oops.log.logBusiness(\n                    `🆕 ${ShareConfig.platform === Platform.FACEBOOK ? 'Facebook' : ShareConfig.platform === Platform.PERSONAL ? '个人' : ''}新手玩家：进入游戏场景（Foods）`\n                );\n\n                success = await smc.sceneMgr.switchToScene(\n                    SceneType.Foods,\n                    1,\n                    showLoading,\n                    this.closeLoadingUI.bind(this)\n                );\n            } else {\n                // 老玩家：进入大厅场景\n                oops.log.logBusiness(\n                    `👤 ${ShareConfig.platform === Platform.FACEBOOK ? 'Facebook' : ShareConfig.platform === Platform.PERSONAL ? '个人' : ''}老玩家：进入大厅场景（Hall）`\n                );\n                success = await smc.sceneMgr.switchToScene(\n                    SceneType.Hall,\n                    undefined,\n                    showLoading,\n                    this.closeLoadingUI.bind(this)\n                );\n            }\n\n            if (success) {\n                oops.log.logBusiness(`🎮 ${isNewPlayer ? '游戏' : '大厅'}场景加载成功`);\n            } else {\n                throw new Error(`${isNewPlayer ? '游戏' : '大厅'}场景加载失败`);\n            }\n        } catch (error) {\n            oops.log.logError('🔥 进入游戏失败:', error);\n\n            // 更新加载界面显示错误\n            this.updateLoadingProgress(0, '进入游戏失败', '请刷新页面重试');\n\n            oops.gui.toast('进入游戏失败，请刷新页面重试');\n\n            // 🎯 失败时也要移除组件，避免内存泄漏\n            this.ent.remove(InitialViewComp);\n        }\n    }\n\n    /** 🚨 错误处理 */\n    private handleInitializationError(error: any): void {\n        oops.log.logError('❌ 游戏初始化失败:', error);\n\n        // 更新加载界面显示错误\n        this.updateLoadingProgress(0, '初始化失败', error.message || '未知错误');\n\n        // 🚨 如果是Bundle加载失败，显示更明确的错误信息\n        if (error.message.includes('bundleOne')) {\n            oops.gui.toast('❌ 游戏资源包缺失，请检查资源配置');\n            // 🎯 严重错误时移除组件，避免内存泄漏\n            this.ent.remove(InitialViewComp);\n            return; // 不重试，直接失败\n        }\n\n        // 其他错误可以重试\n        oops.gui.toast('初始化失败，请检查网络后重试');\n        this.retryInitialization();\n    }\n\n    /** 🔧 初始化重试机制 */\n    private retryInitialization(): void {\n        setTimeout(() => {\n            oops.log.logBusiness('🔄 尝试重新初始化...');\n            this.currentProgress = 0;\n            this.updateLoadingProgress(0, '正在重试...', '重新初始化游戏');\n            this.startFullInitialization();\n        }, 2000);\n    }\n\n    /** 通知HTML加载完成 */\n    private notifyHTMLLoadingComplete(): void {\n        oops.log.logBusiness('🚪 通知HTML加载完成，关闭自定义加载界面');\n\n        try {\n            // 更新进度到100%\n            if (window.updateProgress && typeof window.updateProgress === 'function') {\n                window.updateProgress(100, '加载完成');\n                oops.log.logBusiness('✅ HTML进度更新为100%');\n            } else {\n                oops.log.logBusiness('ℹ️ window.updateProgress 方法不存在（正常情况）');\n            }\n\n            // 隐藏加载界面\n            if (window.hideLoader && typeof window.hideLoader === 'function') {\n                window.hideLoader();\n                oops.log.logBusiness('✅ 自定义加载界面已隐藏');\n            } else {\n                oops.log.logBusiness('ℹ️ window.hideLoader 方法不存在（正常情况）');\n            }\n        } catch (error) {\n            oops.log.logWarn('⚠️ 通知HTML加载完成时出现异常:', error);\n        }\n    }\n\n    /** 关闭加载界面 */\n    private closeLoadingUI(): void {\n        // 🎯 计算启动总时间\n        const endTime = Date.now();\n        const totalTime = endTime - this.startTime;\n\n        // 🎯 自定义加载界面的平台都调用HTML通知\n        if (\n            ShareConfig.platform === Platform.FACEBOOK ||\n            ShareConfig.platform === Platform.PERSONAL\n        ) {\n            this.notifyHTMLLoadingComplete();\n        }\n\n        // Facebook生产环境额外通知\n        if (ShareConfig.platform === Platform.FACEBOOK) {\n            this.notifyFacebookComplete();\n        }\n\n        oops.log.logBusiness('🎉 游戏启动完成！', {\n            totalTime: `${totalTime}ms`,\n            performance: totalTime < 3000 ? '优秀' : totalTime < 5000 ? '良好' : '需要优化',\n        });\n\n        // 移除自己\n        this.ent.remove(InitialViewComp);\n    }\n\n    /** 通知Facebook完成 */\n    private notifyFacebookComplete(): void {\n        if (window.FBInstant && window.FBInstant.setLoadingProgress) {\n            window.FBInstant.setLoadingProgress(100);\n            oops.log.logBusiness('📊 Facebook SDK: 进度已设置为100%');\n        }\n    }\n\n    /** 窗口打开失败回调 */\n    onOpenFailure(): void {\n        oops.log.logError('❌ 窗口打开失败');\n    }\n\n    /** 获取token信息 */\n    private getTokenInfo(): { token: string; expiredTime: number; createdTime: number } | null {\n        try {\n            const tokenInfoStr = oops.storage.get(GameStorageConfig.SSOTokenInfo);\n            if (tokenInfoStr) {\n                return JSON.parse(tokenInfoStr);\n            }\n        } catch (error) {\n            oops.log.logWarn('⚠️ 解析token信息失败:', error);\n        }\n        return null;\n    }\n\n    reset(): void {\n        this.waitComplete = false;\n        this.loadComplete = false;\n        this.currentProgress = 0;\n    }\n}\n"]}