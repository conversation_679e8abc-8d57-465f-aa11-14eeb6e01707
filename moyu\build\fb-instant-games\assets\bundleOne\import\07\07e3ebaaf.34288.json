[1, ["e5LUoqx3RAr41dA5QrbKMj", "546iO8HNJL+LZqvu9egSne@bb814"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 3956340198, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 59184, "length": 5496, "count": 2748, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 59184, "count": 1233, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.5793085098266602, -0.25467556715011597, -0.32978299260139465], "maxPosition", 8, [1, 0.5781332850456238, 0.2911674976348877, 0.32247406244277954]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_04"], [3, "日式寿司_04", [[4, 1, -2, [0, "1caWvsNv9NY66Qzzy/en4y"], [0], [5, true, true], 1], [6, 4, -3, [0, "8d1by5p4NBuqY44dLQOlol"]], [7, 0.6755247712135315, 0, -4, [0, "14MhBBHxxHN4gv+/EN90nG"], [1, 0.01433485746383667, -0.025503411889076233, 5.37186861038208e-05]]], [8, "81ITAbDTRPFoUqHbsd6O1d", null, null, null, -1, 0], [1, 1.372, 0, 1.063]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]