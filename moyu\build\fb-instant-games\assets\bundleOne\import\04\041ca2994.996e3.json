[1, ["d2lw3KtrZG9aD626X+kq2a", "bdc/VAaXlMH57gR5XTjf+v@bdd91"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 2], [3, 0, 1, 2, 2], [5, 0, 1, 2, 3, 4, 5, 2], [6, 0, 1, 3], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 1506809137, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 35472, "length": 5184, "count": 2592, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 35472, "count": 739, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.31822845339775085, -0.22118383646011353, -0.318228542804718], "maxPosition", 8, [1, 0.3182286024093628, 0.22019878029823303, 0.31822851300239563]]], -1], 0, 0, [], [], []], [[[2, "面包_1"], [3, "面包_1", [[4, 4, -2, [0, "c8Q+FG+wdNkI0lLHWcKBG4"]], [5, 1, -3, [0, "bblCgJc+hDnLTBMhD5Hpyy"], [0], [6, true, true], 1], [7, 0.40696075558662415, 0, -4, [0, "1fBI4wDrRBYrZbXRKaue0Z"], [1, -0.0009947866201400757, 0.0501558855175972, -0.011436015367507935]]], [8, "c79BkCFRFLh7Iz16v1hbu2", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]