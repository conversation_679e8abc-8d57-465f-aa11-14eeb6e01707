System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, tween, UIOpacity, Vec3, DEBUG, DEV, oops, JsonUtil, ecs, CCComp, Platform, ShareConfig, SceneType, ClientConst, GameStorageConfig, StorageTestUtils, smc, _dec, _dec2, _class, _crd, ccclass, property, InitialViewComp;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfJsonUtil(extras) {
    _reporterNs.report("JsonUtil", "../../../../../extensions/oops-plugin-framework/assets/core/utils/JsonUtil", _context.meta, extras);
  }

  function _reportPossibleCrUseOfecs(extras) {
    _reporterNs.report("ecs", "../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCCComp(extras) {
    _reporterNs.report("CCComp", "../../../../../extensions/oops-plugin-framework/assets/module/common/CCComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlatform(extras) {
    _reporterNs.report("Platform", "../../../tsrpc/models/ShareConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfShareConfig(extras) {
    _reporterNs.report("ShareConfig", "../../../tsrpc/models/ShareConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSceneType(extras) {
    _reporterNs.report("SceneType", "../../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfClientConst(extras) {
    _reporterNs.report("ClientConst", "../../common/ClientConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameStorageConfig(extras) {
    _reporterNs.report("GameStorageConfig", "../../common/config/GameStorageConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStorageTestUtils(extras) {
    _reporterNs.report("StorageTestUtils", "../../common/config/GameStorageConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "../../common/SingletonModuleComp", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      tween = _cc.tween;
      UIOpacity = _cc.UIOpacity;
      Vec3 = _cc.Vec3;
    }, function (_ccEnv) {
      DEBUG = _ccEnv.DEBUG;
      DEV = _ccEnv.DEV;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      JsonUtil = _unresolved_3.JsonUtil;
    }, function (_unresolved_4) {
      ecs = _unresolved_4.ecs;
    }, function (_unresolved_5) {
      CCComp = _unresolved_5.CCComp;
    }, function (_unresolved_6) {
      Platform = _unresolved_6.Platform;
      ShareConfig = _unresolved_6.ShareConfig;
    }, function (_unresolved_7) {
      SceneType = _unresolved_7.SceneType;
    }, function (_unresolved_8) {
      ClientConst = _unresolved_8.ClientConst;
    }, function (_unresolved_9) {
      GameStorageConfig = _unresolved_9.GameStorageConfig;
      StorageTestUtils = _unresolved_9.StorageTestUtils;
    }, function (_unresolved_10) {
      smc = _unresolved_10.smc;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2839dCGakdHw4LrsrSxiinW", "InitialViewComp", undefined);

      __checkObsolete__(['_decorator', 'tween', 'UIOpacity', 'Vec3']);

      // 扩展window类型
      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 游戏完整初始化组件 - 自定义加载版本
       *
       * 职责：
       * - UI动画播放
       * - Bundle验证和加载
       * - 配置数据加载（配置表 + 语言包）
       * - 用户数据加载
       * - 进入游戏流程
       *
       * 优化：
       * - 所有初始化逻辑集中管理
       * - Bundle加载失败直接报错
       * - 配置表和语言包并行加载
       * - 支持自定义加载界面（Facebook + 个人平台）
       */

      _export("InitialViewComp", InitialViewComp = (_dec = ccclass('InitialViewComp'), _dec2 = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
        error: Error()
      }), ecs) : ecs).register('InitialView', false), _dec(_class = _dec2(_class = class InitialViewComp extends (_crd && CCComp === void 0 ? (_reportPossibleCrUseOfCCComp({
        error: Error()
      }), CCComp) : CCComp) {
        constructor() {
          super(...arguments);
          this.waitComplete = false;
          this.loadComplete = false;
          this.currentProgress = 0;
          this.startTime = 0;
        }

        start() {
          // 🚀 记录启动开始时间
          this.startTime = Date.now();
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🚀 游戏初始化开始', {
            startTime: this.startTime
          }); // 🚀 统一使用自定义加载界面，跳过Cocos内置动画

          if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).FACEBOOK) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🚀 Facebook环境: 自定义加载模式');
          } else if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).PERSONAL) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🚀 个人环境: 自定义加载模式');
            this.updateLoadingProgress(50, '游戏引擎已启动', '个人环境初始化完成');
          } else {
            // 其他环境：使用传统动画
            this.playWaitAnimation();
          }

          this.startFullInitialization();
        }
        /** 🎬 播放等待动画（传统环境） */


        playWaitAnimation() {
          var _this$node$getChildBy;

          var logoOpacity = (_this$node$getChildBy = this.node.getChildByName('logo')) == null ? void 0 : _this$node$getChildBy.getComponent(UIOpacity);

          if (logoOpacity) {
            logoOpacity.opacity = 50;
            tween(logoOpacity.node).to(1, {
              opacity: 255,
              position: new Vec3(0, 0, 0)
            }).call(() => {
              this.waitComplete = true;

              if (this.loadComplete) {
                this.enterGame();
              }
            }).start();
          } else {
            this.waitComplete = true;
          }
        }
        /** 🎯 更新加载进度 */


        updateLoadingProgress(progress, message, detail) {
          this.currentProgress = progress;

          if (window.updateProgress && typeof window.updateProgress === 'function') {
            window.updateProgress(progress, message, detail);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\uD83C\uDFAF \u52A0\u8F7D\u8FDB\u5EA6: " + progress + "% - " + message);
          }
        }
        /** 🚀 完整初始化流程 */


        startFullInitialization() {
          var _this = this;

          return _asyncToGenerator(function* () {
            try {
              // 🧪 测试开关：控制数据清空行为
              if ((_crd && ClientConst === void 0 ? (_reportPossibleCrUseOfClientConst({
                error: Error()
              }), ClientConst) : ClientConst).alwaysNewPlayerTest && (DEV || DEBUG)) {
                (_crd && StorageTestUtils === void 0 ? (_reportPossibleCrUseOfStorageTestUtils({
                  error: Error()
                }), StorageTestUtils) : StorageTestUtils).forceNewPlayerState();
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('🎓 [测试模式] 强制设置为新手状态');
              } // 🚀 优化：并行执行可独立的初始化任务


              yield Promise.all([// 1️⃣ 验证和加载Bundle + 基础UI资源
              _this.loadBundleAndUIResources(), // 2️⃣ 加载配置数据（配置表 + 语言包）
              _this.loadConfigurationData()]); // 3️⃣ 尝试快速用户数据初始化

              yield (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).role.quickInitialize(); // 设置窗口打开失败事件

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).gui.setOpenFailure(_this.onOpenFailure);
              _this.loadComplete = true; // 🚀 自定义加载环境直接进入游戏

              if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
                error: Error()
              }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
                error: Error()
              }), Platform) : Platform).FACEBOOK || (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
                error: Error()
              }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
                error: Error()
              }), Platform) : Platform).PERSONAL) {
                _this.enterGame();
              } else if (_this.waitComplete) {
                _this.enterGame();
              }
            } catch (error) {
              _this.handleInitializationError(error);
            }
          })();
        }
        /** 1️⃣ 验证和加载Bundle + 基础UI资源 */


        loadBundleAndUIResources() {
          return _asyncToGenerator(function* () {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔍 开始验证Bundle和加载基础UI资源...');

            try {
              // 🚀 直接验证bundleOne是否存在，不存在就报错
              try {
                yield (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).res.loadBundle('bundleOne');
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('✅ bundleOne加载成功');
              } catch (error) {
                throw new Error("\u274C \u5173\u952EBundle 'bundleOne' \u52A0\u8F7D\u5931\u8D25: " + error);
              } // 🚀 延迟导入，减少初始bundle大小


              var {
                simpleLoader
              } = yield _context.import("__unresolved_10"); // 🎯 加载基础UI资源

              var tasks = [{
                name: '加载基础UI资源',
                dirs: ['boot'],
                priority: 'high',
                bundle: 'bundleOne'
              }];
              var result = yield simpleLoader.loadTasks(tasks);

              if (!result) {
                throw new Error('基础UI资源加载失败');
              }

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ Bundle和基础UI资源加载完成');
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ Bundle和UI资源加载失败:', error);
              throw error;
            }
          })();
        }
        /** 2️⃣ 加载配置数据资源（配置表 + 语言包） */


        loadConfigurationData() {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔄 开始加载配置数据资源...');

            try {
              // 🚀 优化：配置表和语言包可以并行加载
              var [configResult] = yield Promise.all([(_crd && JsonUtil === void 0 ? (_reportPossibleCrUseOfJsonUtil({
                error: Error()
              }), JsonUtil) : JsonUtil).loadDirAsync(), _this2.loadLanguage()]);

              if (!configResult) {
                throw new Error('配置表加载失败');
              }

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ 配置数据资源加载完成');
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ 配置数据资源加载失败:', error);
              throw error;
            }
          })();
        }
        /** 加载语言包 */


        loadLanguage() {
          return _asyncToGenerator(function* () {
            var language = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).storage.get((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
              error: Error()
            }), GameStorageConfig) : GameStorageConfig).Language, 'en');
            language = 'en';
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).language.setLanguage(language);
          })();
        }
        /** 🎮 进入游戏流程 - 优化版本 */


        enterGame() {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            try {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🎓 开始进入游戏...'); // 🔍 智能判断新手状态

              var isNewPlayer = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).role.isNewPlayer();
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness("\uD83C\uDFAF \u6700\u7EC8\u5224\u5B9A\u65B0\u624B\u72B6\u6001: " + isNewPlayer);
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness("\uD83C\uDFAE \u8FDB\u5165" + (isNewPlayer ? '新手游戏' : '大厅') + "\u573A\u666F");
              var success = false;
              var showLoading = true; // 🎯 使用自定义加载界面的平台不显示Cocos默认加载

              if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
                error: Error()
              }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
                error: Error()
              }), Platform) : Platform).FACEBOOK || (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
                error: Error()
              }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
                error: Error()
              }), Platform) : Platform).PERSONAL) {
                showLoading = false;
              }

              if (isNewPlayer) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness("\uD83C\uDD95 " + ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
                  error: Error()
                }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
                  error: Error()
                }), Platform) : Platform).FACEBOOK ? 'Facebook' : (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
                  error: Error()
                }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
                  error: Error()
                }), Platform) : Platform).PERSONAL ? '个人' : '') + "\u65B0\u624B\u73A9\u5BB6\uFF1A\u8FDB\u5165\u6E38\u620F\u573A\u666F\uFF08Foods\uFF09");
                success = yield (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                  error: Error()
                }), smc) : smc).sceneMgr.switchToScene((_crd && SceneType === void 0 ? (_reportPossibleCrUseOfSceneType({
                  error: Error()
                }), SceneType) : SceneType).Foods, 1, showLoading, _this3.closeLoadingUI.bind(_this3));
              } else {
                // 老玩家：进入大厅场景
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness("\uD83D\uDC64 " + ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
                  error: Error()
                }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
                  error: Error()
                }), Platform) : Platform).FACEBOOK ? 'Facebook' : (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
                  error: Error()
                }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
                  error: Error()
                }), Platform) : Platform).PERSONAL ? '个人' : '') + "\u8001\u73A9\u5BB6\uFF1A\u8FDB\u5165\u5927\u5385\u573A\u666F\uFF08Hall\uFF09");
                success = yield (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                  error: Error()
                }), smc) : smc).sceneMgr.switchToScene((_crd && SceneType === void 0 ? (_reportPossibleCrUseOfSceneType({
                  error: Error()
                }), SceneType) : SceneType).Hall, undefined, showLoading, _this3.closeLoadingUI.bind(_this3));
              }

              if (success) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness("\uD83C\uDFAE " + (isNewPlayer ? '游戏' : '大厅') + "\u573A\u666F\u52A0\u8F7D\u6210\u529F");
              } else {
                throw new Error((isNewPlayer ? '游戏' : '大厅') + "\u573A\u666F\u52A0\u8F7D\u5931\u8D25");
              }
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('🔥 进入游戏失败:', error); // 更新加载界面显示错误

              _this3.updateLoadingProgress(0, '进入游戏失败', '请刷新页面重试');

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).gui.toast('进入游戏失败，请刷新页面重试'); // 🎯 失败时也要移除组件，避免内存泄漏

              _this3.ent.remove(InitialViewComp);
            }
          })();
        }
        /** 🚨 错误处理 */


        handleInitializationError(error) {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logError('❌ 游戏初始化失败:', error); // 更新加载界面显示错误

          this.updateLoadingProgress(0, '初始化失败', error.message || '未知错误'); // 🚨 如果是Bundle加载失败，显示更明确的错误信息

          if (error.message.includes('bundleOne')) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast('❌ 游戏资源包缺失，请检查资源配置'); // 🎯 严重错误时移除组件，避免内存泄漏

            this.ent.remove(InitialViewComp);
            return; // 不重试，直接失败
          } // 其他错误可以重试


          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).gui.toast('初始化失败，请检查网络后重试');
          this.retryInitialization();
        }
        /** 🔧 初始化重试机制 */


        retryInitialization() {
          setTimeout(() => {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔄 尝试重新初始化...');
            this.currentProgress = 0;
            this.updateLoadingProgress(0, '正在重试...', '重新初始化游戏');
            this.startFullInitialization();
          }, 2000);
        }
        /** 通知HTML加载完成 */


        notifyHTMLLoadingComplete() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🚪 通知HTML加载完成，关闭自定义加载界面');

          try {
            // 更新进度到100%
            if (window.updateProgress && typeof window.updateProgress === 'function') {
              window.updateProgress(100, '加载完成');
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ HTML进度更新为100%');
            } else {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('ℹ️ window.updateProgress 方法不存在（正常情况）');
            } // 隐藏加载界面


            if (window.hideLoader && typeof window.hideLoader === 'function') {
              window.hideLoader();
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ 自定义加载界面已隐藏');
            } else {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('ℹ️ window.hideLoader 方法不存在（正常情况）');
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 通知HTML加载完成时出现异常:', error);
          }
        }
        /** 关闭加载界面 */


        closeLoadingUI() {
          // 🎯 计算启动总时间
          var endTime = Date.now();
          var totalTime = endTime - this.startTime; // 🎯 自定义加载界面的平台都调用HTML通知

          if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).FACEBOOK || (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).PERSONAL) {
            this.notifyHTMLLoadingComplete();
          } // Facebook生产环境额外通知


          if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).FACEBOOK) {
            this.notifyFacebookComplete();
          }

          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎉 游戏启动完成！', {
            totalTime: totalTime + "ms",
            performance: totalTime < 3000 ? '优秀' : totalTime < 5000 ? '良好' : '需要优化'
          }); // 移除自己

          this.ent.remove(InitialViewComp);
        }
        /** 通知Facebook完成 */


        notifyFacebookComplete() {
          if (window.FBInstant && window.FBInstant.setLoadingProgress) {
            window.FBInstant.setLoadingProgress(100);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('📊 Facebook SDK: 进度已设置为100%');
          }
        }
        /** 窗口打开失败回调 */


        onOpenFailure() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logError('❌ 窗口打开失败');
        }

        reset() {
          this.waitComplete = false;
          this.loadComplete = false;
          this.currentProgress = 0;
        }

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f15f8a5e1033fcb170fe8d91d64a76565474f284.js.map