[1, ["e5LUoqx3RAr41dA5QrbKMj", "7dNrHmHVdDzpFjcuycjt5w@b900a"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON>", ["node", "__prefab", "_center", "_size"], 3, 1, 4, 5, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 1], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 2553864117, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 40752, "length": 8868, "count": 4434, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 40752, "count": 849, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.3640681207180023, -0.7782949209213257, -0.5163847804069519], "maxPosition", 8, [1, 0.4573097229003906, 0.7498112916946411, 0.4965723752975464]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_25"], [3, "日式寿司_25", [[4, 1, -2, [0, "aapfkLGHRPgJRwgQZsT18u"], [0], [5, true, true], 1], [6, 4, -3, [0, "cc1M6zi+xHq5HdBjqyR9YN"]], [7, -4, [0, "07z80XnrdMDoX0as88Yk7N"], [1, 0.04662080109119415, -0.014241814613342285, -0.009906202554702759], [1, 0.8213778436183929, 1.5281062126159668, 1.0129571557044983]]], [8, "28CBQnTS9HQ7ij5vAz1i4+", null, null, null, -1, 0], [1, 4.454, 0, 6.282]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]