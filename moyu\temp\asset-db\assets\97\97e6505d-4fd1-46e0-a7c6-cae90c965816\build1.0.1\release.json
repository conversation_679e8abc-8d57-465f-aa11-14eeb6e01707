[1, ["786QW1LRpNOasfEX/o0Nvb", "69SPwx+0tBM4A9SQjizyXi@f9941", "15frIdzopJMraF/OwfEzbr@f9941", "cbO01779dCu5bB0LqlKebZ@f9941", "1fRsM+WP1JII5l29FYcER/@f9941", "24sbu4PDlAh7iI7a4/RvOk@f9941", "d3ZxPUoHRPdLjWjyJTpfie@f9941", "158QM9ROBE25JP8HN7uUqg@f9941", "91wHf+VMVG8ZK3289i75GT@f9941"], ["node", "_spriteFrame", "_font", "root", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_parent", "_children", "_lpos"], 1, 9, 4, 1, 2, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_lineHeight", "_outlineWidth", "node", "__prefab", "_color", "_font"], -3, 1, 4, 5, 6], ["cc.Widget", ["_alignFlags", "_left", "_right", "node", "__prefab"], 0, 1, 4], ["47052uw/Y5O1LXaLObj4ARx", ["watchPath", "condition", "valueA", "valueB", "valueComponentName", "valueComponentProperty", "valueComponentDefaultValue", "valueComponentActionValue", "valueAction", "node", "__prefab", "watchNodes", "valueActionColor"], -6, 1, 4, 2, 5], ["cc.Prefab", ["_name"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["c238ewfJ2VJnZ8Gb8YQs5Ts", ["node", "__prefab", "spriteFrames"], 3, 1, 4, 12], ["c238ewfJ2VJnZ8Gb8YQs5Ts", ["node", "__prefab", "spriteFrames"], 3, 1, 4, 3], ["ce662fwsSVPLKpmHx+KocFu", ["watchPath", "componentName", "componentProperty", "node", "__prefab"], 0, 1, 4], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["545c05XsG9GDJispEGWKvYv", ["watchPath", "node", "__prefab"], 2, 1, 4], ["cc.Layout", ["_resizeMode", "_layoutType", "_spacingX", "node", "__prefab"], 0, 1, 4], ["111b1NMzYpCIZ+t9IfKjjyp", ["node", "__prefab"], 3, 1, 4]], [[7, 0, 2], [6, 0, 1, 2, 1], [11, 0, 1, 2, 3, 4, 5, 5], [0, 0, 1, 4, 2, 3, 6, 3], [12, 0, 1, 2, 2], [5, 0, 2], [0, 0, 1, 5, 2, 3, 6, 3], [0, 0, 1, 4, 5, 2, 3, 6, 3], [0, 0, 1, 4, 2, 3, 3], [0, 0, 1, 4, 5, 2, 3, 3], [1, 2, 3, 4, 1], [1, 0, 2, 3, 4, 2], [1, 1, 0, 2, 3, 4, 3], [4, 0, 1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 9], [4, 0, 1, 2, 3, 8, 9, 10, 12, 11, 6], [8, 0, 1, 2, 1], [9, 0, 1, 2, 1], [10, 0, 1, 2, 3, 4, 4], [2, 0, 1, 2, 3, 6, 7, 8, 9, 5], [2, 0, 1, 2, 4, 3, 6, 7, 8, 9, 6], [2, 0, 1, 2, 4, 3, 5, 6, 7, 8, 9, 7], [3, 0, 1, 3, 4, 3], [3, 0, 3, 4, 2], [3, 0, 2, 3, 4, 3], [13, 0, 1, 2, 3, 4, 4], [14, 0, 1, 1]], [[5, "rankCell"], [6, "rankCell", 33554432, [-6, -7, -8], [[1, -2, [0, "46Rpbz0eJJiaodu6kJAN0/"], [5, 560, 90]], [25, -3, [0, "8bVgzTjjdOG584CeHUzv1C"]], [12, 1, 0, -4, [0, "0e1ftGLUVEBo4MbJhC7qKu"], 8], [16, -5, [0, "0fXQA/1L1F66X8ppednHvO"], [9, 10, 11, 12]]], [2, "acbcPqpZpPo5/a/YKQdN4X", null, null, null, -1, 0], [1, 0, -50, 0]], [7, "rank", 33554432, 1, [-15], [[1, -9, [0, "eaKDOGBmBCnrHlcZGtWQsd"], [5, 8.078125, 50.4]], [18, "1", 22, 22, false, -10, [0, "98N2WAoipBtb1tI9J7s52N"], [4, 4282481837], 4], [4, "*.rank", -11, [0, "e7OO25iQJAGJ3Vy+oI3+qG"]], [21, 8, 109.083984375, -12, [0, "b5yMuhMiZMuZe5H05LcK52"]], [14, "*.rank", 6, 1, 3, 3, -14, [0, "b7nFgrWylJD4lPWywsp+WJ"], [4, 4278255360], [-13]]], [2, "d7IYheuW1Ff6lZnNOxd0v/", null, null, null, 1, 0], [1, -166.876953125, 0, 0]], [8, "rankImg", 33554432, 2, [[1, -16, [0, "58yHBznHVM+Z18MQB56Pn7"], [5, 60, 77]], [10, -17, [0, "beAsq8EbVLJa4JDioVv/Mo"], 0], [13, "*.rank", 6, 1, 3, "BhvFrameIndex", "index", "0", "0", -19, [0, "04ZtRzbZhH6aoQ0vE1ryna"], [-18]], [15, -20, [0, "1elRjfWsNHqL74HErGNHq0"], [[null, 1, 2, 3], 0, 6, 6, 6]], [17, "*.rank", "BhvFrameIndex", "index", -21, [0, "5fxsUTcFtLTLn9JD0nSaW1"]]], [2, "d1I1c0FYZPXr9xj85SNL51", null, null, null, 1, 0]], [9, "avatarAndNickName", 33554432, 1, [-25, -26], [[1, -22, [0, "0bcw1j1JhM86q/t8YxtmZk"], [5, 178.4140625, 100]], [24, 1, 1, 5, -23, [0, "e8u1c5iDdJwrr5XCV3XaoC"]], [22, 16, -24, [0, "37OwbIw2BA87fKQsSM9atc"]]], [2, "b0dCpA/8dIxadvsjoyTdtf", null, null, null, 1, 0]], [3, "score", 33554432, 1, [[1, -27, [0, "5bHeFKVhpCHbVB60KrdO2A"], [5, 33.171875, 32.76]], [20, "100", 22, 22, 26, false, 3, -28, [0, "bfNT4ptdhADbRGZudJqOdV"], [4, 4282481837], 7], [4, "*.score", -29, [0, "52GFTDpKFPC4TDIagFyG10"]], [23, 32, 119.783203125, -30, [0, "a0UAZzJwZAP4UMT7tbZ5zV"]]], [2, "dfLDjmeKhMMbRWzkzLqu6D", null, null, null, 1, 0], [1, 143.630859375, 0, 0]], [3, "name", 33554432, 4, [[1, -31, [0, "8392bay5RDpJXdv0fqcFwg"], [5, 93.4140625, 30.240000000000002]], [19, "nickname", 22, 22, 24, false, -32, [0, "f1gOQhKPxN35iBPb+8ql6z"], [4, 4282481837], 6], [4, "*.name", -33, [0, "efhleffJZNsqKv5WJ9vy+s"]]], [2, "63SA9gnnJPYbcMivAs+AG0", null, null, null, 1, 0], [1, 42.5, 0, 0]], [3, "avatar", 33554432, 4, [[1, -34, [0, "10XLYaeSNH+YOxqFPzkOOb"], [5, 80, 80]], [11, 0, -35, [0, "6dRJvQV1pDiKD1xwCPNn34"], 5]], [2, "6aQkN+zpxOE7l6w2OouCTP", null, null, null, 1, 0], [1, -49.20703125, 0, 0]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 4, 0, -3, 5, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 2, 0, 0, 2, 0, -1, 3, 0, 0, 3, 0, 0, 3, 0, -1, 3, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 7, 0, -2, 6, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 4, 1, 35], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, -2, -3, -4, 2, 1, 2, 2, 1, -1, -2, -3, -4], [1, 1, 3, 4, 0, 5, 0, 0, 2, 2, 6, 7, 8]]