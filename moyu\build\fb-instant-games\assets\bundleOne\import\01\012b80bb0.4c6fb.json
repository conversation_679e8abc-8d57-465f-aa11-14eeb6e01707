[1, ["e5LUoqx3RAr41dA5QrbKMj", "08d4tDKrRB8pX0+X/Uu/r6@23f84", "35eYFLYKJH3JTGo2BUOdEA@6c48a", "9cBDnrQ5dNy6rTc4X3R0SP@3c318", "9e7IsRW0FKbqwos4vgKCxF@6c48a", "73o1D76klKn4V7RpxCKr1A@6c48a", "d9KsEeQAhCfZN3Tv/6mJ2C@6c48a", "b82x/hmQdHiJhvtSQYHgnU"], ["node", "root", "data", "_mesh", "albedoMap", "normalMap", "pbrMap", "emissiveMap", "dissolveNoise", "_effectAsset"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Material", ["_name", "_states", "_defines", "_props"], 0, 12]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5], [9, 0, 1, 2, 3, 4]], [[[[1, ".bin", 3398531355, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 68640, "length": 12792, "count": 6396, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 68640, "count": 1430, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.9058430790901184, -0.6880127191543579, -0.3586817979812622], "maxPosition", 8, [1, 0.8905422687530518, 0.8612164258956909, 0.39447975158691406]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_21"], [3, "日式寿司_21", [[4, 1, -2, [0, "64bU0F+wVIW7dAl8bnUDrY"], [0], [5, true, true], 1], [6, 4, -3, [0, "62dmrvzKZODoLRNnce/QxR"]], [7, 0.33673518896102905, 1.2295190691947937, -4, [0, "6ahxRiPpVCc7VVQuE3Agkr"], [1, -0.03840597718954086, 0.09641000628471375, -0.08327403664588928]]], [8, "59Enw2VMlNmqez4HiyOPKF", null, null, null, -1, 0], [1, 0.824, 0, 7.447]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]], [[[9, "日式小食", [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_NORMAL_MAP": true, "USE_INSTANCING": true, "USE_ALBEDO_MAP": true, "USE_PBR_MAP": true, "USE_ALPHA_TEST": true}, {}, {}, {}, {}, {}], [[[{"alphaThreshold": 0.841, "dissolveProgress": 1}, "albedoMap", 6, 0, "normalMap", 6, 1, "pbrMap", 6, 2, "emissiveMap", 6, 3, "dissolveNoise", 6, 4], {}, {}, {}, {}, {}], 11, 0, 0, 0, 0, 0]]], 0, 0, [0, 0, 0, 0, 0, 0], [4, 5, 6, 7, 8, 9], [2, 3, 4, 5, 6, 7]]]]