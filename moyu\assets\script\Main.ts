/*
 * @Author: dgflash
 * @Date: 2021-07-03 16:13:17
 * @LastEditors: dgflash
 * @LastEditTime: 2022-08-05 18:25:56
 */
import { _decorator, Node } from 'cc';
import { oops } from '../../extensions/oops-plugin-framework/assets/core/Oops';
import { Root } from '../../extensions/oops-plugin-framework/assets/core/Root';
import { ecs } from '../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';
import { FBInstantManager } from './facebook-instant-games/FBInstantManager';
import { FacebookGameEvents } from './facebook-instant-games/FacebookGameEvents';
import { CameraEntity } from './game/Camera/CameraEntity';
import { Account } from './game/account/Account';
import { SimpleSceneManager } from './game/common/scene/SimpleSceneManager';
import { Guide } from './game/guide/Guide';
import { Initialize } from './game/initialize/Initialize';
import { Role } from './game/role/Role';

import { Environment, Platform, ShareConfig } from './tsrpc/models/ShareConfig';

const { ccclass, property } = _decorator;

@ccclass('Main')
export class Main extends Root {
    @property({
        type: Node,
        tooltip: '游戏初始画面',
    })
    initial: Node = null!;

    protected iniStart() {
        // 🔧 开始游戏完整初始化计时
        oops.log.start('游戏完整初始化');

        // profiler.hideStats();

        // 使用LogControl自动设置日志级别
        const isProduction = ShareConfig.isProduction;

        if (ShareConfig.environment === Environment.PRODUCTION_FACEBOOK) {
            // Facebook生产环境：超精简模式
        } else if (isProduction) {
            // 其他生产环境：精简模式
        } else {
            // 开发环境：完整日志
        }

        // 🚀 优化：延迟初始化单例模块，减少启动时间
        this.initializeCoreModules();
    }

    /** 🔧 优化：分阶段初始化核心模块 */
    private async initializeCoreModules() {
        const { smc } = await import('./game/common/SingletonModuleComp');

        // 只初始化最基础的模块
        smc.initialize = ecs.getEntity<Initialize>(Initialize);

        // 🚀 Facebook模块懒加载
        if (ShareConfig.platform === Platform.FACEBOOK) {
            const [{ FBInstantManager }, { FacebookGameEvents }] = await Promise.all([
                import('./facebook-instant-games/FBInstantManager'),
                import('./facebook-instant-games/FacebookGameEvents'),
            ]);

            smc.fbInstantManager = ecs.getEntity<FBInstantManager>(FBInstantManager);
            smc.facebookGameEvents = ecs.getEntity<FacebookGameEvents>(FacebookGameEvents);
        }

        // 🚀 其他业务模块延迟加载
        this.loadBusinessModulesLater();
    }

    /** 🔧 业务模块延迟加载 - 优化版本 */
    private async loadBusinessModulesLater() {
        // 🚀 优化：立即开始加载关键模块，不等待空闲时间
        this.loadCriticalModules();

        // 在空闲时间加载非关键模块
        requestIdleCallback(async () => {
            await this.loadNonCriticalModules();
        });
    }

    /** 🚀 立即加载关键模块 */
    private async loadCriticalModules() {
        try {
            const [{ smc }, { Role }, { SimpleSceneManager }] = await Promise.all([
                import('./game/common/SingletonModuleComp'),
                import('./game/role/Role'),
                import('./game/common/scene/SimpleSceneManager'),
            ]);

            // 🚀 优先初始化关键模块
            smc.role = ecs.getEntity<Role>(Role);
            smc.sceneMgr = ecs.getEntity<SimpleSceneManager>(SimpleSceneManager);

            oops.log.logBusiness('✅ 关键模块加载完成');
        } catch (error) {
            oops.log.logError('❌ 关键模块加载失败:', error);
        }
    }

    /** 🔧 加载非关键模块 */
    private async loadNonCriticalModules() {
        try {
            const [{ smc }, { Account }, { Guide }, { CameraEntity }] = await Promise.all([
                import('./game/common/SingletonModuleComp'),
                import('./game/account/Account'),
                import('./game/guide/Guide'),
                import('./game/Camera/CameraEntity'),
            ]);

            // 🚀 性能监控已移除 - 使用Cocos内置profiler即可
            // 开发环境启用内置性能面板
            if (!ShareConfig.isProduction) {
                const { profiler } = await import('cc');
                profiler.showStats();
            }

            // 🚀 直接设置资源下载并发数（替代ResourceOptimizer）
            const { assetManager } = await import('cc');
            assetManager.downloader.maxRequestsPerFrame = 20; // 🚀 增加并发数

            // 初始化非关键业务模块
            smc.account = ecs.getEntity<Account>(Account);
            smc.guide = ecs.getEntity<Guide>(Guide);
            smc.camera = ecs.getEntity<CameraEntity>(CameraEntity);

            oops.log.logBusiness('✅ 非关键模块加载完成');
        } catch (error) {
            oops.log.logError('❌ 非关键模块加载失败:', error);
        }
    }

    protected initGui() {
        // 🚀 延迟加载UI配置
        requestIdleCallback(async () => {
            const { UIConfigData } = await import('./game/common/config/GameUIConfig');
            oops.gui.init(UIConfigData);
        });
    }

    protected initEcsSystem() {
        // ECS系统现在在 loadBusinessModulesLater 中初始化
    }

    protected async run() {
        try {
            // Facebook平台初始化
            if (ShareConfig.platform === Platform.FACEBOOK) {
                await this.initializeFacebook();
            }

            // 开始游戏初始化
            const { smc } = await import('./game/common/SingletonModuleComp');
            smc.initialize.load(this.initial);
        } catch (error) {
            oops.log.logError('游戏初始化失败:', error);

            // 生产环境显示错误
            if (ShareConfig.environment === Environment.PRODUCTION_FACEBOOK) {
                oops.gui.toast('Facebook连接失败，请刷新页面重试');
                return;
            }
            oops.gui.toast('游戏启动失败，请刷新页面重试');
        }
    }

    /** 🔧 Facebook初始化优化 */
    private async initializeFacebook() {
        const { smc } = await import('./game/common/SingletonModuleComp');
        const maxRetries = 3;
        let initSuccess = false;

        for (let i = 0; i < maxRetries; i++) {
            try {
                oops.log.logBusiness(`🚀 Facebook初始化尝试 ${i + 1}/${maxRetries}...`);

                await smc.fbInstantManager.fbInit();
                await smc.facebookGameEvents.onPlayerFirstEntry();

                // 验证Facebook数据
                const fbData = smc.fbInstantManager.getFacebookLoginData();
                if (!fbData || !fbData.facebookId) {
                    throw new Error('Facebook数据不完整或玩家ID为空');
                }

                oops.log.logBusiness('✅ Facebook初始化成功，玩家ID:', fbData.facebookId);
                initSuccess = true;
                break;
            } catch (error) {
                oops.log.logWarn(`⚠️ Facebook初始化失败 (尝试 ${i + 1}/${maxRetries}):`, error);

                if (i < maxRetries - 1) {
                    const retryDelay = (i + 1) * 1000;
                    oops.log.logBusiness(`⏳ 等待 ${retryDelay}ms 后重试...`);
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }
        }

        if (!initSuccess) {
            const errorMsg = 'Facebook多次初始化失败';
            oops.log.logError(errorMsg);

            if (ShareConfig.environment === Environment.PRODUCTION_FACEBOOK) {
                oops.gui.toast('Facebook连接失败，请刷新页面重试');
                return;
            } else {
                oops.log.logWarn('⚠️ 非生产环境：继续游戏启动（降级模式）');
            }
        }
    }
}
