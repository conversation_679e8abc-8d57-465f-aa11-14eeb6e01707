[1, ["80pt/43AZPnaLIAxcNqh3d", "a39e7p0V9IPrMEvpRcD3NG@f9941", "4d7xg0v01BfpronBy2TW0c@f9941", "d0FmJQ79tA4IthGAk9i2K6@f9941", "beeU+ZykZPhqWlafd6Ip4F", "208m2EU+lBRpwKCxPxLV1h@f9941", "14I5OoJkFHdaecy/4b2eXK", "c69rRJRZtJuafF2eOwAOAg@f9941", "cbDOEgQgxELrC4wVWT4EVH@f9941", "39QufBHjlAR4MXPR749vfZ@f9941", "26qSDlZtJO+JKe1m5B/Eag@f9941", "87KJdfUjNA1pqHCGJqumKH", "6bAK8rf8pPE54WiDqq1t7L@f9941", "b4Kl7ucq1AyZ84IiEUyK6K@f9941", "12CGLetYxEn5JY6OsQY9mT@f9941"], ["node", "_spriteFrame", "targetInfo", "root", "asset", "_target", "_parent", "_normalSprite", "data"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_active", "_prefab", "_components", "_parent", "_children", "_lpos"], -2, 4, 9, 1, 2, 5], ["cc.Widget", ["_alignFlags", "_right", "_left", "_top", "_originalWidth", "_originalHeight", "_bottom", "_verticalCenter", "node", "__prefab"], -5, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_target", "_normalSprite"], 2, 1, 4, 1, 6], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedComponents"], 2, 1, 9, 9], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 9], ["cc.Layout", ["_resizeMode", "_layoutType", "_startAxis", "_paddingLeft", "_paddingRight", "_paddingTop", "_paddingBottom", "_spacingY", "_constraint", "_constraintNum", "node", "__prefab"], -7, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["00237YOE/BG059OpYnfe1uX", ["node", "__prefab"], 3, 1, 4]], [[15, 0, 2], [13, 0, 2], [14, 0, 1, 2, 1], [7, 0, 1, 2, 3, 4, 5, 5], [12, 0, 1, 2, 3], [10, 0, 1, 2, 2], [11, 0, 1, 2, 2], [3, 2, 3, 4, 1], [0, 2, 3, 7, 5, 3], [0, 0, 1, 7, 8, 6, 5, 9, 3], [6, 0, 1, 2, 3, 4, 5, 4], [9, 0, 1, 2, 3], [3, 0, 1, 2, 3, 4, 3], [0, 0, 1, 7, 6, 5, 9, 3], [4, 0, 1, 2, 2], [1, 0, 8, 9, 2], [0, 0, 1, 7, 6, 5, 3], [4, 0, 1, 3, 2, 2], [1, 0, 2, 8, 9, 3], [1, 0, 1, 8, 9, 3], [16, 0, 1, 1], [0, 0, 1, 8, 6, 5, 9, 3], [0, 0, 4, 1, 7, 8, 6, 5, 9, 4], [2, 0, 1, 2, 2], [5, 0, 2], [0, 0, 1, 8, 6, 5, 3], [8, 0, 1, 2, 3, 4, 5, 4], [3, 0, 2, 3, 4, 2], [1, 0, 1, 3, 8, 9, 4], [1, 0, 2, 1, 4, 8, 9, 5], [1, 0, 3, 6, 7, 5, 8, 9, 6], [1, 0, 4, 5, 8, 9, 4], [2, 1, 2, 3, 1], [2, 0, 1, 2, 3, 2], [2, 0, 1, 2, 3, 4, 2], [17, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 11], [18, 0, 1, 1], [19, 0, 1, 1]], [[24, "setting"], [25, "setting", ********, [-13, -14], [[2, -9, [0, "c1hVvKAKJAsY8YKb40A5oY"], [5, 750, 1334]], [36, -10, [0, "48O7mvCMxPQ4BoitAgcnAc"]], [37, -11, [0, "2ee6WNkwxPJ6gKlQqDMpa1"]], [31, 45, 1080, 1920, -12, [0, "c9y1hDPe5P24tzvKVZtU1V"]]], [26, "22ahoiOOVHK7EfzrB8wn/z", null, null, -8, 0, [-1, -2, -3, -4, -5, -6, -7]]], [21, "content", ********, [-19, -20, -21, -22, -23], [[2, -15, [0, "83tQuHYDRBJZzxnNgkMSLu"], [5, 450, 440]], [35, 1, 2, 1, 30, 30, 100, 30, 25, 2, 1, -16, [0, "9cBURkj1NCYLhkBHxOaebF"]], [12, 1, 0, -17, [0, "b7TWmCW09L/6FTSljPy5Ch"], 20], [15, 1, -18, [0, "80GeuzJsNOCqdsZRlAxYQZ"]]], [3, "d7XphpDLtKq6lRFVYM1inb", null, null, null, 1, 0], [1, 0, -170, 0]], [9, "soundCell", ********, 2, [-27, -28, -29], [[2, -24, [0, "cf+SD+yg9AdZTDX+iScfMa"], [5, 390, 96]], [12, 1, 0, -25, [0, "11gydzGcBMB5banQTT/6AJ"], 5], [23, 3, -26, [0, "13PdfjIcJI6q4KDaezGaOf"]]], [3, "d0bSPo/MdOwZpiQuTP/tAb", null, null, null, 1, 0], [1, 0, 72, 0]], [9, "musicCell", ********, 2, [-33, -34, -35], [[2, -30, [0, "72BqizgpdGU5uwy7oEf1Z7"], [5, 390, 96]], [12, 1, 0, -31, [0, "e9yzZ7W4pP2aA3BvSnWdH5"], 10], [23, 3, -32, [0, "ecJ+Zd+u5FfrSQFSFdy1KR"]]], [3, "85iZp+2ChIjrU1FZAnun8K", null, null, null, 1, 0], [1, 0, -49, 0]], [22, "vibrateCell", false, ********, 2, [-38, -39, -40], [[2, -36, [0, "00uQL14c1MTbvWxSxlnpY+"], [5, 390, 96]], [12, 1, 0, -37, [0, "47mp+WIE5MMplus8A/PjcG"], 15]], [3, "8etWPAP01MDYP2ztdrvula", null, null, null, 1, 0], [1, 0, -109.5, 0]], [9, "giveUpBtn", ********, 2, [-45], [[2, -41, [0, "8c5YHVX2lOlJHa0FOC70cM"], [5, 158, 68]], [7, -42, [0, "97jJ4D331EL6otfLy2NLWm"], 17], [32, -44, [0, "058AnK31JBw4OCPD576aRk"], -43]], [3, "fft3IV/VtAqL2T+G3JvKcp", null, null, null, 1, 0], [1, 3.****************, -156, 0]], [22, "switchAccounts", false, ********, 2, [-50], [[2, -46, [0, "ceJ0ABU1dGS7NG6lUefWzi"], [5, 158, 68]], [7, -47, [0, "cfRsvstZtCqYzQPQV7FHa4"], 19], [33, 3, -49, [0, "f9SoAwsFZLgoKO29Cycya/"], -48]], [3, "b02qJSYOpKD7tl627+5OMj", null, null, null, 1, 0], [1, 3.****************, -263, 0]], [21, "title", ********, [-54, -55], [[2, -51, [0, "d3vqG+P8ZBZJgOR3jR563o"], [5, 450, 73]], [12, 1, 0, -52, [0, "8ahGh33ilD37Dyij/F8bZ5"], 24], [29, 17, 318.5, 315.5, 116, -53, [0, "aed2MGKixBELk0E11tkhTi"]]], [3, "cfRQ8WNUpGJKz/AG3J2FJ+", null, null, null, 1, 0], [1, 0, 13.5, 0]], [13, "closeBtn", ********, 8, [[2, -56, [0, "c8o+0e8ZBBB5tKZRRjepwm"], [5, 68, 69]], [27, 1, -57, [0, "7ej9l2JnZKEIDfWjwsCWy/"], 22], [34, 3, -59, [0, "e3+yP0BjZDC46nMpyM4yJy"], -58, 23], [28, 33, -10.***************, -12.***************, -60, [0, "749N42OW1MqKbnhc6ESuBy"]]], [3, "2ddhaPEdNDaYuysgtvhIDZ", null, null, null, 1, 0], [1, 201.663, 14.***************, 0]], [9, "Node", ********, 1, [2, 8], [[2, -61, [0, "6c6B9m0E1PErzxhPnWEKGj"], [5, 450, 100]], [30, 18, 788.5405000000001, 445.4595, 273.919, 100, -62, [0, "01Ko6y26tKQrBV+zKgAext"]]], [3, "1cweqzvJFKxLQ5MJ7c8/HQ", null, null, null, 1, 0], [1, 0, 273.919, 0]], [9, "select", ********, 3, [-66], [[2, -63, [0, "bfq1zg5o9NMpASV9pzG2uO"], [5, 48, 48]], [7, -64, [0, "16A6zTuJdFfIZ9KhQDaP/l"], 4], [19, 34, 20, -65, [0, "08xz4bATtPaYN/thpHVi3k"]]], [3, "34s9izWxRNQKM0iTygbxYm", null, null, null, 1, 0], [1, 151, 0, 0]], [9, "select", ********, 4, [-70], [[2, -67, [0, "5dtZ7OVYpFPbu3bil85VAX"], [5, 48, 48]], [7, -68, [0, "04qZ5FxuBPU7l9xCAlmUTf"], 9], [19, 34, 20, -69, [0, "c8AvQHFD9O0ZtgeYcULI3+"]]], [3, "75am1mXVhEWq9CIm47vazz", null, null, null, 1, 0], [1, 151, 0, 0]], [9, "select", ********, 5, [-74], [[2, -71, [0, "8boPU0jQJN77oCaW9Edesg"], [5, 48, 48]], [7, -72, [0, "80jMHH21JOAbUctQJUtf9J"], 14], [19, 34, 20, -73, [0, "fa29ZHK19CMrYCE/lmSRQq"]]], [3, "04x/J5Y6lGJKssTihPL+9A", null, null, null, 1, 0], [1, 151, 0, 0]], [1, ["a0daVw8DRLi6ToMaTA0VS2"]], [13, "音乐", ********, 3, [[2, -75, [0, "a7dHmvUhBDOafXzfZDasCh"], [5, 57, 58]], [7, -76, [0, "5eQn2fs9xGQ7fo/A4K9bkM"], 1], [18, 10, 23, -77, [0, "5ecUJ78pNGmanUgb1BAXSi"]]], [3, "0fCNmO3BZEu5VBSff/V47s", null, null, null, 1, 0], [1, -143.5, 0, 0]], [8, 0, {}, 3, [10, "f05XX5jrpEOYwv6lCoUIav", null, null, -83, [17, "46NCTWUwxFy78kSFoVzQaS", 1, [[20, [1, ["f05XX5jrpEOYwv6lCoUIav"]], [[15, 18, -82, [0, "d79W8rrKJF14ewOpHzK+Ab"]]]]], [[11, "nameLbl", ["_name"], -78], [5, ["_lpos"], -79, [1, 0, 0, 0]], [5, ["_lrot"], -80, [3, 0, 0, 0, 1]], [5, ["_euler"], -81, [1, 0, 0, 0]], [6, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 77.546875, 37.8]], [4, "Sound", ["_string"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [4, "Sound", ["_dataID"], [1, ["48BGi+JnJOKpaEdqvfVVS1"]]]]], 2]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [13, "音效", ********, 4, [[2, -84, [0, "46FOeSQ/hGn4FA/04EPwS+"], [5, 63, 50]], [7, -85, [0, "6f+N4ybTBNgb6e35xhbpqp"], 6], [18, 10, 20, -86, [0, "1enkT3mCNJuaZyJTmX0WNO"]]], [3, "36wEU1yJdHUrvTVc40ooPF", null, null, null, 1, 0], [1, -143.5, 0, 0]], [8, 0, {}, 4, [10, "f05XX5jrpEOYwv6lCoUIav", null, null, -92, [17, "ceYKey+7lBDaVfTD+f1rr5", 1, [[20, [1, ["f05XX5jrpEOYwv6lCoUIav"]], [[15, 18, -91, [0, "d79W8rrKJF14ewOpHzK+Ab"]]]]], [[11, "nameLbl", ["_name"], -87], [5, ["_lpos"], -88, [1, 0, 0, 0]], [5, ["_lrot"], -89, [3, 0, 0, 0, 1]], [5, ["_euler"], -90, [1, 0, 0, 0]], [6, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 154.875, 37.8]], [4, "SoundEffects", ["_string"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [4, "SoundEffects", ["_dataID"], [1, ["48BGi+JnJOKpaEdqvfVVS1"]]]]], 7]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [13, "震动", ********, 5, [[2, -93, [0, "d7OKE+ZEFOb4Eob9VsSfyM"], [5, 63, 55]], [7, -94, [0, "94TFP8y3pByJPQzo6Ukdqi"], 11], [18, 10, 20, -95, [0, "d9ilE57YZCpJ8LruU1CP+G"]]], [3, "a8rDvNpZlIqJj2bK4PjD8u", null, null, null, 1, 0], [1, -143.5, 0, 0]], [8, 0, {}, 5, [10, "f05XX5jrpEOYwv6lCoUIav", null, null, -101, [17, "58wR+z8V5PcrgoJcvzYjw1", 1, [[20, [1, ["f05XX5jrpEOYwv6lCoUIav"]], [[15, 18, -100, [0, "d79W8rrKJF14ewOpHzK+Ab"]]]]], [[11, "nameLbl", ["_name"], -96], [5, ["_lpos"], -97, [1, 0, 0, 0]], [5, ["_lrot"], -98, [3, 0, 0, 0, 1]], [5, ["_euler"], -99, [1, 0, 0, 0]], [6, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 109.265625, 37.8]], [4, "Vibration", ["_string"], [1, ["4a5atXBglJxJGAlAL90RE0"]]]]], 12]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [8, 0, {}, 1, [10, "a0daVw8DRLi6ToMaTA0VS2", null, null, -102, [14, "6cH5XhLHlJb5HbIIaNX3/i", 1, [[11, "mask", ["_name"], 14], [5, ["_lpos"], 14, [1, 0, 0, 0]], [5, ["_lrot"], 14, [3, 0, 0, 0, 1]], [5, ["_euler"], 14, [1, 0, 0, 0]], [6, ["_contentSize"], [1, ["77N2cid5pKDpXplRH/AWEU"]], [5, 1080, 1920]]]], 0]], [16, "ok1", ********, 11, [[2, -103, [0, "b2qfN5OOJCUIK57ie1AlDv"], [5, 49, 38]], [7, -104, [0, "3dOeWFN4NP+IzbcqpIJen5"], 3]], [3, "b3x5cdFSFA14qDYsSFn99o", null, null, null, 1, 0]], [16, "ok2", ********, 12, [[2, -105, [0, "81ESiNgTtCoa/hgdTBPM9t"], [5, 49, 38]], [7, -106, [0, "22NlMQwIpIE40AfJcdmHmu"], 8]], [3, "feyTHza4pFfZDrvVSOXn2W", null, null, null, 1, 0]], [16, "ok3", ********, 13, [[2, -107, [0, "898FzO6XtOv40Rv+TA1rQj"], [5, 49, 38]], [7, -108, [0, "beEvJsdWFNQo5MzNhOfLAu"], 13]], [3, "faN+jg5WFBIZynOX/ORl93", null, null, null, 1, 0]], [8, 0, {}, 6, [10, "f05XX5jrpEOYwv6lCoUIav", null, null, -109, [14, "d7g2mp7wpLwbBEWVNsfCFj", 1, [[4, "VMLabelLanguage_Red", ["_name"], [1, ["f05XX5jrpEOYwv6lCoUIav"]]], [6, ["_lpos"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 7.****************, 0]], [6, ["_lrot"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [3, 0, 0, 0, 1]], [6, ["_euler"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 0, 0]], [4, 28, ["_fontSize"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [6, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 97.15234375, 50.4]], [4, 28, ["_actualFontSize"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [4, "Give Up", ["_string"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [4, false, ["_enableOutline"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [4, "Surrender", ["_dataID"], [1, ["800tdHtlJM2IVdpueUwzRq"]]]]], 16]], [8, 0, {}, 7, [10, "f05XX5jrpEOYwv6lCoUIav", null, null, -110, [14, "81D8nLPg9AKYvuWkOjx/S9", 1, [[4, "VMLabelLanguage_Red", ["_name"], [1, ["f05XX5jrpEOYwv6lCoUIav"]]], [6, ["_lpos"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 7.****************, 0]], [6, ["_lrot"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [3, 0, 0, 0, 1]], [6, ["_euler"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 0, 0]], [4, 28, ["_fontSize"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [6, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 202.015625, 50.4]], [4, 28, ["_actualFontSize"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [4, "Give Up", ["_string"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [4, false, ["_enableOutline"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [4, "Switch_accounts", ["_dataID"], [1, ["800tdHtlJM2IVdpueUwzRq"]]]]], 18]], [8, 0, {}, 8, [10, "f05XX5jrpEOYwv6lCoUIav", null, null, -113, [14, "ebgik2GuFIgqaLPAUIt14M", 1, [[11, "title", ["_name"], 24], [5, ["_lpos"], 24, [1, 0, 0, 0]], [5, ["_lrot"], 24, [3, 0, 0, 0, 1]], [5, ["_euler"], 24, [1, 0, 0, 0]], [11, true, ["_enableOutline"], -111], [6, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 141.03125, 54.4]], [11, "Settings", ["_string"], -112], [4, "Settings", ["_dataID"], [1, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]]]], 21]], [1, ["4a5atXBglJxJGAlAL90RE0"]]], 0, [0, -1, 31, 0, -2, 30, 0, -3, 29, 0, -4, 22, 0, -5, 19, 0, -6, 16, 0, -7, 25, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 25, 0, -2, 10, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, -5, 7, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 15, 0, -2, 16, 0, -3, 11, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 18, 0, -2, 19, 0, -3, 12, 0, 0, 5, 0, 0, 5, 0, -1, 21, 0, -2, 22, 0, -3, 13, 0, 0, 6, 0, 0, 6, 0, 5, 6, 0, 0, 6, 0, -1, 29, 0, 0, 7, 0, 0, 7, 0, 5, 7, 0, 0, 7, 0, -1, 30, 0, 0, 8, 0, 0, 8, 0, 0, 8, 0, -1, 31, 0, -2, 9, 0, 0, 9, 0, 0, 9, 0, 5, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, -1, 26, 0, 0, 12, 0, 0, 12, 0, 0, 12, 0, -1, 27, 0, 0, 13, 0, 0, 13, 0, 0, 13, 0, -1, 28, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 2, 17, 0, 2, 17, 0, 2, 17, 0, 2, 17, 0, 0, 16, 0, 3, 16, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 2, 20, 0, 2, 20, 0, 2, 20, 0, 2, 20, 0, 0, 19, 0, 3, 19, 0, 0, 21, 0, 0, 21, 0, 0, 21, 0, 2, 23, 0, 2, 23, 0, 2, 23, 0, 2, 23, 0, 0, 22, 0, 3, 22, 0, 3, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 3, 29, 0, 3, 30, 0, 2, 32, 0, 2, 32, 0, 3, 31, 0, 8, 1, 2, 6, 10, 8, 6, 10, 113], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 1, 4, 1, 1, 1, 1, 4, 1, 1, 1, 1, 4, 1, 1, 1, 4, 1, 4, 1, 1, 4, 1, 7, 1], [6, 7, 0, 1, 2, 3, 8, 0, 1, 2, 3, 9, 0, 1, 2, 3, 4, 5, 4, 5, 10, 11, 12, 13, 14]]