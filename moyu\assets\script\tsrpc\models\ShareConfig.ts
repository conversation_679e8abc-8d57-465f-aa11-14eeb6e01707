// ShareConfig.ts - master-facebook分支配置 (Facebook生产环境)
// 根据分支自动确定环境配置，简化部署流程

export enum Environment {
    DEVELOPMENT = 'development',
    FACEBOOK_MOCK = 'facebook_mock',
    PRODUCTION_PERSONAL = 'production_personal',
    PRODUCTION_FACEBOOK = 'production_facebook',
}

export enum Platform {
    PERSONAL = 'personal',
    FACEBOOK = 'facebook',
}

export interface ServerConfig {
    environment: Environment;
    platform: Platform;
    serverUrl: string;
    clientUrl: string;
    mongoUrl: string;
    mongoDbName: string;
    port: number;
    gamePort: number;
    gameServerUrl: string;
    isProduction: boolean;
    enableCors: boolean;
    corsOrigins: string[];
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    enableFacebookSDK: boolean;
    facebookAppId?: string;
    enableAnalytics: boolean;
    maxPlayersPerRoom: number;
    gameSettings: {
        enableRanking: boolean;
        enableRewards: boolean;
        enableSocialFeatures: boolean;
    };

    // 兼容旧客户端代码的属性 - 纯HTTP架构
    https: boolean;
    gate: string;
    httpPort: number;
    json: boolean;
    security: boolean;
}

// 根据当前分支确定环境
function getCurrentEnvironment(): Environment {
    // master-facebook 分支固定返回 PRODUCTION_FACEBOOK 环境
    return Environment.PRODUCTION_FACEBOOK;
}

// 🔧 master-facebook分支专用：Facebook生产环境配置
const PRODUCTION_FACEBOOK_CONFIG: ServerConfig = {
    environment: Environment.PRODUCTION_FACEBOOK,
    platform: Platform.FACEBOOK,
    serverUrl: 'https://idlefun.press/api/facebook',
    clientUrl: 'https://idlefun.press/facebook',
    mongoUrl: 'mongodb://mongodb-facebook:27017',
    mongoDbName: 'moyu_facebook',
    port: 3002,
    gamePort: 3003,
    gameServerUrl: 'https://idlefun.press/api/facebook/game',
    isProduction: true,
    enableCors: true,
    corsOrigins: [
        'https://idlefun.press',
        'https://www.facebook.com',
        'https://developers.facebook.com',
    ],
    logLevel: 'warn',
    enableFacebookSDK: true,
    enableAnalytics: true,
    maxPlayersPerRoom: 8,
    gameSettings: {
        enableRanking: true,
        enableRewards: true,
        enableSocialFeatures: true,
    },
    // 兼容属性
    https: true,
    gate: 'idlefun.press:3002',
    httpPort: 3002,
    json: true,
    security: true,
};

// 环境配置映射
const CONFIG_MAP: Record<Environment, ServerConfig> = {
    [Environment.DEVELOPMENT]: PRODUCTION_FACEBOOK_CONFIG, // 简化
    [Environment.FACEBOOK_MOCK]: PRODUCTION_FACEBOOK_CONFIG, // 简化
    [Environment.PRODUCTION_PERSONAL]: PRODUCTION_FACEBOOK_CONFIG, // 简化
    [Environment.PRODUCTION_FACEBOOK]: PRODUCTION_FACEBOOK_CONFIG,
};

// 获取当前配置 - master-facebook分支专用Facebook生产配置
export const ShareConfig: ServerConfig = PRODUCTION_FACEBOOK_CONFIG;

// 配置验证
export function validateConfig(): boolean {
    const config = ShareConfig;

    if (!config.serverUrl || !config.clientUrl) {
        console.error('Missing required URL configuration');
        return false;
    }

    if (!config.mongoUrl || !config.mongoDbName) {
        console.error('Missing required MongoDB configuration');
        return false;
    }

    if (config.enableFacebookSDK && !config.facebookAppId) {
        console.error('Facebook SDK enabled but no App ID provided');
        return false;
    }

    return true;
}

// 打印当前配置信息
export function printConfigInfo(): void {
    console.log('=== ShareConfig Information (master-facebook) ===');
    console.log(`Environment: ${ShareConfig.environment}`);
    console.log(`Platform: ${ShareConfig.platform}`);
    console.log(`Server URL: ${ShareConfig.serverUrl}`);
    console.log(`Client URL: ${ShareConfig.clientUrl}`);
    console.log(`Database: ${ShareConfig.mongoDbName}`);
    console.log(`Port: ${ShareConfig.port}`);
    console.log(`Production: ${ShareConfig.isProduction}`);
    console.log(`Facebook SDK: ${ShareConfig.enableFacebookSDK}`);
    console.log(`Facebook App ID: ${ShareConfig.facebookAppId}`);
    console.log('==================================================');
}

export default ShareConfig;
