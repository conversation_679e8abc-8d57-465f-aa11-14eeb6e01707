/**
 * 客户端和游戏服务器共用的常量
 */
export class GameConst {
    public static dayFreeLimts = 9999; // 每天可以玩多少次
    public static dayFreeLimtsUse = 500; // 每天可以用多少次道具（测试值：5个）
    public static usePropCd = 2000; //道具冷却时间(毫秒)
    public static pickIntervalClick = 0; //点击物品的间隔 毫秒
    public static defaultCountryCode = 'Other'; // 国家代码

    public static newPlayerDefaultProps = {
        moveOut: 1, // 移出道具默认数量
        tips: 1, // 提示道具默认数量
        reShuffle: 1, // 洗牌道具默认数量
        revive: 1, // 复活道具默认数量
    };
}
export enum TTL {
    None = 0,
    OneHour = 3600, // 1小时
    OneDay = 86400, // 1天
    OneDayAndHalf = 129600, // 1天半
    OneWeek = 604800, // 1周
    OneWeekHalf = 302400, // 半周
    OneMonth = 2592000, // 1月
    OneMonthHalf = 1296000, // 半月
}
