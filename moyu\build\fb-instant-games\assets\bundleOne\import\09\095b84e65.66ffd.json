[1, ["d2lw3KtrZG9aD626X+kq2a", "bdc/VAaXlMH57gR5XTjf+v@f8ba6", "d4qGvloFhDIYS8a6/1zCXa@6c48a", "39a7PAYpBGL7y8oyFcn29i@3c318", "63Oy9W+ENAw712rI7WI8s2@6c48a", "73o1D76klKn4V7RpxCKr1A@6c48a", "d9KsEeQAhCfZN3Tv/6mJ2C@6c48a", "b82x/hmQdHiJhvtSQYHgnU"], ["node", "root", "data", "_mesh", "albedoMap", "normalMap", "pbrMap", "emissiveMap", "dissolveNoise", "_effectAsset"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Material", ["_name", "_states", "_defines", "_props"], 0, 12]], [[3, 0, 2], [0, 0, 2], [1, 0, 1, 2, 2], [2, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 3, 4, 3], [7, 0, 1, 2, 3, 4, 5, 5], [8, 0, 1, 2, 3], [9, 0, 1, 2, 3, 4]], [[[[1, "面包_15"], [2, "面包_15", [[3, 4, -2, [0, "15uhREJkBJM64QIMDazlnL"]], [4, 1, -3, [0, "a41oXcdcpAGaBZ6sAotFAw"], [0], [5, true, true], 1], [6, 0.534, 0, -4, [0, "11iCqjo8JPtL2ArtXkQQIy"], [1, 0.00536094605922699, 0.040030837059020996, -0.0009794235229492188]]], [7, "a5SnWKki5PmLuM2IQ5YZ5A", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]], [[[8, ".bin", 3612571762, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 5424, "length": 756, "count": 378, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 5424, "count": 113, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.5420556664466858, -0.144496887922287, -0.27962416410446167], "maxPosition", 8, [1, 0.5698764324188232, 0.1350981891155243, 0.2559223771095276]]], -1], 0, 0, [], [], []], [[[9, "dissolveMaterial-level2", [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_NORMAL_MAP": true, "USE_INSTANCING": true, "USE_ALBEDO_MAP": true, "USE_PBR_MAP": true, "USE_ALPHA_TEST": true}, {}, {}, {}, {}, {}], [[[{"alphaThreshold": 0.841, "dissolveProgress": 1}, "albedoMap", 6, 0, "normalMap", 6, 1, "pbrMap", 6, 2, "emissiveMap", 6, 3, "dissolveNoise", 6, 4], {}, {}, {}, {}, {}], 11, 0, 0, 0, 0, 0]]], 0, 0, [0, 0, 0, 0, 0, 0], [4, 5, 6, 7, 8, 9], [2, 3, 4, 5, 6, 7]]]]