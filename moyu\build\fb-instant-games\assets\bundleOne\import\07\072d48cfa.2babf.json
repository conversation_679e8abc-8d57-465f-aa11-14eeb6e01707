[1, ["10TIMjUuJL/bvlmKfLhqp0", "da3ntCtpxMl6Z2Fa7BIc+E@70ee8"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 1459567512, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 21168, "length": 4608, "count": 2304, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 21168, "count": 441, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.6043457984924316, -0.617621898651123, -0.6176295280456543], "maxPosition", 8, [1, 0.6193612217903137, 0.6105735898017883, 0.622123658657074]]], -1], 0, 0, [], [], []], [[[2, "寿司_10"], [3, "寿司_10", [[4, 1, -2, [0, "9cmyZDJUBC1Zo2eAp33pLZ"], [0], [5, true, true], 1], [6, 4, -3, [0, "2czdQvLmpHbovDqUVoBnFJ"]], [7, 0.6198765933513641, 0, -4, [0, "58r2YrV4ZKX4AQSJS5yG9H"], [1, 0.00750771164894104, -0.0035241544246673584, 0.002247065305709839]]], [8, "dfovts5bRIgr3HZu7ecqGH", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]