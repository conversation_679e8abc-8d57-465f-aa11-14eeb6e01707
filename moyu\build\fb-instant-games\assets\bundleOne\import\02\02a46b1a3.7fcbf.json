[1, ["10TIMjUuJL/bvlmKfLhqp0", "da3ntCtpxMl6Z2Fa7BIc+E@63373"], ["node", "root", "data", "_mesh"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11]], [[3, 0, 2], [0, 0, 2], [1, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 3], [5, 0, 1, 2, 2], [6, 0, 1, 2, 3, 4, 3], [7, 0, 1, 2, 3, 4, 5, 5], [8, 0, 1, 2, 3]], [[[[1, "寿司_1"], [2, "寿司_1", [[3, 1, -2, [0, "47bz6WoYBAwYGT6w0HXalb"], [0], [4, true, true], 1], [5, 4, -3, [0, "5cf1hArBNH8ZEmjf2bL3C0"]], [6, 0.2785416841506958, 1.3760002851486206, -4, [0, "7dQledDe1ObbGBL9rzYf3b"], [1, 0.010507360100746155, 0.05360895395278931, -0.016670584678649902]]], [7, "5bBQmzOu9Jh4G1BUK+qTQs", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]], [[[8, ".bin", 4076875376, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 84000, "length": 12216, "count": 6108, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 84000, "count": 1750, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.25980257987976074, -0.9129328727722168, -0.2952122688293457], "maxPosition", 8, [1, 0.28081730008125305, 1.0201507806777954, 0.2618710994720459]]], -1], 0, 0, [], [], []]]]