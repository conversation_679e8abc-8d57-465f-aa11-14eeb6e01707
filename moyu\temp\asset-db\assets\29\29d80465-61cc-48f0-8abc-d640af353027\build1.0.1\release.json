[1, ["bc5hmEZRBNNpa1vbB9jIn5@7482b"], ["targetInfo", "root", "asset", "value", "data", "_parent"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_children", "_parent"], 0, 4, 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 1], ["cc.TargetInfo", ["localID"], 2]], [[6, 0, 1, 2, 3], [7, 0, 1, 2, 2], [9, 0, 2], [1, 0, 2], [0, 0, 4, 3, 2], [0, 0, 5, 4, 3, 2], [0, 1, 2, 3, 3], [2, 0, 1, 2, 3, 4, 5, 4], [3, 0, 1, 2, 3, 4, 5, 5], [4, 0, 1, 2, 3, 4, 5, 4], [5, 0, 1, 2, 2], [8, 0, 1, 2, 2]], [[3, "hallDuck"], [4, "hallDuck", [-3], [9, "66P7rC1WBO55jmBf6Qva9f", null, null, -2, 0, [-1]]], [2, ["56C5Oxo2BWA4xT3HhFG6ai"]], [6, 0, {}, [7, "56C5Oxo2BWA4xT3HhFG6ai", null, null, -10, [10, "fctQrg35NCHrRuHggTzpol", 1, [[0, "鸭子", ["_name"], 2], [1, ["_lpos"], 2, [1, 0, 0, 0]], [1, ["_lrot"], 2, [3, -0.4718652305968732, -0.5143250644885563, -0.5276764181425673, 0.48411830158426444]], [1, ["_euler"], 2, [1, -90.126, -94.932, -1.463]], [1, ["_lscale"], 2, [1, 3, 3, 3]], [0, true, ["playOnLoad"], -4], [0, false, ["_useBakedAnimation"], -5], [0, 0, ["_shadowReceivingMode"], -6], [11, ["_skinningRoot"], -8, -7], [0, 1, ["_shadowCastingMode"], -9]]], 0]], [2, ["d7IcPyCUVT3qvi9J7wUUMM"]], [5, "翻滚鸭子", 1, [3], [8, "7egelAPq1KQIzNSHJdraq5", null, null, null, 1, 0]], [2, ["29OqHquKhTtL+N0ETaWkvh"]]], 0, [0, -1, 3, 0, 1, 1, 0, -1, 5, 0, 0, 6, 0, 0, 6, 0, 0, 4, 0, 3, 3, 0, 0, 4, 0, 0, 4, 0, 1, 3, 0, 4, 1, 3, 5, 5, 10], [0], [2], [0]]