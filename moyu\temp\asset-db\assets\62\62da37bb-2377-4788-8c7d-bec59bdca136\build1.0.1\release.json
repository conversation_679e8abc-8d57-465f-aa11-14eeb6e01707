[1, ["bd/PXSJtBHnoJk6YxTC+vP"], ["node", "root", "asset", "_target", "data"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_children", "_components", "_parent"], -1, 4, 2, 9, 1], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["cc.TargetInfo", ["localID"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Sprite", ["_type", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_normalColor", "_target"], 2, 1, 4, 5, 1]], [[6, 0, 1, 2, 2], [9, 0, 2], [1, 0, 2], [0, 0, 1, 5, 6, 4, 3], [0, 2, 3, 7, 4, 3], [2, 0, 1, 2, 3, 4, 5, 4], [3, 0, 1, 2, 3, 4, 5, 4], [4, 0, 1, 2, 2], [5, 0, 1, 2, 3], [7, 0, 2], [8, 0, 1, 2, 1], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4, 2]], [[2, "vmButton"], [3, "vmButton", 33554432, [-7], [[10, -3, [1, "b2FpfgEc1Py4FLQrPbDjfF"], [5, 314, 133]], [11, 1, -4, [1, "daO3pUYmlKVIgsdc588k+9"]], [12, 3, -6, [1, "43UvszQV9LRKWvhBXHpprD"], [4, 4292269782], -5]], [6, "30Ahna/hFADJL5JEyiP8Kc", null, null, -2, 0, [-1]]], [9, ["f05XX5jrpEOYwv6lCoUIav"]], [4, 0, {}, 1, [5, "f05XX5jrpEOYwv6lCoUIav", null, null, -8, [7, "8eWUAc4O9I9LUoIPJa7aJ0", 1, [[8, "VMLabelLanguage", ["_name"], 2], [0, ["_lpos"], 2, [1, 0, 0, 0]], [0, ["_lrot"], 2, [3, 0, 0, 0, 1]], [0, ["_euler"], 2, [1, 0, 0, 0]]]], 0]]], 0, [0, -1, 3, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 3, 1, 0, 0, 1, 0, -1, 3, 0, 1, 3, 0, 4, 1, 8], [0], [2], [0]]