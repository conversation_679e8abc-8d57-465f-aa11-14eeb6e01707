[1, ["2b7rASqEdNKp69oQ9/zA7S", "87KJdfUjNA1pqHCGJqumKH", "0be81vzH1FoK7mBxvGGC28@f9941", "60TZW9Q6ZNUZQf73SDw4Vs@f9941", "6aoXk+gYtHg57/JehAnxyU@f9941", "bdG8q6vX1KcbFDmXyII4Pk@f9941", "cbtfJNj/1HWLtsVyJSDiln", "6cKz+ZUM9C+rpahRX/14HR@f9941", "69PPT9dqZBgo8/SaF99eoS@f9941", "fcB51r3FJHYJznkoAKIYK1@f9941", "c5+SZSSGNMoKp6H5lnpasw@f9941", "e2tftiePdHiZlUJSKDsoae@f9941", "dcUKZqxppBPJgr55VZ2X2Q@f9941", "02hWyS6StHRIhijeRUzOmi@f9941", "92Gu9k1TNLgZMPf3ezlcAT@f9941"], ["node", "targetInfo", "_spriteFrame", "root", "asset", "_target", "_parent", "_backgroundImage", "inpuBox", "data"], [["cc.Node", ["_name", "_layer", "_active", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_children", "_parent", "_lpos"], -2, 4, 9, 2, 1, 5], ["cc.Widget", ["_alignFlags", "_bottom", "_originalWidth", "_originalHeight", "_top", "_right", "_left", "_horizontalCenter", "_verticalCenter", "node", "__prefab"], -6, 1, 4], ["cc.Sprite", ["_type", "_sizeMode", "_isTrimmedMode", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Node", ["_name", "_layer", "_active", "_components", "_prefab", "_lpos", "_parent", "_children"], 0, 12, 4, 5, 1, 2], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab", "_target", "_normalColor"], 2, 1, 4, 1, 5], ["47052uw/Y5O1LXaLObj4ARx", ["watchPath", "condition", "_enabled", "node", "__prefab", "watchNodes"], 0, 1, 4, 2], ["cc.Label", ["_string", "_actualFontSize", "_lineHeight", "_horizontalAlign", "_overflow", "_enableWrapText", "node", "__prefab", "_color"], -3, 1, 4, 5], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedComponents"], 2, 1, 9, 9], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["cc.TargetInfo", ["localID"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Layout", ["_layoutType", "_spacingY", "node", "__prefab"], 1, 1, 4], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 9], ["cc.EditBox", ["_inputMode", "node", "__prefab", "_textLabel", "_placeholder<PERSON><PERSON><PERSON>"], 2, 1, 4, 1, 1], ["405ecLg31JDyb8hWju4bULw", ["node", "__prefab", "inpuBox"], 3, 1, 4, 1]], [[18, 0, 2], [13, 0, 1, 2, 3], [11, 0, 1, 2, 3, 4, 5, 5], [14, 0, 1, 2, 2], [17, 0, 2], [4, 0, 1, 2, 1], [0, 3, 4, 8, 5, 3], [10, 0, 1, 2, 3, 4, 5, 4], [2, 0, 3, 4, 5, 2], [0, 0, 1, 8, 7, 6, 5, 9, 3], [8, 0, 1, 2, 2], [4, 0, 1, 1], [0, 0, 1, 8, 6, 5, 3], [2, 3, 4, 5, 1], [5, 0, 1, 2, 4, 3, 2], [15, 0, 1, 2, 3], [0, 0, 1, 8, 7, 6, 5, 3], [16, 0, 1, 2, 2], [0, 0, 1, 7, 6, 5, 3], [0, 0, 1, 8, 6, 5, 9, 3], [0, 0, 2, 1, 7, 6, 5, 9, 4], [0, 0, 1, 7, 6, 5, 9, 3], [8, 0, 1, 3, 2, 2], [4, 0, 1, 2, 3, 1], [2, 0, 1, 3, 4, 5, 3], [1, 0, 4, 1, 3, 9, 10, 5], [1, 0, 6, 5, 4, 1, 7, 8, 2, 3, 9, 10, 10], [19, 0, 1, 2, 3, 3], [20, 0, 1, 1], [9, 0, 2], [0, 0, 2, 1, 8, 7, 6, 5, 9, 4], [3, 0, 1, 7, 3, 4, 5, 3], [3, 0, 2, 1, 6, 3, 4, 5, 4], [3, 0, 1, 6, 3, 4, 5, 3], [12, 0, 1, 2, 3, 4, 5, 4], [2, 0, 1, 2, 3, 4, 5, 4], [2, 3, 4, 1], [5, 0, 1, 2, 2], [5, 0, 1, 2, 3, 2], [1, 0, 6, 5, 4, 1, 2, 3, 9, 10, 8], [1, 0, 5, 1, 9, 10, 4], [1, 0, 6, 5, 1, 2, 9, 10, 6], [1, 0, 4, 1, 2, 3, 9, 10, 6], [1, 4, 1, 2, 3, 9, 10, 5], [1, 0, 2, 3, 9, 10, 4], [6, 0, 1, 3, 4, 5, 3], [6, 0, 3, 4, 5, 2], [6, 2, 0, 3, 4, 5, 3], [7, 0, 3, 1, 2, 4, 5, 6, 7, 7], [7, 0, 3, 1, 2, 4, 5, 6, 7, 8, 7], [7, 0, 1, 2, 6, 7, 8, 4], [21, 0, 1, 2, 3, 4, 2], [22, 0, 1, 2, 1]], [[29, "hallUIView"], [18, "hallUIView", 33554432, [-15, -16, -17, -18, -19], [[5, -10, [0, "77N2cid5pKDpXplRH/AWEU"], [5, 750, 1334]], [44, 5, 750, 2, -11, [0, "63zNQq8NlBQ5QWzOJ4Kgjs"]], [52, -13, [0, "7enO7d9+1NMLRxmsXeVho4"], -12], [36, -14, [0, "542RguQ8FIF44y7flPpMpo"]]], [34, "a0daVw8DRLi6ToMaTA0VS2", null, null, -9, 0, [-1, -2, -3, -4, -5, -6, -7, -8]]], [9, "topBtnNode", 33554432, 1, [-23, -24, -25], [[11, -20, [0, "4ekgjMKlZELabbCLVwFc7A"]], [39, 9, 7, 643.2579999999998, 148.15100000000007, 1085.849, 100, 100, -21, [0, "fad9jliGtI/r7qMu29ltVe"]], [27, 2, 15, -22, [0, "a2uyvexHVDQZCMxTqjN98G"]]], [2, "1eDfCkRjdBzobevFmzIHJ/", null, null, null, 1, 0], [1, -318, 468.84899999999993, 0]], [20, "btnTeam", false, 33554432, [-31], [[5, -26, [0, "61EcVdnpNLkKFtCqEgegCr"], [5, 101, 81]], [8, 1, -27, [0, "43Pe/B3wNPBrcIEcLdoRDj"], 15], [14, 3, -29, [0, "beL729iVNII4RMpG2K7Xz/"], [4, 4292269782], -28], [26, 12, -300.5, 482.53800000000007, 154.60700000000003, 9.5, -486, 2.45799999999997, 105, 97, -30, [0, "342QkrDeFF1ZQ5WL7dDdpg"]]], [2, "2aGlkENwhC+r6kkxwGg0T4", null, null, null, 1, 0], [1, -300, 0, 0]], [21, "btnShare", 33554432, [-37], [[5, -32, [0, "c8hvp9mLlEMq85rOqAhZK3"], [5, 101, 81]], [8, 1, -33, [0, "36Rkycmt1HAYMIB2+BRPHV"], 17], [14, 3, -35, [0, "b8klwxogZLJIBOs/+GZX8i"], [4, 4292269782], -34], [26, 36, -300, -300.5, 154.60700000000003, 9.5, 280.097, 2.45799999999997, 105, 97, -36, [0, "c1Da0DvdlI2b+i9ZWwVPyF"]]], [2, "f9t236LAlADbrpziyxQG3q", null, null, null, 1, 0], [1, 300, 0, 0]], [21, "btnRank", 33554432, [-43], [[5, -38, [0, "ec1n0iUBpCc79bRUkeU5jm"], [5, 101, 81]], [8, 1, -39, [0, "25d4GgHF1FJohAaVcjTpG7"], 19], [14, 3, -41, [0, "97JruPFsVCWqiVHpuYA88Y"], [4, 4292269782], -40], [40, 36, -300.5, 9.5, -42, [0, "bcdFfJbTBNMIQK5L9GlIqz"]]], [2, "05toTTMr9CYa1LP4f2ycE5", null, null, null, 1, 0], [1, 300, 0, 0]], [20, "btnCollect", false, 33554432, [-49], [[5, -44, [0, "35UcVVXClJqpDHWWm3KWci"], [5, 101, 81]], [8, 1, -45, [0, "390WQ9No9Fh64UX/aoJ9Nw"], 21], [14, 3, -47, [0, "d88JLvfkVO1LuXyow2ETUJ"], [4, 4292269782], -46], [41, 12, -300.5, -300.5, 9.5, 101, -48, [0, "27C7/ArpVBqJ4zxchIAEpt"]]], [2, "c5GL0DfBdL6LpYznWT2f9x", null, null, null, 1, 0], [1, -300, 0, 0]], [31, "inputBox", 33554432, [-54, -55], [[[5, -50, [0, "2cTXrk5FxNcrcIVc+ePkxd"], [5, 200, 82]], [24, 1, 0, -51, [0, "548JONfDRHrJpMucOyMoC5"], 22], -52, [43, 559.41, 692.59, 750, 82, -53, [0, "07xCu8LDlLjJxy2kxFaY25"]]], 4, 4, 1, 4], [2, "19IwcJUVNAxK/Xe8kkKr2s", null, null, null, 1, 0], [1, 66.003, 59.563, 0]], [16, "rankLabelNode", 33554432, 1, [-57, -58, -59, -60], [[11, -56, [0, "c6ZS87fXxJU4IE9qT5nedU"]]], [2, "33BB6JlD9KKJBrwNb7mOm8", null, null, null, 1, 0]], [30, "welfareBtn", false, 33554432, 2, [-65], [[5, -61, [0, "08A6T7g1BM0pZAxo1d4k/7"], [5, 72, 72]], [8, 1, -62, [0, "6fYbzZr+VLMZzaE6yQoM3P"], 8], [38, 3, -64, [0, "d2RgRgzoFHr55bATyPgTRL"], -63]], [2, "58BmiPi8ZHE4E9KAXHeaD/", null, null, null, 1, 0], [1, 0, -160, 0]], [18, "btnStart", 33554432, [-70], [[5, -66, [0, "aaT+LtUNtDg4sl74TKP17o"], [5, 376, 129]], [35, 1, 0, false, -67, [0, "4aA7PM4D1GOaYsU1k4ABtL"], 10], [14, 3, -69, [0, "7fklaqWxlJCbk4taXUqaMK"], [4, 4292269782], -68]], [2, "8aWo3OHKJImbSZMLFESzpO", null, null, null, 1, 0]], [9, "midPanel", 33554432, 1, [-74, -75], [[5, -71, [0, "de8uH9h5xOh6AxOgp1jApy"], [5, 750, 100]], [42, 44, 1421.184, 330, 100, 100, -72, [0, "368YgPiJ5GGIA8GBOmSMOH"]], [27, 2, 50, -73, [0, "3cz/yQxbNLNprkZ2cxoREK"]]], [2, "bdi/3HT69MRZOP/C9GlzL9", null, null, null, 1, 0], [1, 0, -287, 0]], [16, "debugNode", 33554432, 1, [7, -79], [[11, -76, [0, "e5gwNNfU1IVIC4jdrZFx3R"]], [47, false, "*.showDebugNode", -78, [0, "dfyl0KfPBKdJSV1KOV0YPb"], [-77]]], [2, "65ciAtLpdHm5toOgNm+LmM", null, null, null, 1, 0]], [4, ["26iaepJgFCqKV3l5WUmOuO"]], [4, ["26iaepJgFCqKV3l5WUmOuO"]], [4, ["26iaepJgFCqKV3l5WUmOuO"]], [4, ["26iaepJgFCqKV3l5WUmOuO"]], [4, ["7eFCmK8SNKy6KspRqyadU8"]], [9, "personBtn", 33554432, 2, [-83], [[5, -80, [0, "62f0fI9bNAQ5yWUxobMJHV"], [5, 72, 72]], [8, 1, -81, [0, "d13QbkX8RL7orgyjq4e1CM"], 6], [37, 3, -82, [0, "40LWxW3npP/JjR9yz3eoWi"]]], [2, "bawerE46dMeot8vYD919P+", null, null, null, 1, 0], [1, 0, -73, 0]], [9, "startNode", 33554432, 1, [10, -86], [[11, -84, [0, "9dgOfkZmdMeKzTM4qijWCF"]], [25, 20, 938.78, 255.019, 129, -85, [0, "655tzG2yNF1oT526l/6Hqm"]]], [2, "6d0j3qraxDVIHg4guofonx", null, null, null, 1, 0], [1, 0, -361.981, 0]], [4, ["f05XX5jrpEOYwv6lCoUIav"]], [9, "黑底", 33554432, 19, [-89, -90], [[5, -87, [0, "f3AEfUf4BPRalcZGv5W3xg"], [5, 376, 38]], [24, 1, 0, -88, [0, "eepMhfFL9C77jtjCw+T0Tg"], 13]], [2, "2aAeqcfplPF6pfUSnmUGK2", null, null, null, 1, 0], [1, 0, -91.58699999999999, 0]], [6, 0, {}, 21, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -106, [22, "e4qJFBLwBIPpGSmni858Aq", 1, [[28, [4, ["f05XX5jrpEOYwv6lCoUIav"]], [[45, "role.selfCountryRank", 2, -105, [0, "02IanLFLZH8pxAyRW7aDaq"], [-104]]]]], [[1, "areaRank", ["_name"], -91], [3, ["_lpos"], -92, [1, 0, 0, 0]], [3, ["_lrot"], -93, [3, 0, 0, 0, 1]], [3, ["_euler"], -94, [1, 0, 0, 0]], [1, 28, ["_fontSize"], -95], [17, ["_contentSize"], [4, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 42.21875, 50.4]], [1, 28, ["_actualFontSize"], -96], [1, false, ["_isBold"], -97], [1, "123", ["_string"], -98], [1, "MyAreaRank", ["_dataID"], -99], [1, true, ["templateMode"], -100], [1, 1, ["watchPathArr", "length"], -101], [1, "role.selfCountryRank", ["watchPathArr", "0"], -102], [1, true, ["_active"], -103]]], 11]], [4, ["f05XX5jrpEOYwv6lCoUIav"]], [6, 0, {}, 21, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -118, [22, "cdRFWBtiNLsqwrLpdNHRG7", 1, [[28, [4, ["f05XX5jrpEOYwv6lCoUIav"]], [[46, "role.selfCountryRank", -117, [0, "16GtJmfJdFGbkByE8VCsw3"], [-116]]]]], [[1, "noRank", ["_name"], -107], [3, ["_lpos"], -108, [1, 0, 0, 0]], [3, ["_lrot"], -109, [3, 0, 0, 0, 1]], [3, ["_euler"], -110, [1, 0, 0, 0]], [1, 28, ["_fontSize"], -111], [17, ["_contentSize"], [4, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 42.21875, 50.4]], [1, 28, ["_actualFontSize"], -112], [1, false, ["_isBold"], -113], [1, "123", ["_string"], -114], [15, "NotCountryListed", ["_dataID"], [4, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]], [1, true, ["_active"], -115]]], 12]], [4, ["f05XX5jrpEOYwv6lCoUIav"]], [4, ["4a5atXBglJxJGAlAL90RE0"]], [4, ["09i06HqvBP2ZaQ4Pk+p3Mt"]], [4, ["4a5atXBglJxJGAlAL90RE0"]], [16, "mid1", 33554432, 11, [3, 4], [[11, -119, [0, "67AkS0sZ5LJr4/qsSrjaXz"]]], [2, "0f3h5eQalIoo2cku1EZ/AK", null, null, null, 1, 0]], [9, "mid2", 33554432, 11, [5, 6], [[11, -120, [0, "0b5G7brcVF4J/A/vrvGmeo"]]], [2, "458OAGCjJAloIZNS1FW+wJ", null, null, null, 1, 0], [1, 0, -150, 0]], [19, "Label", 33554432, 12, [[5, -121, [0, "3begBk7xpJVYMwkfEhSdXS"], [5, 480, 52.92]], [50, "游戏测试，可输入测试关卡", 40, 42, -122, [0, "beIopfR49G2LUg8XC8hHK9"], [4, 3338731305]], [25, 16, 933.54, 933.54, 52.92, -123, [0, "edInj/NTZHPJTwdhVFY1Db"]]], [2, "14TKt+NFRJpr5JzfnOrjr3", null, null, null, 1, 0], [1, 0, -37.52, 0]], [6, 0, {}, 8, [7, "26iaepJgFCqKV3l5WUmOuO", null, null, -124, [10, "589xwVMjdCYJoykCsV1Vc0", 1, [[1, "hallVmRankCell1", ["_name"], 13], [3, ["_lpos"], 13, [1, 0, 0, 0]], [3, ["_lrot"], 13, [3, 0, 0, 0, 1]], [3, ["_euler"], 13, [1, 0, 0, 0]], [1, false, ["_active"], 13], [15, "*.times", ["watchPathArr", "0"], [4, ["f2rmwxfZhAOYpRh0djJser", "09i06HqvBP2ZaQ4Pk+p3Mt"]]]]], 0]], [6, 0, {}, 8, [7, "26iaepJgFCqKV3l5WUmOuO", null, null, -125, [10, "efJQxWkMBJBLx5kdSzWciO", 1, [[1, "hallVmRankCell2", ["_name"], 14], [3, ["_lpos"], 14, [1, 0, 0, 0]], [3, ["_lrot"], 14, [3, 0, 0, 0, 1]], [3, ["_euler"], 14, [1, 0, 0, 0]], [1, false, ["_active"], 14]]], 1]], [6, 0, {}, 8, [7, "26iaepJgFCqKV3l5WUmOuO", null, null, -126, [10, "df046UYXdLbY4SFqEQ9VJ4", 1, [[1, "hallVmRankCell3", ["_name"], 15], [3, ["_lpos"], 15, [1, 0, 0, 0]], [3, ["_lrot"], 15, [3, 0, 0, 0, 1]], [3, ["_euler"], 15, [1, 0, 0, 0]], [1, false, ["_active"], 15]]], 2]], [6, 0, {}, 8, [7, "26iaepJgFCqKV3l5WUmOuO", null, null, -127, [10, "90/Me5PXRORY41YxuCRuf6", 1, [[1, "hallVmRankCell4", ["_name"], 16], [3, ["_lpos"], 16, [1, 0, 0, 0]], [3, ["_lrot"], 16, [3, 0, 0, 0, 1]], [3, ["_euler"], 16, [1, 0, 0, 0]], [1, false, ["_active"], 16]]], 3]], [6, 0, {}, 2, [7, "7eFCmK8SNKy6KspRqyadU8", null, null, -128, [10, "e7E/zCSRBFyIFDlFFALGPs", 1, [[1, "settingBtn", ["_name"], 17], [3, ["_lpos"], 17, [1, 0, 14, 0]], [3, ["_lrot"], 17, [3, 0, 0, 0, 1]], [3, ["_euler"], 17, [1, 0, 0, 0]], [1, true, ["_active"], 17], [15, true, ["_active"], [4, ["c4f0hiv0FKX7tm6MnzVPoc"]]]]], 4]], [12, "ti探险家", 33554432, 18, [[5, -129, [0, "5aEgSCGgZCQoJhsiLicFFK"], [5, 47, 53]], [8, 1, -130, [0, "58yOS7/itHppizyGlGRdX+"], 5]], [2, "2fwj4aW9NM5bH4vNUN5Mdq", null, null, null, 1, 0]], [19, "t1福利", 33554432, 9, [[5, -131, [0, "5cQ9SItzhCz5bjkF/MVNy6"], [5, 56, 49]], [13, -132, [0, "24uPAh1/xPYKqpRvNouSBk"], 7]], [2, "a4FVjvUGxO44VipQMe51jR", null, null, null, 1, 0], [1, -7.815970093361102e-14, 1.8330000000000837, 0]], [6, 0, {}, 10, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -135, [10, "0fiHlC5mRIi5/mmXusqS4x", 1, [[1, "startGame", ["_name"], 20], [3, ["_lpos"], 20, [1, 0, 11.884000000000015, 0]], [3, ["_lrot"], 20, [3, 0, 0, 0, 1]], [3, ["_euler"], 20, [1, 0, 0, 0]], [15, "<PERSON><PERSON><PERSON>", ["_string"], [4, ["4a5atXBglJxJGAlAL90RE0"]]], [17, ["_contentSize"], [4, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 100.46875, 50.4]], [1, "<PERSON><PERSON><PERSON>", ["_dataID"], -133], [1, true, ["_active"], 20], [1, false, ["templateMode"], -134]]], 9]], [12, "t2团队赛", 33554432, 3, [[5, -136, [0, "de9OLROu5Bv5Mfp9rJyexh"], [5, 57, 60]], [13, -137, [0, "01QhixjzBF0JDcP1XUkdqm"], 14]], [2, "39r+GsDihN2JHVVm24WzaR", null, null, null, 1, 0]], [12, "t2分享", 33554432, 4, [[5, -138, [0, "bf572QBW1A06mYpMx/mWbw"], [5, 57, 54]], [13, -139, [0, "2eLUwHPpZPB4N9uWGymRyN"], 16]], [2, "d6EwFGJwxNMZK28OGSTzcY", null, null, null, 1, 0]], [12, "t3排行榜", 33554432, 5, [[5, -140, [0, "edAvKB36VEDLnU0pNQFcXY"], [5, 78, 80]], [13, -141, [0, "2cVC9w5TxDLpgcKbP+0I6F"], 18]], [2, "23FfWC9W9Ps4+c/3cgzJcc", null, null, null, 1, 0]], [12, "t3图鉴", 33554432, 6, [[5, -142, [0, "47lTUzm3dGEZO7DqkftdmB"], [5, 81, 81]], [13, -143, [0, "afuppcJWhO0oD/nEW/z258"], 20]], [2, "0fY1D0GfxHi4gzBpI5fCU2", null, null, null, 1, 0]], [32, "TEXT_LABEL", false, 33554432, 7, [[[23, -144, [0, "6c3GFcDZxKubSUFPsNtHSy"], [5, 198, 82], [0, 0, 1]], -145], 4, 1], [2, "03kQsYjS1DI498aCXslZlI", null, null, null, 1, 0], [1, -98, 41, 0]], [33, "PLACEHOLDER_LABEL", 33554432, 7, [[[23, -146, [0, "2flZLV9VFOd5J5kpdbXnIk"], [5, 198, 82], [0, 0, 1]], -147], 4, 1], [2, "81gCinbDNFzIUDskHIX6/I", null, null, null, 1, 0], [1, -98, 41, 0]], [4, ["09i06HqvBP2ZaQ4Pk+p3Mt"]], [48, "", 0, 40, 60, 1, false, 44, [0, "4cQCFiYRJN4LMDh9VuSuOe"]], [49, "0", 0, 40, 60, 1, false, 45, [0, "f5YHPhNvFLd4gFs+YuF1GE"], [4, 4290493371]], [51, 6, 7, [0, "79G/tFKxxJUJ97MHJPmyJb"], 47, 48]], 0, [0, -1, 24, 0, -2, 22, 0, -3, 39, 0, -4, 36, 0, -5, 35, 0, -6, 34, 0, -7, 33, 0, -8, 32, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 8, 49, 0, 0, 1, 0, 0, 1, 0, -1, 8, 0, -2, 2, 0, -3, 19, 0, -4, 11, 0, -5, 12, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 36, 0, -2, 18, 0, -3, 9, 0, 0, 3, 0, 0, 3, 0, 5, 3, 0, 0, 3, 0, 0, 3, 0, -1, 40, 0, 0, 4, 0, 0, 4, 0, 5, 4, 0, 0, 4, 0, 0, 4, 0, -1, 41, 0, 0, 5, 0, 0, 5, 0, 5, 5, 0, 0, 5, 0, 0, 5, 0, -1, 42, 0, 0, 6, 0, 0, 6, 0, 5, 6, 0, 0, 6, 0, 0, 6, 0, -1, 43, 0, 0, 7, 0, 0, 7, 0, -3, 49, 0, 0, 7, 0, -1, 44, 0, -2, 45, 0, 0, 8, 0, -1, 32, 0, -2, 33, 0, -3, 34, 0, -4, 35, 0, 0, 9, 0, 0, 9, 0, 5, 9, 0, 0, 9, 0, -1, 38, 0, 0, 10, 0, 0, 10, 0, 5, 10, 0, 0, 10, 0, -1, 39, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, -1, 29, 0, -2, 30, 0, 0, 12, 0, -1, 12, 0, 0, 12, 0, -2, 31, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, -1, 37, 0, 0, 19, 0, 0, 19, 0, -2, 21, 0, 0, 21, 0, 0, 21, 0, -1, 22, 0, -2, 24, 0, 1, 23, 0, 1, 23, 0, 1, 23, 0, 1, 23, 0, 1, 26, 0, 1, 26, 0, 1, 26, 0, 1, 26, 0, 1, 27, 0, 1, 27, 0, 1, 27, 0, 1, 27, 0, 1, 23, 0, -1, 22, 0, 0, 22, 0, 3, 22, 0, 1, 25, 0, 1, 25, 0, 1, 25, 0, 1, 25, 0, 1, 28, 0, 1, 28, 0, 1, 28, 0, 1, 28, 0, 1, 25, 0, -1, 24, 0, 0, 24, 0, 3, 24, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 31, 0, 3, 32, 0, 3, 33, 0, 3, 34, 0, 3, 35, 0, 3, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 1, 46, 0, 1, 46, 0, 3, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, -2, 47, 0, 0, 45, 0, -2, 48, 0, 9, 1, 3, 6, 29, 4, 6, 29, 5, 6, 30, 6, 6, 30, 7, 6, 12, 10, 6, 19, 147], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49], [4, 4, 4, 4, 4, 2, 2, 2, 2, 4, 2, 4, 4, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 7], [0, 0, 0, 0, 6, 7, 2, 8, 2, 1, 9, 1, 1, 10, 11, 3, 12, 3, 13, 4, 14, 4, 5, 5]]