[1, ["10TIMjUuJL/bvlmKfLhqp0", "da3ntCtpxMl6Z2Fa7BIc+E@01d6f"], ["node", "root", "data", "_mesh"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11]], [[3, 0, 2], [0, 0, 2], [1, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 3], [5, 0, 1, 2, 2], [6, 0, 1, 2, 3, 4, 3], [7, 0, 1, 2, 3, 4, 5, 5], [8, 0, 1, 2, 3]], [[[[1, "寿司_4"], [2, "寿司_4", [[3, 1, -2, [0, "68sCLKBCdDR6PtOuoGCwPo"], [0], [4, true, true], 1], [5, 4, -3, [0, "cflRTntqFDJKTVpxRUW5s+"]], [6, 0.8981926739215851, 0, -4, [0, "dbNsm8lT5JroJaDdipxa3X"], [1, -0.007650405168533325, 0.0866018533706665, 0.017898976802825928]]], [7, "645cNxkjtF9od9ptF9F6CO", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]], [[[8, ".bin", 3106256094, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 68640, "length": 12792, "count": 6396, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 68640, "count": 1430, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.9058430790901184, -0.6880127191543579, -0.3586817979812622], "maxPosition", 8, [1, 0.8905422687530518, 0.8612164258956909, 0.39447975158691406]]], -1], 0, 0, [], [], []]]]