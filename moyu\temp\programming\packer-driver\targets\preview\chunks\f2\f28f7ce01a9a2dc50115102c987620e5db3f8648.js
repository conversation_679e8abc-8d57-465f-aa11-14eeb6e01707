System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, oops, ecs, LanguageData, VM, GameConst, PropType, RecordType, SceneItemType, GameStorageConfig, DataManager, GameEvent, smc, RoleModelComp, _dec, _class, _crd, Role;

  function _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfecs(extras) {
    _reporterNs.report("ecs", "../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLanguageData(extras) {
    _reporterNs.report("LanguageData", "../../../../extensions/oops-plugin-framework/assets/libs/gui/language/LanguageData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfVM(extras) {
    _reporterNs.report("VM", "../../../../extensions/oops-plugin-framework/assets/libs/model-view/ViewModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../tsrpc/models/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPropType(extras) {
    _reporterNs.report("PropType", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRecordType(extras) {
    _reporterNs.report("RecordType", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRecordTypeData(extras) {
    _reporterNs.report("RecordTypeData", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSceneItemType(extras) {
    _reporterNs.report("SceneItemType", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUserGameData(extras) {
    _reporterNs.report("UserGameData", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameStorageConfig(extras) {
    _reporterNs.report("GameStorageConfig", "../common/config/GameStorageConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataManager(extras) {
    _reporterNs.report("DataManager", "../common/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEvent(extras) {
    _reporterNs.report("GameEvent", "../common/Enum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "../common/SingletonModuleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRoleModelComp(extras) {
    _reporterNs.report("RoleModelComp", "./model/RoleModelComp", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      ecs = _unresolved_3.ecs;
    }, function (_unresolved_4) {
      LanguageData = _unresolved_4.LanguageData;
    }, function (_unresolved_5) {
      VM = _unresolved_5.VM;
    }, function (_unresolved_6) {
      GameConst = _unresolved_6.GameConst;
    }, function (_unresolved_7) {
      PropType = _unresolved_7.PropType;
      RecordType = _unresolved_7.RecordType;
      SceneItemType = _unresolved_7.SceneItemType;
    }, function (_unresolved_8) {
      GameStorageConfig = _unresolved_8.GameStorageConfig;
    }, function (_unresolved_9) {
      DataManager = _unresolved_9.DataManager;
    }, function (_unresolved_10) {
      GameEvent = _unresolved_10.GameEvent;
    }, function (_unresolved_11) {
      smc = _unresolved_11.smc;
    }, function (_unresolved_12) {
      RoleModelComp = _unresolved_12.RoleModelComp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c4784kzvHBHJaIs0pmyr2aI", "Role", undefined);

      /**
       * 角色管理器 - 重构版本
       * 职责：
       * 1. 用户数据的加载、更新和管理
       * 2. 道具系统的操作和验证
       * 3. 游戏进度的管理
       * 4. ViewModel的数据绑定
       */
      _export("Role", Role = (_dec = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
        error: Error()
      }), ecs) : ecs).register('Role'), _dec(_class = class Role extends (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
        error: Error()
      }), ecs) : ecs).Entity {
        constructor() {
          super(...arguments);
          this.RoleModel = void 0;
          // 🔄 待同步的道具更新队列
          this.pendingPropUpdates = [];
          this.dataManager = void 0;
        }

        // ==================== 初始化 ====================
        init() {
          this.add(_crd && RoleModelComp === void 0 ? (_reportPossibleCrUseOfRoleModelComp({
            error: Error()
          }), RoleModelComp) : RoleModelComp);
          this.dataManager = (_crd && DataManager === void 0 ? (_reportPossibleCrUseOfDataManager({
            error: Error()
          }), DataManager) : DataManager).getInstance();
        }

        destroy() {
          this.removeFromViewModel();
          this.remove(_crd && RoleModelComp === void 0 ? (_reportPossibleCrUseOfRoleModelComp({
            error: Error()
          }), RoleModelComp) : RoleModelComp);
        } // ==================== 数据加载 ====================

        /**
         * 加载用户数据
         * @returns 是否加载成功
         */


        loadData() {
          var _this = this;

          return _asyncToGenerator(function* () {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔄 开始加载用户数据...');

            if (!_this.validateNetworkConnection()) {
              return false;
            }

            try {
              var response = yield (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).net.hcGame.callApi('UserInfo', {});

              if (response.isSucc) {
                yield _this.waitForUserDataInitialization();
                return true;
              } else {
                _this.handleAPIError('UserInfo', response.err);

                return false;
              }
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ 用户数据加载异常:', error);
              return false;
            }
          })();
        }
        /**
         * 更新用户数据（由DataManager调用）
         */


        updateUserData(newData) {
          var isFirstInitialization = !this.hasUserData(); // 确保数据结构完整

          this.ensureDataIntegrity(newData); // 合并数据

          if (this.RoleModel.userGameData) {
            this.mergeUserData(this.RoleModel.userGameData, newData);
          } else {
            this.RoleModel.userGameData = newData;
          } // 更新ViewModel


          this.updateViewModel(); // 首次初始化触发事件

          if (isFirstInitialization) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🎉 用户数据首次初始化完成');
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
              error: Error()
            }), GameEvent) : GameEvent).UserDataInitialized, this.RoleModel.userGameData);
          }
        } // ==================== 道具系统 ====================

        /**
         * 获取道具数据
         */


        getPropData(propType) {
          var _this$RoleModel$userG;

          if (!this.validateUserData()) {
            return this.createDefaultPropData(propType);
          }

          var propData = (_this$RoleModel$userG = this.RoleModel.userGameData.propUseData) == null ? void 0 : _this$RoleModel$userG[propType];

          if (!propData) {
            // 创建默认道具数据
            var defaultData = this.createDefaultPropData(propType);
            this.RoleModel.userGameData.propUseData = this.RoleModel.userGameData.propUseData || {};
            this.RoleModel.userGameData.propUseData[propType] = defaultData;
            return defaultData;
          }

          return propData;
        }
        /**
         * 获取道具数据（别名方法，兼容现有代码）
         */


        getPropsDataByType(propType) {
          return this.getPropData(propType);
        }
        /**
         * 尝试消耗道具（检查数量并显示提示）
         * @param args 道具参数（包含类型和数量）
         * @param showToast 是否显示提示
         * @param failureMessageKey 失败时的消息键
         * @returns 是否可以消耗
         */


        tryCostProp(args, showToast, failureMessageKey) {
          if (showToast === void 0) {
            showToast = true;
          }

          if (!this.validateUserData()) {
            if (showToast) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).gui.toast('用户数据未初始化');
            }

            return false;
          }

          var absAmount = Math.abs(args.amount);
          var canUse = this.canUseProp(args.propType, absAmount);

          if (!canUse && showToast && failureMessageKey) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast((_crd && LanguageData === void 0 ? (_reportPossibleCrUseOfLanguageData({
              error: Error()
            }), LanguageData) : LanguageData).getLangByID(failureMessageKey) || failureMessageKey);
          }

          return canUse;
        }
        /**
         * 更新道具数据（别名方法，兼容现有代码）
         */


        updatePropData(args) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            return yield _this2.updateProp(args.propType, args.amount, args.reason);
          })();
        }
        /**
         * 检查道具是否足够
         */


        canUseProp(propType, amount) {
          var propData = this.getPropData(propType);
          return propData.amount >= Math.abs(amount);
        }
        /**
         * 更新道具数量
         */


        updateProp(propType, amount, reason) {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            var _this3$RoleModel;

            // 消耗操作前检查数量
            if (amount < 0 && !_this3.canUseProp(propType, Math.abs(amount))) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).gui.toast((_crd && LanguageData === void 0 ? (_reportPossibleCrUseOfLanguageData({
                error: Error()
              }), LanguageData) : LanguageData).getLangByID('UseLimitsDaily'));
              return false;
            } // 🔍 检查是否为临时数据状态


            var userData = (_this3$RoleModel = _this3.RoleModel) == null ? void 0 : _this3$RoleModel.userGameData;

            if (userData != null && userData.isTemporaryData) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🔄 检测到临时数据状态，本地更新道具数量'); // 在临时状态下，直接更新本地数据

              var propData = _this3.getPropsDataByType(propType);

              if (propData) {
                propData.amount += amount;
                propData.lastUpdateTime = new Date();
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness("\u2705 \u9053\u5177\u672C\u5730\u66F4\u65B0: " + propType + " " + (amount > 0 ? '+' : '') + amount + " (\u4E34\u65F6)"); // 触发道具使用事件

                if (amount < 0) {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
                    error: Error()
                  }), GameEvent) : GameEvent).UseProp, {
                    propType,
                    amount
                  });
                } // 等后台登录完成后同步到服务器


                _this3.queuePropUpdateForSync(propType, amount, reason);

                return true;
              }
            }

            try {
              var response = yield (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).net.hcGame.callApi('UpdateProp', {
                propType,
                amount,
                reason: reason || 'player_action'
              });

              if (response.isSucc) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness("\u2705 \u9053\u5177\u66F4\u65B0\u6210\u529F: " + propType + " " + (amount > 0 ? '+' : '') + amount); // 触发道具使用事件

                if (amount < 0) {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
                    error: Error()
                  }), GameEvent) : GameEvent).UseProp, {
                    propType,
                    amount
                  });
                }

                return true;
              } else {
                _this3.handleAPIError('UpdateProp', response.err);

                return false;
              }
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ 道具更新异常:', error);
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).gui.toast('网络错误，请重试');
              return false;
            }
          })();
        }
        /**
         * 批量更新道具
         */


        batchUpdateProps(updates) {
          var _this4 = this;

          return _asyncToGenerator(function* () {
            for (var update of updates) {
              var success = yield _this4.updateProp(update.propType, update.amount, update.reason);

              if (!success) {
                return false;
              }
            }

            return true;
          })();
        } // ==================== 游戏进度 ====================

        /**
         * 获取当前通关进度
         */


        getGameProgress() {
          if (!this.validateUserData()) {
            return 0;
          }

          return this.RoleModel.userGameData.index || 0;
        }
        /**
         * 获取已通过的关卡索引（别名方法，兼容现有代码）
         */


        getPassIndex() {
          return this.getGameProgress();
        }
        /**
         * 获取下一关卡索引（循环通关）
         */


        getNextLevelIndex(currentProgress) {
          var progress = currentProgress != null ? currentProgress : this.getGameProgress();
          var nextLevel = (progress + 1) % (_crd && SceneItemType === void 0 ? (_reportPossibleCrUseOfSceneItemType({
            error: Error()
          }), SceneItemType) : SceneItemType).Max;
          return nextLevel === 0 ? 1 : nextLevel;
        }
        /**
         * 更新游戏通关进度
         */


        updateGameProgress(newIndex, isGM) {
          var _this5 = this;

          return _asyncToGenerator(function* () {
            if (isGM === void 0) {
              isGM = false;
            }

            var targetIndex = newIndex != null ? newIndex : _this5.getGameProgress() + 1;

            try {
              var response = yield (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).net.hcGame.callApi('UpdateProgress', {
                index: targetIndex,
                isGm: isGM
              });

              if (response.isSucc) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness("\u2705 \u6E38\u620F\u8FDB\u5EA6\u66F4\u65B0\u6210\u529F: " + targetIndex); // 触发通关事件

                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
                  error: Error()
                }), GameEvent) : GameEvent).GamePass, {
                  oldIndex: _this5.getGameProgress(),
                  newIndex: targetIndex,
                  isGm: isGM
                });
                return true;
              } else {
                _this5.handleAPIError('UpdateProgress', response.err);

                return false;
              }
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ 游戏进度更新异常:', error);
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).gui.toast('网络错误，请重试');
              return false;
            }
          })();
        } // ==================== 新手引导 ====================

        /**
         * 检查是否为新玩家
         */


        isNewPlayer() {
          // 优先使用服务端数据
          if (this.hasUserData()) {
            var _this$RoleModel$userG2;

            return (_this$RoleModel$userG2 = this.RoleModel.userGameData.isNewPlayer) != null ? _this$RoleModel$userG2 : true;
          }

          var localRecord = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).storage.getJson((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
            error: Error()
          }), GameStorageConfig) : GameStorageConfig).UserDumpKey, 0);
          return !localRecord || String(localRecord) === '0';
        }
        /**
         * 完成新手引导
         */


        completeNewPlayerGuide() {
          var _this6 = this;

          return _asyncToGenerator(function* () {
            var _this6$RoleModel;

            if (!_this6.isNewPlayer()) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ 用户已完成新手引导');
              return true;
            } // 🔍 检查是否为临时数据状态


            var userData = (_this6$RoleModel = _this6.RoleModel) == null ? void 0 : _this6$RoleModel.userGameData;

            if (userData != null && userData.isTemporaryData) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🔄 检测到临时数据状态，延迟新手引导完成'); // 先更新本地状态，等后台登录完成后再同步到服务器

              if (_this6.RoleModel && _this6.RoleModel.userGameData) {
                _this6.RoleModel.userGameData.isNewPlayer = false;
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('🔄 本地新手状态已更新为false（临时）');
              } // 监听后台登录完成事件，然后同步状态


              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).message.once('UserDataLoaded', /*#__PURE__*/_asyncToGenerator(function* () {
                yield _this6.syncNewPlayerStatusToServer();
              }), _this6);
              return true;
            }

            return yield _this6.syncNewPlayerStatusToServer();
          })();
        }
        /**
         * 🔄 同步新手状态到服务器
         */


        syncNewPlayerStatusToServer() {
          var _this7 = this;

          return _asyncToGenerator(function* () {
            try {
              var response = yield (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).net.hcGame.callApi('GameUpdateSimpleData', {
                isNewPlayer: false
              });

              if (response.isSucc) {
                // 🎯 立即更新本地数据，确保状态同步
                if (_this7.RoleModel && _this7.RoleModel.userGameData) {
                  _this7.RoleModel.userGameData.isNewPlayer = false;
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness('🔄 本地新手状态已更新为false');
                }

                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('✅ 新手引导完成');
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
                  error: Error()
                }), GameEvent) : GameEvent).BasicInfoUpdate, {
                  isNewPlayer: false
                });
                return true;
              } else {
                _this7.handleAPIError('GameUpdateSimpleData', response.err);

                return false;
              }
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ 新手状态更新异常:', error);
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).gui.toast('网络错误，请重试');
              return false;
            }
          })();
        }
        /**
         * 🔄 将道具更新加入同步队列
         */


        queuePropUpdateForSync(propType, amount, reason) {
          var _this8 = this;

          this.pendingPropUpdates.push({
            propType,
            amount,
            reason
          });
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness("\uD83D\uDCDD \u9053\u5177\u66F4\u65B0\u5DF2\u52A0\u5165\u540C\u6B65\u961F\u5217: " + propType + " " + amount); // 监听后台登录完成事件

          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).message.once('UserDataLoaded', /*#__PURE__*/_asyncToGenerator(function* () {
            yield _this8.syncPendingPropUpdates();
          }), this);
        }
        /**
         * 🔄 同步待处理的道具更新到服务器
         */


        syncPendingPropUpdates() {
          var _this9 = this;

          return _asyncToGenerator(function* () {
            if (_this9.pendingPropUpdates.length === 0) {
              return;
            }

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness("\uD83D\uDD04 \u5F00\u59CB\u540C\u6B65 " + _this9.pendingPropUpdates.length + " \u4E2A\u9053\u5177\u66F4\u65B0"); // 复制队列并清空原队列

            var updates = [..._this9.pendingPropUpdates];
            _this9.pendingPropUpdates = [];

            for (var update of updates) {
              try {
                var response = yield (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                  error: Error()
                }), smc) : smc).net.hcGame.callApi('UpdateProp', {
                  propType: update.propType,
                  amount: update.amount,
                  reason: update.reason || 'delayed_sync'
                });

                if (response.isSucc) {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness("\u2705 \u9053\u5177\u540C\u6B65\u6210\u529F: " + update.propType + " " + (update.amount > 0 ? '+' : '') + update.amount);
                } else {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logWarn("\u26A0\uFE0F \u9053\u5177\u540C\u6B65\u5931\u8D25: " + update.propType, response.err);
                }
              } catch (error) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logError("\u274C \u9053\u5177\u540C\u6B65\u5F02\u5E38: " + update.propType, error);
              }
            }

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('✅ 道具更新同步完成');
          })();
        } // ==================== 记录数据 ====================

        /**
         * 获取指定日期的记录数据
         */


        getRecordData(recordType, dateString) {
          var _this$RoleModel$userG3;

          if (!this.validateUserData()) {
            return null;
          }

          var targetDate = dateString || new Date().toDateString();
          var recordData = (_this$RoleModel$userG3 = this.RoleModel.userGameData.recordData) == null ? void 0 : _this$RoleModel$userG3[targetDate];
          return (recordData == null ? void 0 : recordData[recordType]) || null;
        }
        /**
         * 🔧 新增：获取指定关卡的今日挑战次数
         * @param levelId 关卡ID
         * @param dateString 可选的日期字符串，默认为今日
         * @returns 该关卡的挑战次数
         */


        getLevelChallengeCount(levelId, dateString) {
          var _recordData$levelDeta;

          var recordData = this.getRecordData((_crd && RecordType === void 0 ? (_reportPossibleCrUseOfRecordType({
            error: Error()
          }), RecordType) : RecordType).Level, dateString);

          if (!(recordData != null && recordData.levelDetails)) {
            return 0;
          }

          var levelKey = "level_" + levelId;
          return ((_recordData$levelDeta = recordData.levelDetails[levelKey]) == null ? void 0 : _recordData$levelDeta.attempts) || 0;
        }
        /**
         * 🔧 新增：获取当前关卡的今日挑战次数
         * @param dateString 可选的日期字符串，默认为今日
         * @returns 当前关卡的挑战次数
         */


        getCurrentLevelChallengeCount(dateString) {
          var currentLevelId = this.getNextLevelIndex(); // 获取当前要挑战的关卡

          return this.getLevelChallengeCount(currentLevelId, dateString);
        } // ==================== 工具方法 ====================

        /**
         * 获取完整的用户游戏数据
         */


        getUserGameData() {
          if (!this.validateUserData()) {
            throw new Error('用户数据尚未初始化，请等待登录完成');
          }

          return this.RoleModel.userGameData;
        } // ==================== 私有方法 ====================

        /**
         * 验证用户数据是否可用
         */


        validateUserData() {
          var _this$RoleModel;

          return !!((_this$RoleModel = this.RoleModel) != null && _this$RoleModel.userGameData);
        }
        /**
         * 检查是否有用户数据
         */


        hasUserData() {
          return this.validateUserData();
        }
        /**
         * 验证网络连接
         */


        validateNetworkConnection() {
          var _net;

          if (!((_net = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).net) != null && _net.hcGame)) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 网络连接未初始化');
            return false;
          }

          return true;
        }
        /**
         * 等待用户数据初始化完成
         */


        waitForUserDataInitialization() {
          var _this10 = this;

          return _asyncToGenerator(function* () {
            var maxRetries = 30; // 🚀 减少最大重试次数

            var retryCount = 0;

            while (retryCount < maxRetries) {
              if (_this10.hasUserData()) {
                return;
              } // 🚀 使用更短的等待时间，但增加检查频率


              yield new Promise(resolve => setTimeout(resolve, 30));
              retryCount++;

              if (retryCount % 5 === 0) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness("\u23F3 \u7B49\u5F85\u7528\u6237\u6570\u636E\u521D\u59CB\u5316... " + retryCount + "/" + maxRetries);
              }
            } // 🚀 超时后不抛出错误，而是记录警告并继续


            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 用户数据初始化超时，但继续执行');
          })();
        }
        /**
         * 确保数据结构完整性
         */


        ensureDataIntegrity(data) {
          var _data$isNewPlayer;

          data.key = data.key || 0;
          data.userName = data.userName || 'Player';
          data.index = data.index || 0;
          data.isNewPlayer = (_data$isNewPlayer = data.isNewPlayer) != null ? _data$isNewPlayer : true;
          data.propUseData = data.propUseData || {};
          data.recordData = data.recordData || {};
        }
        /**
         * 深度合并用户数据
         */


        mergeUserData(target, source) {
          Object.keys(source).forEach(key => {
            var sourceValue = source[key];
            var targetValue = target[key];

            if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
              if (targetValue && typeof targetValue === 'object') {
                this.mergeUserData(targetValue, sourceValue);
              } else {
                target[key] = sourceValue;
              }
            } else {
              target[key] = sourceValue;
            }
          });
        }
        /**
         * 创建默认道具数据 - 优化版本
         */


        createDefaultPropData(propType) {
          // 🎁 为新手玩家提供默认道具数量
          var defaultAmount = this.getNewPlayerDefaultAmount(propType);
          return {
            amount: defaultAmount,
            propType,
            propId: propType,
            desc: "\u9053\u5177" + propType,
            getTime: new Date(),
            lastResetTime: new Date(),
            lastUpdateTime: new Date()
          };
        }
        /**
         * 🎁 获取新手玩家的默认道具数量
         */


        getNewPlayerDefaultAmount(propType) {
          // 🚀 检查是否启用新手快速启动
          if (!(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).newPlayerFastStart || !this.isNewPlayer()) {
            return 0;
          } // 🎯 新手玩家默认道具配置（使用配置常量）


          switch (propType) {
            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsMoveOut:
              // 移出道具
              return (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).newPlayerDefaultProps.moveOut;

            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsTips:
              // 提示道具
              return (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).newPlayerDefaultProps.tips;

            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsReShuffle:
              // 重新洗牌道具
              return (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).newPlayerDefaultProps.reShuffle;

            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsDayLeftCount:
              // 每日挑战剩余次数
              return (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).dayFreeLimts;
            // 使用配置的默认值

            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsRevive:
              // 复活道具
              return (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).newPlayerDefaultProps.revive;

            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsExp:
              // 游戏经验
              return 0;

            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsCoin:
              // 玩家金币
              return 0;

            default:
              return 0;
          }
        }
        /**
         * 更新ViewModel
         */


        updateViewModel() {
          if (!this.hasUserData()) {
            return;
          }

          this.removeFromViewModel();

          var viewModelData = _extends({
            userId: this.RoleModel.userGameData.key,
            userName: this.RoleModel.userGameData.userName,
            level: this.getGameProgress(),
            isNewPlayer: this.RoleModel.userGameData.isNewPlayer,
            index: this.RoleModel.userGameData.index,
            propUseData: this.RoleModel.userGameData.propUseData
          }, this.RoleModel.userGameData);

          (_crd && VM === void 0 ? (_reportPossibleCrUseOfVM({
            error: Error()
          }), VM) : VM).add(viewModelData, 'role');
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎯 ViewModel已更新');
        }
        /**
         * 从ViewModel移除数据
         */


        removeFromViewModel() {
          (_crd && VM === void 0 ? (_reportPossibleCrUseOfVM({
            error: Error()
          }), VM) : VM).remove('role');
        }
        /**
         * 统一的API错误处理
         */


        handleAPIError(apiName, error) {
          var _error$code;

          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logError("\u274C " + apiName + " API\u5931\u8D25:", error);
          var errorMessage = (error == null ? void 0 : error.message) || (error == null || (_error$code = error.code) == null ? void 0 : _error$code.toString()) || '操作失败';
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).gui.toast(errorMessage);
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f28f7ce01a9a2dc50115102c987620e5db3f8648.js.map