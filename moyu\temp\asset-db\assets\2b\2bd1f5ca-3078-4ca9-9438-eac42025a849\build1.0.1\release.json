[1, ["fcC9n9y+lHcKH/a80gWwjv", "12Y9dMgWdJKJGmTiZyQR9H@2e76e", "12Y9dMgWdJKJGmTiZyQR9H@a804a"], ["node", "_mesh", "root", "data"], [["cc.Node", ["_name", "_layer", "_prefab", "_parent", "_components", "_children", "_lrot", "_euler", "_lpos", "_lscale"], 1, 4, 1, 9, 2, 5, 5, 5, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowReceivingMode", "_name", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 1, 1, 4, 3, 4, 6], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PlaneCollider", ["node", "__prefab"], 3, 1, 4], ["cc.RigidBody", ["_group", "_type", "node", "__prefab"], 1, 1, 4]], [[4, 0, 2], [5, 0, 1, 2, 3, 4, 5, 5], [2, 1], [6, 0, 1, 1], [7, 0, 1, 2, 3, 3], [0, 0, 1, 3, 5, 4, 2, 8, 6, 7, 3], [0, 0, 1, 3, 4, 2, 6, 9, 7, 3], [1, 1, 0, 2, 3, 4, 5, 6, 3], [2, 0, 1, 3], [1, 0, 2, 3, 4, 5, 6, 2], [1, 2, 3, 4, 5, 6, 1], [3, 0, 2], [0, 0, 1, 5, 2, 3], [0, 0, 1, 3, 4, 2, 3]], [[11, "wallSquareCell"], [12, "wallSquareCell", 524288, [-2, -3, -4, -5, -6], [1, "8dmUaE6lNLCrP2WfnK7W4o", null, null, null, -1, 0]], [5, "planeHead", 524288, 1, [-10], [[3, -7, [0, "a842UN0Y9P5rb5tS5NUrzW"]], [4, 2, 2, -8, [0, "fcIdqE2K5G+pE6E/YxWgad"]], [10, -9, [0, "142yoL0B9Mq6VQbXIpTTgK"], [2], [2], 3]], [1, "cbPj46Em1Dpaks+krcy1it", null, null, null, 1, 0], [1, 0, 0, -5], [3, 0.7071067811865475, 0, 0, 0.7071067811865476], [1, 89.99999999999999, 0, 0]], [5, "planeLeft", 524288, 1, [-14], [[3, -11, [0, "4d3HYiz9dEOJT+QJXCXhzT"]], [4, 2, 2, -12, [0, "06Qak7IftOLpEuoobFjtM/"]], [9, 0, -13, [0, "a9OXGxT7JCKLaPOK/UdCxI"], [6], [2], 7]], [1, "f7LXIt60BOIrVnNpCjx423", null, null, null, 1, 0], [1, -5, 0, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 0, 0, -90]], [5, "planeRight", 524288, 1, [-18], [[3, -15, [0, "75umijJ8VIgroFJuEz75Ge"]], [4, 2, 2, -16, [0, "424zs7EhNMTpDwxlzX2Mi8"]], [9, 0, -17, [0, "adyJcUFsZPQpdx4Qn0ow3D"], [10], [2], 11]], [1, "8cMz5a7BlLuIJLeFPrF7+1", null, null, null, 1, 0], [1, 5, 0, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 0, 0, 90]], [5, "planeBack", 524288, 1, [-22], [[3, -19, [0, "9e/nju8FpCUamxaMtoLNG2"]], [4, 2, 2, -20, [0, "4b4e52cHZBEbCOA817QA3T"]], [10, -21, [0, "23HwgCYUxGeripyL+jX2yI"], [14], [2], 15]], [1, "dahzXUFRNDbbCfU4mZotPM", null, null, null, 1, 0], [1, 0, 0, 5], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, -89.99999999999999, 0, 0]], [13, "planeBottom", 524288, 1, [[3, -23, [0, "6bMDJl+9hP1KZkzCwFst9T"]], [4, 2, 2, -24, [0, "b9mrRuRnVAgrxZa8uWwYwB"]], [9, 0, -25, [0, "0buIMPPuVKnIRQ20bQwDYM"], [16], [2], 17]], [1, "c3kUefjz1E8ZvMEaUrT4fq", null, null, null, 1, 0]], [6, "C<PERSON>", 524288, 2, [[7, "Cube<ModelComponent>", 0, -26, [0, "81o74Z+wRCv5u05AXn4r1m"], [0], [8, true, true], 1]], [1, "270Hos1QtBlLq8jn67zhto", null, null, null, 1, 0], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, 10, 1, 1], [1, -89.99999999999999, 0, 0]], [6, "Cube-001", 524288, 3, [[7, "Cube<ModelComponent>", 0, -27, [0, "4cYOk4EQ1N07hG8MH+oGEc"], [4], [8, true, true], 5]], [1, "88PpgR8WVE452sFa3uqEEF", null, null, null, 1, 0], [3, 0, 0, 0.7071067811865475, 0.7071067811865476], [1, 1, 1, 10], [1, 0, 0, 90]], [6, "Cube-002", 524288, 4, [[7, "Cube<ModelComponent>", 0, -28, [0, "6f4w7HroZM4rH22o3L6vPQ"], [8], [8, true, true], 9]], [1, "90jooOuh1FcYZOtTHEiaXh", null, null, null, 1, 0], [3, 0, 0, -0.7071067811865475, 0.7071067811865476], [1, 1, 1, 10], [1, 0, 0, -90]], [6, "Cube-003", 524288, 5, [[7, "Cube<ModelComponent>", 0, -29, [0, "5aNq54RxFNt5iYT3nq+yd/"], [12], [8, true, true], 13]], [1, "f0uMTLeshJJJuAw76xEQYs", null, null, null, 1, 0], [3, 0.7071067811865475, 0, 0, 0.7071067811865476], [1, 10, 1, 1], [1, 89.99999999999999, 0, 0]]], 0, [0, 2, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, -5, 6, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 8, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, -1, 9, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 10, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 3, 1, 29], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1], [0, 2, 0, 1, 0, 2, 0, 1, 0, 2, 0, 1, 0, 2, 0, 1, 0, 1]]