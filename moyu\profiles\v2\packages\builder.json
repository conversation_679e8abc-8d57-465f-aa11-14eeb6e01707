{"__version__": "1.3.9", "log": {"level": 1}, "common": {"polyfills": {"asyncFunctions": true}, "server": "https://moyugame2024.com.cn/", "scenes": [{"url": "db://assets/main.scene", "uuid": "5ffb0632-00ad-4109-939c-d7992fd81d20"}], "buildPath": "project://build", "outputName": "fb-instant-games", "mainBundleCompressionType": "merge_all_json", "platform": "fb-instant-games", "mainBundleIsRemote": false, "md5Cache": true, "name": "moyu", "useBuiltinServer": false, "startScene": "5ffb0632-00ad-4109-939c-d7992fd81d20", "useSplashScreen": false, "md5CacheOptions": {"handleTemplateMd5Link": true}, "overwriteProjectSettings": {"includeModules": {"gfx-webgl2": "on"}}, "bundleConfigs": [], "debug": false, "experimentalEraseModules": true, "sourceMaps": false, "inlineEnum": true}, "BuildTaskManager": {"taskMap": {"1730731289584": {"type": "build", "id": "1730731289584", "progress": 1, "state": "success", "stage": "build", "message": "2025-7-6 23:16:02 build success in 52 s!", "detailMessage": "// ---- build task cc-obfuscated-js：onAfterBuild ----\r", "options": {"name": "moyu", "server": "http://***************:9000/", "platform": "wechatgame", "buildPath": "project://build", "debug": false, "md5Cache": true, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "on"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "off"}}, "nativeCodeBundleMode": "wasm", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_all_json", "useSplashScreen": true, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "5ffb0632-00ad-4109-939c-d7992fd81d20", "outputName": "wechatgame", "taskName": "wechatgame", "scenes": [{"url": "db://assets/facebookTest.scene", "uuid": "17ccc69d-7fbd-45f2-9da2-3f1806c2e5c4"}, {"url": "db://assets/main.scene", "uuid": "5ffb0632-00ad-4109-939c-d7992fd81d20"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.8", "logDest": "project://temp/builder/log/wechatgame2024-11-4 22-41.log", "buildMode": "normal", "mangleProperties": false, "inlineEnum": true}, "time": "2025-7-6 23:15:10", "dirty": false}, "1731638023229": {"type": "build", "id": "1731638023229", "progress": 1, "state": "success", "stage": "build", "message": "2025-7-12 00:14:16 build success in 29 s!", "detailMessage": "// ---- build task cocos-service：onAfterCompressSettings ----\r", "options": {"name": "moyu", "server": "https://moyugame2024.com.cn/", "platform": "web-mobile", "buildPath": "project://build", "debug": false, "md5Cache": true, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"includeModules": {"gfx-webgl2": "on", "physics": "inherit-project-setting", "physics-2d": "inherit-project-setting"}, "macroConfig": {"cleanupImageCache": "inherit-project-setting"}}, "nativeCodeBundleMode": "both", "polyfills": {"asyncFunctions": true}, "experimentalEraseModules": false, "startSceneAssetBundle": false, "bundleConfigs": [], "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_dep", "useSplashScreen": false, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "5ffb0632-00ad-4109-939c-d7992fd81d20", "outputName": "web-mobile", "taskName": "web-mobile", "scenes": [{"url": "db://assets/main.scene", "uuid": "5ffb0632-00ad-4109-939c-d7992fd81d20"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.8", "logDest": "project://temp/builder/log/web-mobile2024-11-15 10-33.log", "buildMode": "normal", "mangleProperties": false, "inlineEnum": true}, "time": "2025-7-12 00:13:46", "dirty": false}, "1748572229196": {"type": "build", "id": "1748572229196", "progress": 1, "state": "success", "stage": "build", "message": "2025-7-12 14:41:14 build success in 37 s!", "detailMessage": "mini-font : \rsuccess time : 308ms\r", "options": {"name": "moyu", "server": "https://127.0.0.1:8080", "platform": "fb-instant-games", "buildPath": "project://build", "debug": false, "buildMode": "normal", "mangleProperties": false, "md5Cache": true, "skipCompressTexture": false, "sourceMaps": false, "overwriteProjectSettings": {"macroConfig": {"cleanupImageCache": "inherit-project-setting"}, "includeModules": {"physics": "inherit-project-setting", "physics-2d": "inherit-project-setting", "gfx-webgl2": "on"}}, "nativeCodeBundleMode": "asmjs", "polyfills": {"asyncFunctions": false}, "experimentalEraseModules": true, "startSceneAssetBundle": false, "bundleConfigs": [], "inlineEnum": true, "useBuiltinServer": false, "md5CacheOptions": {"excludes": [], "includes": [], "replaceOnly": [], "handleTemplateMd5Link": true}, "mainBundleIsRemote": false, "mainBundleCompressionType": "merge_all_json", "useSplashScreen": false, "bundleCommonChunk": false, "packAutoAtlas": true, "startScene": "5ffb0632-00ad-4109-939c-d7992fd81d20", "outputName": "fb-instant-games", "taskName": "fb-instant-games", "scenes": [{"url": "db://assets/main.scene", "uuid": "5ffb0632-00ad-4109-939c-d7992fd81d20"}], "wasmCompressionMode": false, "packages": {}, "__version__": "1.3.9", "logDest": "project://temp/builder/log/fb-instant-games2025-5-30 10-30.log", "optimizeHotUpdate": false}, "time": "2025-7-12 14:40:36", "dirty": false}}}}