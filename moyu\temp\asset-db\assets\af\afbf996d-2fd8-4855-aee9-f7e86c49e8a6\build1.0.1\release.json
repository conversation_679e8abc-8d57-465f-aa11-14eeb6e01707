[1, ["56oY0PFUVKRJA95WSWzdXD", "e28dJLVXJC3YFObxU5gPQx@18f32"], ["node", "_mesh", "root", "data"], [["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "_direction", "node", "__prefab", "_center"], 0, 1, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lrot", "_euler"], 2, 9, 4, 5, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", [], 3], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["<PERSON>.<PERSON><PERSON><PERSON>", ["_radius", "_height", "_direction", "node", "__prefab", "_center"], 0, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [7, 0, 1, 2, 3, 4, 5, 4], [1, 0, 2], [2, 0, 1, 2, 3, 4, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 1], [6, 0, 1, 2, 2], [0, 0, 1, 3, 4, 5, 3], [0, 0, 1, 2, 3, 4, 5, 4], [8, 0, 1, 2, 3, 4, 5, 5]], [[2, "甜菜"], [3, "甜菜", [[4, 1, -2, [0, "52c1uoyJVBprrzXpONJYxX"], [0], [5], 1], [6, 4, -3, [0, "a3ll+/L+JIM6EfVmulolyZ"]], [7, 0.385, 0, -4, [0, "0av0dpkiBJbZLvf5NogLDP"], [1, 0, 0, -0.245251]], [8, 0.147, 0.834, 2, -5, [0, "4e4DMnuFhF0LwT3ZCGmYX5"], [1, 0, 0, 0.4]], [1, 0.452, 0.19, 2, -6, [0, "ffVAP1npJIIoZAczT5qOWo"], [1, 0.2, -0.4, 1.2]], [1, 0.743, 0.505, 2, -7, [0, "46q5k5xEdHhZMu3hHah4Jf"], [1, -0.1, 0.4, 0.7]]], [9, "9d4kbvJNNKUZJ/+Jq9QBuh", null, null, null, -1, 0], [3, 1, 0, 0, 6.123233995736766e-17], [1, 180, 0, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 3, 1, 7], [0, 0], [-1, 1], [0, 1]]