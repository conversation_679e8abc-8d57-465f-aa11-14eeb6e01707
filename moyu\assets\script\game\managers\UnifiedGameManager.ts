import { Node, Vec3 } from 'cc';
import { oops } from '../../../../extensions/oops-plugin-framework/assets/core/Oops';
import { Utils } from '../../utils/Utils';
import { simpleLoader } from '../common/loader/SimpleLoadingManager';
import { smc } from '../common/SingletonModuleComp';
import { LevelConfig } from '../common/table/LevelConfigTypes';
import { GuideView3DItemComp } from '../guide/view/GuideView3DItemComp';
import { configManager } from './ConfigManager';
// 引入 GameEntity，但避免循环引用
type GameEntity = import('../scenes/Game/GameEntity').GameEntity;

/** 游戏状态枚举 */
export enum GameState {
    Loading = 'Loading',
    SimpleMode = 'SimpleMode',
    HardMode = 'HardMode',
    Paused = 'Paused',
    GameOver = 'GameOver',
    Win = 'Win',
}

/** 统一游戏管理器 - 合并多个管理器功能 */
export class UnifiedGameManager {
    private gameEntity: GameEntity;

    // 状态管理
    private currentState: GameState = GameState.Loading;
    private previousGameState: GameState = GameState.Loading; // 🎯 记录前一个游戏状态
    private stateCallbacks: Map<GameState, Array<() => void>> = new Map();

    // 关卡管理
    private currentLevelConfig: LevelConfig | null = null;
    private currentLevelIndex: number = 1; // 🎯 记录当前关卡索引

    // 输入管理
    private inputEnabled: boolean = false;

    // 物品生成管理
    private refillStrategy = {
        adjustByPerformance: false,
        adjustByTime: false,
        minInterval: 3000,
        maxInterval: 4500,
        difficultyMultiplier: 1.0,
    };

    constructor(gameEntity: GameEntity) {
        this.gameEntity = gameEntity;
        this.initializeStateCallbacks();
    }

    // =================== 状态管理 ===================

    private initializeStateCallbacks(): void {
        this.stateCallbacks.set(GameState.SimpleMode, []);
        this.stateCallbacks.set(GameState.HardMode, []);
        this.stateCallbacks.set(GameState.Loading, []);
        this.stateCallbacks.set(GameState.GameOver, []);
        this.stateCallbacks.set(GameState.Win, []);
        this.stateCallbacks.set(GameState.Paused, []);
    }

    onStateChange(state: GameState, callback: () => void): void {
        const callbacks = this.stateCallbacks.get(state);
        if (callbacks) {
            callbacks.push(callback);
        }
    }

    changeState(newState: GameState): void {
        if (this.currentState === newState) return;

        oops.log.logBusiness(`🎮 状态切换: ${this.currentState} → ${newState}`);

        // 🎯 记录前一个状态（仅在游戏进行状态时记录）
        if (
            this.currentState === GameState.SimpleMode ||
            this.currentState === GameState.HardMode
        ) {
            this.previousGameState = this.currentState;
        }

        this.currentState = newState;

        // 执行状态回调
        const callbacks = this.stateCallbacks.get(newState);
        if (callbacks) {
            callbacks.forEach(callback => callback());
        }
    }

    getCurrentState(): GameState {
        return this.currentState;
    }

    isSimpleMode(): boolean {
        return this.currentState === GameState.SimpleMode;
    }

    isHardMode(): boolean {
        return this.currentState === GameState.HardMode;
    }

    /**
     * 🎯 判断之前是否为困难模式（用于Win/GameOver状态时的模式判断）
     */
    wasPreviouslyHardMode(): boolean {
        return this.previousGameState === GameState.HardMode;
    }

    /**
     * 🎯 判断之前是否为简单模式（用于Win/GameOver状态时的模式判断）
     */
    wasPreviouslySimpleMode(): boolean {
        return this.previousGameState === GameState.SimpleMode;
    }

    isPaused(): boolean {
        return this.currentState === GameState.Paused || this.gameEntity.GameModel.pasue;
    }

    isGameOver(): boolean {
        return this.currentState === GameState.GameOver;
    }

    isWin(): boolean {
        return this.currentState === GameState.Win;
    }

    // =================== 关卡加载管理 ===================

    async loadLevel(levelIndex: number, onEssentialLoaded?: () => void): Promise<void> {
        try {
            oops.log.logBusiness(`🎮 开始加载关卡 ${levelIndex}`);

            // 🎯 记录当前关卡索引
            this.currentLevelIndex = levelIndex;

            // 加载关卡配置
            this.currentLevelConfig = await configManager.getLevelConfig(levelIndex);
            if (!this.currentLevelConfig) {
                throw new Error(`关卡配置不存在: ${levelIndex}`);
            }

            // 加载简单模式物品资源
            await this.loadEasyModeItems();

            // 🔧 设置游戏模式（但输入仍禁用，等待物品生成完成）
            oops.log.logBusiness('🎯 设置游戏状态为简单模式，但输入仍禁用');
            this.changeState(GameState.SimpleMode);

            // 加载场景和UI（wallSceneView现在包含在gameSceneView中）
            await this.gameEntity.loadWallAndScene();
            await this.loadGameUI(onEssentialLoaded);

            // 🔧 调用游戏实体的beforeStartGame方法（此时状态已经是SimpleMode）
            oops.log.logBusiness('🎮 调用GameEntity.beforeStartGame()');
            this.gameEntity.beforeStartGame();

            // 🎯 准备配置，但不自动创建道具（由startGame调用）

            oops.log.logBusiness(`✅ 关卡 ${levelIndex} 加载完成`);
        } catch (error) {
            oops.log.logError(`❌ 关卡 ${levelIndex} 加载失败:`, error);
            throw error;
        }
    }

    private async loadEasyModeItems(): Promise<void> {
        if (!this.currentLevelConfig) return;

        const easyItems = this.currentLevelConfig.easyModeItems;
        if (easyItems.length === 0) return;

        // 🎯 获取关卡配置的物品路径
        const itemPrefabPath = this.gameEntity.getItemPrefabPath();

        // 获取去重的物品名称并加载
        const uniqueItemNames = Array.from(new Set(easyItems.map(item => item.name)));
        const prefabPaths = uniqueItemNames.map(name => `${itemPrefabPath}${name}`);

        await simpleLoader.loadPrefabs(prefabPaths);
        oops.log.logBusiness(`✅ 简单模式物品资源加载完成`);
    }

    private async loadGameUI(onUIReady?: () => void): Promise<void> {
        return new Promise<void>(resolve => {
            this.gameEntity.loadGameUIWithCallback(() => {
                if (onUIReady) {
                    onUIReady();
                }
                resolve();
            });
        });
    }

    getCurrentLevelConfig(): LevelConfig | null {
        return this.currentLevelConfig;
    }

    getCurrentLevelIndex(): number {
        return this.currentLevelIndex;
    }

    // =================== 输入管理 ===================

    setInputEnabled(enabled: boolean): void {
        this.inputEnabled = enabled;
    }

    isInputEnabled(): boolean {
        return this.inputEnabled;
    }

    // =================== 物品生成管理 ===================

    setRefillStrategy(strategy: any): void {
        this.refillStrategy = { ...this.refillStrategy, ...strategy };
    }

    checkAndRefillItems(touchedItem?: Node): void {
        // 🎯 使用统一的状态检查
        if (this.isSimpleMode() || this.isGameOver() || this.isWin()) {
            return;
        }

        // 简化的补充逻辑
        const currentItems = this.gameEntity.GameModel.allItemsToPick.size;
        const shouldRefill = currentItems < 5; // 简单阈值

        if (shouldRefill && this.gameEntity.GameModel.allItemsToCreate.length > 0) {
            const refillCount = Math.min(3, this.gameEntity.GameModel.allItemsToCreate.length);

            for (let i = 0; i < refillCount; i++) {
                const itemName = this.gameEntity.GameModel.allItemsToCreate.pop();
                if (itemName) {
                    const bornPos = new Vec3(0, 5, 0);
                    this.gameEntity.createItemOnPos(bornPos, itemName, i + 1, true);
                }
            }
        }
    }

    // =================== 游戏控制 ===================

    initializeEasyModeItems(): void {
        if (!this.currentLevelConfig) return;

        this.gameEntity.GameModel.allItemsToCreate = [];
        const easyModeItems = this.currentLevelConfig.easyModeItems;

        for (const item of easyModeItems) {
            for (let i = 0; i < item.count; i++) {
                this.gameEntity.GameModel.allItemsToCreate.push(item.name);
            }
        }

        this.gameEntity.GameModel.allItemsToCreate = Utils.arrRandomly(
            this.gameEntity.GameModel.allItemsToCreate
        );
        oops.log.logBusiness(
            `🎮 简单模式物品初始化完成: ${this.gameEntity.GameModel.allItemsToCreate.length} 个物品`
        );
    }

    initializeHardModeItems(): void {
        if (!this.currentLevelConfig) return;

        this.gameEntity.GameModel.allItemsToCreate = [];
        const hardModeItems = this.currentLevelConfig.hardModeItems;

        for (const item of hardModeItems) {
            for (let i = 0; i < item.count; i++) {
                this.gameEntity.GameModel.allItemsToCreate.push(item.name);
            }
        }

        this.gameEntity.GameModel.allItemsToCreate = Utils.arrRandomly(
            this.gameEntity.GameModel.allItemsToCreate
        );
        oops.log.logBusiness(
            `🔥 困难模式物品初始化完成: ${this.gameEntity.GameModel.allItemsToCreate.length} 个物品`
        );
    }

    /**
     * 🚀 分批创建困难模式道具 - 性能优化版本
     */
    createHardModeItemsBatched(): void {
        const batchSize = 20; // 每批创建20个道具
        const batchDelay = 50; // 每批间隔50ms
        const allItems = this.gameEntity.GameModel.allItemsToCreate;
        let currentBatch = 0;
        const totalBatches = Math.ceil(allItems.length / batchSize);

        oops.log.logBusiness(`🚀 开始分批创建 ${allItems.length} 个道具，共 ${totalBatches} 批`);

        const createBatch = () => {
            const startIndex = currentBatch * batchSize;
            const endIndex = Math.min(startIndex + batchSize, allItems.length);

            // 创建当前批次的道具
            for (let i = startIndex; i < endIndex; i++) {
                const itemName = allItems[i];
                const bornPos = this.gameEntity.getRandomBornPos();
                this.gameEntity.createItemOnPos(bornPos, itemName, i);
            }

            currentBatch++;

            if (currentBatch < totalBatches) {
                // 继续下一批
                setTimeout(createBatch, batchDelay);
            } else {
                // 所有批次完成
                oops.log.logBusiness(`🎉 所有道具创建完成，总计: ${allItems.length} 个`);
                this.gameEntity.handleInputEnabling();
            }
        };

        // 开始第一批
        createBatch();
    }

    createInitialItemsInScene(): void {
        if (this.gameEntity.GameModel.allItemsToCreate.length === 0) {
            oops.log.logWarn('⚠️ 没有道具需要创建');
            return;
        }

        const itemCount = this.gameEntity.GameModel.allItemsToCreate.length;
        oops.log.logBusiness(`🎯 开始创建 ${itemCount} 个道具`);

        // 🚀 性能优化：困难模式使用分批创建
        if (this.isHardMode() && itemCount > 50) {
            oops.log.logBusiness('🚀 困难模式大量道具，使用分批创建优化性能');
            this.createHardModeItemsBatched();
            return;
        }

        let itemIndex = 0;
        const isNewPlayer = this.gameEntity.isSimpleMode() && smc.role?.isNewPlayer();
        const isSimpleMode = this.gameEntity.isSimpleMode();

        // 🎯 新手玩家：使用固定九宫格位置
        let fixedPositions: Vec3[] = [];
        if (isNewPlayer) {
            fixedPositions = [
                new Vec3(-2, 8, -2), // 1. 左上
                new Vec3(0, 8, -2), // 2. 中上
                new Vec3(2, 8, -2), // 3. 右上
                new Vec3(-2, 8, 0), // 4. 左中
                new Vec3(0, 8, 0), // 5. 中心
                new Vec3(2, 8, 0), // 6. 右中
                new Vec3(-2, 8, 2), // 7. 左下
                new Vec3(0, 8, 2), // 8. 中下
                new Vec3(2, 8, 2), // 9. 右下
            ];
            oops.log.logBusiness('🎓 新手玩家：使用固定九宫格位置创建道具');
        }

        while (this.gameEntity.GameModel.allItemsToCreate.length > 0) {
            const itemName = this.gameEntity.GameModel.allItemsToCreate.pop();
            if (itemName) {
                let bornPos: Vec3;

                if (isNewPlayer && itemIndex < fixedPositions.length) {
                    // 🎯 新手玩家简单模式：使用固定位置
                    bornPos = fixedPositions[itemIndex];
                } else {
                    // 🎲 其他情况：使用随机位置（老玩家简单模式 + 所有困难模式）
                    bornPos = this.generateRandomPosition(isSimpleMode);
                }

                // 创建道具（新手不随机旋转）
                this.gameEntity.createItemOnPos(bornPos, itemName, itemIndex++, !isNewPlayer);
            }
        }

        oops.log.logBusiness(`🎉 所有道具创建完成，总计: ${itemIndex} 个`);

        // 🎯 新手玩家：启动引导系统
        if (isNewPlayer) {
            this.startNewPlayerGuide(itemIndex);
        }
    }

    /**
     * 🎲 生成随机位置
     */
    private generateRandomPosition(isSimpleMode: boolean): Vec3 {
        if (isSimpleMode) {
            // 简单模式：较小的随机范围
            const radius = 3;
            const angle = Math.random() * Math.PI * 2;
            const distance = Math.random() * radius;

            return new Vec3(
                Math.cos(angle) * distance,
                8 + Math.random() * 2, // 高度在8-10之间
                Math.sin(angle) * distance
            );
        } else {
            // 困难模式：较大的随机范围
            return new Vec3(
                (Math.random() - 0.5) * 10, // x: -5 到 5
                Math.random() * 3 + 2, // y: 2 到 5
                (Math.random() - 0.5) * 10 // z: -5 到 5
            );
        }
    }

    /**
     * 🎯 启动新手引导系统
     */
    private startNewPlayerGuide(itemCount: number): void {
        setTimeout(() => {
            const guideSteps = Math.min(itemCount, 9);

            // 🎯 设置引导的最后一步
            if (smc.guide?.GuideModel) {
                smc.guide.GuideModel.last = guideSteps;
                oops.log.logBusiness(`🎓 设置引导最后一步: ${guideSteps}`);
            }

            // 为前9个道具添加引导组件
            for (let i = 0; i < guideSteps; i++) {
                this.addGuideComponentToItem(i, i + 1);
            }
        }, 100); // 延迟确保道具创建完成
    }

    /**
     * 🎯 为道具添加引导组件
     */
    private addGuideComponentToItem(itemIndex: number, guideStep: number): void {
        const itemNode = this.gameEntity.GameModel.allItemsToPick.get(itemIndex);
        if (!itemNode) {
            oops.log.logWarn(`⚠️ 道具节点未找到: ${itemIndex}`);
            return;
        }

        let guideComp = itemNode.getComponent(GuideView3DItemComp);
        if (!guideComp) {
            guideComp = itemNode.addComponent(GuideView3DItemComp);
        }

        if (guideComp && (guideComp as any).setStep) {
            (guideComp as any).setStep(guideStep);
            oops.log.logBusiness(`🎓 为道具 ${itemIndex} 添加引导步骤: ${guideStep}`);
        }
    }

    // =================== 清理 ===================

    destroy(): void {
        this.currentLevelConfig = null;
        this.stateCallbacks.clear();
    }
}
