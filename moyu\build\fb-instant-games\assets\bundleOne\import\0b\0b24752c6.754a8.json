[1, ["4dY5k+ZyFJCoet3ZYX0+BD@6c48a", "f9CRkabcxLvq6KIEvhs7nZ@3c318", "dbWsBKgUFH1LGSE9TfBSVf@6c48a", "73o1D76klKn4V7RpxCKr1A@6c48a", "d9KsEeQAhCfZN3Tv/6mJ2C@6c48a", "b82x/hmQdHiJhvtSQYHgnU", "10TIMjUuJL/bvlmKfLhqp0", "da3ntCtpxMl6Z2Fa7BIc+E@cb172"], ["node", "albedoMap", "normalMap", "pbrMap", "emissiveMap", "dissolveNoise", "_effectAsset", "root", "data", "_mesh"], [["cc.Material", ["_name", "_states", "_defines", "_props"], 0, 12], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11]], [[4, 0, 2], [0, 0, 1, 2, 3, 4], [1, 0, 2], [2, 0, 1, 2, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5], [9, 0, 1, 2, 3]], [[[[1, "dissolveMaterial- level3", [{"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}, {"rasterizerState": {}, "depthStencilState": {}, "blendState": {"targets": [{}]}}], [{"USE_NORMAL_MAP": true, "USE_INSTANCING": true, "USE_ALBEDO_MAP": true, "USE_PBR_MAP": true, "USE_ALPHA_TEST": true}, {}, {}, {}, {}, {}], [[[{"alphaThreshold": 0.841, "dissolveProgress": 1}, "albedoMap", 6, 0, "normalMap", 6, 1, "pbrMap", 6, 2, "emissiveMap", 6, 3, "dissolveNoise", 6, 4], {}, {}, {}, {}, {}], 11, 0, 0, 0, 0, 0]]], 0, 0, [0, 0, 0, 0, 0, 0], [1, 2, 3, 4, 5, 6], [0, 1, 2, 3, 4, 5]], [[[2, "寿司_6"], [3, "寿司_6", [[4, 1, -2, [0, "4cQWlnZNFF/r1mWOBpA6w3"], [0], [5, true, true], 1], [6, 4, -3, [0, "ac+Ycv7sZFqpCIyHzWxYDl"]], [7, 0.5309024155139923, 1.2099224925041199, -4, [0, "98nB/iq1RFuotwG13uRQ16"], [1, 0.0021637380123138428, -0.026851177215576172, 0.07911688089370728]]], [8, "02Ba22jkdA9Le/n7wSZoHL", null, null, null, -1, 0]]], 0, [0, 7, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 8, 1, 4], [0, 0], [-1, 9], [6, 7]], [[[9, ".bin", 2553818651, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 44400, "length": 9060, "count": 4530, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 44400, "count": 925, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.5287386775016785, -1.1627148389816284, -0.38010257482528687], "maxPosition", 8, [1, 0.5330661535263062, 1.109012484550476, 0.5383363366127014]]], -1], 0, 0, [], [], []]]]