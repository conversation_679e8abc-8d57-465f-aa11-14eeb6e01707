[1, ["a5an87t09ObYJlmlhN888z@b47c0", "dbpwaPqU9IqbJzjIyI92wF@b47c0"], ["node", "_parent", "_cameraComponent", "game", "scene", "_envmapHDR", "_diffuseMapHDR"], ["cc.ImageAsset", ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_layer", "_id", "_components", "_children", "_parent", "_lpos", "_lrot", "_euler"], -1, 9, 2, 1, 5, 5, 5], ["cc.UITransform", ["_name", "node", "_contentSize", "__prefab", "_anchorPoint"], 2, 1, 5, 4, 5], ["cc.Widget", ["_alignFlags", "_originalHeight", "_alignMode", "_left", "_right", "_top", "_bottom", "_originalWidth", "_verticalCenter", "node", "__prefab"], -6, 1, 4], "cc.TextureCube", ["cc.Camera", ["_projection", "_far", "_visibility", "_fov", "_fovAxis", "_near", "_priority", "_orthoHeight", "_clearFlags", "node", "_color"], -6, 1, 5], ["RenderQueueDesc", ["stages", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sortMode"], 0], ["cc.SceneAsset", ["_name"], 2], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_lpos"], 1, 1, 2, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.<PERSON>", ["_name", "node", "__prefab", "_cameraComponent"], 2, 1, 4, 1], ["0eec0s4qrZF7onPlYBrD+y+", ["node", "game", "gui", "initial"], 3, 1, 1, 1, 1], ["cc.Label", ["_string", "_actualFontSize", "_lineHeight", "node", "_color"], 0, 1, 5], ["cc.Scene", ["_name", "_children", "_prefab", "_globals"], 2, 2, 4, 4], ["cc.PrefabInfo", ["root", "asset", "fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots"], -3], ["cc.SceneGlobals", ["ambient", "shadows", "_skybox", "fog", "octree", "skin", "lightProbeInfo", "postSettings"], 3, 4, 4, 4, 4, 4, 4, 4, 4], ["cc.AmbientInfo", ["_skyIllumLDR", "_skyColorHDR", "_groundAlbedoHDR", "_skyColorLDR", "_groundAlbedoLDR"], 2, 5, 5, 5, 5], ["cc.ShadowsInfo", ["_enabled", "_type", "_shadowColor"], 1, 5], ["cc.SkyboxInfo", ["_envLightingType", "_enabled", "_envmapHDR", "_diffuseMapHDR"], 1, 6, 6], ["cc.FogInfo", [], 3], ["cc.OctreeInfo", [], 3], ["cc.SkinInfo", [], 3], ["cc.LightProbeInfo", [], 3], ["cc.PostSettingsInfo", [], 3], ["cc.DirectionalLight", ["_colorTemperature", "_shadowEnabled", "_shadowPcf", "_shadowDistance", "_shadowFar", "node", "_staticSettings"], -2, 1, 4], ["cc.StaticLightSettings", ["_castShadow"], 2], ["<PERSON><PERSON><PERSON><PERSON><PERSON>", ["_flows"], 3, 9], ["ShadowFlow", ["_name", "_stages"], 2, 9], ["ShadowStage", ["_name"], 2], ["ForwardFlow", ["_name", "_priority", "_stages"], 1, 9], ["ForwardStage", ["_name", "renderQueues"], 2, 9]], [[9, 0, 2], [1, 0, 6, 4, 7, 8, 9, 2], [7, 0, 2], [1, 0, 1, 2, 5, 4, 7, 4], [1, 0, 1, 2, 6, 5, 4, 4], [1, 0, 1, 3, 5, 4, 4], [1, 0, 1, 6, 5, 3], [1, 0, 1, 2, 6, 4, 4], [8, 0, 1, 2, 3, 4, 3], [2, 0, 1, 3, 2, 2], [2, 1, 2, 1], [2, 1, 2, 4, 1], [10, 0, 1, 2, 3, 2], [3, 0, 1, 2, 9, 10, 4], [3, 0, 3, 4, 5, 6, 7, 1, 9, 8], [3, 0, 8, 9, 3], [11, 0, 1, 2, 3, 1], [12, 0, 1, 2, 3, 4, 4], [13, 0, 1, 2, 3, 2], [14, 0, 1, 2, 3, 4, 5, 7], [15, 0, 1, 2, 3, 4, 5, 6, 7, 1], [16, 0, 1, 2, 3, 4, 2], [17, 0, 1, 2, 3], [18, 0, 1, 2, 3, 3], [19, 1], [20, 1], [21, 1], [22, 1], [23, 1], [5, 0, 3, 4, 5, 1, 2, 9, 7], [5, 0, 6, 7, 1, 8, 2, 9, 10, 7], [24, 0, 1, 2, 3, 4, 5, 6, 6], [25, 0, 2], [26, 0, 1], [27, 0, 1, 2], [28, 0, 2], [29, 0, 1, 2, 3], [30, 0, 1, 2], [6, 0, 2], [6, 1, 2, 0, 4]], [[[[2, "main"], [3, "gui", 512, 33554432, [-5, -6], [[9, "Canvas<UITransform>", -1, [0, "03ZMhNEpRK75Hmdzr8ZGhG"], [5, 750, 1334]], [12, "<PERSON><PERSON><Canvas>", -3, [0, "7acBC/yU9N05fc6HGd8fZS"], -2], [13, 45, 1280, 1, -4, [0, "1chkRDp1xCFqsnwJh1NVq1"]]], [1, 375, 667, 0]], [4, "initial", 512, 33554432, 1, [-9], [[10, -7, [5, 240, 50.40000000000009]], [14, 45, 255, 255, 641.8, 641.8, 100, 100, -8]]], [5, "root", 512, "14hVXuCNlJBZetS8jZbbDK", [-12, 1], [[16, -11, -10, 1, 2]]], [6, "game", 512, 3, [-13, -14]], [7, "title", 512, 33554432, 2, [[11, -15, [5, 560, 52.92], [0, 0.5, 1]], [17, "这里想做个动画，减少等待时间", 40, 42, -16, [4, 255]], [15, 18, -26.46, -17]]], [18, "main", [3], [19, null, null, "5ffb0632-00ad-4109-939c-d7992fd81d20", null, null, null], [20, [21, 0.78125, [2, 0.68684, 0.684588, 0.696015, 0.520833125], [2, 0.687046, 0.668477, 0.64093, 0], [2, 0.2, 0.5019607843137255, 0.8, 0.78125], [2, 0.2784313725490196, 0.2784313725490196, 0.2784313725490196, 1]], [22, true, 1, [4, 2147483648]], [23, 1, true, 0, 1], [24], [25], [26], [27], [28]]], [1, "Main Camera 3D", 4, [[29, 0, 20.13605442176871, 1, 0, 120, 1083310080, -18]], [1, 0, 60, 0], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, -90, 0, 0]], [1, "Directional Light", 4, [[31, 6500, true, 1, 65, 100, -19, [32, true]]], [1, 0, 60, 0.1], [3, -0.9320426565427332, 0, 0, 0.36234857028111017], [1, -137.511, 0, 0]], [8, "UICamera", 512, 1, [-20], [1, 0, 0, 1000]], [30, 0, 1073741824, 667, 2000, 6, 1107296256, 9, [4, 0]]], 0, [0, 0, 1, 0, 2, 10, 0, 0, 1, 0, 0, 1, 0, -1, 9, 0, -2, 2, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, 3, 4, 0, 0, 3, 0, -1, 4, 0, -1, 7, 0, -2, 8, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 7, 0, 0, 8, 0, -1, 10, 0, 4, 6, 1, 1, 3, 3, 1, 6, 20], [0, 0], [5, 6], [0, 1]], [[{"base": "2,2,0,0,2,0", "rgbe": true, "mipmapMode": 2, "mipmapAtlas": {"front": "a5an87t09ObYJlmlhN888z@b47c0@e9a6d", "back": "a5an87t09ObYJlmlhN888z@b47c0@40c10", "left": "a5an87t09ObYJlmlhN888z@b47c0@8fd34", "right": "a5an87t09ObYJlmlhN888z@b47c0@74afd", "top": "a5an87t09ObYJlmlhN888z@b47c0@bb97f", "bottom": "a5an87t09ObYJlmlhN888z@b47c0@7d38f"}, "mipmapLayout": [{"left": 0, "top": 0, "width": 384, "height": 384, "level": 0}, {"left": 0, "top": 384, "width": 192, "height": 192, "level": 1}, {"left": 192, "top": 384, "width": 96, "height": 96, "level": 2}, {"left": 288, "top": 384, "width": 48, "height": 48, "level": 3}, {"left": 336, "top": 384, "width": 24, "height": 24, "level": 4}, {"left": 360, "top": 384, "width": 12, "height": 12, "level": 5}, {"left": 372, "top": 384, "width": 6, "height": 6, "level": 6}, {"left": 378, "top": 384, "width": 3, "height": 3, "level": 7}, {"left": 381, "top": 384, "width": 1, "height": 1, "level": 8}]}], [4], 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"base": "2,2,0,0,2,0", "rgbe": true, "mipmaps": [{"front": "dbpwaPqU9IqbJzjIyI92wF@b47c0@e9a6d", "back": "dbpwaPqU9IqbJzjIyI92wF@b47c0@40c10", "left": "dbpwaPqU9IqbJzjIyI92wF@b47c0@8fd34", "right": "dbpwaPqU9IqbJzjIyI92wF@b47c0@74afd", "top": "dbpwaPqU9IqbJzjIyI92wF@b47c0@bb97f", "bottom": "dbpwaPqU9IqbJzjIyI92wF@b47c0@7d38f"}]}], [4], 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[{"fmt": "0", "w": 0, "h": 0}, -1], [0], 0, [], [], []], [[[33, [[34, "ShadowFlow", [[35, "ShadowStage"]]], [36, "ForwardFlow", 1, [[37, "ForwardStage", [[38, ["default"]], [39, true, 1, ["default"]]]]]]]]], 0, 0, [], [], []]]]