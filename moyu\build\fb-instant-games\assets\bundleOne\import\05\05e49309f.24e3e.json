[1, ["e5LUoqx3RAr41dA5QrbKMj", "deYHMKrYVH35oTfJtKq1CP@86922"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 1369066652, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 38592, "length": 5250, "count": 2625, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 38592, "count": 804, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.22634577751159668, -0.3768339157104492, -0.17503538727760315], "maxPosition", 8, [1, 0.4580959677696228, 0.5428921580314636, 0.12548121809959412]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_12"], [3, "日式寿司_12", [[4, 1, -2, [0, "f2d0rBfTFOvYEZdQ3DSVGK"], [0], [5, true, true], 1], [6, 4, -3, [0, "b4Gqcpo/FEPo7V+QdVymyx"]], [7, 0.335811048746109, 0.1412721574306488, -4, [0, "c8Itf7YQ9Aeqfk+GcMVDFT"], [1, 0.007962137460708618, 0.00028876960277557373, -0.06423831358551979]]], [8, "ffte4lTCRHqIXVazAd9Xmg", null, null, null, -1, 0], [1, 1.93, 0, 4.327]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]