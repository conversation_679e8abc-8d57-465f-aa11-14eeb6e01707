[1, ["56oY0PFUVKRJA95WSWzdXD", "e28dJLVXJC3YFObxU5gPQx@331be"], ["node", "root", "data", "_mesh"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lrot", "_euler"], 2, 9, 4, 5, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", [], 3], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "_direction", "node", "__prefab", "_center"], 0, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11]], [[3, 0, 2], [6, 0, 1, 2, 3, 4, 5, 4], [0, 0, 2], [1, 0, 1, 2, 3, 4, 2], [2, 0, 1, 2, 3, 4, 5, 2], [4, 1], [5, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 5, 5], [8, 0, 1, 2, 3]], [[[[2, "韭葱"], [3, "韭葱", [[4, 1, -2, [0, "79JL+kSwhAYqiPPkdZfEMU"], [0], [5], 1], [6, 4, -3, [0, "3ewj1kWulPpr46U1+VZ6ja"]], [1, 0.244, 4.297, 2, -4, [0, "9dT3t/JNVKkIVIEdLJ94wM"], [1, -0.05, -0.009845957159996033, 0.39472508430480957]], [1, 0.329, 0.042, 2, -5, [0, "a0pXtaHrpDm5YqPpmRuuYL"], [1, -0.619812, -0.009846, 1.894725]], [1, 0.314, 0.042, 2, -6, [0, "4dJQPHj4lO87SLO301uiav"], [1, 0.380188, -0.009846, 1.594725]], [1, 0.314, 0.34, 0, -7, [0, "b7AVdSvK1JZ4wosJ9PkjST"], [1, -0.019812, -0.009846, 1.294725]]], [7, "0cJY5XmUVKf7wQ5ptJdHIp", null, null, null, -1, 0], [3, 0.9659258262890683, -1.5848095757158825e-17, 0.25881904510252074, 5.914589856893349e-17], [1, 180, -30, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 7], [0, 0], [-1, 3], [0, 1]], [[[8, ".bin", 3898951783, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 75744, "length": 8190, "count": 4095, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 75744, "count": 1052, "stride": 72}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}, {"name": "a_texCoord1", "format": 21, "isNormalized": false}, {"name": "a_texCoord2", "format": 21, "isNormalized": false}, {"name": "a_texCoord3", "format": 21, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.9460094571113586, -0.3946647047996521, -1.996366262435913], "maxPosition", 8, [1, 0.7063846588134766, 0.37497279047966003, 2.7858164310455322]]], -1], 0, 0, [], [], []]]]