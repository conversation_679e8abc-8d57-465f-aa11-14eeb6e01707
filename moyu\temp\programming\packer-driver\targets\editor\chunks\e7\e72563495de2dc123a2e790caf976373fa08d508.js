System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, GameConst, _crd, TTL;

  _export("GameConst", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "666c9HajcFAgLCQqxspkyQw", "GameConst", undefined);

      /**
       * 客户端和游戏服务器共用的常量
       */
      _export("GameConst", GameConst = class GameConst {});

      GameConst.dayFreeLimts = 9999;
      // 每天可以玩多少次
      GameConst.dayFreeLimtsUse = 500;
      // 每天可以用多少次道具（测试值：5个）
      GameConst.usePropCd = 2000;
      //道具冷却时间(毫秒)
      GameConst.pickIntervalClick = 0;
      //点击物品的间隔 毫秒
      GameConst.defaultCountryCode = 'Other';
      // 国家代码
      GameConst.newPlayerDefaultProps = {
        moveOut: 1,
        // 移出道具默认数量
        tips: 1,
        // 提示道具默认数量
        reShuffle: 1,
        // 洗牌道具默认数量
        revive: 1 // 复活道具默认数量

      };

      _export("TTL", TTL = /*#__PURE__*/function (TTL) {
        TTL[TTL["None"] = 0] = "None";
        TTL[TTL["OneHour"] = 3600] = "OneHour";
        TTL[TTL["OneDay"] = 86400] = "OneDay";
        TTL[TTL["OneDayAndHalf"] = 129600] = "OneDayAndHalf";
        TTL[TTL["OneWeek"] = 604800] = "OneWeek";
        TTL[TTL["OneWeekHalf"] = 302400] = "OneWeekHalf";
        TTL[TTL["OneMonth"] = 2592000] = "OneMonth";
        TTL[TTL["OneMonthHalf"] = 1296000] = "OneMonthHalf";
        return TTL;
      }({}));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e72563495de2dc123a2e790caf976373fa08d508.js.map