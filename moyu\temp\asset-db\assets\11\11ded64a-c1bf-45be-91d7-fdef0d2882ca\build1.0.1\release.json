[1, ["b4I7YcsGhNL6mS31zX1XdO", "95TyOyGIxPI7V5IpoYIKgY", "a0y+TzRwZCtabO5y/fh/rg@f9941", "0982eGAU5Hxo3KLcMRx1d/@f9941", "2a2bBI3aVJmqTx3LdMBpoc@f9941", "0be81vzH1FoK7mBxvGGC28@f9941", "0az/wiTl9MW6+wGBbdjbTn@f9941"], ["node", "root", "asset", "_spriteFrame", "targetInfo", "data", "_parent"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "_active", "__editorExtras__", "_prefab", "_components", "_parent", "_lpos", "_children"], -2, 4, 9, 1, 5, 2], ["cc.Widget", ["_alignFlags", "_left", "_right", "_originalWidth", "_top", "_originalHeight", "node", "__prefab"], -3, 1, 4], ["cc.Sprite", ["_sizeMode", "_type", "_isTrimmedMode", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Label", ["_actualFontSize", "_string", "_fontSize", "_lineHeight", "_isBold", "node", "__prefab", "_color"], -2, 1, 4, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["47052uw/Y5O1LXaLObj4ARx", ["watchPath", "valueA", "node", "__prefab", "watchNodes"], 1, 1, 4, 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedComponents"], 2, 1, 9, 9], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 9], ["4bff6xrIC5LtYScAfbMmEdR", ["_dataID", "node", "__prefab"], 2, 1, 4], ["cc.Layout", ["_layoutType", "_spacingY", "node", "__prefab"], 1, 1, 4], ["545c05XsG9GDJispEGWKvYv", ["watchPath", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON>", ["_transition", "node", "__prefab"], 2, 1, 4], ["7ebbbU7W4lOaqFX+E0LMIQ4", ["node", "__prefab"], 3, 1, 4]], [[8, 0, 2], [16, 0, 2], [14, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4, 5, 5], [4, 0, 1, 2, 1], [15, 0, 1, 2, 3], [0, 2, 4, 7, 5, 3], [4, 0, 1, 1], [9, 0, 1, 2, 3, 4, 5, 4], [0, 0, 1, 7, 9, 6, 5, 8, 3], [12, 0, 1, 2, 3], [20, 0, 1, 2, 2], [5, 0, 1, 2, 3, 4, 3], [6, 0, 1, 3, 2, 2], [13, 0, 1, 2, 2], [17, 0, 1, 1], [1, 0, 4, 6, 7, 3], [2, 1, 3, 4, 5, 2], [0, 0, 1, 7, 6, 5, 3], [0, 0, 2, 1, 7, 6, 5, 8, 4], [0, 0, 1, 7, 6, 5, 8, 3], [6, 0, 1, 2, 2], [1, 0, 1, 2, 4, 3, 6, 7, 6], [19, 0, 1, 2, 3, 3], [3, 1, 0, 2, 3, 5, 6, 7, 5], [7, 0, 2], [0, 0, 1, 9, 6, 5, 3], [0, 0, 1, 7, 9, 6, 5, 3], [0, 0, 2, 1, 9, 6, 5, 8, 4], [0, 0, 3, 1, 7, 9, 6, 5, 8, 4], [0, 0, 3, 1, 7, 6, 5, 8, 4], [5, 0, 2, 3, 4, 2], [11, 0, 1, 2, 3, 4, 5, 4], [18, 0, 1, 2, 2], [1, 0, 1, 6, 7, 3], [1, 0, 2, 6, 7, 3], [1, 0, 4, 3, 5, 6, 7, 5], [1, 0, 1, 2, 3, 6, 7, 5], [1, 0, 1, 2, 3, 5, 6, 7, 6], [2, 0, 3, 4, 5, 2], [2, 1, 0, 2, 3, 4, 5, 4], [3, 1, 0, 4, 5, 6, 4], [3, 0, 2, 3, 5, 6, 7, 4], [21, 0, 1, 2, 2], [22, 0, 1, 1]], [[25, "gameUIView"], [26, "gameUIView", 33554432, [-10, -11, -12, -13, -14, -15], [[4, -7, [0, "77N2cid5pKDpXplRH/AWEU"], [5, 750, 1334]], [38, 5, 539, 539, 2, 2, -8, [0, "63zNQq8NlBQ5QWzOJ4Kgjs"]], [44, -9, [0, "95ifdpcjVKYY0wnt/ngONZ"]]], [32, "a0daVw8DRLi6ToMaTA0VS2", null, null, -6, 0, [-1, -2, -3, -4, -5]]], [28, "clock", 512, 33554432, [-19, -20], [[4, -16, [0, "f6EjWPm/pMFrROegyAQqAa"], [5, 240, 93]], [17, 1, -17, [0, "72UehYsAJOda+mpOnvba+i"], 3], [16, 17, 80, -18, [0, "5ahh9dSiROSp60EEWQcFKF"]]], [3, "64bqqmeoZCnJj+izOP6y6j", null, null, null, 1, 0], [1, 0, -76.5, 0]], [9, "bottomNode", 33554432, 1, [-24, -25, -26], [[4, -21, [0, "96Su/ls2FDMKe8AAqgWEIO"], [5, 750, 160]], [36, 44, 1669.0749999999998, 100, 250.825, -22, [0, "2b12DgP/FLFZwD0khpsEgJ"]], [40, 1, 0, false, -23, [0, "7cYpafuFhI4aEKE3LzgsgI"], 9]], [3, "c4bRFzpq1Cxr+VrsV+rFOO", null, null, null, 1, 0], [1, 0, -587, 0]], [18, "stateNode", 33554432, 1, [[7, -27, [0, "d85Y0BKmhKZ7ECa0Yd/n7O"]], [12, "*.isNewGuide", 1, -29, [0, "c6IFmctjFOhbipaD+WjVJK"], [-28]], [12, "*.isHardMode", 1, -30, [0, "47WOpeNSJPOZbk4uq+JvOp"], [2]], [12, "*.isSimpleMode", 1, -32, [0, "db5Ds+tZVPSL9PKzuzb3D/"], [-31]], [31, "*.isNewGuide", -34, [0, "2eQuPAlzZD5J4XLP2owiRx"], [-33]]], [3, "ffwkPulllD+450kucq3q3T", null, null, null, 1, 0]], [9, "guideNode", 33554432, 1, [-38], [[7, -35, [0, "7cmeinCnROfoJkDa3ko2MO"]], [16, 17, 170.70900000000006, -36, [0, "e1PEgeFEFI2oB0FsuKNJRR"]], [23, 2, 20, -37, [0, "7dmfqedZlPcLU6Ez3XZhlK"]]], [3, "77/6+skbFCNat8ZyJ4RLhd", null, null, null, 1, 0], [1, 0, 446.29099999999994, 0]], [9, "settingBtn", 33554432, 1, [-42], [[4, -39, [0, "e5pVfawbBB9I8iHoLzgKzd"], [5, 72, 72]], [17, 1, -40, [0, "da+WoQaWhOCZxSzJQfhmSP"], 5], [43, 3, -41, [0, "59dAmd2ehLt4xk1Uzofa2v"]]], [3, "29BMES65dLe4TdozrZUGRi", null, null, null, 1, 0], [1, -318, 468.849, 0]], [29, "bebugInfo", false, 33554432, 1, [-45, -46, -47], [[7, -43, [0, "4f4nMDi7NLPJ7FlYmM5XY2"]], [23, 2, 5, -44, [0, "96WDcgIGJNBaojg5fUK/fa"]]], [3, "eeAJOPVchPGKt0bMJicWX4", null, null, null, 1, 0], [1, -264.057, 340.765, 0]], [27, "eazyModeShow", 33554432, 5, [-49, -50], [[7, -48, [0, "94cCmACDpEu4Z4Ml7h7/mr"]]], [3, "750k1T3YdJb64d7sMZbupt", null, null, null, 1, 0]], [30, "lblIndex", false, 33554432, 7, [[4, -51, [0, "82eIWXyxJEAozg9fcUN6r5"], [5, 42.255859375, 30.240000000000002]], [42, 20, 20, 24, -52, [0, "14YqTXr+tEKbp6i7q0fFbZ"], [4, 4283427091]], [22, 17, 461.04728124999997, 338.8720703125, 8.881784197001252e-15, 42.255859375, -53, [0, "4cnlrPz3FJDYzLzFv7FT7a"]], [11, "*.lblIndex", -54, [0, "basq8opKNOer+7gptSXSHz"]]], [3, "85Lys+dt1NOY9DO+tY3782", null, null, null, 1, 0], [1, 0, 34.879999999999995, 0]], [20, "targetCountLbl", 33554432, 7, [[4, -55, [0, "dbQSuEFx5JAZdc3KJ7sntG"], [5, 108.955078125, 30.240000000000002]], [24, "TargetCount", 20, 20, 24, -56, [0, "91xM5Sx+pIHpbPEDKydBBe"], [4, 4283427091]], [37, 17, 465.80728124999996, 338.8720703125, 42.255859375, -57, [0, "3fTPrXkR9H/bct68lIz9KD"]], [11, "*.targetCountLbl", -58, [0, "15p2fIcI1Eb48E+M20DXdJ"]]], [3, "cbusHhathKpKiTAd8oOzkz", null, null, null, 1, 0], [1, 0, 34.879999999999995, 0]], [20, "curTotalLbl", 33554432, 7, [[4, -59, [0, "79H2TRw4RBHoY9bUcD1rim"], [5, 101.162109375, 30.240000000000002]], [24, "CurTotalLbl", 20, 20, 24, -60, [0, "01NF1d8FNEt73+hsI9UimP"], [4, 4283427091]], [22, 17, 465.80728124999996, 338.8720703125, 35.24000000000001, 42.255859375, -61, [0, "91BCpNEqlLYaMJtRdeaNT/"]], [11, "*.curTotalLbl", -62, [0, "1dT2hiZpFGrYgWLGw5V8h3"]]], [3, "cdi+l9F79ABLFlqZlK3Zjq", null, null, null, 1, 0], [1, 0, -0.36000000000000654, 18.051]], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [6, 0, {}, 8, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -64, [13, "7bCcefmp5LQbV2LX+c03MC", 1, [[15, [1, ["f05XX5jrpEOYwv6lCoUIav"]], [[33, "GuideTips", -63, [0, "85pziH3V5NiLtyX9e/DUqG"]]]]], [[5, "guideTips", ["_name"], [1, ["f05XX5jrpEOYwv6lCoUIav"]]], [2, ["_lpos"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, -32.708, 0]], [2, ["_lrot"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [3, 0, 0, 0, 1]], [2, ["_euler"], [1, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 0, 0]], [5, 45, ["_fontSize"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [2, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 0, 50.4]], [5, 45, ["_actualFontSize"], [1, ["4a5atXBglJxJGAlAL90RE0"]]], [5, "", ["_string"], [1, ["4a5atXBglJxJGAlAL90RE0"]]]]], 1]], [9, "topNode", 33554432, 1, [2], [[7, -65, [0, "39DEcXnr1Jq6wA48pmZYJz"]], [16, 17, 10, -66, [0, "d6g6NdJx1NTpUZJRToMR7j"]]], [3, "9evIxirJ1CLaJL8iBouWql", null, null, null, 1, 0], [1, 0, 607, 0]], [19, "lblTime", 512, 33554432, 2, [[4, -67, [0, "2a7OwTCAxH9ImWEObkhepB"], [5, 102.3046875, 50.4]], [41, "00:00", 40, true, -68, [0, "2akTGeTvFAH6815lSmrStG"]], [11, "*.lblTime", -69, [0, "540odulLJDMJYlsG5zzV06"]]], [3, "05TYgVt0lKTpXFO4lMPqly", null, null, null, 1, 0], [1, 26.519, 0, 0]], [6, 0, {}, 3, [8, "b4sR4lhohPC57Z37TkjD9N", null, null, -71, [13, "a8Dx12yrRGmrMevie2FRRD", 1, [[15, [1, ["b4sR4lhohPC57Z37TkjD9N"]], [[34, 8, 65, -70, [0, "a56z0ECbFNuq6h8roU+Lo4"]]]]], [[5, "propCell0", ["_name"], [1, ["b4sR4lhohPC57Z37TkjD9N"]]], [2, ["_lpos"], [1, ["b4sR4lhohPC57Z37TkjD9N"]], [1, -230, -0.41200000000000614, 0]], [2, ["_lrot"], [1, ["b4sR4lhohPC57Z37TkjD9N"]], [3, 0, 0, 0, 1]], [2, ["_euler"], [1, ["b4sR4lhohPC57Z37TkjD9N"]], [1, 0, 0, 0]], [2, ["_lpos"], [1, ["6bYCYowotHN67mWe0hdfwY"]], [1, 0, 0, 0]], [5, 11.999999999999986, ["_bottom"], [1, ["724Zel5/NIFZpNnmbW6lPy"]]], [2, ["_lpos"], [1, ["c6OmMA2CJLk5CO82RTF7Rn"]], [1, 59.86699999999999, 45.837, 0]], [5, 93.337, ["_bottom"], [1, ["f5ypDq0+9J9qTGNanj//2x"]]]]], 6]], [6, 0, {}, 3, [8, "b4sR4lhohPC57Z37TkjD9N", null, null, -73, [13, "85QCHpnf9PZoTWqjSsA9gj", 1, [[15, [1, ["b4sR4lhohPC57Z37TkjD9N"]], [[35, 32, 65, -72, [0, "3e1ALrRq9Iv5UPx+/mpqKG"]]]]], [[5, "propCell2", ["_name"], [1, ["b4sR4lhohPC57Z37TkjD9N"]]], [2, ["_lpos"], [1, ["b4sR4lhohPC57Z37TkjD9N"]], [1, 230, -0.41200000000000614, 0]], [2, ["_lrot"], [1, ["b4sR4lhohPC57Z37TkjD9N"]], [3, 0, 0, 0, 1]], [2, ["_euler"], [1, ["b4sR4lhohPC57Z37TkjD9N"]], [1, 0, 0, 0]]]], 8]], [6, 0, {}, 8, [8, "f05XX5jrpEOYwv6lCoUIav", null, null, -77, [21, "84KsJ50MFGmavshN6lMCYS", 1, [[10, "guideTitle", ["_name"], 12], [14, ["_lpos"], 12, [1, 0, 50.128, 0]], [14, ["_lrot"], 12, [3, 0, 0, 0, 1]], [14, ["_euler"], 12, [1, 0, 0, 0]], [10, 50, ["_fontSize"], -74], [2, ["_contentSize"], [1, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 18.359375, 50.4]], [10, 50, ["_actualFontSize"], -75], [10, "1", ["_string"], -76], [5, "GuideTitle", ["_dataID"], [1, ["807dKXf5tHrJtEdIFnHMo0"]]]]], 0]], [1, ["4a5atXBglJxJGAlAL90RE0"]], [19, "clockImg", 512, 33554432, 2, [[4, -78, [0, "5dEWt7SjtFtL8iERMVhBvQ"], [5, 45, 44]], [39, 0, -79, [0, "47qC3c7fZD5r2pGSmXcp4L"], 2]], [3, "3acaJLpeNOPqnMKf0QfTOJ", null, null, null, 1, 0], [1, -57.839, 0, 0]], [18, "t1设置", 33554432, 6, [[4, -80, [0, "159hQw2zhDX7Yc5zjf9y4I"], [5, 36, 46]], [17, 1, -81, [0, "0ebmopCZNLmobY08m49KhT"], 4]], [3, "e4O8ZtoANFcLBH9ccn/DfQ", null, null, null, 1, 0]], [6, 0, {}, 3, [8, "b4sR4lhohPC57Z37TkjD9N", null, null, -82, [21, "4cme2euwpBLp+itKNmJ48V", 1, [[5, "propCell1", ["_name"], [1, ["b4sR4lhohPC57Z37TkjD9N"]]], [2, ["_lpos"], [1, ["b4sR4lhohPC57Z37TkjD9N"]], [1, 0, -0.41200000000000614, 0]], [2, ["_lrot"], [1, ["b4sR4lhohPC57Z37TkjD9N"]], [3, 0, 0, 0, 1]], [2, ["_euler"], [1, ["b4sR4lhohPC57Z37TkjD9N"]], [1, 0, 0, 0]]]], 7]]], 0, [0, -1, 13, 0, -2, 18, 0, -3, 17, 0, -4, 22, 0, -5, 16, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 4, 0, -2, 6, 0, -3, 14, 0, -4, 3, 0, -5, 7, 0, -6, 5, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 20, 0, -2, 15, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, -1, 16, 0, -2, 22, 0, -3, 17, 0, 0, 4, 0, -1, 5, 0, 0, 4, 0, 0, 4, 0, -1, 8, 0, 0, 4, 0, -1, 6, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 8, 0, 0, 6, 0, 0, 6, 0, 0, 6, 0, -1, 21, 0, 0, 7, 0, 0, 7, 0, -1, 9, 0, -2, 10, 0, -3, 11, 0, 0, 8, 0, -1, 18, 0, -2, 13, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 11, 0, 0, 13, 0, 1, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 1, 16, 0, 0, 17, 0, 1, 17, 0, 4, 19, 0, 4, 19, 0, 4, 19, 0, 1, 18, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 1, 22, 0, 5, 1, 2, 6, 14, 82], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [2, 2, 3, 3, 3, 3, 2, 2, 2, 3], [1, 1, 2, 3, 4, 5, 0, 0, 0, 6]]