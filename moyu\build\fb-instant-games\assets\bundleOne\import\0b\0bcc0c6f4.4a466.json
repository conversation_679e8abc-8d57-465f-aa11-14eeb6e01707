[1, ["e5LUoqx3RAr41dA5QrbKMj", "abjcPYzetJ+LV2EH2h784l@a1855"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 3586321649, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 56928, "length": 12996, "count": 6498, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 56928, "count": 1186, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.6709607839584351, -0.4144606590270996, -0.2966964840888977], "maxPosition", 8, [1, 0.7362074255943298, 0.5165777206420898, 0.29581549763679504]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_15"], [3, "日式寿司_15", [[4, 1, -2, [0, "03zD7pDa1ImZ5Rgp/NcmxM"], [0], [5, true, true], 1], [6, 4, -3, [0, "60VsxLAOtFFZS2yIJU0zdJ"]], [7, 0.7035841047763824, 0, -4, [0, "7exDETfPxGyZ2tvj1RN8q4"], [1, 0.03262332081794739, 0.05105853080749512, -0.00044049322605133057]]], [8, "54/pOY8pdFB5Vd2PF3BHWK", null, null, null, -1, 0], [1, 1.998, 0, 5.029]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]