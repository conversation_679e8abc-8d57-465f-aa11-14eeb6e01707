[1, ["bdG8q6vX1KcbFDmXyII4Pk@f9941", "95TyOyGIxPI7V5IpoYIKgY", "beeU+ZykZPhqWlafd6Ip4F"], ["node", "targetInfo", "root", "asset", "_spriteFrame", "_backgroundImage", "_placeholder<PERSON><PERSON><PERSON>", "_textLabel", "data", "_parent"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_children", "_components", "_parent", "_lpos"], -1, 4, 2, 9, 1, 5], ["cc.Node", ["_name", "_layer", "_active", "_parent", "_components", "_prefab", "_lpos"], 0, 1, 12, 4, 5], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedComponents"], 2, 1, 9, 9], ["cc.UITransform", ["node", "__prefab", "_contentSize", "_anchorPoint"], 3, 1, 4, 5, 5], ["cc.Label", ["_string", "_horizontalAlign", "_actualFontSize", "_fontSize", "_overflow", "_enableWrapText", "node", "__prefab", "_color"], -3, 1, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.MountedComponentsInfo", ["targetInfo", "components"], 3, 4, 9], ["cc.TargetInfo", ["localID"], 2], ["cc.Widget", ["_alignFlags", "node", "__prefab"], 2, 1, 4], ["cc.CompPrefabInfo", ["fileId"], 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["4bff6xrIC5LtYScAfbMmEdR", ["_dataID", "node", "__prefab"], 2, 1, 4], ["cc.Sprite", ["_type", "_sizeMode", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.EditBox", ["_inputMode", "_maxLength", "node", "__prefab", "_textLabel", "_placeholder<PERSON><PERSON><PERSON>", "_backgroundImage"], 1, 1, 4, 1, 1, 6]], [[13, 0, 1, 2, 3], [12, 0, 2], [10, 0, 2], [14, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 5, 5], [3, 0, 1, 2, 1], [0, 2, 3, 7, 4, 3], [6, 0, 1, 2, 3, 4, 5, 4], [15, 0, 1, 2, 2], [16, 0, 1, 2, 3], [3, 0, 1, 2, 3, 1], [5, 0, 2], [0, 0, 1, 5, 6, 4, 3], [0, 0, 1, 7, 5, 6, 4, 8, 3], [0, 0, 1, 5, 6, 4, 8, 3], [1, 0, 2, 1, 3, 4, 5, 6, 4], [1, 0, 1, 3, 4, 5, 6, 3], [8, 0, 1, 2, 3, 4, 5, 4], [2, 0, 1, 3, 2, 2], [2, 0, 1, 2, 2], [9, 0, 1, 1], [11, 0, 1, 2, 2], [4, 0, 1, 2, 3, 4, 5, 6, 7, 7], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 7], [17, 0, 1, 2, 2], [18, 0, 1, 2, 3, 4, 3], [19, 0, 1, 2, 3, 4, 5, 6, 3]], [[11, "editBoxNode"], [12, "editBoxNode", ********, [-5, -6], [[5, -4, [1, "6cPaWujd9BCJYdUAjIP0JG"], [5, 376.578125, 60]]], [17, "f05XX5jrpEOYwv6lCoUIav", null, null, -3, 0, [-1, -2]]], [2, ["4a5atXBglJxJGAlAL90RE0"]], [14, "EditBox", ********, [-12, -13], [[5, -7, [1, "7ae2ep09FLHoObQPI3gzpL"], [5, 250, 29.255]], [25, 1, 0, -8, [1, "8dD7VWwtxHhbP6eNxeH7O8"], 2], [26, 6, 8, -11, [1, "916eopZOJCC6M9n2sVZLmV"], -10, -9, 3]], [4, "ddOfelTBZE1rggF7WlbAIs", null, null, null, 1, 0], [1, 0, -15.36, 0]], [6, 0, {}, 1, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -23, [18, "eaXK79jL1DLI2yR06q+Pwy", 1, [[20, [2, ["f05XX5jrpEOYwv6lCoUIav"]], [[21, 4, -22, [1, "64UMdg1p1FdJNCUr1LBy7h"]]]]], [[0, "inputName", ["_name"], -14], [3, ["_lpos"], -15, [1, -140, -11.***************, 0]], [3, ["_lrot"], -16, [3, 0, 0, 0, 1]], [3, ["_euler"], -17, [1, 0, 0, 0]], [0, 28, ["_fontSize"], -18], [8, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 96.578125, 37.8]], [0, 28, ["_actualFontSize"], -19], [9, "account", ["_dataID"], [2, ["807dKXf5tHrJtEdIFnHMo0"]]], [0, "account", ["_string"], -20], [0, 30, ["_lineHeight"], -21]]], 0]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [2, ["4a5atXBglJxJGAlAL90RE0"]], [13, "node", ********, 1, [-25, 3], [[5, -24, [1, "b8YzmdduxAio6kjkLI8vm8"], [5, 250, 60]]], [4, "69q9sWWqJA97+Y5p9TX9uk", null, null, null, 1, 0], [1, 63.2890625, 0, 0]], [2, ["f05XX5jrpEOYwv6lCoUIav"]], [16, "PLACEHOLDER_LABEL", ********, 3, [[[10, -26, [1, "65PABYbBFD+oKiMMB1/8CQ"], [5, 248, 29.255], [0, 0, 1]], -27, [24, "username_tips", -28, [1, "e34fdXI8pErIudzokGEhEC"]]], 4, 1, 4], [4, "afI3gpV49F66OArF8TDqq2", null, null, null, 1, 0], [1, -123, 14.6275, 0]], [6, 0, {}, 7, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -29, [19, "09sQr/0RFDc7MTGVUn+MyB", 1, [[0, "accountMsg", ["_name"], 8], [3, ["_lpos"], 8, [1, 0, 16.***************, 0]], [3, ["_lrot"], 8, [3, 0, 0, 0, 1]], [3, ["_euler"], 8, [1, 0, 0, 0]], [0, 15, ["_fontSize"], 2], [8, ["_contentSize"], [2, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 109.5703125, 27.72]], [0, 15, ["_actualFontSize"], 2], [0, false, ["_enableShadow"], 2], [0, false, ["_isUnderline"], 2], [9, "username_tips", ["_dataID"], [2, ["800tdHtlJM2IVdpueUwzRq"]]], [0, "forget_password", ["_string"], 2], [0, 22, ["_lineHeight"], 2]]], 1]], [15, "TEXT_LABEL", false, ********, 3, [[[10, -30, [1, "0bEfqhR8lOTZtnQhHBoMMX"], [5, 248, 29.255], [0, 0, 1]], -31], 4, 1], [4, "2bXdpKVOFLVZlwE9ooS0gn", null, null, null, 1, 0], [1, -123, 14.6275, 0]], [22, "", 0, 40, 20, 1, false, 11, [1, "71wbpeDSRJJIR6SalXggaP"]], [23, "username_tips", 0, 20, 20, 1, false, 9, [1, "28jx8A8HJIlpFtWwyDq5Bf"], [4, **********]]], 0, [0, -1, 10, 0, -2, 4, 0, 2, 1, 0, 0, 1, 0, -1, 4, 0, -2, 7, 0, 0, 3, 0, 0, 3, 0, 6, 13, 0, 7, 12, 0, 0, 3, 0, -1, 11, 0, -2, 9, 0, 1, 5, 0, 1, 5, 0, 1, 5, 0, 1, 5, 0, 1, 6, 0, 1, 6, 0, 1, 6, 0, 1, 6, 0, 0, 4, 0, 2, 4, 0, 0, 7, 0, -1, 10, 0, 0, 9, 0, -2, 13, 0, 0, 9, 0, 2, 10, 0, 0, 11, 0, -2, 12, 0, 8, 1, 3, 9, 7, 31], [0, 0, 0, 0], [3, 3, 4, 5], [1, 2, 0, 0]]