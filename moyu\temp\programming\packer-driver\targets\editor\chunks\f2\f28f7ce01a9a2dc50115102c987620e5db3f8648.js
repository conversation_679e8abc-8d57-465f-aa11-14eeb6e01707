System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, oops, ecs, LanguageData, VM, RecordType, SceneItemType, GameStorageConfig, DataManager, GameEvent, smc, RoleModelComp, _dec, _class, _crd, Role;

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfecs(extras) {
    _reporterNs.report("ecs", "../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLanguageData(extras) {
    _reporterNs.report("LanguageData", "../../../../extensions/oops-plugin-framework/assets/libs/gui/language/LanguageData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfVM(extras) {
    _reporterNs.report("VM", "../../../../extensions/oops-plugin-framework/assets/libs/model-view/ViewModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPropType(extras) {
    _reporterNs.report("PropType", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRecordType(extras) {
    _reporterNs.report("RecordType", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRecordTypeData(extras) {
    _reporterNs.report("RecordTypeData", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSceneItemType(extras) {
    _reporterNs.report("SceneItemType", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUserGameData(extras) {
    _reporterNs.report("UserGameData", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameStorageConfig(extras) {
    _reporterNs.report("GameStorageConfig", "../common/config/GameStorageConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataManager(extras) {
    _reporterNs.report("DataManager", "../common/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEvent(extras) {
    _reporterNs.report("GameEvent", "../common/Enum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "../common/SingletonModuleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRoleModelComp(extras) {
    _reporterNs.report("RoleModelComp", "./model/RoleModelComp", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      ecs = _unresolved_3.ecs;
    }, function (_unresolved_4) {
      LanguageData = _unresolved_4.LanguageData;
    }, function (_unresolved_5) {
      VM = _unresolved_5.VM;
    }, function (_unresolved_6) {
      RecordType = _unresolved_6.RecordType;
      SceneItemType = _unresolved_6.SceneItemType;
    }, function (_unresolved_7) {
      GameStorageConfig = _unresolved_7.GameStorageConfig;
    }, function (_unresolved_8) {
      DataManager = _unresolved_8.DataManager;
    }, function (_unresolved_9) {
      GameEvent = _unresolved_9.GameEvent;
    }, function (_unresolved_10) {
      smc = _unresolved_10.smc;
    }, function (_unresolved_11) {
      RoleModelComp = _unresolved_11.RoleModelComp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c4784kzvHBHJaIs0pmyr2aI", "Role", undefined);

      /**
       * 角色管理器 - 重构版本
       * 职责：
       * 1. 用户数据的加载、更新和管理
       * 2. 道具系统的操作和验证
       * 3. 游戏进度的管理
       * 4. ViewModel的数据绑定
       */
      _export("Role", Role = (_dec = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
        error: Error()
      }), ecs) : ecs).register('Role'), _dec(_class = class Role extends (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
        error: Error()
      }), ecs) : ecs).Entity {
        constructor(...args) {
          super(...args);
          this.RoleModel = void 0;
          this.dataManager = void 0;
        }

        // ==================== 初始化 ====================
        init() {
          this.add(_crd && RoleModelComp === void 0 ? (_reportPossibleCrUseOfRoleModelComp({
            error: Error()
          }), RoleModelComp) : RoleModelComp);
          this.dataManager = (_crd && DataManager === void 0 ? (_reportPossibleCrUseOfDataManager({
            error: Error()
          }), DataManager) : DataManager).getInstance();
        }

        destroy() {
          this.removeFromViewModel();
          this.remove(_crd && RoleModelComp === void 0 ? (_reportPossibleCrUseOfRoleModelComp({
            error: Error()
          }), RoleModelComp) : RoleModelComp);
        } // ==================== 数据加载 ====================

        /**
         * 加载用户数据
         * @returns 是否加载成功
         */


        async loadData() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🔄 开始加载用户数据...');

          if (!this.validateNetworkConnection()) {
            return false;
          }

          try {
            const response = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).net.hcGame.callApi('UserInfo', {});

            if (response.isSucc) {
              await this.waitForUserDataInitialization();
              return true;
            } else {
              this.handleAPIError('UserInfo', response.err);
              return false;
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 用户数据加载异常:', error);
            return false;
          }
        }
        /**
         * 更新用户数据（由DataManager调用）
         */


        updateUserData(newData) {
          const isFirstInitialization = !this.hasUserData(); // 确保数据结构完整

          this.ensureDataIntegrity(newData); // 合并数据

          if (this.RoleModel.userGameData) {
            this.mergeUserData(this.RoleModel.userGameData, newData);
          } else {
            this.RoleModel.userGameData = newData;
          } // 更新ViewModel


          this.updateViewModel(); // 首次初始化触发事件

          if (isFirstInitialization) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🎉 用户数据首次初始化完成');
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
              error: Error()
            }), GameEvent) : GameEvent).UserDataInitialized, this.RoleModel.userGameData);
          }
        } // ==================== 道具系统 ====================

        /**
         * 获取道具数据
         */


        getPropData(propType) {
          var _this$RoleModel$userG;

          if (!this.validateUserData()) {
            return this.createDefaultPropData(propType);
          }

          const propData = (_this$RoleModel$userG = this.RoleModel.userGameData.propUseData) == null ? void 0 : _this$RoleModel$userG[propType];

          if (!propData) {
            // 创建默认道具数据
            const defaultData = this.createDefaultPropData(propType);
            this.RoleModel.userGameData.propUseData = this.RoleModel.userGameData.propUseData || {};
            this.RoleModel.userGameData.propUseData[propType] = defaultData;
            return defaultData;
          }

          return propData;
        }
        /**
         * 获取道具数据（别名方法，兼容现有代码）
         */


        getPropsDataByType(propType) {
          return this.getPropData(propType);
        }
        /**
         * 尝试消耗道具（检查数量并显示提示）
         * @param args 道具参数（包含类型和数量）
         * @param showToast 是否显示提示
         * @param failureMessageKey 失败时的消息键
         * @returns 是否可以消耗
         */


        tryCostProp(args, showToast = true, failureMessageKey) {
          if (!this.validateUserData()) {
            if (showToast) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).gui.toast('用户数据未初始化');
            }

            return false;
          }

          const absAmount = Math.abs(args.amount);
          const canUse = this.canUseProp(args.propType, absAmount);

          if (!canUse && showToast && failureMessageKey) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast((_crd && LanguageData === void 0 ? (_reportPossibleCrUseOfLanguageData({
              error: Error()
            }), LanguageData) : LanguageData).getLangByID(failureMessageKey) || failureMessageKey);
          }

          return canUse;
        }
        /**
         * 更新道具数据（别名方法，兼容现有代码）
         */


        async updatePropData(args) {
          return await this.updateProp(args.propType, args.amount, args.reason);
        }
        /**
         * 检查道具是否足够
         */


        canUseProp(propType, amount) {
          const propData = this.getPropData(propType);
          return propData.amount >= Math.abs(amount);
        }
        /**
         * 更新道具数量
         */


        async updateProp(propType, amount, reason) {
          // 消耗操作前检查数量
          if (amount < 0 && !this.canUseProp(propType, Math.abs(amount))) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast((_crd && LanguageData === void 0 ? (_reportPossibleCrUseOfLanguageData({
              error: Error()
            }), LanguageData) : LanguageData).getLangByID('UseLimitsDaily'));
            return false;
          }

          try {
            const response = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).net.hcGame.callApi('UpdateProp', {
              propType,
              amount,
              reason: reason || 'player_action'
            });

            if (response.isSucc) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness(`✅ 道具更新成功: ${propType} ${amount > 0 ? '+' : ''}${amount}`); // 触发道具使用事件

              if (amount < 0) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
                  error: Error()
                }), GameEvent) : GameEvent).UseProp, {
                  propType,
                  amount
                });
              }

              return true;
            } else {
              this.handleAPIError('UpdateProp', response.err);
              return false;
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 道具更新异常:', error);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast('网络错误，请重试');
            return false;
          }
        }
        /**
         * 批量更新道具
         */


        async batchUpdateProps(updates) {
          for (const update of updates) {
            const success = await this.updateProp(update.propType, update.amount, update.reason);

            if (!success) {
              return false;
            }
          }

          return true;
        } // ==================== 游戏进度 ====================

        /**
         * 获取当前通关进度
         */


        getGameProgress() {
          if (!this.validateUserData()) {
            return 0;
          }

          return this.RoleModel.userGameData.index || 0;
        }
        /**
         * 获取已通过的关卡索引（别名方法，兼容现有代码）
         */


        getPassIndex() {
          return this.getGameProgress();
        }
        /**
         * 获取下一关卡索引（循环通关）
         */


        getNextLevelIndex(currentProgress) {
          const progress = currentProgress != null ? currentProgress : this.getGameProgress();
          const nextLevel = (progress + 1) % (_crd && SceneItemType === void 0 ? (_reportPossibleCrUseOfSceneItemType({
            error: Error()
          }), SceneItemType) : SceneItemType).Max;
          return nextLevel === 0 ? 1 : nextLevel;
        }
        /**
         * 更新游戏通关进度
         */


        async updateGameProgress(newIndex, isGM = false) {
          const targetIndex = newIndex != null ? newIndex : this.getGameProgress() + 1;

          try {
            const response = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).net.hcGame.callApi('UpdateProgress', {
              index: targetIndex,
              isGm: isGM
            });

            if (response.isSucc) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness(`✅ 游戏进度更新成功: ${targetIndex}`); // 触发通关事件

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
                error: Error()
              }), GameEvent) : GameEvent).GamePass, {
                oldIndex: this.getGameProgress(),
                newIndex: targetIndex,
                isGm: isGM
              });
              return true;
            } else {
              this.handleAPIError('UpdateProgress', response.err);
              return false;
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 游戏进度更新异常:', error);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast('网络错误，请重试');
            return false;
          }
        } // ==================== 新手引导 ====================

        /**
         * 检查是否为新玩家
         */


        isNewPlayer() {
          // 优先使用服务端数据
          if (this.hasUserData()) {
            var _this$RoleModel$userG2;

            return (_this$RoleModel$userG2 = this.RoleModel.userGameData.isNewPlayer) != null ? _this$RoleModel$userG2 : true;
          } // 备用方案：检查本地存储


          const localRecord = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).storage.getJson((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
            error: Error()
          }), GameStorageConfig) : GameStorageConfig).UserDumpKey, 0);
          return !localRecord || String(localRecord) === '0';
        }
        /**
         * 完成新手引导
         */


        async completeNewPlayerGuide() {
          if (!this.isNewPlayer()) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('✅ 用户已完成新手引导');
            return true;
          }

          try {
            const response = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).net.hcGame.callApi('GameUpdateSimpleData', {
              isNewPlayer: false
            });

            if (response.isSucc) {
              // 🎯 立即更新本地数据，确保状态同步
              if (this.RoleModel && this.RoleModel.userGameData) {
                this.RoleModel.userGameData.isNewPlayer = false;
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('🔄 本地新手状态已更新为false');
              }

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ 新手引导完成');
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
                error: Error()
              }), GameEvent) : GameEvent).BasicInfoUpdate, {
                isNewPlayer: false
              });
              return true;
            } else {
              this.handleAPIError('GameUpdateSimpleData', response.err);
              return false;
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 新手状态更新异常:', error);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast('网络错误，请重试');
            return false;
          }
        } // ==================== 记录数据 ====================

        /**
         * 获取指定日期的记录数据
         */


        getRecordData(recordType, dateString) {
          var _this$RoleModel$userG3;

          if (!this.validateUserData()) {
            return null;
          }

          const targetDate = dateString || new Date().toDateString();
          const recordData = (_this$RoleModel$userG3 = this.RoleModel.userGameData.recordData) == null ? void 0 : _this$RoleModel$userG3[targetDate];
          return (recordData == null ? void 0 : recordData[recordType]) || null;
        }
        /**
         * 🔧 新增：获取指定关卡的今日挑战次数
         * @param levelId 关卡ID
         * @param dateString 可选的日期字符串，默认为今日
         * @returns 该关卡的挑战次数
         */


        getLevelChallengeCount(levelId, dateString) {
          var _recordData$levelDeta;

          const recordData = this.getRecordData((_crd && RecordType === void 0 ? (_reportPossibleCrUseOfRecordType({
            error: Error()
          }), RecordType) : RecordType).Level, dateString);

          if (!(recordData != null && recordData.levelDetails)) {
            return 0;
          }

          const levelKey = `level_${levelId}`;
          return ((_recordData$levelDeta = recordData.levelDetails[levelKey]) == null ? void 0 : _recordData$levelDeta.attempts) || 0;
        }
        /**
         * 🔧 新增：获取当前关卡的今日挑战次数
         * @param dateString 可选的日期字符串，默认为今日
         * @returns 当前关卡的挑战次数
         */


        getCurrentLevelChallengeCount(dateString) {
          const currentLevelId = this.getNextLevelIndex(); // 获取当前要挑战的关卡

          return this.getLevelChallengeCount(currentLevelId, dateString);
        } // ==================== 工具方法 ====================

        /**
         * 获取完整的用户游戏数据
         */


        getUserGameData() {
          if (!this.validateUserData()) {
            throw new Error('用户数据尚未初始化，请等待登录完成');
          }

          return this.RoleModel.userGameData;
        } // ==================== 私有方法 ====================

        /**
         * 验证用户数据是否可用
         */


        validateUserData() {
          var _this$RoleModel;

          return !!((_this$RoleModel = this.RoleModel) != null && _this$RoleModel.userGameData);
        }
        /**
         * 检查是否有用户数据
         */


        hasUserData() {
          return this.validateUserData();
        }
        /**
         * 验证网络连接
         */


        validateNetworkConnection() {
          var _net;

          if (!((_net = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).net) != null && _net.hcGame)) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 网络连接未初始化');
            return false;
          }

          return true;
        }
        /**
         * 等待用户数据初始化完成 - 优化版本
         */


        async waitForUserDataInitialization() {
          const maxRetries = 30; // 🚀 减少最大重试次数

          let retryCount = 0;

          while (retryCount < maxRetries) {
            if (this.hasUserData()) {
              return;
            } // 🚀 使用更短的等待时间，但增加检查频率


            await new Promise(resolve => setTimeout(resolve, 30));
            retryCount++;

            if (retryCount % 5 === 0) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness(`⏳ 等待用户数据初始化... ${retryCount}/${maxRetries}`);
            }
          } // 🚀 超时后不抛出错误，而是记录警告并继续


          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logWarn('⚠️ 用户数据初始化超时，但继续执行');
        }
        /**
         * 确保数据结构完整性
         */


        ensureDataIntegrity(data) {
          var _data$isNewPlayer;

          data.key = data.key || 0;
          data.userName = data.userName || 'Player';
          data.index = data.index || 0;
          data.isNewPlayer = (_data$isNewPlayer = data.isNewPlayer) != null ? _data$isNewPlayer : true;
          data.propUseData = data.propUseData || {};
          data.recordData = data.recordData || {};
        }
        /**
         * 深度合并用户数据
         */


        mergeUserData(target, source) {
          Object.keys(source).forEach(key => {
            const sourceValue = source[key];
            const targetValue = target[key];

            if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
              if (targetValue && typeof targetValue === 'object') {
                this.mergeUserData(targetValue, sourceValue);
              } else {
                target[key] = sourceValue;
              }
            } else {
              target[key] = sourceValue;
            }
          });
        }
        /**
         * 创建默认道具数据
         */


        createDefaultPropData(propType) {
          return {
            amount: 0,
            propType,
            propId: propType,
            desc: `道具${propType}`,
            getTime: new Date(),
            lastResetTime: new Date(),
            lastUpdateTime: new Date()
          };
        }
        /**
         * 更新ViewModel
         */


        updateViewModel() {
          if (!this.hasUserData()) {
            return;
          }

          this.removeFromViewModel();
          const viewModelData = {
            userId: this.RoleModel.userGameData.key,
            userName: this.RoleModel.userGameData.userName,
            level: this.getGameProgress(),
            isNewPlayer: this.RoleModel.userGameData.isNewPlayer,
            index: this.RoleModel.userGameData.index,
            propUseData: this.RoleModel.userGameData.propUseData,
            // 扩展其他需要的数据
            ...this.RoleModel.userGameData
          };
          (_crd && VM === void 0 ? (_reportPossibleCrUseOfVM({
            error: Error()
          }), VM) : VM).add(viewModelData, 'role');
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎯 ViewModel已更新');
        }
        /**
         * 从ViewModel移除数据
         */


        removeFromViewModel() {
          (_crd && VM === void 0 ? (_reportPossibleCrUseOfVM({
            error: Error()
          }), VM) : VM).remove('role');
        }
        /**
         * 统一的API错误处理
         */


        handleAPIError(apiName, error) {
          var _error$code;

          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logError(`❌ ${apiName} API失败:`, error);
          const errorMessage = (error == null ? void 0 : error.message) || (error == null || (_error$code = error.code) == null ? void 0 : _error$code.toString()) || '操作失败';
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).gui.toast(errorMessage);
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f28f7ce01a9a2dc50115102c987620e5db3f8648.js.map