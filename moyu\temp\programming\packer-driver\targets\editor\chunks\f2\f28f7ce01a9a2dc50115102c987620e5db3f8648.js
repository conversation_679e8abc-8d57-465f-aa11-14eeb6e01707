System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, oops, ecs, LanguageData, VM, GameConst, Platform, ShareConfig, PropType, RecordType, SceneItemType, GameStorageConfig, GameEvent, smc, RoleModelComp, _dec, _class, _crd, Role;

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfecs(extras) {
    _reporterNs.report("ecs", "../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLanguageData(extras) {
    _reporterNs.report("LanguageData", "../../../../extensions/oops-plugin-framework/assets/libs/gui/language/LanguageData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfVM(extras) {
    _reporterNs.report("VM", "../../../../extensions/oops-plugin-framework/assets/libs/model-view/ViewModel", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../tsrpc/models/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlatform(extras) {
    _reporterNs.report("Platform", "../../tsrpc/models/ShareConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfShareConfig(extras) {
    _reporterNs.report("ShareConfig", "../../tsrpc/models/ShareConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPropType(extras) {
    _reporterNs.report("PropType", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRecordType(extras) {
    _reporterNs.report("RecordType", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRecordTypeData(extras) {
    _reporterNs.report("RecordTypeData", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSceneItemType(extras) {
    _reporterNs.report("SceneItemType", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUserGameData(extras) {
    _reporterNs.report("UserGameData", "../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameStorageConfig(extras) {
    _reporterNs.report("GameStorageConfig", "../common/config/GameStorageConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEvent(extras) {
    _reporterNs.report("GameEvent", "../common/Enum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "../common/SingletonModuleComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRoleModelComp(extras) {
    _reporterNs.report("RoleModelComp", "./model/RoleModelComp", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      ecs = _unresolved_3.ecs;
    }, function (_unresolved_4) {
      LanguageData = _unresolved_4.LanguageData;
    }, function (_unresolved_5) {
      VM = _unresolved_5.VM;
    }, function (_unresolved_6) {
      GameConst = _unresolved_6.GameConst;
    }, function (_unresolved_7) {
      Platform = _unresolved_7.Platform;
      ShareConfig = _unresolved_7.ShareConfig;
    }, function (_unresolved_8) {
      PropType = _unresolved_8.PropType;
      RecordType = _unresolved_8.RecordType;
      SceneItemType = _unresolved_8.SceneItemType;
    }, function (_unresolved_9) {
      GameStorageConfig = _unresolved_9.GameStorageConfig;
    }, function (_unresolved_10) {
      GameEvent = _unresolved_10.GameEvent;
    }, function (_unresolved_11) {
      smc = _unresolved_11.smc;
    }, function (_unresolved_12) {
      RoleModelComp = _unresolved_12.RoleModelComp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c4784kzvHBHJaIs0pmyr2aI", "Role", undefined);

      /**
       * 角色管理器 - 重构版本
       * 职责：
       * 1. 用户数据的加载、更新和管理
       * 2. 道具系统的操作和验证
       * 3. 游戏进度的管理
       * 4. ViewModel的数据绑定
       */
      _export("Role", Role = (_dec = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
        error: Error()
      }), ecs) : ecs).register('Role'), _dec(_class = class Role extends (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
        error: Error()
      }), ecs) : ecs).Entity {
        constructor(...args) {
          super(...args);
          this.RoleModel = void 0;
          // 🔄 待同步的道具更新队列
          this.pendingPropUpdates = [];
        }

        // ==================== 初始化 ====================
        init() {
          this.add(_crd && RoleModelComp === void 0 ? (_reportPossibleCrUseOfRoleModelComp({
            error: Error()
          }), RoleModelComp) : RoleModelComp);
        }

        destroy() {
          this.removeFromViewModel();
          this.remove(_crd && RoleModelComp === void 0 ? (_reportPossibleCrUseOfRoleModelComp({
            error: Error()
          }), RoleModelComp) : RoleModelComp);
        } // ==================== 数据加载 ====================

        /**
         * 🚀 快速初始化用户数据 - 统一入口
         */


        async quickInitialize() {
          try {
            if (this.isNewPlayer()) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🚀 启用新手快速启动模式');
              await this.initializeNewPlayerData(); // 🔄 在后台继续完整的用户数据加载

              this.loadDataInBackground();
            } else {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🚀 老玩家完整登录'); // 🔄 传统模式：完整加载用户数据

              await this.loadData();
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 快速用户数据初始化失败:', error); // 失败时创建基础数据，确保游戏可以启动

            await this.initializeNewPlayerData();
          }
        }
        /**
         * 加载用户数据
         * @returns 是否加载成功
         */


        async loadData() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🔄 开始加载用户数据...');

          if (!this.validateNetworkConnection()) {
            return false;
          }

          try {
            const response = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).net.hcGame.callApi('UserInfo', {});

            if (response.isSucc) {
              // UserInfo API成功后，数据应该已经通过DataManager更新了
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ 用户数据加载成功');
              return true;
            } else {
              this.handleAPIError('UserInfo', response.err);
              return false;
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 用户数据加载异常:', error);
            return false;
          }
        }
        /**
         * 更新用户数据（由DataManager调用）
         */


        updateUserData(newData) {
          const isFirstInitialization = !this.hasUserData(); // 确保数据结构完整

          this.ensureDataIntegrity(newData); // 合并数据

          if (this.RoleModel.userGameData) {
            this.mergeUserData(this.RoleModel.userGameData, newData);
          } else {
            this.RoleModel.userGameData = newData;
          } // 更新ViewModel


          this.updateViewModel(); // 首次初始化触发事件

          if (isFirstInitialization) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🎉 用户数据首次初始化完成');
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
              error: Error()
            }), GameEvent) : GameEvent).UserDataInitialized, this.RoleModel.userGameData);
          }
        } // ==================== 道具系统 ====================

        /**
         * 获取道具数据
         */


        getPropData(propType) {
          var _this$RoleModel$userG;

          if (!this.hasUserData()) {
            return this.createDefaultPropData(propType);
          }

          const propData = (_this$RoleModel$userG = this.RoleModel.userGameData.propUseData) == null ? void 0 : _this$RoleModel$userG[propType];

          if (!propData) {
            // 创建默认道具数据
            const defaultData = this.createDefaultPropData(propType);
            this.RoleModel.userGameData.propUseData = this.RoleModel.userGameData.propUseData || {};
            this.RoleModel.userGameData.propUseData[propType] = defaultData;
            return defaultData;
          }

          return propData;
        }
        /**
         * 获取道具数据（别名方法，兼容现有代码）
         */


        getPropsDataByType(propType) {
          return this.getPropData(propType);
        }
        /**
         * 尝试消耗道具（检查数量并显示提示）
         * @param args 道具参数（包含类型和数量）
         * @param showToast 是否显示提示
         * @param failureMessageKey 失败时的消息键
         * @returns 是否可以消耗
         */


        tryCostProp(args, showToast = true, failureMessageKey) {
          if (!this.hasUserData()) {
            if (showToast) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).gui.toast('用户数据未初始化');
            }

            return false;
          }

          const absAmount = Math.abs(args.amount);
          const canUse = this.canUseProp(args.propType, absAmount);

          if (!canUse && showToast && failureMessageKey) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast((_crd && LanguageData === void 0 ? (_reportPossibleCrUseOfLanguageData({
              error: Error()
            }), LanguageData) : LanguageData).getLangByID(failureMessageKey) || failureMessageKey);
          }

          return canUse;
        }
        /**
         * 更新道具数据（别名方法，兼容现有代码）
         */


        async updatePropData(args) {
          return await this.updateProp(args.propType, args.amount, args.reason);
        }
        /**
         * 检查道具是否足够
         */


        canUseProp(propType, amount) {
          const propData = this.getPropData(propType);
          return propData.amount >= Math.abs(amount);
        }
        /**
         * 更新道具数量
         */


        async updateProp(propType, amount, reason) {
          var _this$RoleModel;

          // 消耗操作前检查数量
          if (amount < 0 && !this.canUseProp(propType, Math.abs(amount))) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast((_crd && LanguageData === void 0 ? (_reportPossibleCrUseOfLanguageData({
              error: Error()
            }), LanguageData) : LanguageData).getLangByID('UseLimitsDaily'));
            return false;
          } // 🔍 检查是否为临时数据状态


          const userData = (_this$RoleModel = this.RoleModel) == null ? void 0 : _this$RoleModel.userGameData;

          if (userData != null && userData.isTemporaryData) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔄 检测到临时数据状态，本地更新道具数量'); // 在临时状态下，直接更新本地数据

            const propData = this.getPropsDataByType(propType);

            if (propData) {
              propData.amount += amount;
              propData.lastUpdateTime = new Date();
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness(`✅ 道具本地更新: ${propType} ${amount > 0 ? '+' : ''}${amount} (临时)`); // 触发道具使用事件

              if (amount < 0) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
                  error: Error()
                }), GameEvent) : GameEvent).UseProp, {
                  propType,
                  amount
                });
              } // 等后台登录完成后同步到服务器


              this.queuePropUpdateForSync(propType, amount, reason);
              return true;
            }
          }

          try {
            const response = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).net.hcGame.callApi('UpdateProp', {
              propType,
              amount,
              reason: reason || 'player_action'
            });

            if (response.isSucc) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness(`✅ 道具更新成功: ${propType} ${amount > 0 ? '+' : ''}${amount}`); // 触发道具使用事件

              if (amount < 0) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
                  error: Error()
                }), GameEvent) : GameEvent).UseProp, {
                  propType,
                  amount
                });
              }

              return true;
            } else {
              this.handleAPIError('UpdateProp', response.err);
              return false;
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 道具更新异常:', error);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast('网络错误，请重试');
            return false;
          }
        }
        /**
         * 批量更新道具
         */


        async batchUpdateProps(updates) {
          for (const update of updates) {
            const success = await this.updateProp(update.propType, update.amount, update.reason);

            if (!success) {
              return false;
            }
          }

          return true;
        } // ==================== 游戏进度 ====================

        /**
         * 获取当前通关进度
         */


        getGameProgress() {
          if (!this.hasUserData()) {
            return 0;
          }

          return this.RoleModel.userGameData.index || 0;
        }
        /**
         * 获取已通过的关卡索引（别名方法，兼容现有代码）
         */


        getPassIndex() {
          return this.getGameProgress();
        }
        /**
         * 获取下一关卡索引（循环通关）
         */


        getNextLevelIndex(currentProgress) {
          const progress = currentProgress != null ? currentProgress : this.getGameProgress();
          const nextLevel = (progress + 1) % (_crd && SceneItemType === void 0 ? (_reportPossibleCrUseOfSceneItemType({
            error: Error()
          }), SceneItemType) : SceneItemType).Max;
          return nextLevel === 0 ? 1 : nextLevel;
        }
        /**
         * 更新游戏通关进度
         */


        async updateGameProgress(newIndex, isGM = false) {
          const targetIndex = newIndex != null ? newIndex : this.getGameProgress() + 1;

          try {
            const response = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).net.hcGame.callApi('UpdateProgress', {
              index: targetIndex,
              isGm: isGM
            });

            if (response.isSucc) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness(`✅ 游戏进度更新成功: ${targetIndex}`); // 触发通关事件

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
                error: Error()
              }), GameEvent) : GameEvent).GamePass, {
                oldIndex: this.getGameProgress(),
                newIndex: targetIndex,
                isGm: isGM
              });
              return true;
            } else {
              this.handleAPIError('UpdateProgress', response.err);
              return false;
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 游戏进度更新异常:', error);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast('网络错误，请重试');
            return false;
          }
        } // ==================== 新手引导 ====================

        /**
         * 检查是否为新玩家 - 统一判断逻辑
         */


        isNewPlayer() {
          // 1. 优先使用服务端数据
          if (this.hasUserData()) {
            var _this$RoleModel$userG2;

            return (_this$RoleModel$userG2 = this.RoleModel.userGameData.isNewPlayer) != null ? _this$RoleModel$userG2 : true;
          } // 2. 检查本地存储的登录记录


          const hasLoginRecord = !!(_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).storage.get((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
            error: Error()
          }), GameStorageConfig) : GameStorageConfig).SSOToken);

          if (hasLoginRecord) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔍 检测到登录记录，判定为老玩家');
            return false;
          } // 3. 检查是否有其他用户数据痕迹


          const userDumpKey = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).storage.getJson((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
            error: Error()
          }), GameStorageConfig) : GameStorageConfig).UserDumpKey, null);

          if (userDumpKey && String(userDumpKey) !== '0') {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔍 检测到用户数据痕迹，判定为老玩家');
            return false;
          } // 4. 默认判定为新手


          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🆕 无任何用户数据痕迹，判定为新手玩家');
          return true;
        }
        /**
         * 完成新手引导
         */


        async completeNewPlayerGuide() {
          var _this$RoleModel2;

          if (!this.isNewPlayer()) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('✅ 用户已完成新手引导');
            return true;
          } // 🔍 检查是否为临时数据状态


          const userData = (_this$RoleModel2 = this.RoleModel) == null ? void 0 : _this$RoleModel2.userGameData;

          if (userData != null && userData.isTemporaryData) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔄 检测到临时数据状态，延迟新手引导完成'); // 先更新本地状态，等后台登录完成后再同步到服务器

            if (this.RoleModel && this.RoleModel.userGameData) {
              this.RoleModel.userGameData.isNewPlayer = false;
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🔄 本地新手状态已更新为false（临时）');
            } // 监听后台登录完成事件，然后同步状态


            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).message.once('UserDataLoaded', async () => {
              await this.syncNewPlayerStatusToServer();
            }, this);
            return true;
          }

          return await this.syncNewPlayerStatusToServer();
        }
        /**
         * 🔄 同步新手状态到服务器
         */


        async syncNewPlayerStatusToServer() {
          try {
            const response = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).net.hcGame.callApi('GameUpdateSimpleData', {
              isNewPlayer: false
            });

            if (response.isSucc) {
              // 🎯 立即更新本地数据，确保状态同步
              if (this.RoleModel && this.RoleModel.userGameData) {
                this.RoleModel.userGameData.isNewPlayer = false;
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('🔄 本地新手状态已更新为false');
              }

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ 新手引导完成');
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).message.dispatchEvent((_crd && GameEvent === void 0 ? (_reportPossibleCrUseOfGameEvent({
                error: Error()
              }), GameEvent) : GameEvent).BasicInfoUpdate, {
                isNewPlayer: false
              });
              return true;
            } else {
              this.handleAPIError('GameUpdateSimpleData', response.err);
              return false;
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 新手状态更新异常:', error);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast('网络错误，请重试');
            return false;
          }
        }
        /**
         * 🔄 将道具更新加入同步队列
         */


        queuePropUpdateForSync(propType, amount, reason) {
          this.pendingPropUpdates.push({
            propType,
            amount,
            reason
          });
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness(`📝 道具更新已加入同步队列: ${propType} ${amount}`); // 监听后台登录完成事件

          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).message.once('UserDataLoaded', async () => {
            await this.syncPendingPropUpdates();
          }, this);
        }
        /**
         * 🔄 同步待处理的道具更新到服务器
         */


        async syncPendingPropUpdates() {
          if (this.pendingPropUpdates.length === 0) {
            return;
          }

          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness(`🔄 开始同步 ${this.pendingPropUpdates.length} 个道具更新`); // 复制队列并清空原队列

          const updates = [...this.pendingPropUpdates];
          this.pendingPropUpdates = [];

          for (const update of updates) {
            try {
              const response = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).net.hcGame.callApi('UpdateProp', {
                propType: update.propType,
                amount: update.amount,
                reason: update.reason || 'delayed_sync'
              });

              if (response.isSucc) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness(`✅ 道具同步成功: ${update.propType} ${update.amount > 0 ? '+' : ''}${update.amount}`);
              } else {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logWarn(`⚠️ 道具同步失败: ${update.propType}`, response.err);
              }
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError(`❌ 道具同步异常: ${update.propType}`, error);
            }
          }

          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('✅ 道具更新同步完成');
        } // ==================== 新手数据初始化 ====================

        /**
         * 🚀 初始化新手玩家数据
         */


        async initializeNewPlayerData() {
          try {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🚀 开始创建默认的新手玩家数据...'); // 创建完整的基础用户数据结构

            const currentTime = new Date();
            const basicUserData = {
              // 基础标识
              key: 0,
              guuid: '',
              googleUuid: '',
              facebookId: '',
              userName: '',
              nickName: '',
              sex: 1,
              // SexType.None
              createtime: currentTime,
              openid: '',
              platform: 'web',
              platformType: 'web',
              avatar: '',
              avatarId: 0,
              countryCode: 'Other',
              // 游戏进度
              passTimes: 0,
              index: 0,
              currCountryPassTimes: 0,
              lastChangeCountryTime: currentTime,
              selfCountryRank: 0,
              // 道具和记录数据
              propUseData: this.createDefaultProps(),
              recordData: {},
              // 新手和游客状态
              isNewPlayer: true,
              isGuest: true,
              lastStep: 0,
              // 🔄 标记为临时数据
              isTemporaryData: true
            }; // 🎯 设置用户数据

            this.updateUserData(basicUserData);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('✅ 新手玩家数据初始化完成');
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 新手玩家数据初始化失败:', error);
          }
        }
        /**
         * 🎁 创建新手玩家默认道具
         */


        createDefaultProps() {
          const currentTime = new Date();
          const newPlayerProps = {}; // 🎯 为每种道具类型创建默认数据

          const propTypes = [(_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
            error: Error()
          }), PropType) : PropType).PropsMoveOut, (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
            error: Error()
          }), PropType) : PropType).PropsTips, (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
            error: Error()
          }), PropType) : PropType).PropsReShuffle, (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
            error: Error()
          }), PropType) : PropType).PropsDayLeftCount, (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
            error: Error()
          }), PropType) : PropType).PropsRevive, (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
            error: Error()
          }), PropType) : PropType).PropsExp, (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
            error: Error()
          }), PropType) : PropType).PropsCoin];
          propTypes.forEach(propType => {
            newPlayerProps[propType] = {
              propType: propType,
              amount: this.getNewPlayerDefaultAmount(propType),
              lastUpdateTime: currentTime
            };
          });
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎁 新手默认道具创建完成', {
            propCount: Object.keys(newPlayerProps).length
          });
          return newPlayerProps;
        }
        /**
         * 🔄 后台加载完整用户数据
         */


        loadDataInBackground() {
          // 使用setTimeout确保不阻塞当前流程
          setTimeout(async () => {
            try {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🔄 开始后台加载完整用户数据...'); // 🚀 强制执行完整的登录流程，不跳过

              await this.forceCompleteUserDataLoad();
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ 后台用户数据加载完成'); // 触发数据更新事件，通知游戏其他模块

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).message.dispatchEvent('UserDataLoaded');
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logWarn('⚠️ 后台用户数据加载失败:', error);
            }
          }, 100);
        }
        /**
         * 🔄 强制执行完整的用户数据加载流程
         */


        async forceCompleteUserDataLoad() {
          try {
            var _this$RoleModel3;

            // 🔄 临时清除基础数据标记，强制执行完整登录
            const tempUserData = (_this$RoleModel3 = this.RoleModel) == null ? void 0 : _this$RoleModel3.userGameData;

            if (tempUserData) {
              // 标记这是临时数据，需要完整登录
              tempUserData.isTemporaryData = true;
            } // 🔄 执行完整的登录流程


            await this.performCompleteLogin();
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 强制完整登录失败:', error);
            throw error;
          }
        }
        /**
         * 🔐 执行完整的登录流程
         */


        async performCompleteLogin() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🔐 开始执行完整登录流程...');

          try {
            const {
              LoginViewComp
            } = await _context.import("__unresolved_12"); // 🔧 根据平台选择不同的登录方式

            if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
              error: Error()
            }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
              error: Error()
            }), Platform) : Platform).FACEBOOK) {
              // Facebook环境：执行Facebook自动登录
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🔐 Facebook环境：执行自动登录...');
              const loginSuccess = await LoginViewComp.doFacebookLogin();

              if (loginSuccess) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('✅ Facebook自动登录成功');
              } else {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logWarn('⚠️ Facebook自动登录失败');
              }
            } else {
              // 🔐 其他环境：使用现有的游客登录逻辑
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🔐 执行游客登录流程...'); // 🚀 直接使用LoginViewComp的游客登录方法

              const loginViewComp = new LoginViewComp();
              const loginSuccess = await loginViewComp.loginGuestButton();

              if (loginSuccess) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('✅ 游客登录成功');
              } else {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logWarn('⚠️ 游客登录失败');
              }
            }

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('✅ 登录流程完成');
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 登录流程失败:', error); // 不抛出错误，允许游戏继续运行
          }
        } // ==================== 记录数据 ====================

        /**
         * 获取指定日期的记录数据
         */


        getRecordData(recordType, dateString) {
          var _this$RoleModel$userG3;

          if (!this.hasUserData()) {
            return null;
          }

          const targetDate = dateString || new Date().toDateString();
          const recordData = (_this$RoleModel$userG3 = this.RoleModel.userGameData.recordData) == null ? void 0 : _this$RoleModel$userG3[targetDate];
          return (recordData == null ? void 0 : recordData[recordType]) || null;
        }
        /**
         * 🔧 新增：获取指定关卡的今日挑战次数
         * @param levelId 关卡ID
         * @param dateString 可选的日期字符串，默认为今日
         * @returns 该关卡的挑战次数
         */


        getLevelChallengeCount(levelId, dateString) {
          var _recordData$levelDeta;

          const recordData = this.getRecordData((_crd && RecordType === void 0 ? (_reportPossibleCrUseOfRecordType({
            error: Error()
          }), RecordType) : RecordType).Level, dateString);

          if (!(recordData != null && recordData.levelDetails)) {
            return 0;
          }

          const levelKey = `level_${levelId}`;
          return ((_recordData$levelDeta = recordData.levelDetails[levelKey]) == null ? void 0 : _recordData$levelDeta.attempts) || 0;
        }
        /**
         * 🔧 新增：获取当前关卡的今日挑战次数
         * @param dateString 可选的日期字符串，默认为今日
         * @returns 当前关卡的挑战次数
         */


        getCurrentLevelChallengeCount(dateString) {
          const currentLevelId = this.getNextLevelIndex(); // 获取当前要挑战的关卡

          return this.getLevelChallengeCount(currentLevelId, dateString);
        } // ==================== 工具方法 ====================

        /**
         * 获取完整的用户游戏数据
         */


        getUserGameData() {
          if (!this.hasUserData()) {
            throw new Error('用户数据尚未初始化，请等待登录完成');
          }

          return this.RoleModel.userGameData;
        } // ==================== 私有方法 ====================

        /**
         * 检查是否有用户数据
         */


        hasUserData() {
          var _this$RoleModel4;

          return !!((_this$RoleModel4 = this.RoleModel) != null && _this$RoleModel4.userGameData);
        }
        /**
         * 验证网络连接
         */


        validateNetworkConnection() {
          var _net;

          if (!((_net = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).net) != null && _net.hcGame)) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 网络连接未初始化');
            return false;
          }

          return true;
        }
        /**
         * 确保数据结构完整性
         */


        ensureDataIntegrity(data) {
          var _data$isNewPlayer;

          data.key = data.key || 0;
          data.userName = data.userName || 'Player';
          data.index = data.index || 0;
          data.isNewPlayer = (_data$isNewPlayer = data.isNewPlayer) != null ? _data$isNewPlayer : true;
          data.propUseData = data.propUseData || {};
          data.recordData = data.recordData || {};
        }
        /**
         * 深度合并用户数据
         */


        mergeUserData(target, source) {
          Object.keys(source).forEach(key => {
            const sourceValue = source[key];
            const targetValue = target[key];

            if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
              if (targetValue && typeof targetValue === 'object') {
                this.mergeUserData(targetValue, sourceValue);
              } else {
                target[key] = sourceValue;
              }
            } else {
              target[key] = sourceValue;
            }
          });
        }
        /**
         * 创建默认道具数据 - 优化版本
         */


        createDefaultPropData(propType) {
          // 🎁 为新手玩家提供默认道具数量
          const defaultAmount = this.getNewPlayerDefaultAmount(propType);
          return {
            amount: defaultAmount,
            propType,
            propId: propType,
            desc: `道具${propType}`,
            getTime: new Date(),
            lastResetTime: new Date(),
            lastUpdateTime: new Date()
          };
        }
        /**
         * 🎁 获取新手玩家的默认道具数量
         */


        getNewPlayerDefaultAmount(propType) {
          if (!this.isNewPlayer()) {
            return 0;
          } // 🎯 新手玩家默认道具配置（使用配置常量）


          switch (propType) {
            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsMoveOut:
              // 移出道具
              return (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).newPlayerDefaultProps.moveOut;

            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsTips:
              // 提示道具
              return (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).newPlayerDefaultProps.tips;

            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsReShuffle:
              // 重新洗牌道具
              return (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).newPlayerDefaultProps.reShuffle;

            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsDayLeftCount:
              // 每日挑战剩余次数
              return (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).dayFreeLimts;
            // 使用配置的默认值

            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsRevive:
              // 复活道具
              return (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
                error: Error()
              }), GameConst) : GameConst).newPlayerDefaultProps.revive;

            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsExp:
              // 游戏经验
              return 0;

            case (_crd && PropType === void 0 ? (_reportPossibleCrUseOfPropType({
              error: Error()
            }), PropType) : PropType).PropsCoin:
              // 玩家金币
              return 0;

            default:
              return 0;
          }
        }
        /**
         * 更新ViewModel
         */


        updateViewModel() {
          if (!this.hasUserData()) {
            return;
          }

          this.removeFromViewModel();
          const viewModelData = {
            userId: this.RoleModel.userGameData.key,
            userName: this.RoleModel.userGameData.userName,
            level: this.getGameProgress(),
            isNewPlayer: this.RoleModel.userGameData.isNewPlayer,
            index: this.RoleModel.userGameData.index,
            propUseData: this.RoleModel.userGameData.propUseData,
            // 扩展其他需要的数据
            ...this.RoleModel.userGameData
          };
          (_crd && VM === void 0 ? (_reportPossibleCrUseOfVM({
            error: Error()
          }), VM) : VM).add(viewModelData, 'role');
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎯 ViewModel已更新');
        }
        /**
         * 从ViewModel移除数据
         */


        removeFromViewModel() {
          (_crd && VM === void 0 ? (_reportPossibleCrUseOfVM({
            error: Error()
          }), VM) : VM).remove('role');
        }
        /**
         * 统一的API错误处理
         */


        handleAPIError(apiName, error) {
          var _error$code;

          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logError(`❌ ${apiName} API失败:`, error);
          const errorMessage = (error == null ? void 0 : error.message) || (error == null || (_error$code = error.code) == null ? void 0 : _error$code.toString()) || '操作失败';
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).gui.toast(errorMessage);
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f28f7ce01a9a2dc50115102c987620e5db3f8648.js.map