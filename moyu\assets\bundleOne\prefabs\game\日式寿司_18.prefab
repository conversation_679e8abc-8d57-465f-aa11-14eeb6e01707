[{"__type__": "cc.Prefab", "_name": "日式寿司_18", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "日式寿司_18", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 2.577, "y": 0, "z": 6.415}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_materials": [{"__uuid__": "e52d4a2a-c774-40af-8d5d-03942b6ca323", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 4}, "_mesh": {"__uuid__": "6b985a4d-52c5-4453-a190-ba9cfc4f3d4d@4aa06", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 1, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_reflectionProbeBlendId": -1, "_reflectionProbeBlendWeight": 0, "_enabledGlobalStandardSkinObject": false, "_enableMorph": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74YX4OZrJCGZSR/S5ZYrkf"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": true, "_receiveShadow": true, "_recieveShadow": true, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 6}, "_group": 4, "_type": 1, "_mass": 1, "_allowSleep": true, "_linearDamping": 0.1, "_angularDamping": 0.1, "_useGravity": true, "_linearFactor": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_angularFactor": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bqpr54ydCXIftMBiMwa6c"}, {"__type__": "cc.<PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 8}, "_material": null, "_isTrigger": false, "_center": {"__type__": "cc.Vec3", "x": -0.059935033321380615, "y": 0.11511364579200745, "z": -0.00043658167123794556}, "_radius": 0.7444825172424316, "_cylinderHeight": 0, "_direction": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a46eX9jRdMIL9vKNslwF3V"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "84HeDywQRFsKw/W854Pekf", "targetOverrides": null}]