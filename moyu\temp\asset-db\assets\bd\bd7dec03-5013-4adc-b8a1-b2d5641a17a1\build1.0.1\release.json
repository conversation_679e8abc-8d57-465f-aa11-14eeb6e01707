[1, ["45pr6QbClFE7hhoGpWznMB@f9941", "95PU6NsyFP47zCxuI46CqD@f9941", "5dTF+HH4tEgq1M6e/G195g@f9941", "e4Rn19LYNAUINMJR9vx5z/@f9941", "14I5OoJkFHdaecy/4b2eXK", "629DINOxBAKaqhi112CNFc@f9941", "ef78X+c5hF6IGSmD1Oq8/6@f9941", "e14rtp9bpPMqPrDYiRroEs@f9941", "9bmN7rP4xBbbwDJ7nf7v8d@f9941", "d2l58QPhFF8LUcuPQZklKy", "786QW1LRpNOasfEX/o0Nvb", "85PYmo0LlOmJKrbSMVZisd", "47io4JbX5NFK16QQdvTpAe@f9941"], ["node", "_spriteFrame", "targetInfo", "root", "asset", "value", "_font", "process", "data", "_parent"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_components", "_parent", "_children", "_lpos", "_lrot", "_euler"], -1, 4, 9, 1, 2, 5, 5, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_lineHeight", "_overflow", "_isBold", "_enableOutline", "_enableShadow", "_isSystemFontUsed", "node", "__prefab", "_color", "_font"], -6, 1, 4, 5, 6], ["47052uw/Y5O1LXaLObj4ARx", ["watchPath", "valueA", "condition", "node", "__prefab", "watchNodes"], 0, 1, 4, 2], ["cc.Layout", ["_layoutType", "_resizeMode", "_paddingLeft", "_spacingX", "_spacingY", "_verticalDirection", "node", "__prefab"], -3, 1, 4], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides", "mountedComponents", "removedComponents"], 2, 1, 9, 9, 9], ["cc.Sprite", ["_type", "node", "__prefab", "_spriteFrame"], 2, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 6], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 6], ["cc.TargetInfo", ["localID"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.CompPrefabInfo", ["fileId"], 2], ["4bff6xrIC5LtYScAfbMmEdR", ["_dataID", "node", "__prefab"], 2, 1, 4], ["11b96k/RIZF57Loehxyl6Hs", ["_dataID", "isRawSize", "node", "__prefab"], 1, 1, 4], ["cc.MountedComponentsInfo", ["components", "targetInfo"], 2, 4], ["cc.Widget", ["_alignFlags", "_left", "_right", "_originalWidth", "_originalHeight", "node", "__prefab"], -2, 1, 4], ["cc.BlockInputEvents", ["node", "__prefab"], 3, 1, 4], ["3a3a7O5S75E6bMqPN6ETYhB", ["node", "__prefab", "process"], 3, 1, 4, 1]], [[18, 0, 2], [17, 0, 1, 2, 1], [8, 0, 1, 2, 3, 4, 5, 5], [16, 0, 2], [5, 1, 2, 3, 1], [0, 0, 1, 6, 7, 5, 4, 8, 3], [11, 0, 1, 2, 2], [0, 0, 1, 6, 5, 4, 3], [2, 0, 2, 1, 3, 4, 5, 4], [10, 0, 1, 2, 3], [13, 0, 1, 2, 3], [0, 0, 1, 6, 5, 4, 8, 3], [14, 0, 1, 2, 2], [19, 0, 1, 2, 2], [0, 2, 3, 6, 4, 3], [7, 0, 1, 2, 3, 4, 5, 4], [5, 0, 1, 2, 3, 2], [4, 0, 1, 2, 2], [2, 0, 3, 4, 5, 2], [6, 0, 2], [0, 0, 1, 7, 5, 4, 3], [0, 0, 1, 7, 5, 4, 8, 3], [0, 0, 1, 6, 7, 5, 4, 8, 9, 10, 3], [9, 0, 1, 2, 3, 4, 5, 4], [4, 0, 1, 3, 2, 4, 2], [12, 0, 1, 2, 2], [15, 0, 1, 2, 2], [1, 0, 1, 2, 3, 4, 5, 6, 9, 10, 11, 8], [1, 0, 1, 2, 5, 6, 7, 9, 10, 7], [1, 0, 1, 2, 3, 8, 9, 10, 12, 6], [1, 0, 1, 2, 3, 4, 9, 10, 6], [2, 0, 1, 3, 4, 5, 3], [20, 0, 1, 2, 3, 3], [3, 0, 2, 3, 6, 7, 4], [3, 1, 0, 6, 7, 3], [3, 1, 0, 4, 5, 6, 7, 5], [21, 0, 1, 2], [22, 0, 1, 2, 3, 4, 5, 6, 6], [23, 0, 1, 1], [24, 0, 1, 2, 1]], [[19, "GameResult"], [20, "GameResult", 33554432, [-12, -13, -14, -15], [[1, -5, [0, "77N2cid5pKDpXplRH/AWEU"], [5, 750, 1334]], [37, 5, 539, 539, 2, 2, -6, [0, "63zNQq8NlBQ5QWzOJ4Kgjs"]], [38, -7, [0, "256Gj76CNDsbk/E3RQMINa"]], [39, -9, [0, "f6FqXeSmdEdIYg8HKdF0zg"], -8], [18, "*.isGuideLevel", -11, [0, "275gbcH+9N7qqwhmKlpdfM"], [-10]]], [23, "a0daVw8DRLi6ToMaTA0VS2", null, null, -4, 0, [-1, -2, -3]]], [21, "processBar", 33554432, [-19, -20, -21, -22, -23, -24, -25, -26, -27, -28], [[1, -16, [0, "80KlYO4NJL25pE3OLy+M3+"], [5, 571, 53]], [16, 1, -17, [0, "e2DDzUGuxDzqbr2i09fWOs"], 23], [33, 1, 7.9, 3, -18, [0, "b7hYv3k39NX5+4Tr+XSZdA"]]], [2, "1d3F1DXpZEdKvmNxSRRzp1", null, null, null, 1, 0], [1, 0, -266.63, 0]], [5, "fail", 33554432, 1, [-33, 2, -34], [[1, -29, [0, "93yYYS8PdMLZvOwWgdnGUp"], [5, 367, 169]], [16, 1, -30, [0, "c7p3XNrh5JUpuk+Ky/uQfH"], 24], [18, "*.isWin", -32, [0, "dcLG0fS/1J5a5zu8CurB/Y"], [-31]]], [2, "ff0RkTiLJJ3JaZ/XF4emZY", null, null, null, 1, 0], [1, 0, 292.476, 0]], [11, "lblTitle", 33554432, 1, [[1, -35, [0, "cfACh4GtBHD7JE8WFXSKF5"], [5, 800, 104.8]], [27, "victory", 80, 80, 80, 3, true, true, -36, [0, "dcsVmsrvNHOqJOZPCR/T7e"], [4, 4288413691]], [31, "*.isWin", 1, -38, [0, "3avfWRvyJGQ549vtqGjJKA"], [-37]], [13, "victory", -39, [0, "21lJDsDRVIh5B7HL75/EIM"]]], [2, "58i5TWzNtB0I9055sH3A30", null, null, null, 1, 0], [1, 0, 249.37, 0]], [5, "bottom", 33554432, 1, [-42, -43, -44], [[1, -40, [0, "21cmjJwsFNApXOK5JGmnyB"], [5, 500, 312.03999999999996]], [35, 1, 2, 9, 0, -41, [0, "17nEACSz9LdpdI99yIj5kg"]]], [2, "76SsYQI5pCWqWFy9U2eh+t", null, null, null, 1, 0], [1, 0, -248.396, 0]], [5, "1", 33554432, 2, [-49], [[1, -45, [0, "35CAQzEItL3osK6loXsvB/"], [5, 53, 39]], [4, -46, [0, "6cfiKZcUlMWpse6WQXGq7n"], 4], [8, "*.percent", 3, 10, -48, [0, "bbEy8vKiRIFbQYpW+SbeC+"], [-47]]], [2, "722GXXwcxLWbJHRHtwygn2", null, null, null, 1, 0], [1, -251.10000000000002, 0, 0]], [5, "2", 33554432, 2, [-54], [[1, -50, [0, "08qRlm8TlMy5ImHOxKzthb"], [5, 53, 39]], [4, -51, [0, "f0KTtoD21EFKBKwjJK3jDi"], 6], [8, "*.percent", 3, 20, -53, [0, "9f1hsSqW5LY61z/txdA9T5"], [-52]]], [2, "e1JVQtWa9AerUm1l6zNzHo", null, null, null, 1, 0], [1, -195.10000000000002, 0, 0]], [5, "3", 33554432, 2, [-59], [[1, -55, [0, "eeit5ICORMzZ7h+OY7rlL/"], [5, 53, 39]], [4, -56, [0, "89ZXBaaM5IG5ulGmsmyu2u"], 8], [8, "*.percent", 3, 30, -58, [0, "4et+4Bwa9M6Jyy5VTEluOY"], [-57]]], [2, "37I0lkaLlO7oDX8bdBwrsM", null, null, null, 1, 0], [1, -139.10000000000002, 0, 0]], [5, "4", 33554432, 2, [-64], [[1, -60, [0, "10QVbaBkhKgIKwj5y2kW1X"], [5, 53, 39]], [4, -61, [0, "9eDoLIMFVIYaIqEq5/6+e1"], 10], [8, "*.percent", 3, 40, -63, [0, "acmvOfa8hEC6cKXoEvtEI5"], [-62]]], [2, "8644dGK65AGbzdQoDLnmCN", null, null, null, 1, 0], [1, -83.10000000000002, 0, 0]], [5, "5", 33554432, 2, [-69], [[1, -65, [0, "c1x3iQtbpJtYst7PkiZ4qc"], [5, 53, 39]], [4, -66, [0, "31gLMvF9ZPsJCsvi63Ensp"], 12], [8, "*.percent", 3, 50, -68, [0, "687mWorHFFapvuGLGy+jjY"], [-67]]], [2, "df9/ZP9QdNFIn6G/r+3OJx", null, null, null, 1, 0], [1, -27.100000000000023, 0, 0]], [5, "6", 33554432, 2, [-74], [[1, -70, [0, "2bswPSX4NEGY9O+77zFV2p"], [5, 53, 39]], [4, -71, [0, "b4dxeqc+1KiIDGL0sgLPI6"], 14], [8, "*.percent", 3, 60, -73, [0, "20uIYBlU1Fop5ynZDDKoAK"], [-72]]], [2, "e2019sDbRFq7I/UqE0/bBb", null, null, null, 1, 0], [1, 28.899999999999977, 0, 0]], [5, "7", 33554432, 2, [-79], [[1, -75, [0, "96qPa31aZDq5HuN3Tzw9IN"], [5, 53, 39]], [4, -76, [0, "92z0wUehVGbLSluc7EEB62"], 16], [8, "*.percent", 3, 70, -78, [0, "a1lhAvYslKMowYE/D3azyc"], [-77]]], [2, "24VrkeFrtFlbVJjDGIsheu", null, null, null, 1, 0], [1, 84.89999999999998, 0, 0]], [5, "8", 33554432, 2, [-84], [[1, -80, [0, "81asKm705BCYQOlBGMw2sf"], [5, 53, 39]], [4, -81, [0, "283VD+V29O0LkUKQfxkWLa"], 18], [8, "*.percent", 3, 80, -83, [0, "7bK+P5B3tIwZ49hGCL0UCf"], [-82]]], [2, "38jy/mZptEOog12aStZzvf", null, null, null, 1, 0], [1, 140.89999999999998, 0, 0]], [5, "9", 33554432, 2, [-89], [[1, -85, [0, "2083r3qftNfZyj0OVVmeCB"], [5, 53, 39]], [4, -86, [0, "d1iMB2U0VDDaI13Wo4TtVz"], 20], [8, "*.percent", 3, 90, -88, [0, "65VZPNriZCHIt0KNymH8nA"], [-87]]], [2, "76NxZlohxAaZ6EPzevsWhB", null, null, null, 1, 0], [1, 196.89999999999998, 0, 0]], [22, "10", 33554432, 2, [-94], [[1, -90, [0, "81J0d9yjBOYoa48uNh/LyV"], [5, 53, 39]], [4, -91, [0, "9ad+O9w4VEFo9lCOWqASpS"], 22], [8, "*.percent", 3, 100, -93, [0, "d6v8J0PhBD5rxTTRbSGrKe"], [-92]]], [2, "1dt7c1n6BL7aDU0PLxdh0e", null, null, null, 1, 0], [1, 252.89999999999998, 0, 0], [3, 0, 0, 1, 6.123233995736766e-17], [1, 0, 0, 180]], [11, "process", 33554432, 3, [[1, -95, [0, "4e+DyBQTRE0adjSjaJIt0/"], [5, 106.716796875, 54.4]], [28, "Percent", 28, 28, true, true, true, -96, [0, "b8Dckhm/5LHKhiE2TPdxAv"]], [13, "Percent", -97, [0, "40aijakX1KsanOaUMaj9dD"]]], [2, "5dI3uCQmFAvrYlDipeucpo", null, null, null, 1, 0], [1, -243.335, -220.438, 0]], [5, "replayNode", 33554432, 5, [-100, -101], [[1, -98, [0, "a2TzIH2Q1GVqsYPW69BB1u"], [5, 100, 145.76]], [34, 1, 2, -99, [0, "26fXalY/tCupTH7bBQDa+F"]]], [2, "1dMvbXOOlIxIkxmoVLv3Yx", null, null, null, 1, 0], [1, 0, 38.860000000000014, 0]], [7, "failImg", 33554432, 3, [[1, -102, [0, "68G297TZJG+IZ7shIImaop"], [5, 223, 126]], [16, 1, -103, [0, "8cSJWWMpVBFqdl/7CDhLiN"], 2], [32, "fail", false, -104, [0, "8eY3o0r5JPn65cfsukqpfA"]]], [2, "a1HtfkvHNJw5DGvsmQnN8C", null, null, null, 1, 0]], [7, "亮1", 33554432, 6, [[1, -105, [0, "74miVuj/NO3bUg3PvufgVy"], [5, 53, 39]], [4, -106, [0, "7eTQo7A15Ab7GTgE+UzZOC"], 3]], [2, "aa3J4vbO5JYaZSDxCJUPGh", null, null, null, 1, 0]], [7, "spriteFrame", 33554432, 7, [[1, -107, [0, "40N3r1Y91Nz5l5hUDFQRRT"], [5, 53, 39]], [4, -108, [0, "2ch7O1anpEgboV9E/7Gzpz"], 5]], [2, "c11LPXXwxLmYx7Y0J+gT+2", null, null, null, 1, 0]], [7, "spriteFrame", 33554432, 8, [[1, -109, [0, "fbnFSwhI1ARajEzV+Iz+c9"], [5, 53, 39]], [4, -110, [0, "9e+W1yl9xD6qlnCqk4KmrD"], 7]], [2, "01tIte5xFMXqHUdF++ua2n", null, null, null, 1, 0]], [7, "spriteFrame", 33554432, 9, [[1, -111, [0, "5buz185DVIo5ZSJVGxtfTD"], [5, 53, 39]], [4, -112, [0, "f1nihFykdOTaZMlyLnpir/"], 9]], [2, "482dTzoG9KAL8KpPjgoQi+", null, null, null, 1, 0]], [7, "spriteFrame", 33554432, 10, [[1, -113, [0, "4aqjMblUlJV5mP/I7x2FN+"], [5, 53, 39]], [4, -114, [0, "e3qKd2EklPHJcng3rvcQjz"], 11]], [2, "40VNefjmZLZoYy23BOcjMK", null, null, null, 1, 0]], [7, "spriteFrame", 33554432, 11, [[1, -115, [0, "8dIfUD5LZILoO0GDVKz8jv"], [5, 53, 39]], [4, -116, [0, "92OXUIN35PoZ8b8VluBSq5"], 13]], [2, "c5TU5Y+eJIb7EqesPAt/xq", null, null, null, 1, 0]], [7, "spriteFrame", 33554432, 12, [[1, -117, [0, "44SNY2nVBHrZclt0ZtbBbV"], [5, 53, 39]], [4, -118, [0, "dck8wDJG5MQqm60fFuaoV3"], 15]], [2, "56vRCtdQlIhb3A4Qu0I/e1", null, null, null, 1, 0]], [7, "spriteFrame", 33554432, 13, [[1, -119, [0, "56EpCecx1Mpr9e9WQlNbSb"], [5, 53, 39]], [4, -120, [0, "20eOuGDP1JeK4A+EzsSFQM"], 17]], [2, "7d4lw3HK1P0rwHhNqC10if", null, null, null, 1, 0]], [7, "spriteFrame", 33554432, 14, [[1, -121, [0, "16wAhDwuFJ97Y4H638df6/"], [5, 53, 39]], [4, -122, [0, "7bWO22Sl5Oq7BU36w40eJx"], 19]], [2, "a42waZW/lE65RJ9nwveXBM", null, null, null, 1, 0]], [7, "亮1", 33554432, 15, [[1, -123, [0, "daHyVJEGVPqpmg8FApDU5x"], [5, 53, 39]], [4, -124, [0, "54wbmLXz5AHq6Sz/jMUgpK"], 21]], [2, "23leJqDfNOYY+9UNyXFyUz", null, null, null, 1, 0]], [3, ["95gnH/w+JEGqHSJXjfiQG6"]], [11, "lblTimes", 33554432, 17, [[1, -125, [0, "fan6C+qU5EqZEfv42KiIs/"], [5, 84.09375, 32.76]], [29, "DayLimt", 24, 24, 26, false, -126, [0, "a26IRS2qVE2aBTIRe2cpSK"], 26], [13, "DayLimt", -127, [0, "f7FCgYXM1KwLq2B6fGNuJD"]]], [2, "4ellUFhzpABLE8KdUxMiJJ", null, null, null, 1, 0], [1, 0, 56.5, 0]], [14, 0, {}, 17, [15, "87kTBNQWJI3pftCW6wRRdv", null, null, -128, [24, "a0YXqc9cdIW40qgYP8y2lO", 1, [[36, [null], [3, ["fdpk1fsslHobj/mM3LbXuR"]]]], [[9, "btnReplay", ["_name"], [3, ["87kTBNQWJI3pftCW6wRRdv"]]], [6, ["_lpos"], [3, ["87kTBNQWJI3pftCW6wRRdv"]], [1, 0, -16.379999999999995, 0]], [6, ["_lrot"], [3, ["87kTBNQWJI3pftCW6wRRdv"]], [3, 0, 0, 0, 1]], [6, ["_euler"], [3, ["87kTBNQWJI3pftCW6wRRdv"]], [1, 0, 0, 0]], [9, 1, ["_sizeMode"], [3, ["d6DPSk5Y1K8pnS8DeaPIj+"]]], [6, ["_contentSize"], [3, ["cdKt0uXpFGNbiqFioh9jcQ"]], [5, 263, 113]], [9, -81.5, ["_left"], [3, ["54mK9AptNNwaiUV05x/xQl"]]], [9, -81.5, ["_right"], [3, ["54mK9AptNNwaiUV05x/xQl"]]], [9, 0, ["_bottom"], [3, ["54mK9AptNNwaiUV05x/xQl"]]], [9, 1, ["_type"], [3, ["d6DPSk5Y1K8pnS8DeaPIj+"]]], [26, ["_spriteFrame"], [3, ["d6DPSk5Y1K8pnS8DeaPIj+"]], 28], [6, ["_outlineColor"], [3, ["9fJkiblSpOQbUJPdFapuF8", "4a5atXBglJxJGAlAL90RE0"]], [4, 4280629146]], [9, false, ["_isBold"], [3, ["9fJkiblSpOQbUJPdFapuF8", "4a5atXBglJxJGAlAL90RE0"]]], [6, ["_lpos"], [3, ["9fJkiblSpOQbUJPdFapuF8", "f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 5.792, 0]], [9, true, ["_active"], [3, ["87kTBNQWJI3pftCW6wRRdv"]]]], [[3, ["3avKd6uvpGtp07bTxHtW80"]]]], 27]], [11, "lblContent", 33554432, 5, [[1, -129, [0, "a4wyRqeJtP6r5RVkC3efla"], [5, 447.70263671875, 35.28]], [30, "ContentToday", 26, 26, 28, 3, -130, [0, "1eMGZiO6pPjpg4uXU2DxOs"]], [13, "ContentToday", -131, [0, "d7oUtwpk1OeIVI2ukYMJBK"]]], [2, "b1xFAmUBRKg6JIVbg4kWzj", null, null, null, 1, 0], [1, 0, 138.38, 0]], [14, 0, {}, 1, [15, "a0daVw8DRLi6ToMaTA0VS2", null, null, -135, [17, "7bm457irVEvZzS/a0VPd7u", 1, [[9, "mask", ["_name"], [3, ["a0daVw8DRLi6ToMaTA0VS2"]]], [6, ["_lpos"], [3, ["a0daVw8DRLi6ToMaTA0VS2"]], [1, 0, 0, 0]], [6, ["_lrot"], [3, ["a0daVw8DRLi6ToMaTA0VS2"]], [3, 0, 0, 0, 1]], [6, ["_euler"], [3, ["a0daVw8DRLi6ToMaTA0VS2"]], [1, 0, 0, 0]], [25, ["_spriteFrame"], -132, 1], [10, 0, ["_type"], -133], [6, ["_contentSize"], [3, ["77N2cid5pKDpXplRH/AWEU"]], [5, 750, 1334]], [12, ["_color"], -134, [4, 4278190080]]]], 0]], [3, ["08QaVy/ehM0oBtHFRmM2vy"]], [14, 0, {}, 5, [15, "95gnH/w+JEGqHSJXjfiQG6", null, null, -139, [17, "b23Z9Eh1JBp50GGKLc18W3", 1, [[10, "btnNext", ["_name"], 29], [12, ["_lpos"], 29, [1, 0, -99.51999999999998, 0]], [12, ["_lrot"], 29, [3, 0, 0, 0, 1]], [12, ["_euler"], 29, [1, 0, 0, 0]], [6, ["_lpos"], [3, ["29p8nV+15G9bL0lI1ojBwh"]], [1, 0, 3.61, 0]], [10, 0, ["_bottom"], -136], [6, ["_contentSize"], [3, ["e7CpmUbH9E16rvDUCELR7L"]], [5, 263, 113]], [10, 118.5, ["_left"], -137], [10, 118.5, ["_right"], -138]]], 25]], [3, ["79ESqtjuNEJYoFvIo1IgHq"]]], 0, [0, -1, 31, 0, -2, 35, 0, -3, 33, 0, 3, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 7, 16, 0, 0, 1, 0, -1, 31, 0, 0, 1, 0, -1, 33, 0, -2, 4, 0, -3, 3, 0, -4, 5, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, -3, 8, 0, -4, 9, 0, -5, 10, 0, -6, 11, 0, -7, 12, 0, -8, 13, 0, -9, 14, 0, -10, 15, 0, 0, 3, 0, 0, 3, 0, -1, 3, 0, 0, 3, 0, -1, 18, 0, -3, 16, 0, 0, 4, 0, 0, 4, 0, -1, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, -1, 35, 0, -2, 17, 0, -3, 32, 0, 0, 6, 0, 0, 6, 0, -1, 19, 0, 0, 6, 0, -1, 19, 0, 0, 7, 0, 0, 7, 0, -1, 20, 0, 0, 7, 0, -1, 20, 0, 0, 8, 0, 0, 8, 0, -1, 21, 0, 0, 8, 0, -1, 21, 0, 0, 9, 0, 0, 9, 0, -1, 22, 0, 0, 9, 0, -1, 22, 0, 0, 10, 0, 0, 10, 0, -1, 23, 0, 0, 10, 0, -1, 23, 0, 0, 11, 0, 0, 11, 0, -1, 24, 0, 0, 11, 0, -1, 24, 0, 0, 12, 0, 0, 12, 0, -1, 25, 0, 0, 12, 0, -1, 25, 0, 0, 13, 0, 0, 13, 0, -1, 26, 0, 0, 13, 0, -1, 26, 0, 0, 14, 0, 0, 14, 0, -1, 27, 0, 0, 14, 0, -1, 27, 0, 0, 15, 0, 0, 15, 0, -1, 28, 0, 0, 15, 0, -1, 28, 0, 0, 16, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, -1, 30, 0, -2, 31, 0, 0, 18, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 30, 0, 0, 30, 0, 0, 30, 0, 3, 31, 0, 0, 32, 0, 0, 32, 0, 0, 32, 0, 2, 34, 0, 2, 34, 0, 2, 34, 0, 3, 33, 0, 2, 36, 0, 2, 36, 0, 2, 36, 0, 3, 35, 0, 8, 1, 2, 9, 3, 139], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4, 6, 4, 5], [4, 5, 6, 2, 3, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 2, 3, 7, 8, 9, 10, 11, 12]]