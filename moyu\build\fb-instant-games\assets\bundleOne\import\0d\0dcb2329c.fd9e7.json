[1, ["e5LUoqx3RAr41dA5QrbKMj", "aeO5Mhq1JBMo5Ev99FP2nh@a75eb"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 1032651587, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 44592, "length": 8760, "count": 4380, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 44592, "count": 929, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.5565157532691956, -0.9562192559242249, -0.40942084789276123], "maxPosition", 8, [1, 0.6791124939918518, 0.9564791321754456, 0.3295751214027405]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_16"], [3, "日式寿司_16", [[4, 1, -2, [0, "00RbhPh/pA+oLaSqcB+GkY"], [0], [5, true, true], 1], [6, 4, -3, [0, "2e0fpsQWtIHLl0GgAu0eBZ"]], [7, 0.618, 1.247, -4, [0, "45lBCE80BLA5r6ChYwJfMt"], [1, 0.061298370361328125, 0.00012993812561035156, -0.039922863245010376]]], [8, "6037L7XiRHqYGgESAjDETQ", null, null, null, -1, 0], [1, 0.844, 0, 6.032]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]