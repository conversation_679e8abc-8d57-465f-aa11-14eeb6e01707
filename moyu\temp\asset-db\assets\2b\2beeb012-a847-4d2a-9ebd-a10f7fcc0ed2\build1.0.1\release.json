[1, ["87KJdfUjNA1pqHCGJqumKH"], ["node", "root", "targetInfo", "asset", "data"], [["cc.Node", ["_name", "_layer", "_obj<PERSON><PERSON>s", "__editorExtras__", "_prefab", "_children", "_components", "_parent", "_lpos"], -1, 4, 2, 9, 1, 5], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "targetOverrides", "nestedPrefabInstanceRoots", "root", "instance", "asset"], 0, 1, 4, 6], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "root", "asset", "nestedPrefabInstanceRoots"], 0, 1, 1, 2], ["cc.PrefabInstance", ["fileId", "prefabRootNode", "propertyOverrides"], 2, 1, 9], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 1], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 1, 8], ["CCPropertyOverrideInfo", ["propertyPath", "targetInfo", "value"], 2, 4, 8], ["CCPropertyOverrideInfo", ["value", "propertyPath", "targetInfo"], 1, 4], ["cc.TargetInfo", ["localID"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.Layout", ["_layoutType", "node", "__prefab"], 2, 1, 4], ["47052uw/Y5O1LXaLObj4ARx", ["watchPath", "condition", "node", "__prefab", "watchNodes"], 1, 1, 4, 2], ["cc.Widget", ["_alignFlags", "_left", "_right", "_top", "_bottom", "_verticalCenter", "_originalWidth", "_originalHeight", "node", "__prefab"], -5, 1, 4], ["4c088mplRRK5o6eY0e1zc45", ["node", "__prefab"], 3, 1, 4]], [[11, 0, 2], [7, 0, 1, 2, 3], [10, 0, 1, 2, 3], [8, 0, 1, 2, 2], [9, 0, 1, 2, 2], [12, 0, 2], [0, 2, 3, 7, 4, 3], [3, 0, 1, 2, 3, 4, 5, 4], [6, 0, 1, 2, 2], [2, 0, 2], [0, 0, 1, 5, 6, 4, 3], [0, 0, 1, 7, 5, 6, 4, 8, 3], [4, 0, 1, 2, 3, 4, 5, 5], [5, 0, 1, 2, 3, 4, 5, 4], [1, 0, 1, 1], [1, 0, 1, 2, 1], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3, 4, 3], [15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 9], [16, 0, 1, 1]], [[9, "hallVmRankCell"], [10, "hallVmRankCell", 33554432, [-7], [[15, -5, [5, "1ehE0yjgpOdpGShi2Ty+Al"], [5, 260.15625, 50.4]], [19, -6, [5, "03iYLSvBVIBqw4wSv7GMSF"]]], [13, "26iaepJgFCqKV3l5WUmOuO", null, null, -4, 0, [-1, -2, -3]]], [11, "centerNode", 33554432, 1, [-13, -14, -15], [[14, -8, [5, "fft5apgMpHxagaJ1pBxC86"]], [16, 2, -9, [5, "93oBg+vsxKg6F6FCmgN86P"]], [17, "*.times", 2, -11, [5, "73+99BOgdGvI5ITjxxfnh1"], [-10]], [18, 2, 80.078125, 80.078125, -24.8, -24.8, 400, 100, 100, -12, [5, "04OHEtIjFMXIgN519oY9pR"]]], [12, "e6nDtHVlNF4qOQl/P3kQ1A", null, null, null, 1, 0], [1, 0, 400, 0]], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [0, ["09i06HqvBP2ZaQ4Pk+p3Mt"]], [6, 0, {}, 2, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -20, [8, "f2rmwxfZhAOYpRh0djJser", 1, [[1, "vmTimesLabel", ["_name"], -16], [3, ["_lpos"], -17, [1, 0, -76, 0]], [3, ["_lrot"], -18, [3, 0, 0, 0, 1]], [3, ["_euler"], -19, [1, 0, 0, 0]], [2, "vmTimesLabel", ["_dataID"], [0, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]], [4, ["_contentSize"], [0, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 245.625, 50.4]]]], 2]], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [6, 0, {}, 2, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -21, [8, "8eCNJYQF5Ck6oLAgJvxVQU", 1, [[1, "vmRankLabel", ["_name"], 3], [3, ["_lpos"], 3, [1, 0, 24.8, 0]], [3, ["_lrot"], 3, [3, 0, 0, 0, 1]], [3, ["_euler"], 3, [1, 0, 0, 0]], [1, true, ["templateMode"], 4], [1, 1, ["watchPathArr", "length"], 4], [1, "*.rank", ["watchPathArr", "0"], 4], [1, "vmRankLabel", ["_dataID"], 4], [4, ["_contentSize"], [0, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 248.4375, 50.4]], [1, true, ["_active"], 3]]], 0]], [6, 0, {}, 2, [7, "f05XX5jrpEOYwv6lCoUIav", null, null, -22, [8, "59oamas7BK6IOgDZ/Oxeew", 1, [[2, "vmCountryLabel", ["_name"], [0, ["f05XX5jrpEOYwv6lCoUIav"]]], [4, ["_lpos"], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, -25.599999999999998, 0]], [4, ["_lrot"], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [3, 0, 0, 0, 1]], [4, ["_euler"], [0, ["f05XX5jrpEOYwv6lCoUIav"]], [1, 0, 0, 0]], [2, false, ["templateMode"], [0, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]], [2, 0, ["watchPathArr", "length"], [0, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]], [2, "hgcrvm.vmRank1", ["watchPathArr", "0"], [0, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]], [2, "UnKnow", ["_dataID"], [0, ["09i06HqvBP2ZaQ4Pk+p3Mt"]]], [4, ["_contentSize"], [0, ["6cPaWujd9BCJYdUAjIP0JG"]], [5, 248.4375, 50.4]], [2, true, ["_active"], [0, ["f05XX5jrpEOYwv6lCoUIav"]]]]], 1]]], 0, [0, -1, 5, 0, -2, 8, 0, -3, 7, 0, 1, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 0, 2, 0, -1, 5, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, -2, 8, 0, -3, 5, 0, 2, 6, 0, 2, 6, 0, 2, 6, 0, 2, 6, 0, 1, 5, 0, 1, 7, 0, 1, 8, 0, 4, 1, 22], [0, 0, 0], [3, 3, 3], [0, 0, 0]]