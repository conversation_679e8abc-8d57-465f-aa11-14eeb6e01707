[1, ["e5LUoqx3RAr41dA5QrbKMj", "7aFOLVgVNFU49/CYXKpRTU@09de3"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 648086442, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 18720, "length": 4224, "count": 2112, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 18720, "count": 390, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.3278489112854004, -0.40615835785865784, -0.24166010320186615], "maxPosition", 8, [1, 0.3437731862068176, 0.406735897064209, 0.11318347603082657]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_09"], [3, "日式寿司_09", [[4, 1, -2, [0, "34y0GGKWpH55h0E8OQLpxu"], [0], [5, true, true], 1], [6, 4, -3, [0, "acUxulKRBF3oZUSIoLEHFa"]], [7, 0.335811048746109, 0.1412721574306488, -4, [0, "d3jrWDCVVGWbA+VLz1hEdu"], [1, 0.007962137460708618, 0.00028876960277557373, -0.06423831358551979]]], [8, "58ms1kGAVCgY3VSjFQZBhK", null, null, null, -1, 0], [1, 1.131, 0, 2.713]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]