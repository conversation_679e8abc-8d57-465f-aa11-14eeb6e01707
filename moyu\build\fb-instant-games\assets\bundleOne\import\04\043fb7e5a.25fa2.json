[1, ["143fb7e5a@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [[[{"name": "列2", "rect": {"x": 232, "y": 590, "width": 223, "height": 92}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 223, "height": 92}, "rotated": true, "capInsets": [83, 36, 91, 40], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "图层 6", "rect": {"x": 3, "y": 3, "width": 581, "height": 92}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 581, "height": 92}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "铜", "rect": {"x": 297, "y": 3, "width": 60, "height": 77}, "offset": {"x": 0, "y": 0.5}, "originalSize": {"width": 60, "height": 78}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "金", "rect": {"x": 297, "y": 86, "width": 60, "height": 77}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 60, "height": 77}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "图层 7", "rect": {"x": 101, "y": 3, "width": 581, "height": 92}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 581, "height": 92}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "亮", "rect": {"x": 232, "y": 819, "width": 140, "height": 64}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 140, "height": 64}, "rotated": true, "capInsets": [54, 32, 55, 30], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "暗", "rect": {"x": 302, "y": 819, "width": 139, "height": 51}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 139, "height": 51}, "rotated": true, "capInsets": [59, 21, 55, 18], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "银", "rect": {"x": 297, "y": 169, "width": 60, "height": 77}, "offset": {"x": 0, "y": 0.5}, "originalSize": {"width": 60, "height": 78}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "图层 5", "rect": {"x": 199, "y": 3, "width": 581, "height": 92}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 581, "height": 92}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "底框", "rect": {"x": 3, "y": 590, "width": 223, "height": 223}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 223, "height": 223}, "rotated": false, "capInsets": [88, 117, 77, 81], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "列2底", "rect": {"x": 3, "y": 819, "width": 223, "height": 145}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 223, "height": 145}, "rotated": false, "capInsets": [55, 65, 81, 62], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]]]