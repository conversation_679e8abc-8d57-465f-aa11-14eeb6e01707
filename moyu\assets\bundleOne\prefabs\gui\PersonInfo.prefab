[{"__type__": "cc.Prefab", "_name": "PersonInfo", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "PersonInfo", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 10}], "_active": true, "_components": [{"__id__": 360}, {"__id__": 362}, {"__id__": 364}], "_prefab": {"__id__": 366}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "142393a8-2641-4775-a79c-cbfe1bd9e5ca", "__expectedType__": "cc.Prefab"}, "fileId": "a0daVw8DRLi6ToMaTA0VS2", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "4bd7+FLBlMqJQRLHhjl3Px", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 5}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_name"], "value": "mask"}, {"__type__": "cc.TargetInfo", "localID": ["a0daVw8DRLi6ToMaTA0VS2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.Node", "_name": "info", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 11}, {"__id__": 303}, {"__id__": 340}], "_active": true, "_components": [{"__id__": 357}], "_prefab": {"__id__": 359}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 12}, {"__id__": 106}, {"__id__": 171}], "_active": true, "_components": [{"__id__": 296}, {"__id__": 298}, {"__id__": 300}], "_prefab": {"__id__": 302}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "top", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 13}, {"__id__": 30}], "_active": true, "_components": [{"__id__": 103}], "_prefab": {"__id__": 105}, "_lpos": {"__type__": "cc.Vec3", "x": 5, "y": 280, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 12}, "_prefab": {"__id__": 14}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 13}, "asset": {"__uuid__": "803aa5c7-2370-41d0-a7f0-e0b674b17af4", "__expectedType__": "cc.Prefab"}, "fileId": "b2LyNAFEdCsIkChnpFdNTi", "instance": {"__id__": 15}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "e0AXo1n4ZDzYNNXbObynR3", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 16}], "propertyOverrides": [{"__id__": 20}, {"__id__": 22}, {"__id__": 23}, {"__id__": 24}, {"__id__": 25}, {"__id__": 27}, {"__id__": 29}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 17}, "components": [{"__id__": 18}]}, {"__type__": "cc.TargetInfo", "localID": ["b2LyNAFEdCsIkChnpFdNTi"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 13}}, "node": {"__id__": 13}, "_enabled": true, "__prefab": {"__id__": 19}, "_alignFlags": 8, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83qKcxJGJM5Zib7HLNtNBK"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 21}, "propertyPath": ["_name"], "value": "avatar"}, {"__type__": "cc.TargetInfo", "localID": ["b2LyNAFEdCsIkChnpFdNTi"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 21}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -181, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 21}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 21}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 26}, "propertyPath": ["_name"], "value": "avatarImg"}, {"__type__": "cc.TargetInfo", "localID": ["5a8880C91AI6EEZ9EsW7dy"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 28}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 138, "height": 151}}, {"__type__": "cc.TargetInfo", "localID": ["fagAZlPmdJDYrk089Nl/sl"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 26}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.Node", "_name": "info", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 12}, "_children": [{"__id__": 31}, {"__id__": 53}], "_active": true, "_components": [{"__id__": 100}], "_prefab": {"__id__": 102}, "_lpos": {"__type__": "cc.Vec3", "x": 72.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 30}, "_prefab": {"__id__": 32}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 31}, "asset": {"__uuid__": "e5d968d6-3ed8-4c7d-a200-55a04647482b", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 33}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "7e2xCz43xI/IVXLckctAnt", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 34}], "propertyOverrides": [{"__id__": 38}, {"__id__": 40}, {"__id__": 41}, {"__id__": 42}, {"__id__": 43}, {"__id__": 45}, {"__id__": 47}, {"__id__": 48}, {"__id__": 49}, {"__id__": 50}, {"__id__": 52}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 35}, "components": [{"__id__": 36}]}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 31}}, "node": {"__id__": 31}, "_enabled": true, "__prefab": {"__id__": 37}, "_alignFlags": 17, "_target": null, "_left": 94.41552734375, "_right": 94.41552734375, "_top": 9.883000000000017, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 166.1689453125, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4VR1gISJLmabF6uR0oAc+"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_name"], "value": "name"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 51.216999999999985, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 39}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 44}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 163.3125, "height": 37.8}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_string"], "value": "nickname"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_fontSize"], "value": 26}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_actualFontSize"], "value": 26}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 46}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 173, "g": 124, "b": 65, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["watchPath"], "value": "role.nick<PERSON>ame"}, {"__type__": "cc.TargetInfo", "localID": ["4dgANpwkxN8pTx4nabOESy"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 51}, "propertyPath": ["_dataID"], "value": "role.nick<PERSON>ame"}, {"__type__": "cc.Node", "_name": "join", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 30}, "_children": [{"__id__": 54}, {"__id__": 74}], "_active": true, "_components": [{"__id__": 91}, {"__id__": 93}, {"__id__": 95}, {"__id__": 97}], "_prefab": {"__id__": 99}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -32.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 53}, "_prefab": {"__id__": 55}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 54}, "asset": {"__uuid__": "80a6dff8-dc06-4f9d-a2c8-03170daa1ddd", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 56}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "c4deIIQWNOX7mqSyAFsBs1", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 57}, {"__id__": 59}, {"__id__": 60}, {"__id__": 61}, {"__id__": 62}, {"__id__": 64}, {"__id__": 66}, {"__id__": 67}, {"__id__": 68}, {"__id__": 69}, {"__id__": 71}, {"__id__": 72}, {"__id__": 73}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_name"], "value": "jonInTimeLabel"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 22.6, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 58}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_string"], "value": "2024/11/11"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 65}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 133.125, "height": 37.8}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 173, "g": 124, "b": 65, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_fontSize"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 63}, "propertyPath": ["_actualFontSize"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 70}, "propertyPath": ["templateMode"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["48BGi+JnJOKpaEdqvfVVS1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 70}, "propertyPath": ["watchPathArr", "length"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 70}, "propertyPath": ["watchPathArr", "0"], "value": "*.createtime"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 70}, "propertyPath": ["_dataID"], "value": "RegisterTime"}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 53}, "_prefab": {"__id__": 75}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 74}, "asset": {"__uuid__": "c1093e7c-9dd6-4c78-a9c6-28a6a82bc9be", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 76}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "0a5s5qHllMQrs3SqwJwp6p", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 77}, {"__id__": 79}, {"__id__": 80}, {"__id__": 81}, {"__id__": 82}, {"__id__": 84}, {"__id__": 86}, {"__id__": 88}, {"__id__": 89}, {"__id__": 90}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 78}, "propertyPath": ["_name"], "value": "passTimeLabel"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 78}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -12.679999999999996, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 78}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 78}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 83}, "propertyPath": ["_string"], "value": "pass"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 85}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 107.625, "height": 32.76}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["_dataID"], "value": "PassTimes"}, {"__type__": "cc.TargetInfo", "localID": ["47zQiH5ZpHGZOMEAJOGjcx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["templateMode"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["watchPathArr", "length"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 87}, "propertyPath": ["watchPathArr", "0"], "value": "role.passTimes"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 92}, "_contentSize": {"__type__": "cc.Size", "width": 345, "height": 95}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbdDrP3+BNPaLmCmEc2jkr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 94}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2eb6b950-13d4-4fe1-8144-d17e78f89615@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c8Nw4kUMNIPLgo/6Ajof6t"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 96}, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47MP5bSsxPOJKM+X3+xWYi"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 98}, "_resizeMode": 0, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 6, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b28kM0w7tKwKKPNOq4K9QK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f65/bqkwNLVIskojn8Ti5Q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 30}, "_enabled": true, "__prefab": {"__id__": 101}, "_contentSize": {"__type__": "cc.Size", "width": 355, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00Lkeap5NJYpHr3Ccb/28l"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e3zKlICv1PFZRiwZ+JZi99", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": {"__id__": 104}, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 160}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86mtK/Vs5CZps9k5bgBstg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edMTQvghtPnoKNMcTpRxmG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "mid", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 107}, {"__id__": 138}], "_active": true, "_components": [{"__id__": 166}, {"__id__": 168}], "_prefab": {"__id__": 170}, "_lpos": {"__type__": "cc.Vec3", "x": 5, "y": 125, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "标记", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 106}, "_children": [{"__id__": 108}], "_active": true, "_components": [{"__id__": 131}, {"__id__": 133}, {"__id__": 135}], "_prefab": {"__id__": 137}, "_lpos": {"__type__": "cc.Vec3", "x": -217.5, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 107}, "_prefab": {"__id__": 109}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 108}, "asset": {"__uuid__": "80a6dff8-dc06-4f9d-a2c8-03170daa1ddd", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 110}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "c27DPVRahFsqVQ1hmgVTZH", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 111}], "propertyOverrides": [{"__id__": 115}, {"__id__": 117}, {"__id__": 118}, {"__id__": 119}, {"__id__": 120}, {"__id__": 122}, {"__id__": 124}, {"__id__": 125}, {"__id__": 126}, {"__id__": 127}, {"__id__": 128}, {"__id__": 130}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 112}, "components": [{"__id__": 113}]}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 108}}, "node": {"__id__": 108}, "_enabled": true, "__prefab": {"__id__": 114}, "_alignFlags": 10, "_target": null, "_left": 13.43225000000001, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bpmiMr8NKTKnyblxdtlmm"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 116}, "propertyPath": ["_name"], "value": "countryCode"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 116}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 82.40100000000001, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 116}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 116}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 121}, "propertyPath": ["_string"], "value": "countryCode"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 123}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 162.9375, "height": 37.8}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 121}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 173, "g": 124, "b": 65, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 121}, "propertyPath": ["_fontSize"], "value": 22}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 121}, "propertyPath": ["_actualFontSize"], "value": 22}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 121}, "propertyPath": ["_horizontalAlign"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 129}, "propertyPath": ["watchPath"], "value": "role.countryCode"}, {"__type__": "cc.TargetInfo", "localID": ["48BGi+JnJOKpaEdqvfVVS1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 129}, "propertyPath": ["_dataID"], "value": "role.countryCode"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 107}, "_enabled": true, "__prefab": {"__id__": 132}, "_contentSize": {"__type__": "cc.Size", "width": 25, "height": 31}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88fSHmLulN6qDIdbfdiWpO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 107}, "_enabled": true, "__prefab": {"__id__": 134}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "033c5d93-1ab9-4664-b46d-b393de0fa622@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b5cRc9BGZOcrHRSuk2H3sK"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 107}, "_enabled": true, "__prefab": {"__id__": 136}, "_alignFlags": 10, "_target": null, "_left": 20, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7/pEsM/dFBKSm7ip6aWQ6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "94Sj+zPKtOypR1qQt+DEwQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 106}, "_prefab": {"__id__": 139}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 138}, "asset": {"__uuid__": "62da37bb-2377-4788-8c7d-bec59bdca136", "__expectedType__": "cc.Prefab"}, "fileId": "30Ahna/hFADJL5JEyiP8Kc", "instance": {"__id__": 140}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "f7T3myHJpJCqEGNB/Fl+6F", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 141}], "propertyOverrides": [{"__id__": 146}, {"__id__": 148}, {"__id__": 149}, {"__id__": 150}, {"__id__": 151}, {"__id__": 153}, {"__id__": 155}, {"__id__": 157}, {"__id__": 159}, {"__id__": 160}, {"__id__": 161}, {"__id__": 162}, {"__id__": 163}, {"__id__": 164}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 142}, "components": [{"__id__": 143}]}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 138}}, "node": {"__id__": 144}, "_enabled": true, "__prefab": {"__id__": 145}, "templateMode": false, "watchPath": "", "labelType": "cc.Label", "watchPathArr": [], "_dataID": "ModifyArea", "_id": ""}, {"__type__": "cc.Node", "__editorExtras__": {}}, {"__type__": "cc.CompPrefabInfo", "fileId": "03cZZBD5BE04qK/8Iudydn"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 147}, "propertyPath": ["_name"], "value": "vmButton"}, {"__type__": "cc.TargetInfo", "localID": ["30Ahna/hFADJL5JEyiP8Kc"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 147}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 141.58000000000004, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 147}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 147}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 152}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "20f26d84-53e9-4146-9c0a-0b13f12d5d61@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["daO3pUYmlKVIgsdc588k+9"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 154}, "propertyPath": ["_string"], "value": "ModifyArea"}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 156}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 135.3203125, "height": 54.4}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 158}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 158, "height": 68}}, {"__type__": "cc.TargetInfo", "localID": ["b2FpfgEc1Py4FLQrPbDjfF"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 154}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 154}, "propertyPath": ["_fontSize"], "value": 26}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 154}, "propertyPath": ["_actualFontSize"], "value": 26}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 154}, "propertyPath": ["_enableOutline"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 154}, "propertyPath": ["_outlineColor"], "value": {"__type__": "cc.Color", "r": 37, "g": 88, "b": 26, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 165}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 2.1580000000000155, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["8eWUAc4O9I9LUoIPJa7aJ0", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": {"__id__": 167}, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 110}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36uIH+R5RGP6yZRmG8OehG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 106}, "_enabled": true, "__prefab": {"__id__": 169}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d0166250-efdb-40e0-8b61-18093d8b62ba@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53/LurpxFPHJ5UaYdSVyxT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a5esKyNJxFsb5jp2zpqT6r", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "down", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 172}, {"__id__": 210}, {"__id__": 222}, {"__id__": 276}], "_active": true, "_components": [{"__id__": 291}, {"__id__": 293}], "_prefab": {"__id__": 295}, "_lpos": {"__type__": "cc.Vec3", "x": 5, "y": -155, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "邀请有礼 底", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 171}, "_children": [{"__id__": 173}, {"__id__": 179}], "_active": true, "_components": [{"__id__": 203}, {"__id__": 205}, {"__id__": 207}], "_prefab": {"__id__": 209}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 149.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "灯光", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 172}, "_children": [], "_active": true, "_components": [{"__id__": 174}, {"__id__": 176}], "_prefab": {"__id__": 178}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -85.8889999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 173}, "_enabled": true, "__prefab": {"__id__": 175}, "_contentSize": {"__type__": "cc.Size", "width": 285, "height": 222}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daYxKWDhNLAblCCkk2LSVO"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 173}, "_enabled": true, "__prefab": {"__id__": 177}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "2abfff9a-72b9-4236-aebc-d32f1e697d9f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a12P0/ER5FO60a0pXDnIgD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9dyzOj/eRLMYLs1SO86ziD", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 172}, "_prefab": {"__id__": 180}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 179}, "asset": {"__uuid__": "8728975f-5233-40d6-9a87-08626aba6287", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 181}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "ffX0o8tORMfb04qPjoTsoF", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 182}, {"__id__": 184}, {"__id__": 186}, {"__id__": 188}, {"__id__": 190}, {"__id__": 192}, {"__id__": 194}, {"__id__": 196}, {"__id__": 198}, {"__id__": 199}, {"__id__": 200}, {"__id__": 201}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 183}, "propertyPath": ["_name"], "value": "VMLabelLanguage_White"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 185}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 4.982999999999947, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 187}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 189}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 191}, "propertyPath": ["_fontSize"], "value": 26}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 193}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 146.0859375, "height": 39.28}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 195}, "propertyPath": ["_actualFontSize"], "value": 26}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 197}, "propertyPath": ["_string"], "value": "InvitationGift"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 197}, "propertyPath": ["_enableOutline"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 197}, "propertyPath": ["_outlineColor"], "value": {"__type__": "cc.Color", "r": 153, "g": 63, "b": 46, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 197}, "propertyPath": ["_lineHeight"], "value": 28}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 202}, "propertyPath": ["_dataID"], "value": "InvitationGift"}, {"__type__": "cc.TargetInfo", "localID": ["09i06HqvBP2ZaQ4Pk+p3Mt"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 172}, "_enabled": true, "__prefab": {"__id__": 204}, "_contentSize": {"__type__": "cc.Size", "width": 327, "height": 55}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27J/V91WJJZaeiNoUnKsgd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 172}, "_enabled": true, "__prefab": {"__id__": 206}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e0b0b573-4f44-4aa8-9b8a-4668e4a2edc8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "30CSRXu/dAHaZ+7OPhYFHO"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 172}, "_enabled": true, "__prefab": {"__id__": 208}, "_alignFlags": 17, "_target": null, "_left": 0, "_right": 0, "_top": 28, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2tVklQ1dNZoubUoPinmnF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "60BAj5EelHGLzQQXXr73JZ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "投影", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 171}, "_children": [{"__id__": 211}], "_active": true, "_components": [{"__id__": 217}, {"__id__": 219}], "_prefab": {"__id__": 221}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -57.166, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "松鼠", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 210}, "_children": [], "_active": true, "_components": [{"__id__": 212}, {"__id__": 214}], "_prefab": {"__id__": 216}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 59.79899999999998, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 211}, "_enabled": true, "__prefab": {"__id__": 213}, "_contentSize": {"__type__": "cc.Size", "width": 134, "height": 149}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8f0eaRR8FGYL0yQKAL3hW1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 211}, "_enabled": true, "__prefab": {"__id__": 215}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3b3468b5-c29a-4379-a39f-ed1a889bd4a6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4+RRRgyRGJLfUIwztNbw0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8skFVKwRCQJukllOJ714w", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 210}, "_enabled": true, "__prefab": {"__id__": 218}, "_contentSize": {"__type__": "cc.Size", "width": 149, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5b4xd7MAlBIbZyf6EO2873"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 210}, "_enabled": true, "__prefab": {"__id__": 220}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "51972bb9-f86b-4a00-abea-adc2035b8d15@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "580PDwJfdI64wNlmy/zCPI"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7cgovbbPxAxbC8De7PBg2n", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 171}, "_children": [{"__id__": 223}, {"__id__": 245}], "_active": true, "_components": [{"__id__": 273}], "_prefab": {"__id__": 275}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -121.469, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "小底已通过", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 222}, "_children": [{"__id__": 224}], "_active": true, "_components": [{"__id__": 236}, {"__id__": 238}, {"__id__": 240}, {"__id__": 242}], "_prefab": {"__id__": 244}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 223}, "_prefab": {"__id__": 225}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 224}, "asset": {"__uuid__": "f3afcd4e-9f0c-4e5b-b1b2-40e72c0d7169", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 226}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "93gjAcl0BKNK0kEi7ruzy4", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 227}, {"__id__": 229}, {"__id__": 230}, {"__id__": 231}, {"__id__": 232}, {"__id__": 234}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 228}, "propertyPath": ["_name"], "value": "VMLabelLanguage_Yellow"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 228}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 228}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 228}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 233}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 209.6875, "height": 30.240000000000002}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 235}, "propertyPath": ["_dataID"], "value": "CompletedChallenges"}, {"__type__": "cc.TargetInfo", "localID": ["36m7MuCL5KzbRIiB/pDpxj"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 237}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cpnW6KvRK56C2UvOh9yxf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 239}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "3869745f-c01b-4627-801d-2fa2d205ac2e@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8bIK7k8uxFmqXU7WvIE713"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 241}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 38, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "78k2w39AlA0asGnslbmfoL"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 223}, "_enabled": true, "__prefab": {"__id__": 243}, "watchPath": "*.<PERSON><PERSON><PERSON>", "foreachChildMode": false, "condition": 0, "foreachChildType": 0, "valueA": 1, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 223}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56XK969spOf4RHIoNeAAeE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10hmGWNTBCZ5qRITZT+zWL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "小底未通过", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 222}, "_children": [{"__id__": 246}], "_active": true, "_components": [{"__id__": 264}, {"__id__": 266}, {"__id__": 268}, {"__id__": 270}], "_prefab": {"__id__": 272}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 245}, "_prefab": {"__id__": 247}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 246}, "asset": {"__uuid__": "8728975f-5233-40d6-9a87-08626aba6287", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 248}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "bff6xcsixPm4wvObUjhsXO", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 249}, {"__id__": 251}, {"__id__": 252}, {"__id__": 253}, {"__id__": 254}, {"__id__": 256}, {"__id__": 258}, {"__id__": 259}, {"__id__": 260}, {"__id__": 261}, {"__id__": 262}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 250}, "propertyPath": ["_name"], "value": "VMLabelLanguage_White"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 250}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 250}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 250}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 255}, "propertyPath": ["_fontSize"], "value": 22}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 257}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 127.9609375, "height": 30.240000000000002}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 255}, "propertyPath": ["_actualFontSize"], "value": 22}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 255}, "propertyPath": ["_lineHeight"], "value": 24}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 255}, "propertyPath": ["_isBold"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 255}, "propertyPath": ["_string"], "value": "NoPassLevels"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 263}, "propertyPath": ["_dataID"], "value": "NoPassLevels"}, {"__type__": "cc.TargetInfo", "localID": ["09i06HqvBP2ZaQ4Pk+p3Mt"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 245}, "_enabled": true, "__prefab": {"__id__": 265}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1b+uySAFEmpPvojvF3FdH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 245}, "_enabled": true, "__prefab": {"__id__": 267}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "a120e0be-766d-4402-8fb1-eed150b91058@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23WTB1GRBPLaESNnPdIJ9B"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 245}, "_enabled": true, "__prefab": {"__id__": 269}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 32, "_originalHeight": 38, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1a9SmY+o9BXprTOz1Vyk1j"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 245}, "_enabled": true, "__prefab": {"__id__": 271}, "watchPath": "*.<PERSON><PERSON><PERSON>", "foreachChildMode": false, "condition": 0, "foreachChildType": 0, "valueA": 0, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 245}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "19f0QA3YpDtL6ff/DMh3f/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a7ql+zBbxJEaP2muLfuAza", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 222}, "_enabled": true, "__prefab": {"__id__": 274}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abvgvIPItHpb5AjxLBLEyP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76wnxMBcNMzJNmaNK24Y71", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 171}, "_prefab": {"__id__": 277}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 276}, "asset": {"__uuid__": "c1093e7c-9dd6-4c78-a9c6-28a6a82bc9be", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 278}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "f4kZnDOOBOz4W+1Fe4WDcX", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 279}, {"__id__": 281}, {"__id__": 282}, {"__id__": 283}, {"__id__": 284}, {"__id__": 286}, {"__id__": 288}, {"__id__": 289}, {"__id__": 290}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 280}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -170.065, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 280}, "propertyPath": ["_name"], "value": "record"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 280}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 280}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 285}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 145.125, "height": 32.76}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 287}, "propertyPath": ["_dataID"], "value": "ContentToday"}, {"__type__": "cc.TargetInfo", "localID": ["47zQiH5ZpHGZOMEAJOGjcx"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 287}, "propertyPath": ["templateMode"], "value": true}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 287}, "propertyPath": ["watchPathArr", "length"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 287}, "propertyPath": ["watchPathArr", "0"], "value": "*.lbl<PERSON><PERSON><PERSON>"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 171}, "_enabled": true, "__prefab": {"__id__": 292}, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 410}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66gb+tcChBxZMhS3cuW/nP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 171}, "_enabled": true, "__prefab": {"__id__": 294}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d0166250-efdb-40e0-8b61-18093d8b62ba@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33vNnFHLRCJb6/EmobjXyY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "96amr5mSNDCo+oOae2VM0H", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 297}, "_contentSize": {"__type__": "cc.Size", "width": 590, "height": 820}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4aZQCb2DdDqYTM4z0iAuRw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 299}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "26a920e5-66d2-4ef8-929e-d66e41fc46a0@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53SRtbqZNDTKbIxpjbBlwM"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": {"__id__": 301}, "_resizeMode": 1, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 50, "_paddingRight": 50, "_paddingTop": 50, "_paddingBottom": 50, "_spacingX": 0, "_spacingY": 20, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 2, "_constraintNum": 1, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "620O/rm/ZEb6YX0ZxV4CmW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c9Cl0DW4JF4ZSXUsxlcb3i", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "title", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 304}, {"__id__": 323}], "_active": true, "_components": [{"__id__": 333}, {"__id__": 335}, {"__id__": 337}], "_prefab": {"__id__": 339}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 398.086, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 303}, "_prefab": {"__id__": 305}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 304}, "asset": {"__uuid__": "8728975f-5233-40d6-9a87-08626aba6287", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 306}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "14LBNjJuJBW7K0o2fekUeg", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 307}, {"__id__": 309}, {"__id__": 310}, {"__id__": 311}, {"__id__": 312}, {"__id__": 314}, {"__id__": 316}, {"__id__": 317}, {"__id__": 318}, {"__id__": 319}, {"__id__": 320}, {"__id__": 321}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 308}, "propertyPath": ["_name"], "value": "title"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 308}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 308}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 308}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 313}, "propertyPath": ["_enableOutline"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 315}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 266.0390625, "height": 48.1}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 313}, "propertyPath": ["_string"], "value": "Settings"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 313}, "propertyPath": ["_fontSize"], "value": 34}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 313}, "propertyPath": ["_actualFontSize"], "value": 34}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 313}, "propertyPath": ["_outlineColor"], "value": {"__type__": "cc.Color", "r": 29, "g": 96, "b": 117, "a": 255}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 313}, "propertyPath": ["_lineHeight"], "value": 35}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 322}, "propertyPath": ["_dataID"], "value": "PlayerInformation"}, {"__type__": "cc.TargetInfo", "localID": ["09i06HqvBP2ZaQ4Pk+p3Mt"]}, {"__type__": "cc.Node", "_name": "closeBtn", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 303}, "_children": [], "_active": true, "_components": [{"__id__": 324}, {"__id__": 326}, {"__id__": 328}, {"__id__": 330}], "_prefab": {"__id__": 332}, "_lpos": {"__type__": "cc.Vec3", "x": 271.663, "y": 14.970000000000027, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 323}, "_enabled": true, "__prefab": {"__id__": 325}, "_contentSize": {"__type__": "cc.Size", "width": 68, "height": 69}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "feQxtseBBAmr5IgkGIjIrP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 323}, "_enabled": true, "__prefab": {"__id__": 327}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6b00af2b-7fca-4f13-9e16-883aaad6decb@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "83jvKbhlhGpJxzBhUNwwr0"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 323}, "_enabled": true, "__prefab": {"__id__": 329}, "clickEvents": [], "_interactable": true, "_transition": 3, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": {"__uuid__": "b42a5eee-72ad-40c9-9f38-222114c8ae8a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": {"__id__": 323}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2czDRzWc9BT5qW4SCj8uky"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 323}, "_enabled": true, "__prefab": {"__id__": 331}, "_alignFlags": 33, "_target": null, "_left": 0, "_right": -10.663000000000011, "_top": -12.970000000000027, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86W4DjDxNJaIYMfh3omAbf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a95kLmHpBByqsklSf271iu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 303}, "_enabled": true, "__prefab": {"__id__": 334}, "_contentSize": {"__type__": "cc.Size", "width": 590, "height": 73}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "943ZT+JMpIg4H+057tLZ+K"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 303}, "_enabled": true, "__prefab": {"__id__": 336}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "120862de-b58c-449f-9258-e8eb1063d993@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89STazpY1N5Z9KKWJdZi0U"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 303}, "_enabled": true, "__prefab": {"__id__": 338}, "_alignFlags": 17, "_target": null, "_left": 318.5, "_right": 315.5, "_top": -384.586, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 116, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22b7FbYP9OSKfswX02ulXr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "62ST9SqUdAKYKJDa7YLhvi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 10}, "_prefab": {"__id__": 341}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 340}, "asset": {"__uuid__": "80a6dff8-dc06-4f9d-a2c8-03170daa1ddd", "__expectedType__": "cc.Prefab"}, "fileId": "f05XX5jrpEOYwv6lCoUIav", "instance": {"__id__": 342}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "d4Z9pMomhEcaGWdCOeqQ1c", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 343}, {"__id__": 345}, {"__id__": 346}, {"__id__": 347}, {"__id__": 348}, {"__id__": 350}, {"__id__": 352}, {"__id__": 354}, {"__id__": 355}, {"__id__": 356}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 344}, "propertyPath": ["_name"], "value": "uuid"}, {"__type__": "cc.TargetInfo", "localID": ["f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 344}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -381.57, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 344}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 344}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 349}, "propertyPath": ["_string"], "value": "uid:00000"}, {"__type__": "cc.TargetInfo", "localID": ["4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 351}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 63.546875, "height": 37.8}}, {"__type__": "cc.TargetInfo", "localID": ["6cPaWujd9BCJYdUAjIP0JG"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 353}, "propertyPath": ["templateMode"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["48BGi+JnJOKpaEdqvfVVS1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 353}, "propertyPath": ["watchPathArr", "length"], "value": 1}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 353}, "propertyPath": ["watchPathArr", "0"], "value": "role.guuid"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 353}, "propertyPath": ["_dataID"], "value": "UUID"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": {"__id__": 358}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "01JOSufuxD2b+12LNVCnrm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "87st9QOS1Ma5hMOrGh/5kp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 361}, "_contentSize": {"__type__": "cc.Size", "width": 749.9999999999999, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77N2cid5pKDpXplRH/AWEU"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 363}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 2, "_originalHeight": 2, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63zNQq8NlBQ5QWzOJ4Kgjs"}, {"__type__": "57053nawwtNYJmviHWY0LN+", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 365}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27PEsFUVpOZql3uqKN41v2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a0daVw8DRLi6ToMaTA0VS2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 340}, {"__id__": 304}, {"__id__": 276}, {"__id__": 246}, {"__id__": 224}, {"__id__": 179}, {"__id__": 138}, {"__id__": 108}, {"__id__": 74}, {"__id__": 54}, {"__id__": 31}, {"__id__": 13}, {"__id__": 2}]}]