{"1": {"desc": "蔬菜", "sceneType": 3, "levelName": "蔬菜", "difficulty": 1, "firstItem": "南瓜|1.2|0.6:3,土豆|1.5|0.8:3,大蒜|1.5|1:3", "itemPrefabPaths": "prefabs/game/", "item": "半个茄子|1.5|1.2:6,南瓜|1.2|0.6:6,土豆|1.5|0.8:6,大蒜|1.5|1:6,洋葱半个|2|1.2:6,玉米|1.6|0.7:6,甜菜|1.5|1:6,生姜|1.7|1.3:6,番茄|2|1.5:6,红卷心菜|1.4|0.7:6,红椒|1.3|1.2:6,红薯|1.2|0.8:6,绿卷心菜|1.4|0.8:6,胡桃南瓜|1.1|0.7:6,胡萝卜|1.5|1:6,芦笋|2|1:6,花椰菜|1.1|0.7:6,青椒|1.3|1.2:6,韭葱|0.9|0.4:6,黄椒|1.3|1.2:6,黄瓜|1|0.6:6,黄西葫芦|1.3|1.2:6", "targetTotal": 210, "levelTime": 600, "startItem": 50, "threshold": "80|90|2,60|80|1,50|70|1", "spawnAnimation": "bottomPush", "spawnInterval": 3000, "bgPath": "img/bg/bg_1", "wallType": 1}, "2": {"desc": "烘培", "sceneType": 3, "levelName": "面包", "difficulty": 1, "firstItem": "面包_1|3|1.6:6,面包_2|3|1.6:6,面包_3|3|1.6:6", "itemPrefabPaths": "prefabs/game/", "item": "面包_1|3|1.6:6,面包_2|3|1.6:6,面包_3|3|1.6:6,面包_4|3|1.6:6,面包_5|3|1.6:6,面包_6|3|1.6:6,面包_7|3|1.6:6,面包_8|3|1.6:6,面包_9|3|1.6:6,面包_10|3|1.6:6,面包_11|3|1.6:6,面包_12|3|1.6:6,面包_13|3|1.6:6,面包_14|3|1.6:6,面包_15|3|1.6:6,面包_16|3|1.6:6,面包_17|3|1.6:6,面包_18|3|1.6:6,面包_19|3|1.6:6,面包_20|3|1.6:6,面包_21|3|1.6:6,面包_22|3|1.6:6,面包_23|3|1.6:6,面包_24|3|1.6:6,面包_25|3|1.6:6,面包_26|3|1.6:6,面包_27|3|1.6:6,面包_28|3|1.6:6,面包_29|3|1.6:6,面包_30|3|1.6:6", "targetTotal": 210, "levelTime": 600, "startItem": 50, "threshold": "80|90|2,60|80|1,50|70|1", "spawnAnimation": "bottomPush", "spawnInterval": 3000, "bgPath": "img/bg/bg_2", "wallType": 1}, "3": {"desc": "寿司", "sceneType": 3, "levelName": "寿司", "difficulty": 1, "firstItem": "日式寿司_01|2|1:6,日式寿司_02|2|1:6,日式寿司_03|2|1:6", "itemPrefabPaths": "prefabs/game/", "item": "日式寿司_01|2|1:6,日式寿司_02|2|1:6,日式寿司_03|2|1:6,日式寿司_04|2|1:6,日式寿司_05|2|1:6,日式寿司_06|2|1:6,日式寿司_07|2|1:6,日式寿司_08|2|1:6,日式寿司_09|2|1:6,日式寿司_10|2|1:6,日式寿司_11|2|1:6,日式寿司_12|2|1:6,日式寿司_13|2|1:6,日式寿司_14|2|1:6,日式寿司_15|2|1:6,日式寿司_16|2|1:6,日式寿司_17|2|1:6,日式寿司_18|2|1:6,日式寿司_19|2|1:6,日式寿司_20|2|1:6,日式寿司_21|2|1:6,日式寿司_22|2|1:6,日式寿司_23|2|1:6,日式寿司_24|2|1:6,日式寿司_25|2|1:6,日式寿司_26|2|1:6,日式寿司_27|2|1:6", "targetTotal": 210, "levelTime": 600, "startItem": 50, "threshold": "80|90|2,60|80|1,50|70|1", "spawnAnimation": "bottomPush", "spawnInterval": 3000, "bgPath": "img/bg/bg_3", "wallType": 1}}