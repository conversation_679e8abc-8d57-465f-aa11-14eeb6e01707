[{"__type__": "cc.Prefab", "_name": "寿司_9", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "寿司_9", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}, {"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.<PERSON><PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 3}, "_materials": [{"__uuid__": "104c8323-52e2-4bfd-bbe5-98a7cb86aa74", "__expectedType__": "cc.Material"}], "_visFlags": 0, "bakeSettings": {"__id__": 4}, "_mesh": {"__uuid__": "dade7b42-b69c-4c97-a676-15aec121cf84@7528b", "__expectedType__": "cc.Mesh"}, "_shadowCastingMode": 1, "_shadowReceivingMode": 1, "_shadowBias": 0, "_shadowNormalBias": 0, "_reflectionProbeId": -1, "_reflectionProbeBlendId": -1, "_reflectionProbeBlendWeight": 0, "_enabledGlobalStandardSkinObject": false, "_enableMorph": true, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5c3LAMgDFJA6r6rTDwHdEs"}, {"__type__": "cc.ModelBakeSettings", "texture": null, "uvParam": {"__type__": "cc.Vec4", "x": 0, "y": 0, "z": 0, "w": 0}, "_bakeable": false, "_castShadow": true, "_receiveShadow": true, "_recieveShadow": true, "_lightmapSize": 64, "_useLightProbe": false, "_bakeToLightProbe": true, "_reflectionProbeType": 0, "_bakeToReflectionProbe": true}, {"__type__": "cc.RigidBody", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 6}, "_group": 4, "_type": 1, "_mass": 1, "_allowSleep": true, "_linearDamping": 0.1, "_angularDamping": 0.1, "_useGravity": true, "_linearFactor": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_angularFactor": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aj20EDRVIFIL5LLT/joT+"}, {"__type__": "cc.<PERSON><PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 8}, "_material": null, "_isTrigger": false, "_center": {"__type__": "cc.Vec3", "x": 0.04662080109119415, "y": -0.014241814613342285, "z": -0.009906202554702759}, "_radius": 0.5064785778522491, "_cylinderHeight": 0.5151490569114685, "_direction": 1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cdkJJq9SNJ8JriMCFSPavx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a8ZarvbRxBpore8gRGiq+i", "targetOverrides": null}]