[1, ["10TIMjUuJL/bvlmKfLhqp0", "da3ntCtpxMl6Z2Fa7BIc+E@d9218"], ["node", "root", "data", "_mesh"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11]], [[3, 0, 2], [0, 0, 2], [1, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 3], [5, 0, 1, 2, 2], [6, 0, 1, 2, 3, 4, 3], [7, 0, 1, 2, 3, 4, 5, 5], [8, 0, 1, 2, 3]], [[[[1, "寿司_3"], [2, "寿司_3", [[3, 1, -2, [0, "ddQmMLCTRM+ZcSkkRadOHo"], [0], [4, true, true], 1], [5, 4, -3, [0, "58DgHOE8xPlKQQ6tnRNuLR"]], [6, 0.8984953165054321, 0, -4, [0, "d5tdxvAiVKOqjkA96RcBLm"], [1, 0.0008578896522521973, -0.003106355667114258, 0.02153259515762329]]], [7, "c3UOXZbGtO/43t+6+omUfK", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]], [[[8, ".bin", 3367823309, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 60000, "length": 9414, "count": 4707, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 60000, "count": 1250, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.8976374268531799, -0.8982696533203125, -0.3508329391479492], "maxPosition", 8, [1, 0.8993532061576843, 0.892056941986084, 0.3938981294631958]]], -1], 0, 0, [], [], []]]]