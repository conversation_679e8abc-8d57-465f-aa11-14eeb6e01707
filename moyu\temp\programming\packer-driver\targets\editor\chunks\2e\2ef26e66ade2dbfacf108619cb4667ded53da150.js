System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, oops, Root, ecs, Initialize, Environment, Platform, ShareConfig, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, Main;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfRoot(extras) {
    _reporterNs.report("Root", "../../extensions/oops-plugin-framework/assets/core/Root", _context.meta, extras);
  }

  function _reportPossibleCrUseOfecs(extras) {
    _reporterNs.report("ecs", "../../extensions/oops-plugin-framework/assets/libs/ecs/ECS", _context.meta, extras);
  }

  function _reportPossibleCrUseOfInitialize(extras) {
    _reporterNs.report("Initialize", "./game/initialize/Initialize", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnvironment(extras) {
    _reporterNs.report("Environment", "./tsrpc/models/ShareConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlatform(extras) {
    _reporterNs.report("Platform", "./tsrpc/models/ShareConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfShareConfig(extras) {
    _reporterNs.report("ShareConfig", "./tsrpc/models/ShareConfig", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      Root = _unresolved_3.Root;
    }, function (_unresolved_4) {
      ecs = _unresolved_4.ecs;
    }, function (_unresolved_5) {
      Initialize = _unresolved_5.Initialize;
    }, function (_unresolved_6) {
      Environment = _unresolved_6.Environment;
      Platform = _unresolved_6.Platform;
      ShareConfig = _unresolved_6.ShareConfig;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0eec0s4qrZF7onPlYBrD+y+", "Main", undefined);
      /*
       * @Author: dgflash
       * @Date: 2021-07-03 16:13:17
       * @LastEditors: dgflash
       * @LastEditTime: 2022-08-05 18:25:56
       */


      __checkObsolete__(['_decorator', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Main", Main = (_dec = ccclass('Main'), _dec2 = property({
        type: Node,
        tooltip: '游戏初始画面'
      }), _dec(_class = (_class2 = class Main extends (_crd && Root === void 0 ? (_reportPossibleCrUseOfRoot({
        error: Error()
      }), Root) : Root) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "initial", _descriptor, this);
        }

        iniStart() {
          // 🔧 开始游戏完整初始化计时
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.start('游戏完整初始化'); // profiler.hideStats();
          // 使用LogControl自动设置日志级别

          const isProduction = (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).isProduction;

          if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).environment === (_crd && Environment === void 0 ? (_reportPossibleCrUseOfEnvironment({
            error: Error()
          }), Environment) : Environment).PRODUCTION_FACEBOOK) {// Facebook生产环境：超精简模式
          } else if (isProduction) {// 其他生产环境：精简模式
          } else {// 开发环境：完整日志
          } // 🚀 优化：延迟初始化单例模块，减少启动时间


          this.initializeCoreModules();
        }
        /** 🔧 优化：分阶段初始化核心模块 */


        async initializeCoreModules() {
          const {
            smc
          } = await _context.import("__unresolved_6"); // 只初始化最基础的模块

          smc.initialize = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
            error: Error()
          }), ecs) : ecs).getEntity(_crd && Initialize === void 0 ? (_reportPossibleCrUseOfInitialize({
            error: Error()
          }), Initialize) : Initialize); // 🚀 Facebook模块懒加载

          if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).FACEBOOK) {
            const [{
              FBInstantManager
            }, {
              FacebookGameEvents
            }] = await Promise.all([_context.import("__unresolved_7"), _context.import("__unresolved_8")]);
            smc.fbInstantManager = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
              error: Error()
            }), ecs) : ecs).getEntity(FBInstantManager);
            smc.facebookGameEvents = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
              error: Error()
            }), ecs) : ecs).getEntity(FacebookGameEvents);
          } // 🚀 其他业务模块延迟加载


          this.loadBusinessModulesLater();
        }
        /** 🔧 业务模块延迟加载 - 优化版本 */


        async loadBusinessModulesLater() {
          // 🚀 优化：立即开始加载关键模块，不等待空闲时间
          this.loadCriticalModules(); // 在空闲时间加载非关键模块

          requestIdleCallback(async () => {
            await this.loadNonCriticalModules();
          });
        }
        /** 🚀 立即加载关键模块 */


        async loadCriticalModules() {
          try {
            const [{
              smc
            }, {
              Role
            }, {
              SimpleSceneManager
            }] = await Promise.all([_context.import("__unresolved_9"), _context.import("__unresolved_10"), _context.import("__unresolved_11")]); // 🚀 优先初始化关键模块

            smc.role = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
              error: Error()
            }), ecs) : ecs).getEntity(Role);
            smc.sceneMgr = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
              error: Error()
            }), ecs) : ecs).getEntity(SimpleSceneManager);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('✅ 关键模块加载完成');
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 关键模块加载失败:', error);
          }
        }
        /** 🔧 加载非关键模块 */


        async loadNonCriticalModules() {
          try {
            const [{
              smc
            }, {
              Account
            }, {
              Guide
            }, {
              CameraEntity
            }] = await Promise.all([_context.import("__unresolved_12"), _context.import("__unresolved_13"), _context.import("__unresolved_14"), _context.import("__unresolved_15")]); // 🚀 性能监控已移除 - 使用Cocos内置profiler即可
            // 开发环境启用内置性能面板

            if (!(_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
              error: Error()
            }), ShareConfig) : ShareConfig).isProduction) {
              const {
                profiler
              } = await _context.import('cc');
              profiler.showStats();
            } // 🚀 直接设置资源下载并发数（替代ResourceOptimizer）


            const {
              assetManager
            } = await _context.import('cc');
            assetManager.downloader.maxRequestsPerFrame = 20; // 🚀 增加并发数
            // 初始化非关键业务模块

            smc.account = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
              error: Error()
            }), ecs) : ecs).getEntity(Account);
            smc.guide = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
              error: Error()
            }), ecs) : ecs).getEntity(Guide);
            smc.camera = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
              error: Error()
            }), ecs) : ecs).getEntity(CameraEntity);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('✅ 非关键模块加载完成');
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 非关键模块加载失败:', error);
          }
        }

        initGui() {
          // 🚀 延迟加载UI配置
          requestIdleCallback(async () => {
            const {
              UIConfigData
            } = await _context.import("__unresolved_16");
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.init(UIConfigData);
          });
        }

        initEcsSystem() {// ECS系统现在在 loadBusinessModulesLater 中初始化
        }

        async run() {
          try {
            // Facebook平台初始化
            if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
              error: Error()
            }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
              error: Error()
            }), Platform) : Platform).FACEBOOK) {
              await this.initializeFacebook();
            } // 开始游戏初始化


            const {
              smc
            } = await _context.import("__unresolved_17");
            smc.initialize.load(this.initial);
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('游戏初始化失败:', error); // 生产环境显示错误

            if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
              error: Error()
            }), ShareConfig) : ShareConfig).environment === (_crd && Environment === void 0 ? (_reportPossibleCrUseOfEnvironment({
              error: Error()
            }), Environment) : Environment).PRODUCTION_FACEBOOK) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).gui.toast('Facebook连接失败，请刷新页面重试');
              return;
            }

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast('游戏启动失败，请刷新页面重试');
          }
        }
        /** 🔧 Facebook初始化优化 */


        async initializeFacebook() {
          const {
            smc
          } = await _context.import("__unresolved_18");
          const maxRetries = 3;
          let initSuccess = false;

          for (let i = 0; i < maxRetries; i++) {
            try {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness(`🚀 Facebook初始化尝试 ${i + 1}/${maxRetries}...`);
              await smc.fbInstantManager.fbInit();
              await smc.facebookGameEvents.onPlayerFirstEntry(); // 验证Facebook数据

              const fbData = smc.fbInstantManager.getFacebookLoginData();

              if (!fbData || !fbData.facebookId) {
                throw new Error('Facebook数据不完整或玩家ID为空');
              }

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ Facebook初始化成功，玩家ID:', fbData.facebookId);
              initSuccess = true;
              break;
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logWarn(`⚠️ Facebook初始化失败 (尝试 ${i + 1}/${maxRetries}):`, error);

              if (i < maxRetries - 1) {
                const retryDelay = (i + 1) * 1000;
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness(`⏳ 等待 ${retryDelay}ms 后重试...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
              }
            }
          }

          if (!initSuccess) {
            const errorMsg = 'Facebook多次初始化失败';
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError(errorMsg);

            if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
              error: Error()
            }), ShareConfig) : ShareConfig).environment === (_crd && Environment === void 0 ? (_reportPossibleCrUseOfEnvironment({
              error: Error()
            }), Environment) : Environment).PRODUCTION_FACEBOOK) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).gui.toast('Facebook连接失败，请刷新页面重试');
              return;
            } else {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logWarn('⚠️ 非生产环境：继续游戏启动（降级模式）');
            }
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "initial", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2ef26e66ade2dbfacf108619cb4667ded53da150.js.map