[1, ["1eWFsMVo9G2Iy50BMhdO0J@f9941", "5dZJ5VKmVMn4WQldoN7+vA@f9941"], ["node", "_spriteFrame", "root", "_target", "data"], [["cc.Node", ["_name", "_layer", "_components", "_prefab", "_parent", "_children", "_lpos"], 1, 9, 4, 1, 2, 5], ["cc.UITransform", ["_name", "node", "__prefab", "_contentSize"], 2, 1, 4, 5], ["cc.Label", ["_string", "_actualFontSize", "_overflow", "_cacheMode", "_fontSize", "_enableWrapText", "node", "__prefab", "_color"], -3, 1, 4, 5], ["110c8vEd5NEPL/N9meGQnaX", ["_dataID", "_name", "node", "__prefab"], 1, 1, 4], ["cc.Sprite", ["_name", "_type", "node", "__prefab", "_spriteFrame"], 1, 1, 4, 6], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["_name", "_transition", "_zoomScale", "node", "__prefab", "_normalColor", "_target"], 0, 1, 4, 5, 1], ["9f88b3zWbpKhrJ34QD0++U8", ["node", "__prefab"], 3, 1, 4]], [[6, 0, 2], [7, 0, 1, 2, 3, 4, 5, 5], [1, 1, 2, 3, 1], [5, 0, 2], [0, 0, 1, 5, 2, 3, 3], [0, 0, 1, 4, 2, 3, 6, 3], [0, 0, 1, 4, 5, 2, 3, 6, 3], [0, 0, 1, 4, 2, 3, 3], [1, 0, 1, 2, 3, 2], [2, 0, 1, 4, 2, 3, 6, 7, 8, 6], [2, 0, 1, 2, 5, 3, 6, 7, 8, 6], [3, 0, 2, 3, 2], [3, 1, 0, 2, 3, 3], [4, 0, 1, 2, 3, 4, 3], [4, 2, 3, 4, 1], [8, 0, 1, 2, 3, 4, 5, 6, 4], [9, 0, 1, 1]], [[3, "SystemError"], [4, "SystemError", 33554432, [-5, -6], [[2, -2, [0, "37o1ko8nFK1oK3xmkqtwMG"], [5, 735, 737]], [14, -3, [0, "a0vC/W2QdFb7e69t18Gf21"], 1], [16, -4, [0, "bdSASJmNlHoLt8RU7L7Twq"]]], [1, "c7ZN3fr+hJoZmUZs2A0Ye0", null, null, null, -1, 0]], [6, "btnCancel", 33554432, 1, [-11], [[8, "btn_cancel<UITransform>", -7, [0, "79W4eqKFxMapzMreg0cRB3"], [5, 141, 106]], [13, "btn_cancel<Sprite>", 1, -8, [0, "08v+UeZXZH+a5Tu0BOuubs"], 0], [15, "btn_cancel<Button>", 3, 0.9, -10, [0, "23mGTQh9JPcaNniCjfDZ/c"], [4, 4292269782], -9]], [1, "3aIfIm6bVFaK62tZkb2Iic", null, null, null, 1, 0], [1, 0, -255.48400000000004, 0]], [5, "lblContent", 33554432, 1, [[2, -12, [0, "49SU+NRCVFC7lotcT5Arsj"], [5, 420, 50.4]], [9, "", 42, 42, 3, 1, -13, [0, "867mEteZhIVLc5LxzUTUW5"], [4, 4278190335]], [11, "ServerError", -14, [0, "b5ZWIffeBEXpXPiEfWbn4Z"]]], [1, "7fk7nP/NJBI7+oI60VkgZO", null, null, null, 1, 0], [1, 0, 93.62699999999995, 0]], [7, "lblBtnStr", 33554432, 2, [[2, -15, [0, "73nkyWjZ9I/oxF+6QfBLqr"], [5, 100, 40]], [10, "", 40, 1, false, 1, -16, [0, "0eYsv4mJlIa7/bSpPQvt+7"], [4, 4278190080]], [12, "lab_ok<LanguageLabel>", "sure", -17, [0, "c5vY7CEaJOBbvqXXwmsDAU"]]], [1, "38cZMAuzVCOJPotiPg+6BM", null, null, null, 1, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 2, 0, 0, 2, 0, 0, 2, 0, 3, 2, 0, 0, 2, 0, -1, 4, 0, 0, 3, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 4, 1, 17], [0, 0], [1, 1], [0, 1]]