[1, ["56oY0PFUVKRJA95WSWzdXD", "e28dJLVXJC3YFObxU5gPQx@20cd1"], ["node", "root", "data", "_mesh"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lrot", "_euler"], 2, 9, 4, 5, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", [], 3], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.S<PERSON>ider", ["_radius", "node", "__prefab", "_center"], 2, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11]], [[3, 0, 2], [0, 0, 2], [1, 0, 1, 2, 3, 4, 2], [2, 0, 1, 2, 3, 4, 5, 2], [4, 1], [5, 0, 1, 2, 2], [6, 0, 1, 2, 3, 2], [7, 0, 1, 2, 3, 4, 5, 5], [8, 0, 1, 2, 3]], [[[[1, "红卷心菜"], [2, "红卷心菜", [[3, 1, -2, [0, "8fOMgQ46dMTrZHYK6SllgV"], [0], [4], 1], [5, 4, -3, [0, "4azN7pOGJHZ4Q6p2YdZryX"]], [6, 0.904, -4, [0, "bcTD8z21RLlozN+EqAY9q0"], [1, 0.006800323724746704, 0.17403313517570496, 0]]], [7, "65HvIVW5BHFIJs8R4v9WJO", null, null, null, -1, 0], [3, -0.6830127018922193, -0.18301270189221933, -0.1830127018922193, 0.6830127018922194], [1, -90, -30, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]], [[[8, ".bin", 2822636921, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 39488, "length": 6564, "count": 3282, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 39488, "count": 617, "stride": 64}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}, {"name": "a_texCoord1", "format": 21, "isNormalized": false}, {"name": "a_texCoord2", "format": 21, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.9386654496192932, -0.6959747672080994, -0.797048807144165], "maxPosition", 8, [1, 0.9522660970687866, 1.0440410375595093, 0.9353013634681702]]], -1], 0, 0, [], [], []]]]