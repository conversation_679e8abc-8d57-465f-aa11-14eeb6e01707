{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/role/Role.ts"], "names": ["oops", "ecs", "LanguageData", "VM", "GameConst", "Platform", "ShareConfig", "PropType", "RecordType", "SceneItemType", "GameStorageConfig", "DataManager", "GameEvent", "smc", "RoleModelComp", "Role", "register", "Entity", "RoleModel", "pendingPropUpdates", "dataManager", "init", "add", "getInstance", "destroy", "removeFromViewModel", "remove", "quickInitialize", "log", "logBusiness", "isNewPlayer", "initializeNewPlayerData", "loadDataInBackground", "ensureDataLoaded", "error", "log<PERSON>arn", "loadData", "validateNetworkConnection", "response", "net", "hcGame", "callApi", "isSucc", "waitForUserDataInitialization", "handleAPIError", "err", "logError", "updateUserData", "newData", "isFirstInitialization", "hasUserData", "ensureDataIntegrity", "userGameData", "mergeUserData", "updateViewModel", "message", "dispatchEvent", "UserDataInitialized", "getPropData", "propType", "validateUserData", "createDefaultPropData", "propData", "propUseData", "defaultData", "getPropsDataByType", "tryCostProp", "args", "showToast", "failureMessageKey", "gui", "toast", "absAmount", "Math", "abs", "amount", "canUse", "canUseProp", "getLangByID", "updatePropData", "updateProp", "reason", "userData", "isTemporaryData", "lastUpdateTime", "Date", "UseProp", "queuePropUpdateForSync", "batchUpdateProps", "updates", "update", "success", "getGameProgress", "index", "getPassIndex", "getNextLevelIndex", "currentProgress", "progress", "nextLevel", "Max", "updateGameProgress", "newIndex", "isGM", "targetIndex", "isGm", "GamePass", "oldIndex", "hasLoginRecord", "storage", "get", "SSOToken", "userDumpKey", "get<PERSON>son", "UserDumpKey", "String", "completeNewPlayerGuide", "once", "syncNewPlayerStatusToServer", "BasicInfoUpdate", "push", "syncPendingPropUpdates", "length", "currentTime", "basicUserData", "key", "guuid", "googleUuid", "facebookId", "userName", "nick<PERSON><PERSON>", "sex", "createtime", "openid", "platform", "platformType", "avatar", "avatarId", "countryCode", "passTimes", "currCountryPassTimes", "lastChangeCountryTime", "selfCountryRank", "createDefaultProps", "recordData", "isGuest", "lastStep", "newPlayerProps", "propTypes", "PropsMoveOut", "PropsTips", "PropsReShuffle", "PropsDayLeftCount", "PropsRevive", "PropsExp", "PropsCoin", "for<PERSON>ach", "getNewPlayerDefaultAmount", "propCount", "Object", "keys", "setTimeout", "forceCompleteUserDataLoad", "tempUserData", "performCompleteLogin", "LoginViewComp", "FACEBOOK", "loginSuccess", "doFacebookLogin", "login<PERSON>iewComp", "loginGuestButton", "getRecordData", "recordType", "dateString", "targetDate", "toDateString", "getLevelChallengeCount", "levelId", "Level", "levelDetails", "level<PERSON><PERSON>", "attempts", "getCurrentLevelChallengeCount", "currentLevelId", "getUserGameData", "Error", "maxRetries", "retryCount", "Promise", "resolve", "data", "target", "source", "sourceValue", "targetValue", "Array", "isArray", "defaultAmount", "propId", "desc", "getTime", "lastResetTime", "newPlayerDefaultProps", "moveOut", "tips", "reShuffle", "dayFreeLimts", "revive", "viewModelData", "userId", "level", "apiName", "errorMessage", "code", "toString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,E,iBAAAA,E;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;;AAEfC,MAAAA,Q,iBAAAA,Q;AACAC,MAAAA,U,iBAAAA,U;AAEAC,MAAAA,a,iBAAAA,a;;AAGKC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,W,kBAAAA,W;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,G,kBAAAA,G;;AACAC,MAAAA,a,kBAAAA,a;;;;;;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;sBAEaC,I,WADZ;AAAA;AAAA,sBAAIC,QAAJ,CAAa,MAAb,C,gBAAD,MACaD,IADb,SAC0B;AAAA;AAAA,sBAAIE,MAD9B,CACqC;AAAA;AAAA;AAAA,eACjCC,SADiC;AAGjC;AAHiC,eAIzBC,kBAJyB,GAQ5B,EAR4B;AAAA,eASzBC,WATyB;AAAA;;AAWjC;AAEUC,QAAAA,IAAI,GAAG;AACb,eAAKC,GAAL;AAAA;AAAA;AACA,eAAKF,WAAL,GAAmB;AAAA;AAAA,0CAAYG,WAAZ,EAAnB;AACH;;AAEDC,QAAAA,OAAO,GAAS;AACZ,eAAKC,mBAAL;AACA,eAAKC,MAAL;AAAA;AAAA;AACH,SArBgC,CAuBjC;;AAEA;AACJ;AACA;;;AACyB,cAAfC,eAAe,GAAkB;AACnC,cAAI;AACA;AAAA;AAAA,8BAAKC,GAAL,CAASC,WAAT,CAAqB,mBAArB;;AAEA,gBAAI,KAAKC,WAAL,EAAJ,EAAwB;AACpB;AAAA;AAAA,gCAAKF,GAAL,CAASC,WAAT,CAAqB,eAArB;AACA,oBAAM,KAAKE,uBAAL,EAAN,CAFoB,CAIpB;;AACA,mBAAKC,oBAAL;AACH,aAND,MAMO;AACH;AACA,oBAAM,KAAKC,gBAAL,EAAN;AACH;AACJ,WAbD,CAaE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKN,GAAL,CAASO,OAAT,CAAiB,iBAAjB,EAAoCD,KAApC,EADY,CAEZ;;AACA,kBAAM,KAAKH,uBAAL,EAAN;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACkB,cAARK,QAAQ,GAAqB;AAC/B;AAAA;AAAA,4BAAKR,GAAL,CAASC,WAAT,CAAqB,gBAArB;;AAEA,cAAI,CAAC,KAAKQ,yBAAL,EAAL,EAAuC;AACnC,mBAAO,KAAP;AACH;;AAED,cAAI;AACA,kBAAMC,QAAQ,GAAG,MAAM;AAAA;AAAA,4BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,EAAnC,CAAvB;;AAEA,gBAAIH,QAAQ,CAACI,MAAb,EAAqB;AACjB,oBAAM,KAAKC,6BAAL,EAAN;AACA,qBAAO,IAAP;AACH,aAHD,MAGO;AACH,mBAAKC,cAAL,CAAoB,UAApB,EAAgCN,QAAQ,CAACO,GAAzC;AACA,qBAAO,KAAP;AACH;AACJ,WAVD,CAUE,OAAOX,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKN,GAAL,CAASkB,QAAT,CAAkB,aAAlB,EAAiCZ,KAAjC;AACA,mBAAO,KAAP;AACH;AACJ;AAED;AACJ;AACA;;;AACIa,QAAAA,cAAc,CAACC,OAAD,EAA8B;AACxC,gBAAMC,qBAAqB,GAAG,CAAC,KAAKC,WAAL,EAA/B,CADwC,CAGxC;;AACA,eAAKC,mBAAL,CAAyBH,OAAzB,EAJwC,CAMxC;;AACA,cAAI,KAAK9B,SAAL,CAAekC,YAAnB,EAAiC;AAC7B,iBAAKC,aAAL,CAAmB,KAAKnC,SAAL,CAAekC,YAAlC,EAAgDJ,OAAhD;AACH,WAFD,MAEO;AACH,iBAAK9B,SAAL,CAAekC,YAAf,GAA8BJ,OAA9B;AACH,WAXuC,CAaxC;;;AACA,eAAKM,eAAL,GAdwC,CAgBxC;;AACA,cAAIL,qBAAJ,EAA2B;AACvB;AAAA;AAAA,8BAAKrB,GAAL,CAASC,WAAT,CAAqB,gBAArB;AACA;AAAA;AAAA,8BAAK0B,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,wCAAUC,mBAArC,EAA0D,KAAKvC,SAAL,CAAekC,YAAzE;AACH;AACJ,SApGgC,CAsGjC;;AAEA;AACJ;AACA;;;AACIM,QAAAA,WAAW,CAACC,QAAD,EAA0B;AAAA;;AACjC,cAAI,CAAC,KAAKC,gBAAL,EAAL,EAA8B;AAC1B,mBAAO,KAAKC,qBAAL,CAA2BF,QAA3B,CAAP;AACH;;AAED,gBAAMG,QAAQ,4BAAG,KAAK5C,SAAL,CAAekC,YAAf,CAA4BW,WAA/B,qBAAG,sBAA0CJ,QAA1C,CAAjB;;AACA,cAAI,CAACG,QAAL,EAAe;AACX;AACA,kBAAME,WAAW,GAAG,KAAKH,qBAAL,CAA2BF,QAA3B,CAApB;AACA,iBAAKzC,SAAL,CAAekC,YAAf,CAA4BW,WAA5B,GAA0C,KAAK7C,SAAL,CAAekC,YAAf,CAA4BW,WAA5B,IAA2C,EAArF;AACA,iBAAK7C,SAAL,CAAekC,YAAf,CAA4BW,WAA5B,CAAwCJ,QAAxC,IAAoDK,WAApD;AACA,mBAAOA,WAAP;AACH;;AAED,iBAAOF,QAAP;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,kBAAkB,CAACN,QAAD,EAA0B;AACxC,iBAAO,KAAKD,WAAL,CAAiBC,QAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIO,QAAAA,WAAW,CACPC,IADO,EAEPC,SAAkB,GAAG,IAFd,EAGPC,iBAHO,EAIA;AACP,cAAI,CAAC,KAAKT,gBAAL,EAAL,EAA8B;AAC1B,gBAAIQ,SAAJ,EAAe;AACX;AAAA;AAAA,gCAAKE,GAAL,CAASC,KAAT,CAAe,UAAf;AACH;;AACD,mBAAO,KAAP;AACH;;AAED,gBAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAASP,IAAI,CAACQ,MAAd,CAAlB;AACA,gBAAMC,MAAM,GAAG,KAAKC,UAAL,CAAgBV,IAAI,CAACR,QAArB,EAA+Ba,SAA/B,CAAf;;AAEA,cAAI,CAACI,MAAD,IAAWR,SAAX,IAAwBC,iBAA5B,EAA+C;AAC3C;AAAA;AAAA,8BAAKC,GAAL,CAASC,KAAT,CAAe;AAAA;AAAA,8CAAaO,WAAb,CAAyBT,iBAAzB,KAA+CA,iBAA9D;AACH;;AAED,iBAAOO,MAAP;AACH;AAED;AACJ;AACA;;;AACwB,cAAdG,cAAc,CAACZ,IAAD,EAIC;AACjB,iBAAO,MAAM,KAAKa,UAAL,CAAgBb,IAAI,CAACR,QAArB,EAA+BQ,IAAI,CAACQ,MAApC,EAA4CR,IAAI,CAACc,MAAjD,CAAb;AACH;AAED;AACJ;AACA;;;AACIJ,QAAAA,UAAU,CAAClB,QAAD,EAAqBgB,MAArB,EAA8C;AACpD,gBAAMb,QAAQ,GAAG,KAAKJ,WAAL,CAAiBC,QAAjB,CAAjB;AACA,iBAAOG,QAAQ,CAACa,MAAT,IAAmBF,IAAI,CAACC,GAAL,CAASC,MAAT,CAA1B;AACH;AAED;AACJ;AACA;;;AACoB,cAAVK,UAAU,CAACrB,QAAD,EAAqBgB,MAArB,EAAqCM,MAArC,EAAwE;AAAA;;AACpF;AACA,cAAIN,MAAM,GAAG,CAAT,IAAc,CAAC,KAAKE,UAAL,CAAgBlB,QAAhB,EAA0Bc,IAAI,CAACC,GAAL,CAASC,MAAT,CAA1B,CAAnB,EAAgE;AAC5D;AAAA;AAAA,8BAAKL,GAAL,CAASC,KAAT,CAAe;AAAA;AAAA,8CAAaO,WAAb,CAAyB,gBAAzB,CAAf;AACA,mBAAO,KAAP;AACH,WALmF,CAOpF;;;AACA,gBAAMI,QAAQ,sBAAG,KAAKhE,SAAR,qBAAG,gBAAgBkC,YAAjC;;AACA,cAAK8B,QAAL,YAAKA,QAAD,CAAmBC,eAAvB,EAAwC;AACpC;AAAA;AAAA,8BAAKvD,GAAL,CAASC,WAAT,CAAqB,uBAArB,EADoC,CAGpC;;AACA,kBAAMiC,QAAQ,GAAG,KAAKG,kBAAL,CAAwBN,QAAxB,CAAjB;;AACA,gBAAIG,QAAJ,EAAc;AACVA,cAAAA,QAAQ,CAACa,MAAT,IAAmBA,MAAnB;AACAb,cAAAA,QAAQ,CAACsB,cAAT,GAA0B,IAAIC,IAAJ,EAA1B;AAEA;AAAA;AAAA,gCAAKzD,GAAL,CAASC,WAAT,CACK,aAAY8B,QAAS,IAAGgB,MAAM,GAAG,CAAT,GAAa,GAAb,GAAmB,EAAG,GAAEA,MAAO,OAD5D,EAJU,CAQV;;AACA,kBAAIA,MAAM,GAAG,CAAb,EAAgB;AACZ;AAAA;AAAA,kCAAKpB,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,4CAAU8B,OAArC,EAA8C;AAAE3B,kBAAAA,QAAF;AAAYgB,kBAAAA;AAAZ,iBAA9C;AACH,eAXS,CAaV;;;AACA,mBAAKY,sBAAL,CAA4B5B,QAA5B,EAAsCgB,MAAtC,EAA8CM,MAA9C;AACA,qBAAO,IAAP;AACH;AACJ;;AAED,cAAI;AACA,kBAAM3C,QAAQ,GAAG,MAAM;AAAA;AAAA,4BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,YAAvB,EAAqC;AACxDkB,cAAAA,QADwD;AAExDgB,cAAAA,MAFwD;AAGxDM,cAAAA,MAAM,EAAEA,MAAM,IAAI;AAHsC,aAArC,CAAvB;;AAMA,gBAAI3C,QAAQ,CAACI,MAAb,EAAqB;AACjB;AAAA;AAAA,gCAAKd,GAAL,CAASC,WAAT,CACK,aAAY8B,QAAS,IAAGgB,MAAM,GAAG,CAAT,GAAa,GAAb,GAAmB,EAAG,GAAEA,MAAO,EAD5D,EADiB,CAKjB;;AACA,kBAAIA,MAAM,GAAG,CAAb,EAAgB;AACZ;AAAA;AAAA,kCAAKpB,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,4CAAU8B,OAArC,EAA8C;AAAE3B,kBAAAA,QAAF;AAAYgB,kBAAAA;AAAZ,iBAA9C;AACH;;AAED,qBAAO,IAAP;AACH,aAXD,MAWO;AACH,mBAAK/B,cAAL,CAAoB,YAApB,EAAkCN,QAAQ,CAACO,GAA3C;AACA,qBAAO,KAAP;AACH;AACJ,WAtBD,CAsBE,OAAOX,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKN,GAAL,CAASkB,QAAT,CAAkB,WAAlB,EAA+BZ,KAA/B;AACA;AAAA;AAAA,8BAAKoC,GAAL,CAASC,KAAT,CAAe,UAAf;AACA,mBAAO,KAAP;AACH;AACJ;AAED;AACJ;AACA;;;AAC0B,cAAhBiB,gBAAgB,CAClBC,OADkB,EAEF;AAChB,eAAK,MAAMC,MAAX,IAAqBD,OAArB,EAA8B;AAC1B,kBAAME,OAAO,GAAG,MAAM,KAAKX,UAAL,CAAgBU,MAAM,CAAC/B,QAAvB,EAAiC+B,MAAM,CAACf,MAAxC,EAAgDe,MAAM,CAACT,MAAvD,CAAtB;;AACA,gBAAI,CAACU,OAAL,EAAc;AACV,qBAAO,KAAP;AACH;AACJ;;AACD,iBAAO,IAAP;AACH,SAjQgC,CAmQjC;;AAEA;AACJ;AACA;;;AACIC,QAAAA,eAAe,GAAW;AACtB,cAAI,CAAC,KAAKhC,gBAAL,EAAL,EAA8B;AAC1B,mBAAO,CAAP;AACH;;AACD,iBAAO,KAAK1C,SAAL,CAAekC,YAAf,CAA4ByC,KAA5B,IAAqC,CAA5C;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,YAAY,GAAW;AACnB,iBAAO,KAAKF,eAAL,EAAP;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,iBAAiB,CAACC,eAAD,EAAmC;AAChD,gBAAMC,QAAQ,GAAGD,eAAH,WAAGA,eAAH,GAAsB,KAAKJ,eAAL,EAApC;AACA,gBAAMM,SAAS,GAAG,CAACD,QAAQ,GAAG,CAAZ,IAAiB;AAAA;AAAA,8CAAcE,GAAjD;AACA,iBAAOD,SAAS,KAAK,CAAd,GAAkB,CAAlB,GAAsBA,SAA7B;AACH;AAED;AACJ;AACA;;;AAC4B,cAAlBE,kBAAkB,CAACC,QAAD,EAAoBC,IAAa,GAAG,KAApC,EAA6D;AACjF,gBAAMC,WAAW,GAAGF,QAAH,WAAGA,QAAH,GAAe,KAAKT,eAAL,KAAyB,CAAzD;;AAEA,cAAI;AACA,kBAAMtD,QAAQ,GAAG,MAAM;AAAA;AAAA,4BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,gBAAvB,EAAyC;AAC5DoD,cAAAA,KAAK,EAAEU,WADqD;AAE5DC,cAAAA,IAAI,EAAEF;AAFsD,aAAzC,CAAvB;;AAKA,gBAAIhE,QAAQ,CAACI,MAAb,EAAqB;AACjB;AAAA;AAAA,gCAAKd,GAAL,CAASC,WAAT,CAAsB,eAAc0E,WAAY,EAAhD,EADiB,CAGjB;;AACA;AAAA;AAAA,gCAAKhD,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,0CAAUiD,QAArC,EAA+C;AAC3CC,gBAAAA,QAAQ,EAAE,KAAKd,eAAL,EADiC;AAE3CS,gBAAAA,QAAQ,EAAEE,WAFiC;AAG3CC,gBAAAA,IAAI,EAAEF;AAHqC,eAA/C;AAMA,qBAAO,IAAP;AACH,aAXD,MAWO;AACH,mBAAK1D,cAAL,CAAoB,gBAApB,EAAsCN,QAAQ,CAACO,GAA/C;AACA,qBAAO,KAAP;AACH;AACJ,WArBD,CAqBE,OAAOX,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKN,GAAL,CAASkB,QAAT,CAAkB,aAAlB,EAAiCZ,KAAjC;AACA;AAAA;AAAA,8BAAKoC,GAAL,CAASC,KAAT,CAAe,UAAf;AACA,mBAAO,KAAP;AACH;AACJ,SA/TgC,CAiUjC;;AAEA;AACJ;AACA;;;AACIzC,QAAAA,WAAW,GAAY;AACnB;AACA,cAAI,KAAKoB,WAAL,EAAJ,EAAwB;AAAA;;AACpB,6CAAO,KAAKhC,SAAL,CAAekC,YAAf,CAA4BtB,WAAnC,qCAAkD,IAAlD;AACH,WAJkB,CAMnB;;;AACA,gBAAM6E,cAAc,GAAG,CAAC,CAAC;AAAA;AAAA,4BAAKC,OAAL,CAAaC,GAAb,CAAiB;AAAA;AAAA,sDAAkBC,QAAnC,CAAzB;;AACA,cAAIH,cAAJ,EAAoB;AAChB;AAAA;AAAA,8BAAK/E,GAAL,CAASC,WAAT,CAAqB,mBAArB;AACA,mBAAO,KAAP;AACH,WAXkB,CAanB;;;AACA,gBAAMkF,WAAW,GAAG;AAAA;AAAA,4BAAKH,OAAL,CAAaI,OAAb,CAAqB;AAAA;AAAA,sDAAkBC,WAAvC,EAAoD,IAApD,CAApB;;AACA,cAAIF,WAAW,IAAIG,MAAM,CAACH,WAAD,CAAN,KAAwB,GAA3C,EAAgD;AAC5C;AAAA;AAAA,8BAAKnF,GAAL,CAASC,WAAT,CAAqB,qBAArB;AACA,mBAAO,KAAP;AACH,WAlBkB,CAoBnB;;;AACA;AAAA;AAAA,4BAAKD,GAAL,CAASC,WAAT,CAAqB,sBAArB;AACA,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AACgC,cAAtBsF,sBAAsB,GAAqB;AAAA;;AAC7C,cAAI,CAAC,KAAKrF,WAAL,EAAL,EAAyB;AACrB;AAAA;AAAA,8BAAKF,GAAL,CAASC,WAAT,CAAqB,aAArB;AACA,mBAAO,IAAP;AACH,WAJ4C,CAM7C;;;AACA,gBAAMqD,QAAQ,uBAAG,KAAKhE,SAAR,qBAAG,iBAAgBkC,YAAjC;;AACA,cAAK8B,QAAL,YAAKA,QAAD,CAAmBC,eAAvB,EAAwC;AACpC;AAAA;AAAA,8BAAKvD,GAAL,CAASC,WAAT,CAAqB,uBAArB,EADoC,CAEpC;;AACA,gBAAI,KAAKX,SAAL,IAAkB,KAAKA,SAAL,CAAekC,YAArC,EAAmD;AAC/C,mBAAKlC,SAAL,CAAekC,YAAf,CAA4BtB,WAA5B,GAA0C,KAA1C;AACA;AAAA;AAAA,gCAAKF,GAAL,CAASC,WAAT,CAAqB,wBAArB;AACH,aANmC,CAQpC;;;AACA;AAAA;AAAA,8BAAK0B,OAAL,CAAa6D,IAAb,CACI,gBADJ,EAEI,YAAY;AACR,oBAAM,KAAKC,2BAAL,EAAN;AACH,aAJL,EAKI,IALJ;AAQA,mBAAO,IAAP;AACH;;AAED,iBAAO,MAAM,KAAKA,2BAAL,EAAb;AACH;AAED;AACJ;AACA;;;AAC6C,cAA3BA,2BAA2B,GAAqB;AAC1D,cAAI;AACA,kBAAM/E,QAAQ,GAAG,MAAM;AAAA;AAAA,4BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,sBAAvB,EAA+C;AAClEX,cAAAA,WAAW,EAAE;AADqD,aAA/C,CAAvB;;AAIA,gBAAIQ,QAAQ,CAACI,MAAb,EAAqB;AACjB;AACA,kBAAI,KAAKxB,SAAL,IAAkB,KAAKA,SAAL,CAAekC,YAArC,EAAmD;AAC/C,qBAAKlC,SAAL,CAAekC,YAAf,CAA4BtB,WAA5B,GAA0C,KAA1C;AACA;AAAA;AAAA,kCAAKF,GAAL,CAASC,WAAT,CAAqB,oBAArB;AACH;;AAED;AAAA;AAAA,gCAAKD,GAAL,CAASC,WAAT,CAAqB,UAArB;AACA;AAAA;AAAA,gCAAK0B,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,0CAAU8D,eAArC,EAAsD;AAAExF,gBAAAA,WAAW,EAAE;AAAf,eAAtD;AACA,qBAAO,IAAP;AACH,aAVD,MAUO;AACH,mBAAKc,cAAL,CAAoB,sBAApB,EAA4CN,QAAQ,CAACO,GAArD;AACA,qBAAO,KAAP;AACH;AACJ,WAnBD,CAmBE,OAAOX,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKN,GAAL,CAASkB,QAAT,CAAkB,aAAlB,EAAiCZ,KAAjC;AACA;AAAA;AAAA,8BAAKoC,GAAL,CAASC,KAAT,CAAe,UAAf;AACA,mBAAO,KAAP;AACH;AACJ;AAED;AACJ;AACA;;;AACYgB,QAAAA,sBAAsB,CAAC5B,QAAD,EAAqBgB,MAArB,EAAqCM,MAArC,EAA4D;AACtF,eAAK9D,kBAAL,CAAwBoG,IAAxB,CAA6B;AAAE5D,YAAAA,QAAF;AAAYgB,YAAAA,MAAZ;AAAoBM,YAAAA;AAApB,WAA7B;AACA;AAAA;AAAA,4BAAKrD,GAAL,CAASC,WAAT,CAAsB,mBAAkB8B,QAAS,IAAGgB,MAAO,EAA3D,EAFsF,CAItF;;AACA;AAAA;AAAA,4BAAKpB,OAAL,CAAa6D,IAAb,CACI,gBADJ,EAEI,YAAY;AACR,kBAAM,KAAKI,sBAAL,EAAN;AACH,WAJL,EAKI,IALJ;AAOH;AAED;AACJ;AACA;;;AACwC,cAAtBA,sBAAsB,GAAkB;AAClD,cAAI,KAAKrG,kBAAL,CAAwBsG,MAAxB,KAAmC,CAAvC,EAA0C;AACtC;AACH;;AAED;AAAA;AAAA,4BAAK7F,GAAL,CAASC,WAAT,CAAsB,WAAU,KAAKV,kBAAL,CAAwBsG,MAAO,QAA/D,EALkD,CAOlD;;AACA,gBAAMhC,OAAO,GAAG,CAAC,GAAG,KAAKtE,kBAAT,CAAhB;AACA,eAAKA,kBAAL,GAA0B,EAA1B;;AAEA,eAAK,MAAMuE,MAAX,IAAqBD,OAArB,EAA8B;AAC1B,gBAAI;AACA,oBAAMnD,QAAQ,GAAG,MAAM;AAAA;AAAA,8BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,YAAvB,EAAqC;AACxDkB,gBAAAA,QAAQ,EAAE+B,MAAM,CAAC/B,QADuC;AAExDgB,gBAAAA,MAAM,EAAEe,MAAM,CAACf,MAFyC;AAGxDM,gBAAAA,MAAM,EAAES,MAAM,CAACT,MAAP,IAAiB;AAH+B,eAArC,CAAvB;;AAMA,kBAAI3C,QAAQ,CAACI,MAAb,EAAqB;AACjB;AAAA;AAAA,kCAAKd,GAAL,CAASC,WAAT,CACK,aAAY6D,MAAM,CAAC/B,QAAS,IAAG+B,MAAM,CAACf,MAAP,GAAgB,CAAhB,GAAoB,GAApB,GAA0B,EAAG,GAAEe,MAAM,CAACf,MAAO,EADjF;AAGH,eAJD,MAIO;AACH;AAAA;AAAA,kCAAK/C,GAAL,CAASO,OAAT,CAAkB,cAAauD,MAAM,CAAC/B,QAAS,EAA/C,EAAkDrB,QAAQ,CAACO,GAA3D;AACH;AACJ,aAdD,CAcE,OAAOX,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKN,GAAL,CAASkB,QAAT,CAAmB,aAAY4C,MAAM,CAAC/B,QAAS,EAA/C,EAAkDzB,KAAlD;AACH;AACJ;;AAED;AAAA;AAAA,4BAAKN,GAAL,CAASC,WAAT,CAAqB,YAArB;AACH,SAndgC,CAqdjC;;AAEA;AACJ;AACA;;;AACyC,cAAvBE,uBAAuB,GAAkB;AACnD,cAAI;AACA;AAAA;AAAA,8BAAKH,GAAL,CAASC,WAAT,CAAqB,mBAArB,EADA,CAGA;;AACA,kBAAM6F,WAAW,GAAG,IAAIrC,IAAJ,EAApB;AACA,kBAAMsC,aAAa,GAAG;AAClB;AACAC,cAAAA,GAAG,EAAE,CAFa;AAGlBC,cAAAA,KAAK,EAAE,EAHW;AAIlBC,cAAAA,UAAU,EAAE,EAJM;AAKlBC,cAAAA,UAAU,EAAE,EALM;AAMlBC,cAAAA,QAAQ,EAAE,EANQ;AAOlBC,cAAAA,QAAQ,EAAE,EAPQ;AAQlBC,cAAAA,GAAG,EAAE,CARa;AAQV;AACRC,cAAAA,UAAU,EAAET,WATM;AAUlBU,cAAAA,MAAM,EAAE,EAVU;AAWlBC,cAAAA,QAAQ,EAAE,KAXQ;AAYlBC,cAAAA,YAAY,EAAE,KAZI;AAalBC,cAAAA,MAAM,EAAE,EAbU;AAclBC,cAAAA,QAAQ,EAAE,CAdQ;AAelBC,cAAAA,WAAW,EAAE,OAfK;AAiBlB;AACAC,cAAAA,SAAS,EAAE,CAlBO;AAmBlB7C,cAAAA,KAAK,EAAE,CAnBW;AAoBlB8C,cAAAA,oBAAoB,EAAE,CApBJ;AAqBlBC,cAAAA,qBAAqB,EAAElB,WArBL;AAsBlBmB,cAAAA,eAAe,EAAE,CAtBC;AAwBlB;AACA9E,cAAAA,WAAW,EAAE,KAAK+E,kBAAL,EAzBK;AA0BlBC,cAAAA,UAAU,EAAE,EA1BM;AA4BlB;AACAjH,cAAAA,WAAW,EAAE,IA7BK;AA8BlBkH,cAAAA,OAAO,EAAE,IA9BS;AA+BlBC,cAAAA,QAAQ,EAAE,CA/BQ;AAiClB;AACA9D,cAAAA,eAAe,EAAE;AAlCC,aAAtB,CALA,CA0CA;;AACA,iBAAKpC,cAAL,CAAoB4E,aAApB;AACA;AAAA;AAAA,8BAAK/F,GAAL,CAASC,WAAT,CAAqB,eAArB;AACH,WA7CD,CA6CE,OAAOK,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKN,GAAL,CAASO,OAAT,CAAiB,iBAAjB,EAAoCD,KAApC;AACH;AACJ;AAED;AACJ;AACA;;;AACY4G,QAAAA,kBAAkB,GAA2B;AACjD,gBAAMpB,WAAW,GAAG,IAAIrC,IAAJ,EAApB;AACA,gBAAM6D,cAAsC,GAAG,EAA/C,CAFiD,CAIjD;;AACA,gBAAMC,SAAS,GAAG,CACd;AAAA;AAAA,oCAASC,YADK,EAEd;AAAA;AAAA,oCAASC,SAFK,EAGd;AAAA;AAAA,oCAASC,cAHK,EAId;AAAA;AAAA,oCAASC,iBAJK,EAKd;AAAA;AAAA,oCAASC,WALK,EAMd;AAAA;AAAA,oCAASC,QANK,EAOd;AAAA;AAAA,oCAASC,SAPK,CAAlB;AAUAP,UAAAA,SAAS,CAACQ,OAAV,CAAkBhG,QAAQ,IAAI;AAC1BuF,YAAAA,cAAc,CAACvF,QAAD,CAAd,GAA2B;AACvBA,cAAAA,QAAQ,EAAEA,QADa;AAEvBgB,cAAAA,MAAM,EAAE,KAAKiF,yBAAL,CAA+BjG,QAA/B,CAFe;AAGvByB,cAAAA,cAAc,EAAEsC;AAHO,aAA3B;AAKH,WAND;AAQA;AAAA;AAAA,4BAAK9F,GAAL,CAASC,WAAT,CAAqB,eAArB,EAAsC;AAClCgI,YAAAA,SAAS,EAAEC,MAAM,CAACC,IAAP,CAAYb,cAAZ,EAA4BzB;AADL,WAAtC;AAIA,iBAAOyB,cAAP;AACH;AAED;AACJ;AACA;;;AACYlH,QAAAA,oBAAoB,GAAS;AACjC;AACAgI,UAAAA,UAAU,CAAC,YAAY;AACnB,gBAAI;AACA;AAAA;AAAA,gCAAKpI,GAAL,CAASC,WAAT,CAAqB,oBAArB,EADA,CAGA;;AACA,oBAAM,KAAKoI,yBAAL,EAAN;AAEA;AAAA;AAAA,gCAAKrI,GAAL,CAASC,WAAT,CAAqB,cAArB,EANA,CAQA;;AACA;AAAA;AAAA,gCAAK0B,OAAL,CAAaC,aAAb,CAA2B,gBAA3B;AACH,aAVD,CAUE,OAAOtB,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKN,GAAL,CAASO,OAAT,CAAiB,gBAAjB,EAAmCD,KAAnC;AACH;AACJ,WAdS,EAcP,GAdO,CAAV;AAeH;AAED;AACJ;AACA;;;AAC2C,cAAzB+H,yBAAyB,GAAkB;AACrD,cAAI;AAAA;;AACA;AACA,kBAAMC,YAAY,uBAAG,KAAKhJ,SAAR,qBAAG,iBAAgBkC,YAArC;;AACA,gBAAI8G,YAAJ,EAAkB;AACd;AACCA,cAAAA,YAAD,CAAsB/E,eAAtB,GAAwC,IAAxC;AACH,aAND,CAQA;;;AACA,kBAAM,KAAKgF,oBAAL,EAAN;AACH,WAVD,CAUE,OAAOjI,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKN,GAAL,CAASO,OAAT,CAAiB,cAAjB,EAAiCD,KAAjC;AACA,kBAAMA,KAAN;AACH;AACJ;AAED;AACJ;AACA;;;AACsC,cAApBiI,oBAAoB,GAAkB;AAChD;AAAA;AAAA,4BAAKvI,GAAL,CAASC,WAAT,CAAqB,kBAArB;;AAEA,cAAI;AACA,kBAAM;AAAEuI,cAAAA;AAAF,gBAAoB,wCAA1B,CADA,CAGA;;AACA,gBAAI;AAAA;AAAA,4CAAY/B,QAAZ,KAAyB;AAAA;AAAA,sCAASgC,QAAtC,EAAgD;AAC5C;AACA;AAAA;AAAA,gCAAKzI,GAAL,CAASC,WAAT,CAAqB,yBAArB;AACA,oBAAMyI,YAAY,GAAG,MAAMF,aAAa,CAACG,eAAd,EAA3B;;AAEA,kBAAID,YAAJ,EAAkB;AACd;AAAA;AAAA,kCAAK1I,GAAL,CAASC,WAAT,CAAqB,kBAArB;AACH,eAFD,MAEO;AACH;AAAA;AAAA,kCAAKD,GAAL,CAASO,OAAT,CAAiB,mBAAjB;AACH;AACJ,aAVD,MAUO;AACH;AACA;AAAA;AAAA,gCAAKP,GAAL,CAASC,WAAT,CAAqB,gBAArB,EAFG,CAIH;;AACA,oBAAM2I,aAAa,GAAG,IAAIJ,aAAJ,EAAtB;AACA,oBAAME,YAAY,GAAG,MAAME,aAAa,CAACC,gBAAd,EAA3B;;AAEA,kBAAIH,YAAJ,EAAkB;AACd;AAAA;AAAA,kCAAK1I,GAAL,CAASC,WAAT,CAAqB,UAArB;AACH,eAFD,MAEO;AACH;AAAA;AAAA,kCAAKD,GAAL,CAASO,OAAT,CAAiB,WAAjB;AACH;AACJ;;AAED;AAAA;AAAA,8BAAKP,GAAL,CAASC,WAAT,CAAqB,UAArB;AACH,WA9BD,CA8BE,OAAOK,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKN,GAAL,CAASO,OAAT,CAAiB,YAAjB,EAA+BD,KAA/B,EADY,CAEZ;AACH;AACJ;AAED;AACJ;AACA;;;AACkC,cAAhBD,gBAAgB,GAAkB;AAAA;;AAC5C;AACA,gBAAMiD,QAAQ,uBAAG,KAAKhE,SAAR,qBAAG,iBAAgBkC,YAAjC;;AACA,cAAI8B,QAAQ,IAAI,CAAEA,QAAD,CAAkBC,eAAnC,EAAoD;AAChD;AAAA;AAAA,8BAAKvD,GAAL,CAASC,WAAT,CAAqB,gBAArB;AACA;AACH;;AAED,cAAKqD,QAAL,YAAKA,QAAD,CAAmBC,eAAvB,EAAwC;AACpC;AAAA;AAAA,8BAAKvD,GAAL,CAASC,WAAT,CAAqB,wBAArB;AACH,WAFD,MAEO;AACH;AAAA;AAAA,8BAAKD,GAAL,CAASC,WAAT,CAAqB,oBAArB;AACH,WAZ2C,CAc5C;;;AACA,gBAAM,KAAKO,QAAL,EAAN;AACH,SArpBgC,CAupBjC;;AAEA;AACJ;AACA;;;AACIsI,QAAAA,aAAa,CAACC,UAAD,EAAyBC,UAAzB,EAAqE;AAAA;;AAC9E,cAAI,CAAC,KAAKhH,gBAAL,EAAL,EAA8B;AAC1B,mBAAO,IAAP;AACH;;AAED,gBAAMiH,UAAU,GAAGD,UAAU,IAAI,IAAIvF,IAAJ,GAAWyF,YAAX,EAAjC;AACA,gBAAM/B,UAAU,6BAAG,KAAK7H,SAAL,CAAekC,YAAf,CAA4B2F,UAA/B,qBAAG,uBAAyC8B,UAAzC,CAAnB;AAEA,iBAAO,CAAA9B,UAAU,QAAV,YAAAA,UAAU,CAAG4B,UAAH,CAAV,KAA4B,IAAnC;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACII,QAAAA,sBAAsB,CAACC,OAAD,EAAkBJ,UAAlB,EAA+C;AAAA;;AACjE,gBAAM7B,UAAU,GAAG,KAAK2B,aAAL,CAAmB;AAAA;AAAA,wCAAWO,KAA9B,EAAqCL,UAArC,CAAnB;;AACA,cAAI,EAAC7B,UAAD,YAACA,UAAU,CAAEmC,YAAb,CAAJ,EAA+B;AAC3B,mBAAO,CAAP;AACH;;AAED,gBAAMC,QAAQ,GAAI,SAAQH,OAAQ,EAAlC;AACA,iBAAO,0BAAAjC,UAAU,CAACmC,YAAX,CAAwBC,QAAxB,4CAAmCC,QAAnC,KAA+C,CAAtD;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,6BAA6B,CAACT,UAAD,EAA8B;AACvD,gBAAMU,cAAc,GAAG,KAAKvF,iBAAL,EAAvB,CADuD,CACN;;AACjD,iBAAO,KAAKgF,sBAAL,CAA4BO,cAA5B,EAA4CV,UAA5C,CAAP;AACH,SA/rBgC,CAisBjC;;AAEA;AACJ;AACA;;;AACIW,QAAAA,eAAe,GAAiB;AAC5B,cAAI,CAAC,KAAK3H,gBAAL,EAAL,EAA8B;AAC1B,kBAAM,IAAI4H,KAAJ,CAAU,mBAAV,CAAN;AACH;;AACD,iBAAO,KAAKtK,SAAL,CAAekC,YAAtB;AACH,SA3sBgC,CA6sBjC;;AAEA;AACJ;AACA;;;AACYQ,QAAAA,gBAAgB,GAAY;AAAA;;AAChC,iBAAO,CAAC,sBAAC,KAAK1C,SAAN,aAAC,iBAAgBkC,YAAjB,CAAR;AACH;AAED;AACJ;AACA;;;AACYF,QAAAA,WAAW,GAAY;AAC3B,iBAAO,KAAKU,gBAAL,EAAP;AACH;AAED;AACJ;AACA;;;AACYvB,QAAAA,yBAAyB,GAAY;AAAA;;AACzC,cAAI,UAAC;AAAA;AAAA,0BAAIE,GAAL,aAAC,KAASC,MAAV,CAAJ,EAAsB;AAClB;AAAA;AAAA,8BAAKZ,GAAL,CAASkB,QAAT,CAAkB,YAAlB;AACA,mBAAO,KAAP;AACH;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AAC+C,cAA7BH,6BAA6B,GAAkB;AACzD,gBAAM8I,UAAU,GAAG,EAAnB,CADyD,CAClC;;AACvB,cAAIC,UAAU,GAAG,CAAjB;;AAEA,iBAAOA,UAAU,GAAGD,UAApB,EAAgC;AAC5B,gBAAI,KAAKvI,WAAL,EAAJ,EAAwB;AACpB;AACH,aAH2B,CAK5B;;;AACA,kBAAM,IAAIyI,OAAJ,CAAYC,OAAO,IAAI5B,UAAU,CAAC4B,OAAD,EAAU,EAAV,CAAjC,CAAN;AACAF,YAAAA,UAAU;;AAEV,gBAAIA,UAAU,GAAG,CAAb,KAAmB,CAAvB,EAA0B;AACtB;AAAA;AAAA,gCAAK9J,GAAL,CAASC,WAAT,CAAsB,kBAAiB6J,UAAW,IAAGD,UAAW,EAAhE;AACH;AACJ,WAhBwD,CAkBzD;;;AACA;AAAA;AAAA,4BAAK7J,GAAL,CAASO,OAAT,CAAiB,oBAAjB;AACH;AAED;AACJ;AACA;;;AACYgB,QAAAA,mBAAmB,CAAC0I,IAAD,EAA2B;AAAA;;AAClDA,UAAAA,IAAI,CAACjE,GAAL,GAAWiE,IAAI,CAACjE,GAAL,IAAY,CAAvB;AACAiE,UAAAA,IAAI,CAAC7D,QAAL,GAAgB6D,IAAI,CAAC7D,QAAL,IAAiB,QAAjC;AACA6D,UAAAA,IAAI,CAAChG,KAAL,GAAagG,IAAI,CAAChG,KAAL,IAAc,CAA3B;AACAgG,UAAAA,IAAI,CAAC/J,WAAL,wBAAmB+J,IAAI,CAAC/J,WAAxB,gCAAuC,IAAvC;AACA+J,UAAAA,IAAI,CAAC9H,WAAL,GAAmB8H,IAAI,CAAC9H,WAAL,IAAoB,EAAvC;AACA8H,UAAAA,IAAI,CAAC9C,UAAL,GAAkB8C,IAAI,CAAC9C,UAAL,IAAmB,EAArC;AACH;AAED;AACJ;AACA;;;AACY1F,QAAAA,aAAa,CAACyI,MAAD,EAAuBC,MAAvB,EAAmD;AACpEjC,UAAAA,MAAM,CAACC,IAAP,CAAYgC,MAAZ,EAAoBpC,OAApB,CAA4B/B,GAAG,IAAI;AAC/B,kBAAMoE,WAAW,GAAGD,MAAM,CAACnE,GAAD,CAA1B;AACA,kBAAMqE,WAAW,GAAGH,MAAM,CAAClE,GAAD,CAA1B;;AAEA,gBAAIoE,WAAW,IAAI,OAAOA,WAAP,KAAuB,QAAtC,IAAkD,CAACE,KAAK,CAACC,OAAN,CAAcH,WAAd,CAAvD,EAAmF;AAC/E,kBAAIC,WAAW,IAAI,OAAOA,WAAP,KAAuB,QAA1C,EAAoD;AAChD,qBAAK5I,aAAL,CAAmB4I,WAAnB,EAAuCD,WAAvC;AACH,eAFD,MAEO;AACFF,gBAAAA,MAAD,CAAgBlE,GAAhB,IAAuBoE,WAAvB;AACH;AACJ,aAND,MAMO;AACFF,cAAAA,MAAD,CAAgBlE,GAAhB,IAAuBoE,WAAvB;AACH;AACJ,WAbD;AAcH;AAED;AACJ;AACA;;;AACYnI,QAAAA,qBAAqB,CAACF,QAAD,EAA0B;AACnD;AACA,gBAAMyI,aAAa,GAAG,KAAKxC,yBAAL,CAA+BjG,QAA/B,CAAtB;AAEA,iBAAO;AACHgB,YAAAA,MAAM,EAAEyH,aADL;AAEHzI,YAAAA,QAFG;AAGH0I,YAAAA,MAAM,EAAE1I,QAHL;AAIH2I,YAAAA,IAAI,EAAG,KAAI3I,QAAS,EAJjB;AAKH4I,YAAAA,OAAO,EAAE,IAAIlH,IAAJ,EALN;AAMHmH,YAAAA,aAAa,EAAE,IAAInH,IAAJ,EANZ;AAOHD,YAAAA,cAAc,EAAE,IAAIC,IAAJ;AAPb,WAAP;AASH;AAED;AACJ;AACA;;;AACYuE,QAAAA,yBAAyB,CAACjG,QAAD,EAA6B;AAC1D,cAAI,CAAC,KAAK7B,WAAL,EAAL,EAAyB;AACrB,mBAAO,CAAP;AACH,WAHyD,CAK1D;;;AACA,kBAAQ6B,QAAR;AACI,iBAAK;AAAA;AAAA,sCAASyF,YAAd;AAA4B;AACxB,qBAAO;AAAA;AAAA,0CAAUqD,qBAAV,CAAgCC,OAAvC;;AACJ,iBAAK;AAAA;AAAA,sCAASrD,SAAd;AAAyB;AACrB,qBAAO;AAAA;AAAA,0CAAUoD,qBAAV,CAAgCE,IAAvC;;AACJ,iBAAK;AAAA;AAAA,sCAASrD,cAAd;AAA8B;AAC1B,qBAAO;AAAA;AAAA,0CAAUmD,qBAAV,CAAgCG,SAAvC;;AACJ,iBAAK;AAAA;AAAA,sCAASrD,iBAAd;AAAiC;AAC7B,qBAAO;AAAA;AAAA,0CAAUsD,YAAjB;AAA+B;;AACnC,iBAAK;AAAA;AAAA,sCAASrD,WAAd;AAA2B;AACvB,qBAAO;AAAA;AAAA,0CAAUiD,qBAAV,CAAgCK,MAAvC;;AACJ,iBAAK;AAAA;AAAA,sCAASrD,QAAd;AAAwB;AACpB,qBAAO,CAAP;;AACJ,iBAAK;AAAA;AAAA,sCAASC,SAAd;AAAyB;AACrB,qBAAO,CAAP;;AACJ;AACI,qBAAO,CAAP;AAhBR;AAkBH;AAED;AACJ;AACA;;;AACYpG,QAAAA,eAAe,GAAS;AAC5B,cAAI,CAAC,KAAKJ,WAAL,EAAL,EAAyB;AACrB;AACH;;AAED,eAAKzB,mBAAL;AAEA,gBAAMsL,aAAa,GAAG;AAClBC,YAAAA,MAAM,EAAE,KAAK9L,SAAL,CAAekC,YAAf,CAA4BwE,GADlB;AAElBI,YAAAA,QAAQ,EAAE,KAAK9G,SAAL,CAAekC,YAAf,CAA4B4E,QAFpB;AAGlBiF,YAAAA,KAAK,EAAE,KAAKrH,eAAL,EAHW;AAIlB9D,YAAAA,WAAW,EAAE,KAAKZ,SAAL,CAAekC,YAAf,CAA4BtB,WAJvB;AAKlB+D,YAAAA,KAAK,EAAE,KAAK3E,SAAL,CAAekC,YAAf,CAA4ByC,KALjB;AAMlB9B,YAAAA,WAAW,EAAE,KAAK7C,SAAL,CAAekC,YAAf,CAA4BW,WANvB;AAOlB;AACA,eAAG,KAAK7C,SAAL,CAAekC;AARA,WAAtB;AAWA;AAAA;AAAA,wBAAG9B,GAAH,CAAOyL,aAAP,EAAsB,MAAtB;AACA;AAAA;AAAA,4BAAKnL,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH;AAED;AACJ;AACA;;;AACYJ,QAAAA,mBAAmB,GAAS;AAChC;AAAA;AAAA,wBAAGC,MAAH,CAAU,MAAV;AACH;AAED;AACJ;AACA;;;AACYkB,QAAAA,cAAc,CAACsK,OAAD,EAAkBhL,KAAlB,EAAoC;AAAA;;AACtD;AAAA;AAAA,4BAAKN,GAAL,CAASkB,QAAT,CAAmB,KAAIoK,OAAQ,SAA/B,EAAyChL,KAAzC;AAEA,gBAAMiL,YAAY,GAAG,CAAAjL,KAAK,QAAL,YAAAA,KAAK,CAAEqB,OAAP,MAAkBrB,KAAlB,2BAAkBA,KAAK,CAAEkL,IAAzB,qBAAkB,YAAaC,QAAb,EAAlB,KAA6C,MAAlE;AACA;AAAA;AAAA,4BAAK/I,GAAL,CAASC,KAAT,CAAe4I,YAAf;AACH;;AAx3BgC,O", "sourcesContent": ["import { oops } from '../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { ecs } from '../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';\nimport { LanguageData } from '../../../../extensions/oops-plugin-framework/assets/libs/gui/language/LanguageData';\nimport { VM } from '../../../../extensions/oops-plugin-framework/assets/libs/model-view/ViewModel';\nimport { GameConst } from '../../tsrpc/models/GameConst';\nimport { Platform, ShareConfig } from '../../tsrpc/models/ShareConfig';\nimport {\n    PropType,\n    RecordType,\n    RecordTypeData,\n    SceneItemType,\n    UserGameData,\n} from '../../tsrpc/protocols/base';\nimport { GameStorageConfig } from '../common/config/GameStorageConfig';\nimport { DataManager } from '../common/DataManager';\nimport { GameEvent } from '../common/Enum';\nimport { smc } from '../common/SingletonModuleComp';\nimport { RoleModelComp } from './model/RoleModelComp';\n\n/**\n * 角色管理器 - 重构版本\n * 职责：\n * 1. 用户数据的加载、更新和管理\n * 2. 道具系统的操作和验证\n * 3. 游戏进度的管理\n * 4. ViewModel的数据绑定\n */\**************('Role')\nexport class Role extends ecs.Entity {\n    RoleModel!: RoleModelComp;\n\n    // 🔄 待同步的道具更新队列\n    private pendingPropUpdates: Array<{\n        propType: PropType;\n        amount: number;\n        reason?: string;\n    }> = [];\n    private dataManager!: DataManager;\n\n    // ==================== 初始化 ====================\n\n    protected init() {\n        this.add(RoleModelComp);\n        this.dataManager = DataManager.getInstance();\n    }\n\n    destroy(): void {\n        this.removeFromViewModel();\n        this.remove(RoleModelComp);\n    }\n\n    // ==================== 数据加载 ====================\n\n    /**\n     * 🚀 快速初始化用户数据 - 统一入口\n     */\n    async quickInitialize(): Promise<void> {\n        try {\n            oops.log.logBusiness('🚀 开始快速用户数据初始化...');\n\n            if (this.isNewPlayer()) {\n                oops.log.logBusiness('🚀 启用新手快速启动模式');\n                await this.initializeNewPlayerData();\n\n                // 🔄 在后台继续完整的用户数据加载\n                this.loadDataInBackground();\n            } else {\n                // 🔄 传统模式：完整加载用户数据\n                await this.ensureDataLoaded();\n            }\n        } catch (error) {\n            oops.log.logWarn('⚠️ 快速用户数据初始化失败:', error);\n            // 失败时创建基础数据，确保游戏可以启动\n            await this.initializeNewPlayerData();\n        }\n    }\n\n    /**\n     * 加载用户数据\n     * @returns 是否加载成功\n     */\n    async loadData(): Promise<boolean> {\n        oops.log.logBusiness('🔄 开始加载用户数据...');\n\n        if (!this.validateNetworkConnection()) {\n            return false;\n        }\n\n        try {\n            const response = await smc.net.hcGame.callApi('UserInfo', {});\n\n            if (response.isSucc) {\n                await this.waitForUserDataInitialization();\n                return true;\n            } else {\n                this.handleAPIError('UserInfo', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 用户数据加载异常:', error);\n            return false;\n        }\n    }\n\n    /**\n     * 更新用户数据（由DataManager调用）\n     */\n    updateUserData(newData: UserGameData): void {\n        const isFirstInitialization = !this.hasUserData();\n\n        // 确保数据结构完整\n        this.ensureDataIntegrity(newData);\n\n        // 合并数据\n        if (this.RoleModel.userGameData) {\n            this.mergeUserData(this.RoleModel.userGameData, newData);\n        } else {\n            this.RoleModel.userGameData = newData;\n        }\n\n        // 更新ViewModel\n        this.updateViewModel();\n\n        // 首次初始化触发事件\n        if (isFirstInitialization) {\n            oops.log.logBusiness('🎉 用户数据首次初始化完成');\n            oops.message.dispatchEvent(GameEvent.UserDataInitialized, this.RoleModel.userGameData);\n        }\n    }\n\n    // ==================== 道具系统 ====================\n\n    /**\n     * 获取道具数据\n     */\n    getPropData(propType: PropType): any {\n        if (!this.validateUserData()) {\n            return this.createDefaultPropData(propType);\n        }\n\n        const propData = this.RoleModel.userGameData.propUseData?.[propType];\n        if (!propData) {\n            // 创建默认道具数据\n            const defaultData = this.createDefaultPropData(propType);\n            this.RoleModel.userGameData.propUseData = this.RoleModel.userGameData.propUseData || {};\n            this.RoleModel.userGameData.propUseData[propType] = defaultData;\n            return defaultData;\n        }\n\n        return propData;\n    }\n\n    /**\n     * 获取道具数据（别名方法，兼容现有代码）\n     */\n    getPropsDataByType(propType: PropType): any {\n        return this.getPropData(propType);\n    }\n\n    /**\n     * 尝试消耗道具（检查数量并显示提示）\n     * @param args 道具参数（包含类型和数量）\n     * @param showToast 是否显示提示\n     * @param failureMessageKey 失败时的消息键\n     * @returns 是否可以消耗\n     */\n    tryCostProp(\n        args: { propType: PropType; amount: number },\n        showToast: boolean = true,\n        failureMessageKey?: string\n    ): boolean {\n        if (!this.validateUserData()) {\n            if (showToast) {\n                oops.gui.toast('用户数据未初始化');\n            }\n            return false;\n        }\n\n        const absAmount = Math.abs(args.amount);\n        const canUse = this.canUseProp(args.propType, absAmount);\n\n        if (!canUse && showToast && failureMessageKey) {\n            oops.gui.toast(LanguageData.getLangByID(failureMessageKey) || failureMessageKey);\n        }\n\n        return canUse;\n    }\n\n    /**\n     * 更新道具数据（别名方法，兼容现有代码）\n     */\n    async updatePropData(args: {\n        propType: PropType;\n        amount: number;\n        reason?: string;\n    }): Promise<boolean> {\n        return await this.updateProp(args.propType, args.amount, args.reason);\n    }\n\n    /**\n     * 检查道具是否足够\n     */\n    canUseProp(propType: PropType, amount: number): boolean {\n        const propData = this.getPropData(propType);\n        return propData.amount >= Math.abs(amount);\n    }\n\n    /**\n     * 更新道具数量\n     */\n    async updateProp(propType: PropType, amount: number, reason?: string): Promise<boolean> {\n        // 消耗操作前检查数量\n        if (amount < 0 && !this.canUseProp(propType, Math.abs(amount))) {\n            oops.gui.toast(LanguageData.getLangByID('UseLimitsDaily'));\n            return false;\n        }\n\n        // 🔍 检查是否为临时数据状态\n        const userData = this.RoleModel?.userGameData;\n        if ((userData as any)?.isTemporaryData) {\n            oops.log.logBusiness('🔄 检测到临时数据状态，本地更新道具数量');\n\n            // 在临时状态下，直接更新本地数据\n            const propData = this.getPropsDataByType(propType);\n            if (propData) {\n                propData.amount += amount;\n                propData.lastUpdateTime = new Date();\n\n                oops.log.logBusiness(\n                    `✅ 道具本地更新: ${propType} ${amount > 0 ? '+' : ''}${amount} (临时)`\n                );\n\n                // 触发道具使用事件\n                if (amount < 0) {\n                    oops.message.dispatchEvent(GameEvent.UseProp, { propType, amount });\n                }\n\n                // 等后台登录完成后同步到服务器\n                this.queuePropUpdateForSync(propType, amount, reason);\n                return true;\n            }\n        }\n\n        try {\n            const response = await smc.net.hcGame.callApi('UpdateProp', {\n                propType,\n                amount,\n                reason: reason || 'player_action',\n            });\n\n            if (response.isSucc) {\n                oops.log.logBusiness(\n                    `✅ 道具更新成功: ${propType} ${amount > 0 ? '+' : ''}${amount}`\n                );\n\n                // 触发道具使用事件\n                if (amount < 0) {\n                    oops.message.dispatchEvent(GameEvent.UseProp, { propType, amount });\n                }\n\n                return true;\n            } else {\n                this.handleAPIError('UpdateProp', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 道具更新异常:', error);\n            oops.gui.toast('网络错误，请重试');\n            return false;\n        }\n    }\n\n    /**\n     * 批量更新道具\n     */\n    async batchUpdateProps(\n        updates: Array<{ propType: PropType; amount: number; reason?: string }>\n    ): Promise<boolean> {\n        for (const update of updates) {\n            const success = await this.updateProp(update.propType, update.amount, update.reason);\n            if (!success) {\n                return false;\n            }\n        }\n        return true;\n    }\n\n    // ==================== 游戏进度 ====================\n\n    /**\n     * 获取当前通关进度\n     */\n    getGameProgress(): number {\n        if (!this.validateUserData()) {\n            return 0;\n        }\n        return this.RoleModel.userGameData.index || 0;\n    }\n\n    /**\n     * 获取已通过的关卡索引（别名方法，兼容现有代码）\n     */\n    getPassIndex(): number {\n        return this.getGameProgress();\n    }\n\n    /**\n     * 获取下一关卡索引（循环通关）\n     */\n    getNextLevelIndex(currentProgress?: number): number {\n        const progress = currentProgress ?? this.getGameProgress();\n        const nextLevel = (progress + 1) % SceneItemType.Max;\n        return nextLevel === 0 ? 1 : nextLevel;\n    }\n\n    /**\n     * 更新游戏通关进度\n     */\n    async updateGameProgress(newIndex?: number, isGM: boolean = false): Promise<boolean> {\n        const targetIndex = newIndex ?? this.getGameProgress() + 1;\n\n        try {\n            const response = await smc.net.hcGame.callApi('UpdateProgress', {\n                index: targetIndex,\n                isGm: isGM,\n            });\n\n            if (response.isSucc) {\n                oops.log.logBusiness(`✅ 游戏进度更新成功: ${targetIndex}`);\n\n                // 触发通关事件\n                oops.message.dispatchEvent(GameEvent.GamePass, {\n                    oldIndex: this.getGameProgress(),\n                    newIndex: targetIndex,\n                    isGm: isGM,\n                });\n\n                return true;\n            } else {\n                this.handleAPIError('UpdateProgress', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 游戏进度更新异常:', error);\n            oops.gui.toast('网络错误，请重试');\n            return false;\n        }\n    }\n\n    // ==================== 新手引导 ====================\n\n    /**\n     * 检查是否为新玩家 - 统一判断逻辑\n     */\n    isNewPlayer(): boolean {\n        // 1. 优先使用服务端数据\n        if (this.hasUserData()) {\n            return this.RoleModel.userGameData.isNewPlayer ?? true;\n        }\n\n        // 2. 检查本地存储的登录记录\n        const hasLoginRecord = !!oops.storage.get(GameStorageConfig.SSOToken);\n        if (hasLoginRecord) {\n            oops.log.logBusiness('🔍 检测到登录记录，判定为老玩家');\n            return false;\n        }\n\n        // 3. 检查是否有其他用户数据痕迹\n        const userDumpKey = oops.storage.getJson(GameStorageConfig.UserDumpKey, null);\n        if (userDumpKey && String(userDumpKey) !== '0') {\n            oops.log.logBusiness('🔍 检测到用户数据痕迹，判定为老玩家');\n            return false;\n        }\n\n        // 4. 默认判定为新手\n        oops.log.logBusiness('🆕 无任何用户数据痕迹，判定为新手玩家');\n        return true;\n    }\n\n    /**\n     * 完成新手引导\n     */\n    async completeNewPlayerGuide(): Promise<boolean> {\n        if (!this.isNewPlayer()) {\n            oops.log.logBusiness('✅ 用户已完成新手引导');\n            return true;\n        }\n\n        // 🔍 检查是否为临时数据状态\n        const userData = this.RoleModel?.userGameData;\n        if ((userData as any)?.isTemporaryData) {\n            oops.log.logBusiness('🔄 检测到临时数据状态，延迟新手引导完成');\n            // 先更新本地状态，等后台登录完成后再同步到服务器\n            if (this.RoleModel && this.RoleModel.userGameData) {\n                this.RoleModel.userGameData.isNewPlayer = false;\n                oops.log.logBusiness('🔄 本地新手状态已更新为false（临时）');\n            }\n\n            // 监听后台登录完成事件，然后同步状态\n            oops.message.once(\n                'UserDataLoaded',\n                async () => {\n                    await this.syncNewPlayerStatusToServer();\n                },\n                this\n            );\n\n            return true;\n        }\n\n        return await this.syncNewPlayerStatusToServer();\n    }\n\n    /**\n     * 🔄 同步新手状态到服务器\n     */\n    private async syncNewPlayerStatusToServer(): Promise<boolean> {\n        try {\n            const response = await smc.net.hcGame.callApi('GameUpdateSimpleData', {\n                isNewPlayer: false,\n            });\n\n            if (response.isSucc) {\n                // 🎯 立即更新本地数据，确保状态同步\n                if (this.RoleModel && this.RoleModel.userGameData) {\n                    this.RoleModel.userGameData.isNewPlayer = false;\n                    oops.log.logBusiness('🔄 本地新手状态已更新为false');\n                }\n\n                oops.log.logBusiness('✅ 新手引导完成');\n                oops.message.dispatchEvent(GameEvent.BasicInfoUpdate, { isNewPlayer: false });\n                return true;\n            } else {\n                this.handleAPIError('GameUpdateSimpleData', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 新手状态更新异常:', error);\n            oops.gui.toast('网络错误，请重试');\n            return false;\n        }\n    }\n\n    /**\n     * 🔄 将道具更新加入同步队列\n     */\n    private queuePropUpdateForSync(propType: PropType, amount: number, reason?: string): void {\n        this.pendingPropUpdates.push({ propType, amount, reason });\n        oops.log.logBusiness(`📝 道具更新已加入同步队列: ${propType} ${amount}`);\n\n        // 监听后台登录完成事件\n        oops.message.once(\n            'UserDataLoaded',\n            async () => {\n                await this.syncPendingPropUpdates();\n            },\n            this\n        );\n    }\n\n    /**\n     * 🔄 同步待处理的道具更新到服务器\n     */\n    private async syncPendingPropUpdates(): Promise<void> {\n        if (this.pendingPropUpdates.length === 0) {\n            return;\n        }\n\n        oops.log.logBusiness(`🔄 开始同步 ${this.pendingPropUpdates.length} 个道具更新`);\n\n        // 复制队列并清空原队列\n        const updates = [...this.pendingPropUpdates];\n        this.pendingPropUpdates = [];\n\n        for (const update of updates) {\n            try {\n                const response = await smc.net.hcGame.callApi('UpdateProp', {\n                    propType: update.propType,\n                    amount: update.amount,\n                    reason: update.reason || 'delayed_sync',\n                });\n\n                if (response.isSucc) {\n                    oops.log.logBusiness(\n                        `✅ 道具同步成功: ${update.propType} ${update.amount > 0 ? '+' : ''}${update.amount}`\n                    );\n                } else {\n                    oops.log.logWarn(`⚠️ 道具同步失败: ${update.propType}`, response.err);\n                }\n            } catch (error) {\n                oops.log.logError(`❌ 道具同步异常: ${update.propType}`, error);\n            }\n        }\n\n        oops.log.logBusiness('✅ 道具更新同步完成');\n    }\n\n    // ==================== 新手数据初始化 ====================\n\n    /**\n     * 🚀 初始化新手玩家数据\n     */\n    private async initializeNewPlayerData(): Promise<void> {\n        try {\n            oops.log.logBusiness('🚀 开始初始化新手玩家数据...');\n\n            // 创建完整的基础用户数据结构\n            const currentTime = new Date();\n            const basicUserData = {\n                // 基础标识\n                key: 0,\n                guuid: '',\n                googleUuid: '',\n                facebookId: '',\n                userName: '',\n                nickName: '',\n                sex: 1, // SexType.None\n                createtime: currentTime,\n                openid: '',\n                platform: 'web',\n                platformType: 'web',\n                avatar: '',\n                avatarId: 0,\n                countryCode: 'Other',\n\n                // 游戏进度\n                passTimes: 0,\n                index: 0,\n                currCountryPassTimes: 0,\n                lastChangeCountryTime: currentTime,\n                selfCountryRank: 0,\n\n                // 道具和记录数据\n                propUseData: this.createDefaultProps(),\n                recordData: {},\n\n                // 新手和游客状态\n                isNewPlayer: true,\n                isGuest: true,\n                lastStep: 0,\n\n                // 🔄 标记为临时数据\n                isTemporaryData: true,\n            };\n\n            // 🎯 设置用户数据\n            this.updateUserData(basicUserData as any);\n            oops.log.logBusiness('✅ 新手玩家数据初始化完成');\n        } catch (error) {\n            oops.log.logWarn('⚠️ 新手玩家数据初始化失败:', error);\n        }\n    }\n\n    /**\n     * 🎁 创建新手玩家默认道具\n     */\n    private createDefaultProps(): { [key: number]: any } {\n        const currentTime = new Date();\n        const newPlayerProps: { [key: number]: any } = {};\n\n        // 🎯 为每种道具类型创建默认数据\n        const propTypes = [\n            PropType.PropsMoveOut,\n            PropType.PropsTips,\n            PropType.PropsReShuffle,\n            PropType.PropsDayLeftCount,\n            PropType.PropsRevive,\n            PropType.PropsExp,\n            PropType.PropsCoin,\n        ];\n\n        propTypes.forEach(propType => {\n            newPlayerProps[propType] = {\n                propType: propType,\n                amount: this.getNewPlayerDefaultAmount(propType),\n                lastUpdateTime: currentTime,\n            };\n        });\n\n        oops.log.logBusiness('🎁 新手默认道具创建完成', {\n            propCount: Object.keys(newPlayerProps).length,\n        });\n\n        return newPlayerProps;\n    }\n\n    /**\n     * 🔄 后台加载完整用户数据\n     */\n    private loadDataInBackground(): void {\n        // 使用setTimeout确保不阻塞当前流程\n        setTimeout(async () => {\n            try {\n                oops.log.logBusiness('🔄 开始后台加载完整用户数据...');\n\n                // 🚀 强制执行完整的登录流程，不跳过\n                await this.forceCompleteUserDataLoad();\n\n                oops.log.logBusiness('✅ 后台用户数据加载完成');\n\n                // 触发数据更新事件，通知游戏其他模块\n                oops.message.dispatchEvent('UserDataLoaded');\n            } catch (error) {\n                oops.log.logWarn('⚠️ 后台用户数据加载失败:', error);\n            }\n        }, 100);\n    }\n\n    /**\n     * 🔄 强制执行完整的用户数据加载流程\n     */\n    private async forceCompleteUserDataLoad(): Promise<void> {\n        try {\n            // 🔄 临时清除基础数据标记，强制执行完整登录\n            const tempUserData = this.RoleModel?.userGameData;\n            if (tempUserData) {\n                // 标记这是临时数据，需要完整登录\n                (tempUserData as any).isTemporaryData = true;\n            }\n\n            // 🔄 执行完整的登录流程\n            await this.performCompleteLogin();\n        } catch (error) {\n            oops.log.logWarn('⚠️ 强制完整登录失败:', error);\n            throw error;\n        }\n    }\n\n    /**\n     * 🔐 执行完整的登录流程\n     */\n    private async performCompleteLogin(): Promise<void> {\n        oops.log.logBusiness('🔐 开始执行完整登录流程...');\n\n        try {\n            const { LoginViewComp } = await import('../initialize/view/LoginViewComp');\n\n            // 🔧 根据平台选择不同的登录方式\n            if (ShareConfig.platform === Platform.FACEBOOK) {\n                // Facebook环境：执行Facebook自动登录\n                oops.log.logBusiness('🔐 Facebook环境：执行自动登录...');\n                const loginSuccess = await LoginViewComp.doFacebookLogin();\n\n                if (loginSuccess) {\n                    oops.log.logBusiness('✅ Facebook自动登录成功');\n                } else {\n                    oops.log.logWarn('⚠️ Facebook自动登录失败');\n                }\n            } else {\n                // 🔐 其他环境：使用现有的游客登录逻辑\n                oops.log.logBusiness('🔐 执行游客登录流程...');\n\n                // 🚀 直接使用LoginViewComp的游客登录方法\n                const loginViewComp = new LoginViewComp();\n                const loginSuccess = await loginViewComp.loginGuestButton();\n\n                if (loginSuccess) {\n                    oops.log.logBusiness('✅ 游客登录成功');\n                } else {\n                    oops.log.logWarn('⚠️ 游客登录失败');\n                }\n            }\n\n            oops.log.logBusiness('✅ 登录流程完成');\n        } catch (error) {\n            oops.log.logWarn('⚠️ 登录流程失败:', error);\n            // 不抛出错误，允许游戏继续运行\n        }\n    }\n\n    /**\n     * 🔄 确保用户数据已加载 - 完整登录流程\n     */\n    private async ensureDataLoaded(): Promise<void> {\n        // 检查是否已有用户数据，但排除临时数据\n        const userData = this.RoleModel?.userGameData;\n        if (userData && !(userData as any).isTemporaryData) {\n            oops.log.logBusiness('✅ 用户数据已存在，跳过加载');\n            return;\n        }\n\n        if ((userData as any)?.isTemporaryData) {\n            oops.log.logBusiness('🔄 检测到临时数据，执行完整登录流程...');\n        } else {\n            oops.log.logBusiness('🔄 开始完整用户数据加载流程...');\n        }\n\n        // 直接调用现有的loadData方法\n        await this.loadData();\n    }\n\n    // ==================== 记录数据 ====================\n\n    /**\n     * 获取指定日期的记录数据\n     */\n    getRecordData(recordType: RecordType, dateString?: string): RecordTypeData | null {\n        if (!this.validateUserData()) {\n            return null;\n        }\n\n        const targetDate = dateString || new Date().toDateString();\n        const recordData = this.RoleModel.userGameData.recordData?.[targetDate];\n\n        return recordData?.[recordType] || null;\n    }\n\n    /**\n     * 🔧 新增：获取指定关卡的今日挑战次数\n     * @param levelId 关卡ID\n     * @param dateString 可选的日期字符串，默认为今日\n     * @returns 该关卡的挑战次数\n     */\n    getLevelChallengeCount(levelId: number, dateString?: string): number {\n        const recordData = this.getRecordData(RecordType.Level, dateString);\n        if (!recordData?.levelDetails) {\n            return 0;\n        }\n\n        const levelKey = `level_${levelId}`;\n        return recordData.levelDetails[levelKey]?.attempts || 0;\n    }\n\n    /**\n     * 🔧 新增：获取当前关卡的今日挑战次数\n     * @param dateString 可选的日期字符串，默认为今日\n     * @returns 当前关卡的挑战次数\n     */\n    getCurrentLevelChallengeCount(dateString?: string): number {\n        const currentLevelId = this.getNextLevelIndex(); // 获取当前要挑战的关卡\n        return this.getLevelChallengeCount(currentLevelId, dateString);\n    }\n\n    // ==================== 工具方法 ====================\n\n    /**\n     * 获取完整的用户游戏数据\n     */\n    getUserGameData(): UserGameData {\n        if (!this.validateUserData()) {\n            throw new Error('用户数据尚未初始化，请等待登录完成');\n        }\n        return this.RoleModel.userGameData;\n    }\n\n    // ==================== 私有方法 ====================\n\n    /**\n     * 验证用户数据是否可用\n     */\n    private validateUserData(): boolean {\n        return !!this.RoleModel?.userGameData;\n    }\n\n    /**\n     * 检查是否有用户数据\n     */\n    private hasUserData(): boolean {\n        return this.validateUserData();\n    }\n\n    /**\n     * 验证网络连接\n     */\n    private validateNetworkConnection(): boolean {\n        if (!smc.net?.hcGame) {\n            oops.log.logError('❌ 网络连接未初始化');\n            return false;\n        }\n        return true;\n    }\n\n    /**\n     * 等待用户数据初始化完成\n     */\n    private async waitForUserDataInitialization(): Promise<void> {\n        const maxRetries = 30; // 🚀 减少最大重试次数\n        let retryCount = 0;\n\n        while (retryCount < maxRetries) {\n            if (this.hasUserData()) {\n                return;\n            }\n\n            // 🚀 使用更短的等待时间，但增加检查频率\n            await new Promise(resolve => setTimeout(resolve, 30));\n            retryCount++;\n\n            if (retryCount % 5 === 0) {\n                oops.log.logBusiness(`⏳ 等待用户数据初始化... ${retryCount}/${maxRetries}`);\n            }\n        }\n\n        // 🚀 超时后不抛出错误，而是记录警告并继续\n        oops.log.logWarn('⚠️ 用户数据初始化超时，但继续执行');\n    }\n\n    /**\n     * 确保数据结构完整性\n     */\n    private ensureDataIntegrity(data: UserGameData): void {\n        data.key = data.key || 0;\n        data.userName = data.userName || 'Player';\n        data.index = data.index || 0;\n        data.isNewPlayer = data.isNewPlayer ?? true;\n        data.propUseData = data.propUseData || {};\n        data.recordData = data.recordData || {};\n    }\n\n    /**\n     * 深度合并用户数据\n     */\n    private mergeUserData(target: UserGameData, source: UserGameData): void {\n        Object.keys(source).forEach(key => {\n            const sourceValue = source[key as keyof UserGameData];\n            const targetValue = target[key as keyof UserGameData];\n\n            if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {\n                if (targetValue && typeof targetValue === 'object') {\n                    this.mergeUserData(targetValue as any, sourceValue as any);\n                } else {\n                    (target as any)[key] = sourceValue;\n                }\n            } else {\n                (target as any)[key] = sourceValue;\n            }\n        });\n    }\n\n    /**\n     * 创建默认道具数据 - 优化版本\n     */\n    private createDefaultPropData(propType: PropType): any {\n        // 🎁 为新手玩家提供默认道具数量\n        const defaultAmount = this.getNewPlayerDefaultAmount(propType);\n\n        return {\n            amount: defaultAmount,\n            propType,\n            propId: propType,\n            desc: `道具${propType}`,\n            getTime: new Date(),\n            lastResetTime: new Date(),\n            lastUpdateTime: new Date(),\n        };\n    }\n\n    /**\n     * 🎁 获取新手玩家的默认道具数量\n     */\n    private getNewPlayerDefaultAmount(propType: PropType): number {\n        if (!this.isNewPlayer()) {\n            return 0;\n        }\n\n        // 🎯 新手玩家默认道具配置（使用配置常量）\n        switch (propType) {\n            case PropType.PropsMoveOut: // 移出道具\n                return GameConst.newPlayerDefaultProps.moveOut;\n            case PropType.PropsTips: // 提示道具\n                return GameConst.newPlayerDefaultProps.tips;\n            case PropType.PropsReShuffle: // 重新洗牌道具\n                return GameConst.newPlayerDefaultProps.reShuffle;\n            case PropType.PropsDayLeftCount: // 每日挑战剩余次数\n                return GameConst.dayFreeLimts; // 使用配置的默认值\n            case PropType.PropsRevive: // 复活道具\n                return GameConst.newPlayerDefaultProps.revive;\n            case PropType.PropsExp: // 游戏经验\n                return 0;\n            case PropType.PropsCoin: // 玩家金币\n                return 0;\n            default:\n                return 0;\n        }\n    }\n\n    /**\n     * 更新ViewModel\n     */\n    private updateViewModel(): void {\n        if (!this.hasUserData()) {\n            return;\n        }\n\n        this.removeFromViewModel();\n\n        const viewModelData = {\n            userId: this.RoleModel.userGameData.key,\n            userName: this.RoleModel.userGameData.userName,\n            level: this.getGameProgress(),\n            isNewPlayer: this.RoleModel.userGameData.isNewPlayer,\n            index: this.RoleModel.userGameData.index,\n            propUseData: this.RoleModel.userGameData.propUseData,\n            // 扩展其他需要的数据\n            ...this.RoleModel.userGameData,\n        };\n\n        VM.add(viewModelData, 'role');\n        oops.log.logBusiness('🎯 ViewModel已更新');\n    }\n\n    /**\n     * 从ViewModel移除数据\n     */\n    private removeFromViewModel(): void {\n        VM.remove('role');\n    }\n\n    /**\n     * 统一的API错误处理\n     */\n    private handleAPIError(apiName: string, error: any): void {\n        oops.log.logError(`❌ ${apiName} API失败:`, error);\n\n        const errorMessage = error?.message || error?.code?.toString() || '操作失败';\n        oops.gui.toast(errorMessage);\n    }\n}\n"]}