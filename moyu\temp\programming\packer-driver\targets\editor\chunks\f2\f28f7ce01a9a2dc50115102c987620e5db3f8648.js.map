{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/role/Role.ts"], "names": ["oops", "ecs", "LanguageData", "VM", "GameConst", "PropType", "RecordType", "SceneItemType", "GameStorageConfig", "DataManager", "GameEvent", "smc", "RoleModelComp", "Role", "register", "Entity", "RoleModel", "pendingPropUpdates", "dataManager", "init", "add", "getInstance", "destroy", "removeFromViewModel", "remove", "loadData", "log", "logBusiness", "validateNetworkConnection", "response", "net", "hcGame", "callApi", "isSucc", "waitForUserDataInitialization", "handleAPIError", "err", "error", "logError", "updateUserData", "newData", "isFirstInitialization", "hasUserData", "ensureDataIntegrity", "userGameData", "mergeUserData", "updateViewModel", "message", "dispatchEvent", "UserDataInitialized", "getPropData", "propType", "validateUserData", "createDefaultPropData", "propData", "propUseData", "defaultData", "getPropsDataByType", "tryCostProp", "args", "showToast", "failureMessageKey", "gui", "toast", "absAmount", "Math", "abs", "amount", "canUse", "canUseProp", "getLangByID", "updatePropData", "updateProp", "reason", "userData", "isTemporaryData", "lastUpdateTime", "Date", "UseProp", "queuePropUpdateForSync", "batchUpdateProps", "updates", "update", "success", "getGameProgress", "index", "getPassIndex", "getNextLevelIndex", "currentProgress", "progress", "nextLevel", "Max", "updateGameProgress", "newIndex", "isGM", "targetIndex", "isGm", "GamePass", "oldIndex", "isNewPlayer", "localRecord", "storage", "get<PERSON>son", "UserDumpKey", "String", "completeNewPlayerGuide", "once", "syncNewPlayerStatusToServer", "BasicInfoUpdate", "push", "syncPendingPropUpdates", "length", "log<PERSON>arn", "getRecordData", "recordType", "dateString", "targetDate", "toDateString", "recordData", "getLevelChallengeCount", "levelId", "Level", "levelDetails", "level<PERSON><PERSON>", "attempts", "getCurrentLevelChallengeCount", "currentLevelId", "getUserGameData", "Error", "maxRetries", "retryCount", "Promise", "resolve", "setTimeout", "data", "key", "userName", "target", "source", "Object", "keys", "for<PERSON>ach", "sourceValue", "targetValue", "Array", "isArray", "defaultAmount", "getNewPlayerDefaultAmount", "propId", "desc", "getTime", "lastResetTime", "newPlayerFastStart", "PropsMoveOut", "newPlayerDefaultProps", "moveOut", "PropsTips", "tips", "PropsReShuffle", "reShuffle", "PropsDayLeftCount", "dayFreeLimts", "PropsRevive", "revive", "PropsExp", "PropsCoin", "viewModelData", "userId", "level", "apiName", "errorMessage", "code", "toString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,G,iBAAAA,G;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,E,iBAAAA,E;;AACAC,MAAAA,S,iBAAAA,S;;AAELC,MAAAA,Q,iBAAAA,Q;AACAC,MAAAA,U,iBAAAA,U;AAEAC,MAAAA,a,iBAAAA,a;;AAGKC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,S,kBAAAA,S;;AACAC,MAAAA,G,kBAAAA,G;;AACAC,MAAAA,a,kBAAAA,a;;;;;;;AAET;AACA;AACA;AACA;AACA;AACA;AACA;AACA;sBAEaC,I,WADZ;AAAA;AAAA,sBAAIC,QAAJ,CAAa,MAAb,C,gBAAD,MACaD,IADb,SAC0B;AAAA;AAAA,sBAAIE,MAD9B,CACqC;AAAA;AAAA;AAAA,eACjCC,SADiC;AAGjC;AAHiC,eAIzBC,kBAJyB,GAQ5B,EAR4B;AAAA,eASzBC,WATyB;AAAA;;AAWjC;AAEUC,QAAAA,IAAI,GAAG;AACb,eAAKC,GAAL;AAAA;AAAA;AACA,eAAKF,WAAL,GAAmB;AAAA;AAAA,0CAAYG,WAAZ,EAAnB;AACH;;AAEDC,QAAAA,OAAO,GAAS;AACZ,eAAKC,mBAAL;AACA,eAAKC,MAAL;AAAA;AAAA;AACH,SArBgC,CAuBjC;;AAEA;AACJ;AACA;AACA;;;AACkB,cAARC,QAAQ,GAAqB;AAC/B;AAAA;AAAA,4BAAKC,GAAL,CAASC,WAAT,CAAqB,gBAArB;;AAEA,cAAI,CAAC,KAAKC,yBAAL,EAAL,EAAuC;AACnC,mBAAO,KAAP;AACH;;AAED,cAAI;AACA,kBAAMC,QAAQ,GAAG,MAAM;AAAA;AAAA,4BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,EAAnC,CAAvB;;AAEA,gBAAIH,QAAQ,CAACI,MAAb,EAAqB;AACjB,oBAAM,KAAKC,6BAAL,EAAN;AACA,qBAAO,IAAP;AACH,aAHD,MAGO;AACH,mBAAKC,cAAL,CAAoB,UAApB,EAAgCN,QAAQ,CAACO,GAAzC;AACA,qBAAO,KAAP;AACH;AACJ,WAVD,CAUE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKX,GAAL,CAASY,QAAT,CAAkB,aAAlB,EAAiCD,KAAjC;AACA,mBAAO,KAAP;AACH;AACJ;AAED;AACJ;AACA;;;AACIE,QAAAA,cAAc,CAACC,OAAD,EAA8B;AACxC,gBAAMC,qBAAqB,GAAG,CAAC,KAAKC,WAAL,EAA/B,CADwC,CAGxC;;AACA,eAAKC,mBAAL,CAAyBH,OAAzB,EAJwC,CAMxC;;AACA,cAAI,KAAKxB,SAAL,CAAe4B,YAAnB,EAAiC;AAC7B,iBAAKC,aAAL,CAAmB,KAAK7B,SAAL,CAAe4B,YAAlC,EAAgDJ,OAAhD;AACH,WAFD,MAEO;AACH,iBAAKxB,SAAL,CAAe4B,YAAf,GAA8BJ,OAA9B;AACH,WAXuC,CAaxC;;;AACA,eAAKM,eAAL,GAdwC,CAgBxC;;AACA,cAAIL,qBAAJ,EAA2B;AACvB;AAAA;AAAA,8BAAKf,GAAL,CAASC,WAAT,CAAqB,gBAArB;AACA;AAAA;AAAA,8BAAKoB,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,wCAAUC,mBAArC,EAA0D,KAAKjC,SAAL,CAAe4B,YAAzE;AACH;AACJ,SA5EgC,CA8EjC;;AAEA;AACJ;AACA;;;AACIM,QAAAA,WAAW,CAACC,QAAD,EAA0B;AAAA;;AACjC,cAAI,CAAC,KAAKC,gBAAL,EAAL,EAA8B;AAC1B,mBAAO,KAAKC,qBAAL,CAA2BF,QAA3B,CAAP;AACH;;AAED,gBAAMG,QAAQ,4BAAG,KAAKtC,SAAL,CAAe4B,YAAf,CAA4BW,WAA/B,qBAAG,sBAA0CJ,QAA1C,CAAjB;;AACA,cAAI,CAACG,QAAL,EAAe;AACX;AACA,kBAAME,WAAW,GAAG,KAAKH,qBAAL,CAA2BF,QAA3B,CAApB;AACA,iBAAKnC,SAAL,CAAe4B,YAAf,CAA4BW,WAA5B,GAA0C,KAAKvC,SAAL,CAAe4B,YAAf,CAA4BW,WAA5B,IAA2C,EAArF;AACA,iBAAKvC,SAAL,CAAe4B,YAAf,CAA4BW,WAA5B,CAAwCJ,QAAxC,IAAoDK,WAApD;AACA,mBAAOA,WAAP;AACH;;AAED,iBAAOF,QAAP;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,kBAAkB,CAACN,QAAD,EAA0B;AACxC,iBAAO,KAAKD,WAAL,CAAiBC,QAAjB,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIO,QAAAA,WAAW,CACPC,IADO,EAEPC,SAAkB,GAAG,IAFd,EAGPC,iBAHO,EAIA;AACP,cAAI,CAAC,KAAKT,gBAAL,EAAL,EAA8B;AAC1B,gBAAIQ,SAAJ,EAAe;AACX;AAAA;AAAA,gCAAKE,GAAL,CAASC,KAAT,CAAe,UAAf;AACH;;AACD,mBAAO,KAAP;AACH;;AAED,gBAAMC,SAAS,GAAGC,IAAI,CAACC,GAAL,CAASP,IAAI,CAACQ,MAAd,CAAlB;AACA,gBAAMC,MAAM,GAAG,KAAKC,UAAL,CAAgBV,IAAI,CAACR,QAArB,EAA+Ba,SAA/B,CAAf;;AAEA,cAAI,CAACI,MAAD,IAAWR,SAAX,IAAwBC,iBAA5B,EAA+C;AAC3C;AAAA;AAAA,8BAAKC,GAAL,CAASC,KAAT,CAAe;AAAA;AAAA,8CAAaO,WAAb,CAAyBT,iBAAzB,KAA+CA,iBAA9D;AACH;;AAED,iBAAOO,MAAP;AACH;AAED;AACJ;AACA;;;AACwB,cAAdG,cAAc,CAACZ,IAAD,EAIC;AACjB,iBAAO,MAAM,KAAKa,UAAL,CAAgBb,IAAI,CAACR,QAArB,EAA+BQ,IAAI,CAACQ,MAApC,EAA4CR,IAAI,CAACc,MAAjD,CAAb;AACH;AAED;AACJ;AACA;;;AACIJ,QAAAA,UAAU,CAAClB,QAAD,EAAqBgB,MAArB,EAA8C;AACpD,gBAAMb,QAAQ,GAAG,KAAKJ,WAAL,CAAiBC,QAAjB,CAAjB;AACA,iBAAOG,QAAQ,CAACa,MAAT,IAAmBF,IAAI,CAACC,GAAL,CAASC,MAAT,CAA1B;AACH;AAED;AACJ;AACA;;;AACoB,cAAVK,UAAU,CAACrB,QAAD,EAAqBgB,MAArB,EAAqCM,MAArC,EAAwE;AAAA;;AACpF;AACA,cAAIN,MAAM,GAAG,CAAT,IAAc,CAAC,KAAKE,UAAL,CAAgBlB,QAAhB,EAA0Bc,IAAI,CAACC,GAAL,CAASC,MAAT,CAA1B,CAAnB,EAAgE;AAC5D;AAAA;AAAA,8BAAKL,GAAL,CAASC,KAAT,CAAe;AAAA;AAAA,8CAAaO,WAAb,CAAyB,gBAAzB,CAAf;AACA,mBAAO,KAAP;AACH,WALmF,CAOpF;;;AACA,gBAAMI,QAAQ,sBAAG,KAAK1D,SAAR,qBAAG,gBAAgB4B,YAAjC;;AACA,cAAK8B,QAAL,YAAKA,QAAD,CAAmBC,eAAvB,EAAwC;AACpC;AAAA;AAAA,8BAAKjD,GAAL,CAASC,WAAT,CAAqB,uBAArB,EADoC,CAGpC;;AACA,kBAAM2B,QAAQ,GAAG,KAAKG,kBAAL,CAAwBN,QAAxB,CAAjB;;AACA,gBAAIG,QAAJ,EAAc;AACVA,cAAAA,QAAQ,CAACa,MAAT,IAAmBA,MAAnB;AACAb,cAAAA,QAAQ,CAACsB,cAAT,GAA0B,IAAIC,IAAJ,EAA1B;AAEA;AAAA;AAAA,gCAAKnD,GAAL,CAASC,WAAT,CACK,aAAYwB,QAAS,IAAGgB,MAAM,GAAG,CAAT,GAAa,GAAb,GAAmB,EAAG,GAAEA,MAAO,OAD5D,EAJU,CAQV;;AACA,kBAAIA,MAAM,GAAG,CAAb,EAAgB;AACZ;AAAA;AAAA,kCAAKpB,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,4CAAU8B,OAArC,EAA8C;AAAE3B,kBAAAA,QAAF;AAAYgB,kBAAAA;AAAZ,iBAA9C;AACH,eAXS,CAaV;;;AACA,mBAAKY,sBAAL,CAA4B5B,QAA5B,EAAsCgB,MAAtC,EAA8CM,MAA9C;AACA,qBAAO,IAAP;AACH;AACJ;;AAED,cAAI;AACA,kBAAM5C,QAAQ,GAAG,MAAM;AAAA;AAAA,4BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,YAAvB,EAAqC;AACxDmB,cAAAA,QADwD;AAExDgB,cAAAA,MAFwD;AAGxDM,cAAAA,MAAM,EAAEA,MAAM,IAAI;AAHsC,aAArC,CAAvB;;AAMA,gBAAI5C,QAAQ,CAACI,MAAb,EAAqB;AACjB;AAAA;AAAA,gCAAKP,GAAL,CAASC,WAAT,CACK,aAAYwB,QAAS,IAAGgB,MAAM,GAAG,CAAT,GAAa,GAAb,GAAmB,EAAG,GAAEA,MAAO,EAD5D,EADiB,CAKjB;;AACA,kBAAIA,MAAM,GAAG,CAAb,EAAgB;AACZ;AAAA;AAAA,kCAAKpB,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,4CAAU8B,OAArC,EAA8C;AAAE3B,kBAAAA,QAAF;AAAYgB,kBAAAA;AAAZ,iBAA9C;AACH;;AAED,qBAAO,IAAP;AACH,aAXD,MAWO;AACH,mBAAKhC,cAAL,CAAoB,YAApB,EAAkCN,QAAQ,CAACO,GAA3C;AACA,qBAAO,KAAP;AACH;AACJ,WAtBD,CAsBE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKX,GAAL,CAASY,QAAT,CAAkB,WAAlB,EAA+BD,KAA/B;AACA;AAAA;AAAA,8BAAKyB,GAAL,CAASC,KAAT,CAAe,UAAf;AACA,mBAAO,KAAP;AACH;AACJ;AAED;AACJ;AACA;;;AAC0B,cAAhBiB,gBAAgB,CAClBC,OADkB,EAEF;AAChB,eAAK,MAAMC,MAAX,IAAqBD,OAArB,EAA8B;AAC1B,kBAAME,OAAO,GAAG,MAAM,KAAKX,UAAL,CAAgBU,MAAM,CAAC/B,QAAvB,EAAiC+B,MAAM,CAACf,MAAxC,EAAgDe,MAAM,CAACT,MAAvD,CAAtB;;AACA,gBAAI,CAACU,OAAL,EAAc;AACV,qBAAO,KAAP;AACH;AACJ;;AACD,iBAAO,IAAP;AACH,SAzOgC,CA2OjC;;AAEA;AACJ;AACA;;;AACIC,QAAAA,eAAe,GAAW;AACtB,cAAI,CAAC,KAAKhC,gBAAL,EAAL,EAA8B;AAC1B,mBAAO,CAAP;AACH;;AACD,iBAAO,KAAKpC,SAAL,CAAe4B,YAAf,CAA4ByC,KAA5B,IAAqC,CAA5C;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,YAAY,GAAW;AACnB,iBAAO,KAAKF,eAAL,EAAP;AACH;AAED;AACJ;AACA;;;AACIG,QAAAA,iBAAiB,CAACC,eAAD,EAAmC;AAChD,gBAAMC,QAAQ,GAAGD,eAAH,WAAGA,eAAH,GAAsB,KAAKJ,eAAL,EAApC;AACA,gBAAMM,SAAS,GAAG,CAACD,QAAQ,GAAG,CAAZ,IAAiB;AAAA;AAAA,8CAAcE,GAAjD;AACA,iBAAOD,SAAS,KAAK,CAAd,GAAkB,CAAlB,GAAsBA,SAA7B;AACH;AAED;AACJ;AACA;;;AAC4B,cAAlBE,kBAAkB,CAACC,QAAD,EAAoBC,IAAa,GAAG,KAApC,EAA6D;AACjF,gBAAMC,WAAW,GAAGF,QAAH,WAAGA,QAAH,GAAe,KAAKT,eAAL,KAAyB,CAAzD;;AAEA,cAAI;AACA,kBAAMvD,QAAQ,GAAG,MAAM;AAAA;AAAA,4BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,gBAAvB,EAAyC;AAC5DqD,cAAAA,KAAK,EAAEU,WADqD;AAE5DC,cAAAA,IAAI,EAAEF;AAFsD,aAAzC,CAAvB;;AAKA,gBAAIjE,QAAQ,CAACI,MAAb,EAAqB;AACjB;AAAA;AAAA,gCAAKP,GAAL,CAASC,WAAT,CAAsB,eAAcoE,WAAY,EAAhD,EADiB,CAGjB;;AACA;AAAA;AAAA,gCAAKhD,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,0CAAUiD,QAArC,EAA+C;AAC3CC,gBAAAA,QAAQ,EAAE,KAAKd,eAAL,EADiC;AAE3CS,gBAAAA,QAAQ,EAAEE,WAFiC;AAG3CC,gBAAAA,IAAI,EAAEF;AAHqC,eAA/C;AAMA,qBAAO,IAAP;AACH,aAXD,MAWO;AACH,mBAAK3D,cAAL,CAAoB,gBAApB,EAAsCN,QAAQ,CAACO,GAA/C;AACA,qBAAO,KAAP;AACH;AACJ,WArBD,CAqBE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKX,GAAL,CAASY,QAAT,CAAkB,aAAlB,EAAiCD,KAAjC;AACA;AAAA;AAAA,8BAAKyB,GAAL,CAASC,KAAT,CAAe,UAAf;AACA,mBAAO,KAAP;AACH;AACJ,SAvSgC,CAySjC;;AAEA;AACJ;AACA;;;AACIoC,QAAAA,WAAW,GAAY;AACnB;AACA,cAAI,KAAKzD,WAAL,EAAJ,EAAwB;AAAA;;AACpB,6CAAO,KAAK1B,SAAL,CAAe4B,YAAf,CAA4BuD,WAAnC,qCAAkD,IAAlD;AACH;;AAED,gBAAMC,WAAW,GAAG;AAAA;AAAA,4BAAKC,OAAL,CAAaC,OAAb,CAAqB;AAAA;AAAA,sDAAkBC,WAAvC,EAAoD,CAApD,CAApB;AACA,iBAAO,CAACH,WAAD,IAAgBI,MAAM,CAACJ,WAAD,CAAN,KAAwB,GAA/C;AACH;AAED;AACJ;AACA;;;AACgC,cAAtBK,sBAAsB,GAAqB;AAAA;;AAC7C,cAAI,CAAC,KAAKN,WAAL,EAAL,EAAyB;AACrB;AAAA;AAAA,8BAAKzE,GAAL,CAASC,WAAT,CAAqB,aAArB;AACA,mBAAO,IAAP;AACH,WAJ4C,CAM7C;;;AACA,gBAAM+C,QAAQ,uBAAG,KAAK1D,SAAR,qBAAG,iBAAgB4B,YAAjC;;AACA,cAAK8B,QAAL,YAAKA,QAAD,CAAmBC,eAAvB,EAAwC;AACpC;AAAA;AAAA,8BAAKjD,GAAL,CAASC,WAAT,CAAqB,uBAArB,EADoC,CAEpC;;AACA,gBAAI,KAAKX,SAAL,IAAkB,KAAKA,SAAL,CAAe4B,YAArC,EAAmD;AAC/C,mBAAK5B,SAAL,CAAe4B,YAAf,CAA4BuD,WAA5B,GAA0C,KAA1C;AACA;AAAA;AAAA,gCAAKzE,GAAL,CAASC,WAAT,CAAqB,wBAArB;AACH,aANmC,CAQpC;;;AACA;AAAA;AAAA,8BAAKoB,OAAL,CAAa2D,IAAb,CACI,gBADJ,EAEI,YAAY;AACR,oBAAM,KAAKC,2BAAL,EAAN;AACH,aAJL,EAKI,IALJ;AAQA,mBAAO,IAAP;AACH;;AAED,iBAAO,MAAM,KAAKA,2BAAL,EAAb;AACH;AAED;AACJ;AACA;;;AAC6C,cAA3BA,2BAA2B,GAAqB;AAC1D,cAAI;AACA,kBAAM9E,QAAQ,GAAG,MAAM;AAAA;AAAA,4BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,sBAAvB,EAA+C;AAClEmE,cAAAA,WAAW,EAAE;AADqD,aAA/C,CAAvB;;AAIA,gBAAItE,QAAQ,CAACI,MAAb,EAAqB;AACjB;AACA,kBAAI,KAAKjB,SAAL,IAAkB,KAAKA,SAAL,CAAe4B,YAArC,EAAmD;AAC/C,qBAAK5B,SAAL,CAAe4B,YAAf,CAA4BuD,WAA5B,GAA0C,KAA1C;AACA;AAAA;AAAA,kCAAKzE,GAAL,CAASC,WAAT,CAAqB,oBAArB;AACH;;AAED;AAAA;AAAA,gCAAKD,GAAL,CAASC,WAAT,CAAqB,UAArB;AACA;AAAA;AAAA,gCAAKoB,OAAL,CAAaC,aAAb,CAA2B;AAAA;AAAA,0CAAU4D,eAArC,EAAsD;AAAET,gBAAAA,WAAW,EAAE;AAAf,eAAtD;AACA,qBAAO,IAAP;AACH,aAVD,MAUO;AACH,mBAAKhE,cAAL,CAAoB,sBAApB,EAA4CN,QAAQ,CAACO,GAArD;AACA,qBAAO,KAAP;AACH;AACJ,WAnBD,CAmBE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAKX,GAAL,CAASY,QAAT,CAAkB,aAAlB,EAAiCD,KAAjC;AACA;AAAA;AAAA,8BAAKyB,GAAL,CAASC,KAAT,CAAe,UAAf;AACA,mBAAO,KAAP;AACH;AACJ;AAED;AACJ;AACA;;;AACYgB,QAAAA,sBAAsB,CAAC5B,QAAD,EAAqBgB,MAArB,EAAqCM,MAArC,EAA4D;AACtF,eAAKxD,kBAAL,CAAwB4F,IAAxB,CAA6B;AAAE1D,YAAAA,QAAF;AAAYgB,YAAAA,MAAZ;AAAoBM,YAAAA;AAApB,WAA7B;AACA;AAAA;AAAA,4BAAK/C,GAAL,CAASC,WAAT,CAAsB,mBAAkBwB,QAAS,IAAGgB,MAAO,EAA3D,EAFsF,CAItF;;AACA;AAAA;AAAA,4BAAKpB,OAAL,CAAa2D,IAAb,CACI,gBADJ,EAEI,YAAY;AACR,kBAAM,KAAKI,sBAAL,EAAN;AACH,WAJL,EAKI,IALJ;AAOH;AAED;AACJ;AACA;;;AACwC,cAAtBA,sBAAsB,GAAkB;AAClD,cAAI,KAAK7F,kBAAL,CAAwB8F,MAAxB,KAAmC,CAAvC,EAA0C;AACtC;AACH;;AAED;AAAA;AAAA,4BAAKrF,GAAL,CAASC,WAAT,CAAsB,WAAU,KAAKV,kBAAL,CAAwB8F,MAAO,QAA/D,EALkD,CAOlD;;AACA,gBAAM9B,OAAO,GAAG,CAAC,GAAG,KAAKhE,kBAAT,CAAhB;AACA,eAAKA,kBAAL,GAA0B,EAA1B;;AAEA,eAAK,MAAMiE,MAAX,IAAqBD,OAArB,EAA8B;AAC1B,gBAAI;AACA,oBAAMpD,QAAQ,GAAG,MAAM;AAAA;AAAA,8BAAIC,GAAJ,CAAQC,MAAR,CAAeC,OAAf,CAAuB,YAAvB,EAAqC;AACxDmB,gBAAAA,QAAQ,EAAE+B,MAAM,CAAC/B,QADuC;AAExDgB,gBAAAA,MAAM,EAAEe,MAAM,CAACf,MAFyC;AAGxDM,gBAAAA,MAAM,EAAES,MAAM,CAACT,MAAP,IAAiB;AAH+B,eAArC,CAAvB;;AAMA,kBAAI5C,QAAQ,CAACI,MAAb,EAAqB;AACjB;AAAA;AAAA,kCAAKP,GAAL,CAASC,WAAT,CACK,aAAYuD,MAAM,CAAC/B,QAAS,IAAG+B,MAAM,CAACf,MAAP,GAAgB,CAAhB,GAAoB,GAApB,GAA0B,EAAG,GAAEe,MAAM,CAACf,MAAO,EADjF;AAGH,eAJD,MAIO;AACH;AAAA;AAAA,kCAAKzC,GAAL,CAASsF,OAAT,CAAkB,cAAa9B,MAAM,CAAC/B,QAAS,EAA/C,EAAkDtB,QAAQ,CAACO,GAA3D;AACH;AACJ,aAdD,CAcE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKX,GAAL,CAASY,QAAT,CAAmB,aAAY4C,MAAM,CAAC/B,QAAS,EAA/C,EAAkDd,KAAlD;AACH;AACJ;;AAED;AAAA;AAAA,4BAAKX,GAAL,CAASC,WAAT,CAAqB,YAArB;AACH,SA5agC,CA8ajC;;AAEA;AACJ;AACA;;;AACIsF,QAAAA,aAAa,CAACC,UAAD,EAAyBC,UAAzB,EAAqE;AAAA;;AAC9E,cAAI,CAAC,KAAK/D,gBAAL,EAAL,EAA8B;AAC1B,mBAAO,IAAP;AACH;;AAED,gBAAMgE,UAAU,GAAGD,UAAU,IAAI,IAAItC,IAAJ,GAAWwC,YAAX,EAAjC;AACA,gBAAMC,UAAU,6BAAG,KAAKtG,SAAL,CAAe4B,YAAf,CAA4B0E,UAA/B,qBAAG,uBAAyCF,UAAzC,CAAnB;AAEA,iBAAO,CAAAE,UAAU,QAAV,YAAAA,UAAU,CAAGJ,UAAH,CAAV,KAA4B,IAAnC;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIK,QAAAA,sBAAsB,CAACC,OAAD,EAAkBL,UAAlB,EAA+C;AAAA;;AACjE,gBAAMG,UAAU,GAAG,KAAKL,aAAL,CAAmB;AAAA;AAAA,wCAAWQ,KAA9B,EAAqCN,UAArC,CAAnB;;AACA,cAAI,EAACG,UAAD,YAACA,UAAU,CAAEI,YAAb,CAAJ,EAA+B;AAC3B,mBAAO,CAAP;AACH;;AAED,gBAAMC,QAAQ,GAAI,SAAQH,OAAQ,EAAlC;AACA,iBAAO,0BAAAF,UAAU,CAACI,YAAX,CAAwBC,QAAxB,4CAAmCC,QAAnC,KAA+C,CAAtD;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,6BAA6B,CAACV,UAAD,EAA8B;AACvD,gBAAMW,cAAc,GAAG,KAAKvC,iBAAL,EAAvB,CADuD,CACN;;AACjD,iBAAO,KAAKgC,sBAAL,CAA4BO,cAA5B,EAA4CX,UAA5C,CAAP;AACH,SAtdgC,CAwdjC;;AAEA;AACJ;AACA;;;AACIY,QAAAA,eAAe,GAAiB;AAC5B,cAAI,CAAC,KAAK3E,gBAAL,EAAL,EAA8B;AAC1B,kBAAM,IAAI4E,KAAJ,CAAU,mBAAV,CAAN;AACH;;AACD,iBAAO,KAAKhH,SAAL,CAAe4B,YAAtB;AACH,SAlegC,CAoejC;;AAEA;AACJ;AACA;;;AACYQ,QAAAA,gBAAgB,GAAY;AAAA;;AAChC,iBAAO,CAAC,sBAAC,KAAKpC,SAAN,aAAC,iBAAgB4B,YAAjB,CAAR;AACH;AAED;AACJ;AACA;;;AACYF,QAAAA,WAAW,GAAY;AAC3B,iBAAO,KAAKU,gBAAL,EAAP;AACH;AAED;AACJ;AACA;;;AACYxB,QAAAA,yBAAyB,GAAY;AAAA;;AACzC,cAAI,UAAC;AAAA;AAAA,0BAAIE,GAAL,aAAC,KAASC,MAAV,CAAJ,EAAsB;AAClB;AAAA;AAAA,8BAAKL,GAAL,CAASY,QAAT,CAAkB,YAAlB;AACA,mBAAO,KAAP;AACH;;AACD,iBAAO,IAAP;AACH;AAED;AACJ;AACA;;;AAC+C,cAA7BJ,6BAA6B,GAAkB;AACzD,gBAAM+F,UAAU,GAAG,EAAnB,CADyD,CAClC;;AACvB,cAAIC,UAAU,GAAG,CAAjB;;AAEA,iBAAOA,UAAU,GAAGD,UAApB,EAAgC;AAC5B,gBAAI,KAAKvF,WAAL,EAAJ,EAAwB;AACpB;AACH,aAH2B,CAK5B;;;AACA,kBAAM,IAAIyF,OAAJ,CAAYC,OAAO,IAAIC,UAAU,CAACD,OAAD,EAAU,EAAV,CAAjC,CAAN;AACAF,YAAAA,UAAU;;AAEV,gBAAIA,UAAU,GAAG,CAAb,KAAmB,CAAvB,EAA0B;AACtB;AAAA;AAAA,gCAAKxG,GAAL,CAASC,WAAT,CAAsB,kBAAiBuG,UAAW,IAAGD,UAAW,EAAhE;AACH;AACJ,WAhBwD,CAkBzD;;;AACA;AAAA;AAAA,4BAAKvG,GAAL,CAASsF,OAAT,CAAiB,oBAAjB;AACH;AAED;AACJ;AACA;;;AACYrE,QAAAA,mBAAmB,CAAC2F,IAAD,EAA2B;AAAA;;AAClDA,UAAAA,IAAI,CAACC,GAAL,GAAWD,IAAI,CAACC,GAAL,IAAY,CAAvB;AACAD,UAAAA,IAAI,CAACE,QAAL,GAAgBF,IAAI,CAACE,QAAL,IAAiB,QAAjC;AACAF,UAAAA,IAAI,CAACjD,KAAL,GAAaiD,IAAI,CAACjD,KAAL,IAAc,CAA3B;AACAiD,UAAAA,IAAI,CAACnC,WAAL,wBAAmBmC,IAAI,CAACnC,WAAxB,gCAAuC,IAAvC;AACAmC,UAAAA,IAAI,CAAC/E,WAAL,GAAmB+E,IAAI,CAAC/E,WAAL,IAAoB,EAAvC;AACA+E,UAAAA,IAAI,CAAChB,UAAL,GAAkBgB,IAAI,CAAChB,UAAL,IAAmB,EAArC;AACH;AAED;AACJ;AACA;;;AACYzE,QAAAA,aAAa,CAAC4F,MAAD,EAAuBC,MAAvB,EAAmD;AACpEC,UAAAA,MAAM,CAACC,IAAP,CAAYF,MAAZ,EAAoBG,OAApB,CAA4BN,GAAG,IAAI;AAC/B,kBAAMO,WAAW,GAAGJ,MAAM,CAACH,GAAD,CAA1B;AACA,kBAAMQ,WAAW,GAAGN,MAAM,CAACF,GAAD,CAA1B;;AAEA,gBAAIO,WAAW,IAAI,OAAOA,WAAP,KAAuB,QAAtC,IAAkD,CAACE,KAAK,CAACC,OAAN,CAAcH,WAAd,CAAvD,EAAmF;AAC/E,kBAAIC,WAAW,IAAI,OAAOA,WAAP,KAAuB,QAA1C,EAAoD;AAChD,qBAAKlG,aAAL,CAAmBkG,WAAnB,EAAuCD,WAAvC;AACH,eAFD,MAEO;AACFL,gBAAAA,MAAD,CAAgBF,GAAhB,IAAuBO,WAAvB;AACH;AACJ,aAND,MAMO;AACFL,cAAAA,MAAD,CAAgBF,GAAhB,IAAuBO,WAAvB;AACH;AACJ,WAbD;AAcH;AAED;AACJ;AACA;;;AACYzF,QAAAA,qBAAqB,CAACF,QAAD,EAA0B;AACnD;AACA,gBAAM+F,aAAa,GAAG,KAAKC,yBAAL,CAA+BhG,QAA/B,CAAtB;AAEA,iBAAO;AACHgB,YAAAA,MAAM,EAAE+E,aADL;AAEH/F,YAAAA,QAFG;AAGHiG,YAAAA,MAAM,EAAEjG,QAHL;AAIHkG,YAAAA,IAAI,EAAG,KAAIlG,QAAS,EAJjB;AAKHmG,YAAAA,OAAO,EAAE,IAAIzE,IAAJ,EALN;AAMH0E,YAAAA,aAAa,EAAE,IAAI1E,IAAJ,EANZ;AAOHD,YAAAA,cAAc,EAAE,IAAIC,IAAJ;AAPb,WAAP;AASH;AAED;AACJ;AACA;;;AACYsE,QAAAA,yBAAyB,CAAChG,QAAD,EAA6B;AAC1D;AACA,cAAI,CAAC;AAAA;AAAA,sCAAUqG,kBAAX,IAAiC,CAAC,KAAKrD,WAAL,EAAtC,EAA0D;AACtD,mBAAO,CAAP;AACH,WAJyD,CAM1D;;;AACA,kBAAQhD,QAAR;AACI,iBAAK;AAAA;AAAA,sCAASsG,YAAd;AAA4B;AACxB,qBAAO;AAAA;AAAA,0CAAUC,qBAAV,CAAgCC,OAAvC;;AACJ,iBAAK;AAAA;AAAA,sCAASC,SAAd;AAAyB;AACrB,qBAAO;AAAA;AAAA,0CAAUF,qBAAV,CAAgCG,IAAvC;;AACJ,iBAAK;AAAA;AAAA,sCAASC,cAAd;AAA8B;AAC1B,qBAAO;AAAA;AAAA,0CAAUJ,qBAAV,CAAgCK,SAAvC;;AACJ,iBAAK;AAAA;AAAA,sCAASC,iBAAd;AAAiC;AAC7B,qBAAO;AAAA;AAAA,0CAAUC,YAAjB;AAA+B;;AACnC,iBAAK;AAAA;AAAA,sCAASC,WAAd;AAA2B;AACvB,qBAAO;AAAA;AAAA,0CAAUR,qBAAV,CAAgCS,MAAvC;;AACJ,iBAAK;AAAA;AAAA,sCAASC,QAAd;AAAwB;AACpB,qBAAO,CAAP;;AACJ,iBAAK;AAAA;AAAA,sCAASC,SAAd;AAAyB;AACrB,qBAAO,CAAP;;AACJ;AACI,qBAAO,CAAP;AAhBR;AAkBH;AAED;AACJ;AACA;;;AACYvH,QAAAA,eAAe,GAAS;AAC5B,cAAI,CAAC,KAAKJ,WAAL,EAAL,EAAyB;AACrB;AACH;;AAED,eAAKnB,mBAAL;AAEA,gBAAM+I,aAAa,GAAG;AAClBC,YAAAA,MAAM,EAAE,KAAKvJ,SAAL,CAAe4B,YAAf,CAA4B2F,GADlB;AAElBC,YAAAA,QAAQ,EAAE,KAAKxH,SAAL,CAAe4B,YAAf,CAA4B4F,QAFpB;AAGlBgC,YAAAA,KAAK,EAAE,KAAKpF,eAAL,EAHW;AAIlBe,YAAAA,WAAW,EAAE,KAAKnF,SAAL,CAAe4B,YAAf,CAA4BuD,WAJvB;AAKlBd,YAAAA,KAAK,EAAE,KAAKrE,SAAL,CAAe4B,YAAf,CAA4ByC,KALjB;AAMlB9B,YAAAA,WAAW,EAAE,KAAKvC,SAAL,CAAe4B,YAAf,CAA4BW,WANvB;AAOlB;AACA,eAAG,KAAKvC,SAAL,CAAe4B;AARA,WAAtB;AAWA;AAAA;AAAA,wBAAGxB,GAAH,CAAOkJ,aAAP,EAAsB,MAAtB;AACA;AAAA;AAAA,4BAAK5I,GAAL,CAASC,WAAT,CAAqB,iBAArB;AACH;AAED;AACJ;AACA;;;AACYJ,QAAAA,mBAAmB,GAAS;AAChC;AAAA;AAAA,wBAAGC,MAAH,CAAU,MAAV;AACH;AAED;AACJ;AACA;;;AACYW,QAAAA,cAAc,CAACsI,OAAD,EAAkBpI,KAAlB,EAAoC;AAAA;;AACtD;AAAA;AAAA,4BAAKX,GAAL,CAASY,QAAT,CAAmB,KAAImI,OAAQ,SAA/B,EAAyCpI,KAAzC;AAEA,gBAAMqI,YAAY,GAAG,CAAArI,KAAK,QAAL,YAAAA,KAAK,CAAEU,OAAP,MAAkBV,KAAlB,2BAAkBA,KAAK,CAAEsI,IAAzB,qBAAkB,YAAaC,QAAb,EAAlB,KAA6C,MAAlE;AACA;AAAA;AAAA,4BAAK9G,GAAL,CAASC,KAAT,CAAe2G,YAAf;AACH;;AAhpBgC,O", "sourcesContent": ["import { oops } from '../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { ecs } from '../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';\nimport { LanguageData } from '../../../../extensions/oops-plugin-framework/assets/libs/gui/language/LanguageData';\nimport { VM } from '../../../../extensions/oops-plugin-framework/assets/libs/model-view/ViewModel';\nimport { GameConst } from '../../tsrpc/models/GameConst';\nimport {\n    PropType,\n    RecordType,\n    RecordTypeData,\n    SceneItemType,\n    UserGameData,\n} from '../../tsrpc/protocols/base';\nimport { GameStorageConfig } from '../common/config/GameStorageConfig';\nimport { DataManager } from '../common/DataManager';\nimport { GameEvent } from '../common/Enum';\nimport { smc } from '../common/SingletonModuleComp';\nimport { RoleModelComp } from './model/RoleModelComp';\n\n/**\n * 角色管理器 - 重构版本\n * 职责：\n * 1. 用户数据的加载、更新和管理\n * 2. 道具系统的操作和验证\n * 3. 游戏进度的管理\n * 4. ViewModel的数据绑定\n */\**************('Role')\nexport class Role extends ecs.Entity {\n    RoleModel!: RoleModelComp;\n\n    // 🔄 待同步的道具更新队列\n    private pendingPropUpdates: Array<{\n        propType: PropType;\n        amount: number;\n        reason?: string;\n    }> = [];\n    private dataManager!: DataManager;\n\n    // ==================== 初始化 ====================\n\n    protected init() {\n        this.add(RoleModelComp);\n        this.dataManager = DataManager.getInstance();\n    }\n\n    destroy(): void {\n        this.removeFromViewModel();\n        this.remove(RoleModelComp);\n    }\n\n    // ==================== 数据加载 ====================\n\n    /**\n     * 加载用户数据\n     * @returns 是否加载成功\n     */\n    async loadData(): Promise<boolean> {\n        oops.log.logBusiness('🔄 开始加载用户数据...');\n\n        if (!this.validateNetworkConnection()) {\n            return false;\n        }\n\n        try {\n            const response = await smc.net.hcGame.callApi('UserInfo', {});\n\n            if (response.isSucc) {\n                await this.waitForUserDataInitialization();\n                return true;\n            } else {\n                this.handleAPIError('UserInfo', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 用户数据加载异常:', error);\n            return false;\n        }\n    }\n\n    /**\n     * 更新用户数据（由DataManager调用）\n     */\n    updateUserData(newData: UserGameData): void {\n        const isFirstInitialization = !this.hasUserData();\n\n        // 确保数据结构完整\n        this.ensureDataIntegrity(newData);\n\n        // 合并数据\n        if (this.RoleModel.userGameData) {\n            this.mergeUserData(this.RoleModel.userGameData, newData);\n        } else {\n            this.RoleModel.userGameData = newData;\n        }\n\n        // 更新ViewModel\n        this.updateViewModel();\n\n        // 首次初始化触发事件\n        if (isFirstInitialization) {\n            oops.log.logBusiness('🎉 用户数据首次初始化完成');\n            oops.message.dispatchEvent(GameEvent.UserDataInitialized, this.RoleModel.userGameData);\n        }\n    }\n\n    // ==================== 道具系统 ====================\n\n    /**\n     * 获取道具数据\n     */\n    getPropData(propType: PropType): any {\n        if (!this.validateUserData()) {\n            return this.createDefaultPropData(propType);\n        }\n\n        const propData = this.RoleModel.userGameData.propUseData?.[propType];\n        if (!propData) {\n            // 创建默认道具数据\n            const defaultData = this.createDefaultPropData(propType);\n            this.RoleModel.userGameData.propUseData = this.RoleModel.userGameData.propUseData || {};\n            this.RoleModel.userGameData.propUseData[propType] = defaultData;\n            return defaultData;\n        }\n\n        return propData;\n    }\n\n    /**\n     * 获取道具数据（别名方法，兼容现有代码）\n     */\n    getPropsDataByType(propType: PropType): any {\n        return this.getPropData(propType);\n    }\n\n    /**\n     * 尝试消耗道具（检查数量并显示提示）\n     * @param args 道具参数（包含类型和数量）\n     * @param showToast 是否显示提示\n     * @param failureMessageKey 失败时的消息键\n     * @returns 是否可以消耗\n     */\n    tryCostProp(\n        args: { propType: PropType; amount: number },\n        showToast: boolean = true,\n        failureMessageKey?: string\n    ): boolean {\n        if (!this.validateUserData()) {\n            if (showToast) {\n                oops.gui.toast('用户数据未初始化');\n            }\n            return false;\n        }\n\n        const absAmount = Math.abs(args.amount);\n        const canUse = this.canUseProp(args.propType, absAmount);\n\n        if (!canUse && showToast && failureMessageKey) {\n            oops.gui.toast(LanguageData.getLangByID(failureMessageKey) || failureMessageKey);\n        }\n\n        return canUse;\n    }\n\n    /**\n     * 更新道具数据（别名方法，兼容现有代码）\n     */\n    async updatePropData(args: {\n        propType: PropType;\n        amount: number;\n        reason?: string;\n    }): Promise<boolean> {\n        return await this.updateProp(args.propType, args.amount, args.reason);\n    }\n\n    /**\n     * 检查道具是否足够\n     */\n    canUseProp(propType: PropType, amount: number): boolean {\n        const propData = this.getPropData(propType);\n        return propData.amount >= Math.abs(amount);\n    }\n\n    /**\n     * 更新道具数量\n     */\n    async updateProp(propType: PropType, amount: number, reason?: string): Promise<boolean> {\n        // 消耗操作前检查数量\n        if (amount < 0 && !this.canUseProp(propType, Math.abs(amount))) {\n            oops.gui.toast(LanguageData.getLangByID('UseLimitsDaily'));\n            return false;\n        }\n\n        // 🔍 检查是否为临时数据状态\n        const userData = this.RoleModel?.userGameData;\n        if ((userData as any)?.isTemporaryData) {\n            oops.log.logBusiness('🔄 检测到临时数据状态，本地更新道具数量');\n\n            // 在临时状态下，直接更新本地数据\n            const propData = this.getPropsDataByType(propType);\n            if (propData) {\n                propData.amount += amount;\n                propData.lastUpdateTime = new Date();\n\n                oops.log.logBusiness(\n                    `✅ 道具本地更新: ${propType} ${amount > 0 ? '+' : ''}${amount} (临时)`\n                );\n\n                // 触发道具使用事件\n                if (amount < 0) {\n                    oops.message.dispatchEvent(GameEvent.UseProp, { propType, amount });\n                }\n\n                // 等后台登录完成后同步到服务器\n                this.queuePropUpdateForSync(propType, amount, reason);\n                return true;\n            }\n        }\n\n        try {\n            const response = await smc.net.hcGame.callApi('UpdateProp', {\n                propType,\n                amount,\n                reason: reason || 'player_action',\n            });\n\n            if (response.isSucc) {\n                oops.log.logBusiness(\n                    `✅ 道具更新成功: ${propType} ${amount > 0 ? '+' : ''}${amount}`\n                );\n\n                // 触发道具使用事件\n                if (amount < 0) {\n                    oops.message.dispatchEvent(GameEvent.UseProp, { propType, amount });\n                }\n\n                return true;\n            } else {\n                this.handleAPIError('UpdateProp', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 道具更新异常:', error);\n            oops.gui.toast('网络错误，请重试');\n            return false;\n        }\n    }\n\n    /**\n     * 批量更新道具\n     */\n    async batchUpdateProps(\n        updates: Array<{ propType: PropType; amount: number; reason?: string }>\n    ): Promise<boolean> {\n        for (const update of updates) {\n            const success = await this.updateProp(update.propType, update.amount, update.reason);\n            if (!success) {\n                return false;\n            }\n        }\n        return true;\n    }\n\n    // ==================== 游戏进度 ====================\n\n    /**\n     * 获取当前通关进度\n     */\n    getGameProgress(): number {\n        if (!this.validateUserData()) {\n            return 0;\n        }\n        return this.RoleModel.userGameData.index || 0;\n    }\n\n    /**\n     * 获取已通过的关卡索引（别名方法，兼容现有代码）\n     */\n    getPassIndex(): number {\n        return this.getGameProgress();\n    }\n\n    /**\n     * 获取下一关卡索引（循环通关）\n     */\n    getNextLevelIndex(currentProgress?: number): number {\n        const progress = currentProgress ?? this.getGameProgress();\n        const nextLevel = (progress + 1) % SceneItemType.Max;\n        return nextLevel === 0 ? 1 : nextLevel;\n    }\n\n    /**\n     * 更新游戏通关进度\n     */\n    async updateGameProgress(newIndex?: number, isGM: boolean = false): Promise<boolean> {\n        const targetIndex = newIndex ?? this.getGameProgress() + 1;\n\n        try {\n            const response = await smc.net.hcGame.callApi('UpdateProgress', {\n                index: targetIndex,\n                isGm: isGM,\n            });\n\n            if (response.isSucc) {\n                oops.log.logBusiness(`✅ 游戏进度更新成功: ${targetIndex}`);\n\n                // 触发通关事件\n                oops.message.dispatchEvent(GameEvent.GamePass, {\n                    oldIndex: this.getGameProgress(),\n                    newIndex: targetIndex,\n                    isGm: isGM,\n                });\n\n                return true;\n            } else {\n                this.handleAPIError('UpdateProgress', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 游戏进度更新异常:', error);\n            oops.gui.toast('网络错误，请重试');\n            return false;\n        }\n    }\n\n    // ==================== 新手引导 ====================\n\n    /**\n     * 检查是否为新玩家\n     */\n    isNewPlayer(): boolean {\n        // 优先使用服务端数据\n        if (this.hasUserData()) {\n            return this.RoleModel.userGameData.isNewPlayer ?? true;\n        }\n\n        const localRecord = oops.storage.getJson(GameStorageConfig.UserDumpKey, 0);\n        return !localRecord || String(localRecord) === '0';\n    }\n\n    /**\n     * 完成新手引导\n     */\n    async completeNewPlayerGuide(): Promise<boolean> {\n        if (!this.isNewPlayer()) {\n            oops.log.logBusiness('✅ 用户已完成新手引导');\n            return true;\n        }\n\n        // 🔍 检查是否为临时数据状态\n        const userData = this.RoleModel?.userGameData;\n        if ((userData as any)?.isTemporaryData) {\n            oops.log.logBusiness('🔄 检测到临时数据状态，延迟新手引导完成');\n            // 先更新本地状态，等后台登录完成后再同步到服务器\n            if (this.RoleModel && this.RoleModel.userGameData) {\n                this.RoleModel.userGameData.isNewPlayer = false;\n                oops.log.logBusiness('🔄 本地新手状态已更新为false（临时）');\n            }\n\n            // 监听后台登录完成事件，然后同步状态\n            oops.message.once(\n                'UserDataLoaded',\n                async () => {\n                    await this.syncNewPlayerStatusToServer();\n                },\n                this\n            );\n\n            return true;\n        }\n\n        return await this.syncNewPlayerStatusToServer();\n    }\n\n    /**\n     * 🔄 同步新手状态到服务器\n     */\n    private async syncNewPlayerStatusToServer(): Promise<boolean> {\n        try {\n            const response = await smc.net.hcGame.callApi('GameUpdateSimpleData', {\n                isNewPlayer: false,\n            });\n\n            if (response.isSucc) {\n                // 🎯 立即更新本地数据，确保状态同步\n                if (this.RoleModel && this.RoleModel.userGameData) {\n                    this.RoleModel.userGameData.isNewPlayer = false;\n                    oops.log.logBusiness('🔄 本地新手状态已更新为false');\n                }\n\n                oops.log.logBusiness('✅ 新手引导完成');\n                oops.message.dispatchEvent(GameEvent.BasicInfoUpdate, { isNewPlayer: false });\n                return true;\n            } else {\n                this.handleAPIError('GameUpdateSimpleData', response.err);\n                return false;\n            }\n        } catch (error) {\n            oops.log.logError('❌ 新手状态更新异常:', error);\n            oops.gui.toast('网络错误，请重试');\n            return false;\n        }\n    }\n\n    /**\n     * 🔄 将道具更新加入同步队列\n     */\n    private queuePropUpdateForSync(propType: PropType, amount: number, reason?: string): void {\n        this.pendingPropUpdates.push({ propType, amount, reason });\n        oops.log.logBusiness(`📝 道具更新已加入同步队列: ${propType} ${amount}`);\n\n        // 监听后台登录完成事件\n        oops.message.once(\n            'UserDataLoaded',\n            async () => {\n                await this.syncPendingPropUpdates();\n            },\n            this\n        );\n    }\n\n    /**\n     * 🔄 同步待处理的道具更新到服务器\n     */\n    private async syncPendingPropUpdates(): Promise<void> {\n        if (this.pendingPropUpdates.length === 0) {\n            return;\n        }\n\n        oops.log.logBusiness(`🔄 开始同步 ${this.pendingPropUpdates.length} 个道具更新`);\n\n        // 复制队列并清空原队列\n        const updates = [...this.pendingPropUpdates];\n        this.pendingPropUpdates = [];\n\n        for (const update of updates) {\n            try {\n                const response = await smc.net.hcGame.callApi('UpdateProp', {\n                    propType: update.propType,\n                    amount: update.amount,\n                    reason: update.reason || 'delayed_sync',\n                });\n\n                if (response.isSucc) {\n                    oops.log.logBusiness(\n                        `✅ 道具同步成功: ${update.propType} ${update.amount > 0 ? '+' : ''}${update.amount}`\n                    );\n                } else {\n                    oops.log.logWarn(`⚠️ 道具同步失败: ${update.propType}`, response.err);\n                }\n            } catch (error) {\n                oops.log.logError(`❌ 道具同步异常: ${update.propType}`, error);\n            }\n        }\n\n        oops.log.logBusiness('✅ 道具更新同步完成');\n    }\n\n    // ==================== 记录数据 ====================\n\n    /**\n     * 获取指定日期的记录数据\n     */\n    getRecordData(recordType: RecordType, dateString?: string): RecordTypeData | null {\n        if (!this.validateUserData()) {\n            return null;\n        }\n\n        const targetDate = dateString || new Date().toDateString();\n        const recordData = this.RoleModel.userGameData.recordData?.[targetDate];\n\n        return recordData?.[recordType] || null;\n    }\n\n    /**\n     * 🔧 新增：获取指定关卡的今日挑战次数\n     * @param levelId 关卡ID\n     * @param dateString 可选的日期字符串，默认为今日\n     * @returns 该关卡的挑战次数\n     */\n    getLevelChallengeCount(levelId: number, dateString?: string): number {\n        const recordData = this.getRecordData(RecordType.Level, dateString);\n        if (!recordData?.levelDetails) {\n            return 0;\n        }\n\n        const levelKey = `level_${levelId}`;\n        return recordData.levelDetails[levelKey]?.attempts || 0;\n    }\n\n    /**\n     * 🔧 新增：获取当前关卡的今日挑战次数\n     * @param dateString 可选的日期字符串，默认为今日\n     * @returns 当前关卡的挑战次数\n     */\n    getCurrentLevelChallengeCount(dateString?: string): number {\n        const currentLevelId = this.getNextLevelIndex(); // 获取当前要挑战的关卡\n        return this.getLevelChallengeCount(currentLevelId, dateString);\n    }\n\n    // ==================== 工具方法 ====================\n\n    /**\n     * 获取完整的用户游戏数据\n     */\n    getUserGameData(): UserGameData {\n        if (!this.validateUserData()) {\n            throw new Error('用户数据尚未初始化，请等待登录完成');\n        }\n        return this.RoleModel.userGameData;\n    }\n\n    // ==================== 私有方法 ====================\n\n    /**\n     * 验证用户数据是否可用\n     */\n    private validateUserData(): boolean {\n        return !!this.RoleModel?.userGameData;\n    }\n\n    /**\n     * 检查是否有用户数据\n     */\n    private hasUserData(): boolean {\n        return this.validateUserData();\n    }\n\n    /**\n     * 验证网络连接\n     */\n    private validateNetworkConnection(): boolean {\n        if (!smc.net?.hcGame) {\n            oops.log.logError('❌ 网络连接未初始化');\n            return false;\n        }\n        return true;\n    }\n\n    /**\n     * 等待用户数据初始化完成\n     */\n    private async waitForUserDataInitialization(): Promise<void> {\n        const maxRetries = 30; // 🚀 减少最大重试次数\n        let retryCount = 0;\n\n        while (retryCount < maxRetries) {\n            if (this.hasUserData()) {\n                return;\n            }\n\n            // 🚀 使用更短的等待时间，但增加检查频率\n            await new Promise(resolve => setTimeout(resolve, 30));\n            retryCount++;\n\n            if (retryCount % 5 === 0) {\n                oops.log.logBusiness(`⏳ 等待用户数据初始化... ${retryCount}/${maxRetries}`);\n            }\n        }\n\n        // 🚀 超时后不抛出错误，而是记录警告并继续\n        oops.log.logWarn('⚠️ 用户数据初始化超时，但继续执行');\n    }\n\n    /**\n     * 确保数据结构完整性\n     */\n    private ensureDataIntegrity(data: UserGameData): void {\n        data.key = data.key || 0;\n        data.userName = data.userName || 'Player';\n        data.index = data.index || 0;\n        data.isNewPlayer = data.isNewPlayer ?? true;\n        data.propUseData = data.propUseData || {};\n        data.recordData = data.recordData || {};\n    }\n\n    /**\n     * 深度合并用户数据\n     */\n    private mergeUserData(target: UserGameData, source: UserGameData): void {\n        Object.keys(source).forEach(key => {\n            const sourceValue = source[key as keyof UserGameData];\n            const targetValue = target[key as keyof UserGameData];\n\n            if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {\n                if (targetValue && typeof targetValue === 'object') {\n                    this.mergeUserData(targetValue as any, sourceValue as any);\n                } else {\n                    (target as any)[key] = sourceValue;\n                }\n            } else {\n                (target as any)[key] = sourceValue;\n            }\n        });\n    }\n\n    /**\n     * 创建默认道具数据 - 优化版本\n     */\n    private createDefaultPropData(propType: PropType): any {\n        // 🎁 为新手玩家提供默认道具数量\n        const defaultAmount = this.getNewPlayerDefaultAmount(propType);\n\n        return {\n            amount: defaultAmount,\n            propType,\n            propId: propType,\n            desc: `道具${propType}`,\n            getTime: new Date(),\n            lastResetTime: new Date(),\n            lastUpdateTime: new Date(),\n        };\n    }\n\n    /**\n     * 🎁 获取新手玩家的默认道具数量\n     */\n    private getNewPlayerDefaultAmount(propType: PropType): number {\n        // 🚀 检查是否启用新手快速启动\n        if (!GameConst.newPlayerFastStart || !this.isNewPlayer()) {\n            return 0;\n        }\n\n        // 🎯 新手玩家默认道具配置（使用配置常量）\n        switch (propType) {\n            case PropType.PropsMoveOut: // 移出道具\n                return GameConst.newPlayerDefaultProps.moveOut;\n            case PropType.PropsTips: // 提示道具\n                return GameConst.newPlayerDefaultProps.tips;\n            case PropType.PropsReShuffle: // 重新洗牌道具\n                return GameConst.newPlayerDefaultProps.reShuffle;\n            case PropType.PropsDayLeftCount: // 每日挑战剩余次数\n                return GameConst.dayFreeLimts; // 使用配置的默认值\n            case PropType.PropsRevive: // 复活道具\n                return GameConst.newPlayerDefaultProps.revive;\n            case PropType.PropsExp: // 游戏经验\n                return 0;\n            case PropType.PropsCoin: // 玩家金币\n                return 0;\n            default:\n                return 0;\n        }\n    }\n\n    /**\n     * 更新ViewModel\n     */\n    private updateViewModel(): void {\n        if (!this.hasUserData()) {\n            return;\n        }\n\n        this.removeFromViewModel();\n\n        const viewModelData = {\n            userId: this.RoleModel.userGameData.key,\n            userName: this.RoleModel.userGameData.userName,\n            level: this.getGameProgress(),\n            isNewPlayer: this.RoleModel.userGameData.isNewPlayer,\n            index: this.RoleModel.userGameData.index,\n            propUseData: this.RoleModel.userGameData.propUseData,\n            // 扩展其他需要的数据\n            ...this.RoleModel.userGameData,\n        };\n\n        VM.add(viewModelData, 'role');\n        oops.log.logBusiness('🎯 ViewModel已更新');\n    }\n\n    /**\n     * 从ViewModel移除数据\n     */\n    private removeFromViewModel(): void {\n        VM.remove('role');\n    }\n\n    /**\n     * 统一的API错误处理\n     */\n    private handleAPIError(apiName: string, error: any): void {\n        oops.log.logError(`❌ ${apiName} API失败:`, error);\n\n        const errorMessage = error?.message || error?.code?.toString() || '操作失败';\n        oops.gui.toast(errorMessage);\n    }\n}\n"]}