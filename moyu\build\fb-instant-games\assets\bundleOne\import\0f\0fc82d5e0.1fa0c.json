[1, ["e5LUoqx3RAr41dA5QrbKMj", "3bowoQe9tIJoEBANS4q/KC@5ffdf"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["<PERSON>.<PERSON><PERSON><PERSON>", ["_radius", "_height", "_direction", "node", "__prefab", "_center"], 0, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 5, 4], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 919312546, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 36096, "length": 6696, "count": 3348, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 36096, "count": 752, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.7065050005912781, -0.7065050005912781, -0.08960330486297607], "maxPosition", 8, [1, 0.7065050005912781, 0.7065050005912781, 0.08957860618829727]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_02"], [3, "日式寿司_02", [[4, 1, -2, [0, "d0P+q4tz5EErAXBN9jm3dJ"], [0], [5, true, true], 1], [6, 4, -3, [0, "adR7Zf++dIwJ5tPAl6ZkmR"]], [7, 0.707, 0.165, 2, -4, [0, "71eNdb/WlExbOIfyoAjZXz"], [1, 0, 0, -1.2349337339401245e-05]]], [8, "22Pps3ZPFM84Tt7X15LoI1", null, null, null, -1, 0], [1, 0, 0, 0.873]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]