[{"__type__": "cc.Prefab", "_name": "GameResult", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "GameResult", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 19}, {"__id__": 29}, {"__id__": 201}], "_active": true, "_components": [{"__id__": 281}, {"__id__": 283}, {"__id__": 285}, {"__id__": 287}, {"__id__": 289}], "_prefab": {"__id__": 291}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_prefab": {"__id__": 3}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 2}, "asset": {"__uuid__": "142393a8-2641-4775-a79c-cbfe1bd9e5ca", "__expectedType__": "cc.Prefab"}, "fileId": "a0daVw8DRLi6ToMaTA0VS2", "instance": {"__id__": 4}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "7bm457irVEvZzS/a0VPd7u", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 5}, {"__id__": 7}, {"__id__": 9}, {"__id__": 11}, {"__id__": 13}, {"__id__": 15}, {"__id__": 16}, {"__id__": 18}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 6}, "propertyPath": ["_name"], "value": "mask"}, {"__type__": "cc.TargetInfo", "localID": ["a0daVw8DRLi6ToMaTA0VS2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 8}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["a0daVw8DRLi6ToMaTA0VS2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 10}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["a0daVw8DRLi6ToMaTA0VS2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 12}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["a0daVw8DRLi6ToMaTA0VS2"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "62f4320d-3b10-4029-aaa1-8b5d7608d15c@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["08QaVy/ehM0oBtHFRmM2vy"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_type"], "value": 0}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 17}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 750, "height": 1334}}, {"__type__": "cc.TargetInfo", "localID": ["77N2cid5pKDpXplRH/AWEU"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 14}, "propertyPath": ["_color"], "value": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}}, {"__type__": "cc.Node", "_name": "lblTitle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 20}, {"__id__": 22}, {"__id__": 24}, {"__id__": 26}], "_prefab": {"__id__": 28}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 249.37, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 21}, "_contentSize": {"__type__": "cc.Size", "width": 800, "height": 104.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cfACh4GtBHD7JE8WFXSKF5"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 23}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 251, "g": 255, "b": 155, "a": 255}, "_string": "victory", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 80, "_fontSize": 80, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 80, "_overflow": 3, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcsVmsrvNHOqJOZPCR/T7e"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 25}, "watchPath": "*.isWin", "foreachChildMode": false, "condition": 0, "foreachChildType": 0, "valueA": 1, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 19}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3avfWRvyJGQ549vtqGjJKA"}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 27}, "_dataID": "victory", "labelType": "cc.Label", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21lJDsDRVIh5B7HL75/EIM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "58i5TWzNtB0I9055sH3A30", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "fail", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 30}, {"__id__": 38}, {"__id__": 186}], "_active": true, "_components": [{"__id__": 194}, {"__id__": 196}, {"__id__": 198}], "_prefab": {"__id__": 200}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 292.476, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "failImg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 31}, {"__id__": 33}, {"__id__": 35}], "_prefab": {"__id__": 37}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 30}, "_enabled": true, "__prefab": {"__id__": 32}, "_contentSize": {"__type__": "cc.Size", "width": 223, "height": 126}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68G297TZJG+IZ7shIImaop"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 30}, "_enabled": true, "__prefab": {"__id__": 34}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "efefc5fe-7398-45e8-8192-983d4eabcffa@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cSJWWMpVBFqdl/7CDhLiN"}, {"__type__": "11b96k/RIZF57Loehxyl6Hs", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 30}, "_enabled": true, "__prefab": {"__id__": 36}, "_dataID": "fail", "isRawSize": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8eY3o0r5JPn65cfsukqpfA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a1HtfkvHNJw5DGvsmQnN8C", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "processBar", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 29}, "_children": [{"__id__": 39}, {"__id__": 53}, {"__id__": 67}, {"__id__": 81}, {"__id__": 95}, {"__id__": 109}, {"__id__": 123}, {"__id__": 137}, {"__id__": 151}, {"__id__": 165}], "_active": true, "_components": [{"__id__": 179}, {"__id__": 181}, {"__id__": 183}], "_prefab": {"__id__": 185}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -266.63, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [{"__id__": 40}], "_active": true, "_components": [{"__id__": 46}, {"__id__": 48}, {"__id__": 50}], "_prefab": {"__id__": 52}, "_lpos": {"__type__": "cc.Vec3", "x": -251.10000000000002, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "亮1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 39}, "_children": [], "_active": true, "_components": [{"__id__": 41}, {"__id__": 43}], "_prefab": {"__id__": 45}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 40}, "_enabled": true, "__prefab": {"__id__": 42}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74miVuj/NO3bUg3PvufgVy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 40}, "_enabled": true, "__prefab": {"__id__": 44}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5d4c5f87-1f8b-4482-ad4c-e9efc6d7de60@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7eTQo7A15Ab7GTgE+UzZOC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "aa3J4vbO5JYaZSDxCJUPGh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 47}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35CAQzEItL3osK6loXsvB/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 49}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e4467d7d-2d83-4050-834c-251f6fc79cff@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cfiKZcUlMWpse6WQXGq7n"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 39}, "_enabled": true, "__prefab": {"__id__": 51}, "watchPath": "*.percent", "foreachChildMode": false, "condition": 3, "foreachChildType": 0, "valueA": 10, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 40}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbEy8vKiRIFbQYpW+SbeC+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "722GXXwcxLWbJHRHtwygn2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [{"__id__": 54}], "_active": true, "_components": [{"__id__": 60}, {"__id__": 62}, {"__id__": 64}], "_prefab": {"__id__": 66}, "_lpos": {"__type__": "cc.Vec3", "x": -195.10000000000002, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spriteFrame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 53}, "_children": [], "_active": true, "_components": [{"__id__": 55}, {"__id__": 57}], "_prefab": {"__id__": 59}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": {"__id__": 56}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "40N3r1Y91Nz5l5hUDFQRRT"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 54}, "_enabled": true, "__prefab": {"__id__": 58}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "45a6be90-6c29-4513-b861-a06a56ce7301@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ch7O1anpEgboV9E/7Gzpz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c11LPXXwxLmYx7Y0J+gT+2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 61}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "08qRlm8TlMy5ImHOxKzthb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 63}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "953d4e8d-b321-4fe3-bcc2-c6e238e82a83@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0KTtoD21EFKBKwjJK3jDi"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 53}, "_enabled": true, "__prefab": {"__id__": 65}, "watchPath": "*.percent", "foreachChildMode": false, "condition": 3, "foreachChildType": 0, "valueA": 20, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 54}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9f1hsSqW5LY61z/txdA9T5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e1JVQtWa9AerUm1l6zNzHo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "3", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [{"__id__": 68}], "_active": true, "_components": [{"__id__": 74}, {"__id__": 76}, {"__id__": 78}], "_prefab": {"__id__": 80}, "_lpos": {"__type__": "cc.Vec3", "x": -139.10000000000002, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spriteFrame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 67}, "_children": [], "_active": true, "_components": [{"__id__": 69}, {"__id__": 71}], "_prefab": {"__id__": 73}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": {"__id__": 70}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbnFSwhI1ARajEzV+Iz+c9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": {"__id__": 72}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "45a6be90-6c29-4513-b861-a06a56ce7301@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9e+W1yl9xD6qlnCqk4KmrD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "01tIte5xFMXqHUdF++ua2n", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 75}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eeit5ICORMzZ7h+OY7rlL/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 77}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "953d4e8d-b321-4fe3-bcc2-c6e238e82a83@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89ZXBaaM5IG5ulGmsmyu2u"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 67}, "_enabled": true, "__prefab": {"__id__": 79}, "watchPath": "*.percent", "foreachChildMode": false, "condition": 3, "foreachChildType": 0, "valueA": 30, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 68}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4et+4Bwa9M6Jyy5VTEluOY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "37I0lkaLlO7oDX8bdBwrsM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "4", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [{"__id__": 82}], "_active": true, "_components": [{"__id__": 88}, {"__id__": 90}, {"__id__": 92}], "_prefab": {"__id__": 94}, "_lpos": {"__type__": "cc.Vec3", "x": -83.10000000000002, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spriteFrame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 81}, "_children": [], "_active": true, "_components": [{"__id__": 83}, {"__id__": 85}], "_prefab": {"__id__": 87}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 84}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5buz185DVIo5ZSJVGxtfTD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 82}, "_enabled": true, "__prefab": {"__id__": 86}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "45a6be90-6c29-4513-b861-a06a56ce7301@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1nihFykdOTaZMlyLnpir/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "482dTzoG9KAL8KpPjgoQi+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": {"__id__": 89}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "10QVbaBkhKgIKwj5y2kW1X"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": {"__id__": 91}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "953d4e8d-b321-4fe3-bcc2-c6e238e82a83@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9eDoLIMFVIYaIqEq5/6+e1"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 81}, "_enabled": true, "__prefab": {"__id__": 93}, "watchPath": "*.percent", "foreachChildMode": false, "condition": 3, "foreachChildType": 0, "valueA": 40, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 82}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acmvOfa8hEC6cKXoEvtEI5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8644dGK65AGbzdQoDLnmCN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "5", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [{"__id__": 96}], "_active": true, "_components": [{"__id__": 102}, {"__id__": 104}, {"__id__": 106}], "_prefab": {"__id__": 108}, "_lpos": {"__type__": "cc.Vec3", "x": -27.100000000000023, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spriteFrame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 95}, "_children": [], "_active": true, "_components": [{"__id__": 97}, {"__id__": 99}], "_prefab": {"__id__": 101}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": {"__id__": 98}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4aqjMblUlJV5mP/I7x2FN+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 96}, "_enabled": true, "__prefab": {"__id__": 100}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "45a6be90-6c29-4513-b861-a06a56ce7301@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3qKd2EklPHJcng3rvcQjz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "40VNefjmZLZoYy23BOcjMK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 103}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c1x3iQtbpJtYst7PkiZ4qc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 105}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "953d4e8d-b321-4fe3-bcc2-c6e238e82a83@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31gLMvF9ZPsJCsvi63Ensp"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 107}, "watchPath": "*.percent", "foreachChildMode": false, "condition": 3, "foreachChildType": 0, "valueA": 50, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 96}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "687mWorHFFapvuGLGy+jjY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "df9/ZP9QdNFIn6G/r+3OJx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "6", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [{"__id__": 110}], "_active": true, "_components": [{"__id__": 116}, {"__id__": 118}, {"__id__": 120}], "_prefab": {"__id__": 122}, "_lpos": {"__type__": "cc.Vec3", "x": 28.899999999999977, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spriteFrame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 109}, "_children": [], "_active": true, "_components": [{"__id__": 111}, {"__id__": 113}], "_prefab": {"__id__": 115}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 112}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8dIfUD5LZILoO0GDVKz8jv"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": {"__id__": 114}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "45a6be90-6c29-4513-b861-a06a56ce7301@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92OXUIN35PoZ8b8VluBSq5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "c5TU5Y+eJIb7EqesPAt/xq", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 117}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bswPSX4NEGY9O+77zFV2p"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 119}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "953d4e8d-b321-4fe3-bcc2-c6e238e82a83@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4dxeqc+1KiIDGL0sgLPI6"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 109}, "_enabled": true, "__prefab": {"__id__": 121}, "watchPath": "*.percent", "foreachChildMode": false, "condition": 3, "foreachChildType": 0, "valueA": 60, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 110}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20uIYBlU1Fop5ynZDDKoAK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e2019sDbRFq7I/UqE0/bBb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "7", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [{"__id__": 124}], "_active": true, "_components": [{"__id__": 130}, {"__id__": 132}, {"__id__": 134}], "_prefab": {"__id__": 136}, "_lpos": {"__type__": "cc.Vec3", "x": 84.89999999999998, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spriteFrame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 123}, "_children": [], "_active": true, "_components": [{"__id__": 125}, {"__id__": 127}], "_prefab": {"__id__": 129}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 126}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44SNY2nVBHrZclt0ZtbBbV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 124}, "_enabled": true, "__prefab": {"__id__": 128}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "45a6be90-6c29-4513-b861-a06a56ce7301@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dck8wDJG5MQqm60fFuaoV3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "56vRCtdQlIhb3A4Qu0I/e1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 131}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96qPa31aZDq5HuN3Tzw9IN"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 133}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "953d4e8d-b321-4fe3-bcc2-c6e238e82a83@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92z0wUehVGbLSluc7EEB62"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": {"__id__": 135}, "watchPath": "*.percent", "foreachChildMode": false, "condition": 3, "foreachChildType": 0, "valueA": 70, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 124}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a1lhAvYslKMowYE/D3azyc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "24VrkeFrtFlbVJjDGIsheu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "8", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [{"__id__": 138}], "_active": true, "_components": [{"__id__": 144}, {"__id__": 146}, {"__id__": 148}], "_prefab": {"__id__": 150}, "_lpos": {"__type__": "cc.Vec3", "x": 140.89999999999998, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spriteFrame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 137}, "_children": [], "_active": true, "_components": [{"__id__": 139}, {"__id__": 141}], "_prefab": {"__id__": 143}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 138}, "_enabled": true, "__prefab": {"__id__": 140}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56EpCecx1Mpr9e9WQlNbSb"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 138}, "_enabled": true, "__prefab": {"__id__": 142}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "45a6be90-6c29-4513-b861-a06a56ce7301@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20eOuGDP1JeK4A+EzsSFQM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7d4lw3HK1P0rwHhNqC10if", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 145}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81asKm705BCYQOlBGMw2sf"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 147}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "953d4e8d-b321-4fe3-bcc2-c6e238e82a83@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "283VD+V29O0LkUKQfxkWLa"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 137}, "_enabled": true, "__prefab": {"__id__": 149}, "watchPath": "*.percent", "foreachChildMode": false, "condition": 3, "foreachChildType": 0, "valueA": 80, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 138}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bK+P5B3tIwZ49hGCL0UCf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "38jy/mZptEOog12aStZzvf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "9", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [{"__id__": 152}], "_active": true, "_components": [{"__id__": 158}, {"__id__": 160}, {"__id__": 162}], "_prefab": {"__id__": 164}, "_lpos": {"__type__": "cc.Vec3", "x": 196.89999999999998, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "spriteFrame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 151}, "_children": [], "_active": true, "_components": [{"__id__": 153}, {"__id__": 155}], "_prefab": {"__id__": 157}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 152}, "_enabled": true, "__prefab": {"__id__": 154}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16wAhDwuFJ97Y4H638df6/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 152}, "_enabled": true, "__prefab": {"__id__": 156}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "45a6be90-6c29-4513-b861-a06a56ce7301@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bWO22Sl5Oq7BU36w40eJx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a42waZW/lE65RJ9nwveXBM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": {"__id__": 159}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2083r3qftNfZyj0OVVmeCB"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": {"__id__": 161}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "953d4e8d-b321-4fe3-bcc2-c6e238e82a83@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1iMB2U0VDDaI13Wo4TtVz"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 151}, "_enabled": true, "__prefab": {"__id__": 163}, "watchPath": "*.percent", "foreachChildMode": false, "condition": 3, "foreachChildType": 0, "valueA": 90, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 152}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65VZPNriZCHIt0KNymH8nA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76NxZlohxAaZ6EPzevsWhB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "10", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 38}, "_children": [{"__id__": 166}], "_active": true, "_components": [{"__id__": 172}, {"__id__": 174}, {"__id__": 176}], "_prefab": {"__id__": 178}, "_lpos": {"__type__": "cc.Vec3", "x": 252.89999999999998, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 1, "w": 6.123233995736766e-17}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_id": ""}, {"__type__": "cc.Node", "_name": "亮1", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 165}, "_children": [], "_active": true, "_components": [{"__id__": 167}, {"__id__": 169}], "_prefab": {"__id__": 171}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 168}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "daHyVJEGVPqpmg8FApDU5x"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 170}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "5d4c5f87-1f8b-4482-ad4c-e9efc6d7de60@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54wbmLXz5AHq6Sz/jMUgpK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "23leJqDfNOYY+9UNyXFyUz", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 165}, "_enabled": true, "__prefab": {"__id__": 173}, "_contentSize": {"__type__": "cc.Size", "width": 53, "height": 39}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "81J0d9yjBOYoa48uNh/LyV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 165}, "_enabled": true, "__prefab": {"__id__": 175}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e4467d7d-2d83-4050-834c-251f6fc79cff@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9ad+O9w4VEFo9lCOWqASpS"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 165}, "_enabled": true, "__prefab": {"__id__": 177}, "watchPath": "*.percent", "foreachChildMode": false, "condition": 3, "foreachChildType": 0, "valueA": 100, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 166}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6v8J0PhBD5rxTTRbSGrKe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1dt7c1n6BL7aDU0PLxdh0e", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 180}, "_contentSize": {"__type__": "cc.Size", "width": 571, "height": 53}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80KlYO4NJL25pE3OLy+M3+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 182}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e1e2bb69-f5ba-4f32-a3eb-0d8891ae812c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2DDzUGuxDzqbr2i09fWOs"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 38}, "_enabled": true, "__prefab": {"__id__": 184}, "_resizeMode": 0, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 7.9, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 3, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7hYv3k39NX5+4Tr+XSZdA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1d3F1DXpZEdKvmNxSRRzp1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "process", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 187}, {"__id__": 189}, {"__id__": 191}], "_prefab": {"__id__": 193}, "_lpos": {"__type__": "cc.Vec3", "x": -243.335, "y": -220.438, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 186}, "_enabled": true, "__prefab": {"__id__": 188}, "_contentSize": {"__type__": "cc.Size", "width": 106.716796875, "height": 54.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4e+DyBQTRE0adjSjaJIt0/"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 186}, "_enabled": true, "__prefab": {"__id__": 190}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Percent", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 28, "_fontSize": 28, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 40, "_overflow": 0, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": true, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": true, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b8Dckhm/5LHKhiE2TPdxAv"}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 186}, "_enabled": true, "__prefab": {"__id__": 192}, "_dataID": "Percent", "labelType": "cc.Label", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "40aijakX1KsanOaUMaj9dD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dI3uCQmFAvrYlDipeucpo", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 195}, "_contentSize": {"__type__": "cc.Size", "width": 367, "height": 169}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93yYYS8PdMLZvOwWgdnGUp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 197}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "9b98deeb-3f8c-416d-bc03-27b9dfeeff1d@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c7p3XNrh5JUpuk+Ky/uQfH"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 29}, "_enabled": true, "__prefab": {"__id__": 199}, "watchPath": "*.isWin", "foreachChildMode": false, "condition": 0, "foreachChildType": 0, "valueA": 0, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 29}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcLG0fS/1J5a5zu8CurB/Y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ff0RkTiLJJ3JaZ/XF4emZY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bottom", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 202}, {"__id__": 218}, {"__id__": 268}], "_active": true, "_components": [{"__id__": 276}, {"__id__": 278}], "_prefab": {"__id__": 280}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -248.396, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 201}, "_prefab": {"__id__": 203}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 202}, "asset": {"__uuid__": "d2979f10-3e11-45f0-b51c-b8f4199252b2", "__expectedType__": "cc.Prefab"}, "fileId": "95gnH/w+JEGqHSJXjfiQG6", "instance": {"__id__": 204}, "targetOverrides": null}, {"__type__": "cc.PrefabInstance", "fileId": "b23Z9Eh1JBp50GGKLc18W3", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 205}, {"__id__": 207}, {"__id__": 208}, {"__id__": 209}, {"__id__": 210}, {"__id__": 212}, {"__id__": 214}, {"__id__": 216}, {"__id__": 217}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 206}, "propertyPath": ["_name"], "value": "btnNext"}, {"__type__": "cc.TargetInfo", "localID": ["95gnH/w+JEGqHSJXjfiQG6"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 206}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -99.51999999999998, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 206}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 206}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 211}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 3.61, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["29p8nV+15G9bL0lI1ojBwh"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 213}, "propertyPath": ["_bottom"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["79ESqtjuNEJYoFvIo1IgHq"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 215}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 263, "height": 113}}, {"__type__": "cc.TargetInfo", "localID": ["e7CpmUbH9E16rvDUCELR7L"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 213}, "propertyPath": ["_left"], "value": 118.5}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 213}, "propertyPath": ["_right"], "value": 118.5}, {"__type__": "cc.Node", "_name": "replayNode", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 201}, "_children": [{"__id__": 219}, {"__id__": 227}], "_active": true, "_components": [{"__id__": 263}, {"__id__": 265}], "_prefab": {"__id__": 267}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 38.860000000000014, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "lblTimes", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 218}, "_children": [], "_active": true, "_components": [{"__id__": 220}, {"__id__": 222}, {"__id__": 224}], "_prefab": {"__id__": 226}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 56.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 221}, "_contentSize": {"__type__": "cc.Size", "width": 84.09375, "height": 32.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fan6C+qU5EqZEfv42KiIs/"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 223}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "DayLimt", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 24, "_fontSize": 24, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 26, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "78e905b5-2d1a-4d39-ab1f-117fe8d0dbdb", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a26IRS2qVE2aBTIRe2cpSK"}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 219}, "_enabled": true, "__prefab": {"__id__": 225}, "_dataID": "DayLimt", "labelType": "cc.Label", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f7FCgYXM1KwLq2B6fGNuJD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4ellUFhzpABLE8KdUxMiJJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 218}, "_prefab": {"__id__": 228}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 227}, "asset": {"__uuid__": "853d89a8-d0b9-4e98-92ab-6d2315662b1d", "__expectedType__": "cc.Prefab"}, "fileId": "87kTBNQWJI3pftCW6wRRdv", "instance": {"__id__": 229}}, {"__type__": "cc.PrefabInstance", "fileId": "a0YXqc9cdIW40qgYP8y2lO", "prefabRootNode": {"__id__": 1}, "mountedChildren": [], "mountedComponents": [{"__id__": 230}], "propertyOverrides": [{"__id__": 232}, {"__id__": 234}, {"__id__": 236}, {"__id__": 238}, {"__id__": 240}, {"__id__": 242}, {"__id__": 244}, {"__id__": 246}, {"__id__": 248}, {"__id__": 250}, {"__id__": 252}, {"__id__": 254}, {"__id__": 256}, {"__id__": 258}, {"__id__": 260}], "removedComponents": [{"__id__": 262}]}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 231}, "components": [null]}, {"__type__": "cc.TargetInfo", "localID": ["fdpk1fsslHobj/mM3LbXuR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 233}, "propertyPath": ["_name"], "value": "btnReplay"}, {"__type__": "cc.TargetInfo", "localID": ["87kTBNQWJI3pftCW6wRRdv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 235}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -16.379999999999995, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["87kTBNQWJI3pftCW6wRRdv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 237}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "cc.TargetInfo", "localID": ["87kTBNQWJI3pftCW6wRRdv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 239}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["87kTBNQWJI3pftCW6wRRdv"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 241}, "propertyPath": ["_sizeMode"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["d6DPSk5Y1K8pnS8DeaPIj+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 243}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 263, "height": 113}}, {"__type__": "cc.TargetInfo", "localID": ["cdKt0uXpFGNbiqFioh9jcQ"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 245}, "propertyPath": ["_left"], "value": -81.5}, {"__type__": "cc.TargetInfo", "localID": ["54mK9AptNNwaiUV05x/xQl"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 247}, "propertyPath": ["_right"], "value": -81.5}, {"__type__": "cc.TargetInfo", "localID": ["54mK9AptNNwaiUV05x/xQl"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 249}, "propertyPath": ["_bottom"], "value": 0}, {"__type__": "cc.TargetInfo", "localID": ["54mK9AptNNwaiUV05x/xQl"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 251}, "propertyPath": ["_type"], "value": 1}, {"__type__": "cc.TargetInfo", "localID": ["d6DPSk5Y1K8pnS8DeaPIj+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 253}, "propertyPath": ["_spriteFrame"], "value": {"__uuid__": "478a8e09-6d7e-4d14-ad7a-41076f4e901e@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.TargetInfo", "localID": ["d6DPSk5Y1K8pnS8DeaPIj+"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 255}, "propertyPath": ["_outlineColor"], "value": {"__type__": "cc.Color", "r": 154, "g": 55, "b": 37, "a": 255}}, {"__type__": "cc.TargetInfo", "localID": ["9fJkiblSpOQbUJPdFapuF8", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 257}, "propertyPath": ["_isBold"], "value": false}, {"__type__": "cc.TargetInfo", "localID": ["9fJkiblSpOQbUJPdFapuF8", "4a5atXBglJxJGAlAL90RE0"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 259}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 5.792, "z": 0}}, {"__type__": "cc.TargetInfo", "localID": ["9fJkiblSpOQbUJPdFapuF8", "f05XX5jrpEOYwv6lCoUIav"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 261}, "propertyPath": ["_active"], "value": true}, {"__type__": "cc.TargetInfo", "localID": ["87kTBNQWJI3pftCW6wRRdv"]}, {"__type__": "cc.TargetInfo", "localID": ["3avKd6uvpGtp07bTxHtW80"]}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": true, "__prefab": {"__id__": 264}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 145.76}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2TzIH2Q1GVqsYPW69BB1u"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 218}, "_enabled": true, "__prefab": {"__id__": 266}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "26fXalY/tCupTH7bBQDa+F"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1dMvbXOOlIxIkxmoVLv3Yx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "lblContent", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 201}, "_children": [], "_active": true, "_components": [{"__id__": 269}, {"__id__": 271}, {"__id__": 273}], "_prefab": {"__id__": 275}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 138.38, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 270}, "_contentSize": {"__type__": "cc.Size", "width": 447.70263671875, "height": 35.28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4wyRqeJtP6r5RVkC3efla"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 272}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "ContentToday", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 26, "_fontSize": 26, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 28, "_overflow": 3, "_enableWrapText": true, "_font": null, "_isSystemFontUsed": true, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 0, "_enableOutline": false, "_outlineColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_outlineWidth": 2, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1eMGZiO6pPjpg4uXU2DxOs"}, {"__type__": "4bff6xrIC5LtYScAfbMmEdR", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 268}, "_enabled": true, "__prefab": {"__id__": 274}, "_dataID": "ContentToday", "labelType": "cc.Label", "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d7oUtwpk1OeIVI2ukYMJBK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b1xFAmUBRKg6JIVbg4kWzj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": {"__id__": 277}, "_contentSize": {"__type__": "cc.Size", "width": 500, "height": 312.03999999999996}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "21cmjJwsFNApXOK5JGmnyB"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 201}, "_enabled": true, "__prefab": {"__id__": 279}, "_resizeMode": 1, "_layoutType": 2, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 9, "_verticalDirection": 0, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17nEACSz9LdpdI99yIj5kg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "76SsYQI5pCWqWFy9U2eh+t", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 282}, "_contentSize": {"__type__": "cc.Size", "width": 750, "height": 1334}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "77N2cid5pKDpXplRH/AWEU"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 284}, "_alignFlags": 5, "_target": null, "_left": 539, "_right": 539, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 2, "_originalHeight": 2, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63zNQq8NlBQ5QWzOJ4Kgjs"}, {"__type__": "cc.BlockInputEvents", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 286}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "256Gj76CNDsbk/E3RQMINa"}, {"__type__": "3a3a7O5S75E6bMqPN6ETYhB", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 288}, "process": {"__id__": 186}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6FqXeSmdEdIYg8HKdF0zg"}, {"__type__": "47052uw/Y5O1LXaLObj4ARx", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 1}, "_enabled": true, "__prefab": {"__id__": 290}, "watchPath": "*.isGuideLevel", "foreachChildMode": false, "condition": 0, "foreachChildType": 0, "valueA": 0, "valueB": 0, "valueAction": 0, "valueActionOpacity": 0, "valueActionColor": {"__type__": "cc.Color", "r": 155, "g": 155, "b": 155, "a": 255}, "valueComponentName": "", "valueComponentProperty": "", "valueComponentDefaultValue": "", "valueComponentActionValue": "", "watchNodes": [{"__id__": 227}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "275gbcH+9N7qqwhmKlpdfM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a0daVw8DRLi6ToMaTA0VS2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": [{"__id__": 227}, {"__id__": 202}, {"__id__": 2}]}]