{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/Main.ts"], "names": ["_decorator", "Node", "oops", "Root", "ecs", "Initialize", "Environment", "Platform", "ShareConfig", "ccclass", "property", "Main", "type", "tooltip", "iniStart", "log", "start", "isProduction", "environment", "PRODUCTION_FACEBOOK", "initializeCoreModules", "smc", "initialize", "getEntity", "platform", "FACEBOOK", "FBI<PERSON>antManager", "FacebookGameEvents", "Promise", "all", "fbInstantManager", "facebookGameEvents", "loadBusinessModulesLater", "loadCriticalModules", "requestIdleCallback", "loadNonCriticalModules", "Role", "SimpleSceneManager", "role", "sceneMgr", "logBusiness", "error", "logError", "Account", "Guide", "CameraEntity", "profiler", "showStats", "assetManager", "downloader", "maxRequestsPerFrame", "account", "guide", "camera", "initGui", "UIConfigData", "gui", "init", "initEcsSystem", "run", "initializeFacebook", "load", "initial", "toast", "maxRetries", "initSuccess", "i", "fbInit", "onPlayerFirstEntry", "fbData", "getFacebookLoginData", "facebookId", "Error", "log<PERSON>arn", "retry<PERSON><PERSON><PERSON>", "resolve", "setTimeout", "errorMsg"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AACZC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,G,iBAAAA,G;;AAOAC,MAAAA,U,iBAAAA,U;;AAGAC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;;;;;;AAnBhC;AACA;AACA;AACA;AACA;AACA;;;;;OAgBM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;sBAGjBW,I,WADZF,OAAO,CAAC,MAAD,C,UAEHC,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEX,IADA;AAENY,QAAAA,OAAO,EAAE;AAFH,OAAD,C,2BAFb,MACaF,IADb;AAAA;AAAA,wBAC+B;AAAA;AAAA;;AAAA;AAAA;;AAOjBG,QAAAA,QAAQ,GAAG;AACjB;AACA;AAAA;AAAA,4BAAKC,GAAL,CAASC,KAAT,CAAe,SAAf,EAFiB,CAIjB;AAEA;;AACA,gBAAMC,YAAY,GAAG;AAAA;AAAA,0CAAYA,YAAjC;;AAEA,cAAI;AAAA;AAAA,0CAAYC,WAAZ,KAA4B;AAAA;AAAA,0CAAYC,mBAA5C,EAAiE,CAC7D;AACH,WAFD,MAEO,IAAIF,YAAJ,EAAkB,CACrB;AACH,WAFM,MAEA,CACH;AACH,WAfgB,CAiBjB;;;AACA,eAAKG,qBAAL;AACH;AAED;;;AACmC,cAArBA,qBAAqB,GAAG;AAClC,gBAAM;AAAEC,YAAAA;AAAF,cAAU,uCAAhB,CADkC,CAGlC;;AACAA,UAAAA,GAAG,CAACC,UAAJ,GAAiB;AAAA;AAAA,0BAAIC,SAAJ;AAAA;AAAA,uCAAjB,CAJkC,CAMlC;;AACA,cAAI;AAAA;AAAA,0CAAYC,QAAZ,KAAyB;AAAA;AAAA,oCAASC,QAAtC,EAAgD;AAC5C,kBAAM,CAAC;AAAEC,cAAAA;AAAF,aAAD,EAAuB;AAAEC,cAAAA;AAAF,aAAvB,IAAiD,MAAMC,OAAO,CAACC,GAAR,CAAY,sEAAZ,CAA7D;AAKAR,YAAAA,GAAG,CAACS,gBAAJ,GAAuB;AAAA;AAAA,4BAAIP,SAAJ,CAAgCG,gBAAhC,CAAvB;AACAL,YAAAA,GAAG,CAACU,kBAAJ,GAAyB;AAAA;AAAA,4BAAIR,SAAJ,CAAkCI,kBAAlC,CAAzB;AACH,WAfiC,CAiBlC;;;AACA,eAAKK,wBAAL;AACH;AAED;;;AACsC,cAAxBA,wBAAwB,GAAG;AACrC;AACA,eAAKC,mBAAL,GAFqC,CAIrC;;AACAC,UAAAA,mBAAmB,CAAC,YAAY;AAC5B,kBAAM,KAAKC,sBAAL,EAAN;AACH,WAFkB,CAAnB;AAGH;AAED;;;AACiC,cAAnBF,mBAAmB,GAAG;AAChC,cAAI;AACA,kBAAM,CAAC;AAAEZ,cAAAA;AAAF,aAAD,EAAU;AAAEe,cAAAA;AAAF,aAAV,EAAoB;AAAEC,cAAAA;AAAF,aAApB,IAA8C,MAAMT,OAAO,CAACC,GAAR,CAAY,2GAAZ,CAA1D,CADA,CAOA;;AACAR,YAAAA,GAAG,CAACiB,IAAJ,GAAW;AAAA;AAAA,4BAAIf,SAAJ,CAAoBa,IAApB,CAAX;AACAf,YAAAA,GAAG,CAACkB,QAAJ,GAAe;AAAA;AAAA,4BAAIhB,SAAJ,CAAkCc,kBAAlC,CAAf;AAEA;AAAA;AAAA,8BAAKtB,GAAL,CAASyB,WAAT,CAAqB,YAArB;AACH,WAZD,CAYE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAK1B,GAAL,CAAS2B,QAAT,CAAkB,aAAlB,EAAiCD,KAAjC;AACH;AACJ;AAED;;;AACoC,cAAtBN,sBAAsB,GAAG;AACnC,cAAI;AACA,kBAAM,CAAC;AAAEd,cAAAA;AAAF,aAAD,EAAU;AAAEsB,cAAAA;AAAF,aAAV,EAAuB;AAAEC,cAAAA;AAAF,aAAvB,EAAkC;AAAEC,cAAAA;AAAF,aAAlC,IAAsD,MAAMjB,OAAO,CAACC,GAAR,CAAY,gJAAZ,CAAlE,CADA,CAQA;AACA;;AACA,gBAAI,CAAC;AAAA;AAAA,4CAAYZ,YAAjB,EAA+B;AAC3B,oBAAM;AAAE6B,gBAAAA;AAAF,kBAAe,sBAAa,IAAb,CAArB;AACAA,cAAAA,QAAQ,CAACC,SAAT;AACH,aAbD,CAeA;;;AACA,kBAAM;AAAEC,cAAAA;AAAF,gBAAmB,sBAAa,IAAb,CAAzB;AACAA,YAAAA,YAAY,CAACC,UAAb,CAAwBC,mBAAxB,GAA8C,EAA9C,CAjBA,CAiBkD;AAElD;;AACA7B,YAAAA,GAAG,CAAC8B,OAAJ,GAAc;AAAA;AAAA,4BAAI5B,SAAJ,CAAuBoB,OAAvB,CAAd;AACAtB,YAAAA,GAAG,CAAC+B,KAAJ,GAAY;AAAA;AAAA,4BAAI7B,SAAJ,CAAqBqB,KAArB,CAAZ;AACAvB,YAAAA,GAAG,CAACgC,MAAJ,GAAa;AAAA;AAAA,4BAAI9B,SAAJ,CAA4BsB,YAA5B,CAAb;AAEA;AAAA;AAAA,8BAAK9B,GAAL,CAASyB,WAAT,CAAqB,aAArB;AACH,WAzBD,CAyBE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAK1B,GAAL,CAAS2B,QAAT,CAAkB,cAAlB,EAAkCD,KAAlC;AACH;AACJ;;AAESa,QAAAA,OAAO,GAAG;AAChB;AACApB,UAAAA,mBAAmB,CAAC,YAAY;AAC5B,kBAAM;AAAEqB,cAAAA;AAAF,gBAAmB,wCAAzB;AACA;AAAA;AAAA,8BAAKC,GAAL,CAASC,IAAT,CAAcF,YAAd;AACH,WAHkB,CAAnB;AAIH;;AAESG,QAAAA,aAAa,GAAG,CACtB;AACH;;AAEkB,cAAHC,GAAG,GAAG;AAClB,cAAI;AACA;AACA,gBAAI;AAAA;AAAA,4CAAYnC,QAAZ,KAAyB;AAAA;AAAA,sCAASC,QAAtC,EAAgD;AAC5C,oBAAM,KAAKmC,kBAAL,EAAN;AACH,aAJD,CAMA;;;AACA,kBAAM;AAAEvC,cAAAA;AAAF,gBAAU,wCAAhB;AACAA,YAAAA,GAAG,CAACC,UAAJ,CAAeuC,IAAf,CAAoB,KAAKC,OAAzB;AACH,WATD,CASE,OAAOrB,KAAP,EAAc;AACZ;AAAA;AAAA,8BAAK1B,GAAL,CAAS2B,QAAT,CAAkB,UAAlB,EAA8BD,KAA9B,EADY,CAGZ;;AACA,gBAAI;AAAA;AAAA,4CAAYvB,WAAZ,KAA4B;AAAA;AAAA,4CAAYC,mBAA5C,EAAiE;AAC7D;AAAA;AAAA,gCAAKqC,GAAL,CAASO,KAAT,CAAe,sBAAf;AACA;AACH;;AACD;AAAA;AAAA,8BAAKP,GAAL,CAASO,KAAT,CAAe,gBAAf;AACH;AACJ;AAED;;;AACgC,cAAlBH,kBAAkB,GAAG;AAC/B,gBAAM;AAAEvC,YAAAA;AAAF,cAAU,wCAAhB;AACA,gBAAM2C,UAAU,GAAG,CAAnB;AACA,cAAIC,WAAW,GAAG,KAAlB;;AAEA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,UAApB,EAAgCE,CAAC,EAAjC,EAAqC;AACjC,gBAAI;AACA;AAAA;AAAA,gCAAKnD,GAAL,CAASyB,WAAT,CAAsB,oBAAmB0B,CAAC,GAAG,CAAE,IAAGF,UAAW,KAA7D;AAEA,oBAAM3C,GAAG,CAACS,gBAAJ,CAAqBqC,MAArB,EAAN;AACA,oBAAM9C,GAAG,CAACU,kBAAJ,CAAuBqC,kBAAvB,EAAN,CAJA,CAMA;;AACA,oBAAMC,MAAM,GAAGhD,GAAG,CAACS,gBAAJ,CAAqBwC,oBAArB,EAAf;;AACA,kBAAI,CAACD,MAAD,IAAW,CAACA,MAAM,CAACE,UAAvB,EAAmC;AAC/B,sBAAM,IAAIC,KAAJ,CAAU,sBAAV,CAAN;AACH;;AAED;AAAA;AAAA,gCAAKzD,GAAL,CAASyB,WAAT,CAAqB,uBAArB,EAA8C6B,MAAM,CAACE,UAArD;AACAN,cAAAA,WAAW,GAAG,IAAd;AACA;AACH,aAfD,CAeE,OAAOxB,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAK1B,GAAL,CAAS0D,OAAT,CAAkB,wBAAuBP,CAAC,GAAG,CAAE,IAAGF,UAAW,IAA7D,EAAkEvB,KAAlE;;AAEA,kBAAIyB,CAAC,GAAGF,UAAU,GAAG,CAArB,EAAwB;AACpB,sBAAMU,UAAU,GAAG,CAACR,CAAC,GAAG,CAAL,IAAU,IAA7B;AACA;AAAA;AAAA,kCAAKnD,GAAL,CAASyB,WAAT,CAAsB,QAAOkC,UAAW,WAAxC;AACA,sBAAM,IAAI9C,OAAJ,CAAY+C,OAAO,IAAIC,UAAU,CAACD,OAAD,EAAUD,UAAV,CAAjC,CAAN;AACH;AACJ;AACJ;;AAED,cAAI,CAACT,WAAL,EAAkB;AACd,kBAAMY,QAAQ,GAAG,iBAAjB;AACA;AAAA;AAAA,8BAAK9D,GAAL,CAAS2B,QAAT,CAAkBmC,QAAlB;;AAEA,gBAAI;AAAA;AAAA,4CAAY3D,WAAZ,KAA4B;AAAA;AAAA,4CAAYC,mBAA5C,EAAiE;AAC7D;AAAA;AAAA,gCAAKqC,GAAL,CAASO,KAAT,CAAe,sBAAf;AACA;AACH,aAHD,MAGO;AACH;AAAA;AAAA,gCAAKhD,GAAL,CAAS0D,OAAT,CAAiB,uBAAjB;AACH;AACJ;AACJ;;AA9L0B,O;;;;;iBAKX,I", "sourcesContent": ["/*\n * @Author: dgflash\n * @Date: 2021-07-03 16:13:17\n * @LastEditors: dgflash\n * @LastEditTime: 2022-08-05 18:25:56\n */\nimport { _decorator, Node } from 'cc';\nimport { oops } from '../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { Root } from '../../extensions/oops-plugin-framework/assets/core/Root';\nimport { ecs } from '../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';\nimport { FBInstantManager } from './facebook-instant-games/FBInstantManager';\nimport { FacebookGameEvents } from './facebook-instant-games/FacebookGameEvents';\nimport { CameraEntity } from './game/Camera/CameraEntity';\nimport { Account } from './game/account/Account';\nimport { SimpleSceneManager } from './game/common/scene/SimpleSceneManager';\nimport { Guide } from './game/guide/Guide';\nimport { Initialize } from './game/initialize/Initialize';\nimport { Role } from './game/role/Role';\n\nimport { Environment, Platform, ShareConfig } from './tsrpc/models/ShareConfig';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('Main')\nexport class Main extends Root {\n    @property({\n        type: Node,\n        tooltip: '游戏初始画面',\n    })\n    initial: Node = null!;\n\n    protected iniStart() {\n        // 🔧 开始游戏完整初始化计时\n        oops.log.start('游戏完整初始化');\n\n        // profiler.hideStats();\n\n        // 使用LogControl自动设置日志级别\n        const isProduction = ShareConfig.isProduction;\n\n        if (ShareConfig.environment === Environment.PRODUCTION_FACEBOOK) {\n            // Facebook生产环境：超精简模式\n        } else if (isProduction) {\n            // 其他生产环境：精简模式\n        } else {\n            // 开发环境：完整日志\n        }\n\n        // 🚀 优化：延迟初始化单例模块，减少启动时间\n        this.initializeCoreModules();\n    }\n\n    /** 🔧 优化：分阶段初始化核心模块 */\n    private async initializeCoreModules() {\n        const { smc } = await import('./game/common/SingletonModuleComp');\n\n        // 只初始化最基础的模块\n        smc.initialize = ecs.getEntity<Initialize>(Initialize);\n\n        // 🚀 Facebook模块懒加载\n        if (ShareConfig.platform === Platform.FACEBOOK) {\n            const [{ FBInstantManager }, { FacebookGameEvents }] = await Promise.all([\n                import('./facebook-instant-games/FBInstantManager'),\n                import('./facebook-instant-games/FacebookGameEvents'),\n            ]);\n\n            smc.fbInstantManager = ecs.getEntity<FBInstantManager>(FBInstantManager);\n            smc.facebookGameEvents = ecs.getEntity<FacebookGameEvents>(FacebookGameEvents);\n        }\n\n        // 🚀 其他业务模块延迟加载\n        this.loadBusinessModulesLater();\n    }\n\n    /** 🔧 业务模块延迟加载 - 优化版本 */\n    private async loadBusinessModulesLater() {\n        // 🚀 优化：立即开始加载关键模块，不等待空闲时间\n        this.loadCriticalModules();\n\n        // 在空闲时间加载非关键模块\n        requestIdleCallback(async () => {\n            await this.loadNonCriticalModules();\n        });\n    }\n\n    /** 🚀 立即加载关键模块 */\n    private async loadCriticalModules() {\n        try {\n            const [{ smc }, { Role }, { SimpleSceneManager }] = await Promise.all([\n                import('./game/common/SingletonModuleComp'),\n                import('./game/role/Role'),\n                import('./game/common/scene/SimpleSceneManager'),\n            ]);\n\n            // 🚀 优先初始化关键模块\n            smc.role = ecs.getEntity<Role>(Role);\n            smc.sceneMgr = ecs.getEntity<SimpleSceneManager>(SimpleSceneManager);\n\n            oops.log.logBusiness('✅ 关键模块加载完成');\n        } catch (error) {\n            oops.log.logError('❌ 关键模块加载失败:', error);\n        }\n    }\n\n    /** 🔧 加载非关键模块 */\n    private async loadNonCriticalModules() {\n        try {\n            const [{ smc }, { Account }, { Guide }, { CameraEntity }] = await Promise.all([\n                import('./game/common/SingletonModuleComp'),\n                import('./game/account/Account'),\n                import('./game/guide/Guide'),\n                import('./game/Camera/CameraEntity'),\n            ]);\n\n            // 🚀 性能监控已移除 - 使用Cocos内置profiler即可\n            // 开发环境启用内置性能面板\n            if (!ShareConfig.isProduction) {\n                const { profiler } = await import('cc');\n                profiler.showStats();\n            }\n\n            // 🚀 直接设置资源下载并发数（替代ResourceOptimizer）\n            const { assetManager } = await import('cc');\n            assetManager.downloader.maxRequestsPerFrame = 20; // 🚀 增加并发数\n\n            // 初始化非关键业务模块\n            smc.account = ecs.getEntity<Account>(Account);\n            smc.guide = ecs.getEntity<Guide>(Guide);\n            smc.camera = ecs.getEntity<CameraEntity>(CameraEntity);\n\n            oops.log.logBusiness('✅ 非关键模块加载完成');\n        } catch (error) {\n            oops.log.logError('❌ 非关键模块加载失败:', error);\n        }\n    }\n\n    protected initGui() {\n        // 🚀 延迟加载UI配置\n        requestIdleCallback(async () => {\n            const { UIConfigData } = await import('./game/common/config/GameUIConfig');\n            oops.gui.init(UIConfigData);\n        });\n    }\n\n    protected initEcsSystem() {\n        // ECS系统现在在 loadBusinessModulesLater 中初始化\n    }\n\n    protected async run() {\n        try {\n            // Facebook平台初始化\n            if (ShareConfig.platform === Platform.FACEBOOK) {\n                await this.initializeFacebook();\n            }\n\n            // 开始游戏初始化\n            const { smc } = await import('./game/common/SingletonModuleComp');\n            smc.initialize.load(this.initial);\n        } catch (error) {\n            oops.log.logError('游戏初始化失败:', error);\n\n            // 生产环境显示错误\n            if (ShareConfig.environment === Environment.PRODUCTION_FACEBOOK) {\n                oops.gui.toast('Facebook连接失败，请刷新页面重试');\n                return;\n            }\n            oops.gui.toast('游戏启动失败，请刷新页面重试');\n        }\n    }\n\n    /** 🔧 Facebook初始化优化 */\n    private async initializeFacebook() {\n        const { smc } = await import('./game/common/SingletonModuleComp');\n        const maxRetries = 3;\n        let initSuccess = false;\n\n        for (let i = 0; i < maxRetries; i++) {\n            try {\n                oops.log.logBusiness(`🚀 Facebook初始化尝试 ${i + 1}/${maxRetries}...`);\n\n                await smc.fbInstantManager.fbInit();\n                await smc.facebookGameEvents.onPlayerFirstEntry();\n\n                // 验证Facebook数据\n                const fbData = smc.fbInstantManager.getFacebookLoginData();\n                if (!fbData || !fbData.facebookId) {\n                    throw new Error('Facebook数据不完整或玩家ID为空');\n                }\n\n                oops.log.logBusiness('✅ Facebook初始化成功，玩家ID:', fbData.facebookId);\n                initSuccess = true;\n                break;\n            } catch (error) {\n                oops.log.logWarn(`⚠️ Facebook初始化失败 (尝试 ${i + 1}/${maxRetries}):`, error);\n\n                if (i < maxRetries - 1) {\n                    const retryDelay = (i + 1) * 1000;\n                    oops.log.logBusiness(`⏳ 等待 ${retryDelay}ms 后重试...`);\n                    await new Promise(resolve => setTimeout(resolve, retryDelay));\n                }\n            }\n        }\n\n        if (!initSuccess) {\n            const errorMsg = 'Facebook多次初始化失败';\n            oops.log.logError(errorMsg);\n\n            if (ShareConfig.environment === Environment.PRODUCTION_FACEBOOK) {\n                oops.gui.toast('Facebook连接失败，请刷新页面重试');\n                return;\n            } else {\n                oops.log.logWarn('⚠️ 非生产环境：继续游戏启动（降级模式）');\n            }\n        }\n    }\n}\n"]}