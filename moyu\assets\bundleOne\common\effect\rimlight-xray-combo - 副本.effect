// xray.effect
CCEffect %{
  techniques:
  - name: transparent-xray
    passes:
    - vert: unlit-vs:vert
      frag: xray-fs:frag
      embeddedMacros: { CC_FORWARD_ADD: true }
      depthStencilState:
        depthTest: true
        depthWrite: true
        depthFunc: less_equal
      blendState:
        targets:
        - blend: false
      properties: &props
        tilingOffset:         { value: [1.0, 1.0, 0.0, 0.0] }
        albedoMap:            { value: grey, editor: { displayName: AlbedoMap } }
        mainColor:            { value: [1.0, 1.0, 1.0, 1.0], target: albedo, linear: true, editor: { displayName: Albedo, type: color } }
        albedoScale:          { value: [1.0, 1.0, 1.0], target: albedoScaleAndCutoff.xyz }
        alphaThreshold:       { value: 0.5, target: albedoScaleAndCutoff.w, editor: { parent: USE_ALPHA_TEST, slide: true, range: [0, 1.0], step: 0.001 } }
        emissive:             { value: [0.0, 0.0, 0.0, 1.0], linear: true, editor: { type: color } }
        emissiveScale:        { value: [1.0, 1.0, 1.0], target: emissiveScaleParam.xyz }
        normalMap:            { value: normal }
        emissiveMap:          { value: grey }
        xrayAlpha:            { value: 0.5, editor: { slide: true, range: [0.0, 1.0], step: 0.01, displayName: "X-Ray Alpha" } }

    - vert: unlit-vs:vert
      frag: xray-fs:frag
      embeddedMacros: { CC_FORWARD_ADD: true, XRAY_PASS: true }
      depthStencilState:
        depthTest: true
        depthWrite: false
        depthFunc: greater
      blendState:
        targets:
        - blend: true
          blendSrc: src_alpha
          blendDst: one_minus_src_alpha
          blendDstAlpha: one_minus_src_alpha
      properties: *props
}%
CCProgram shared-ubos %{
  uniform Constants {
    vec4 tilingOffset;
    vec4 albedo;
    vec4 albedoScaleAndCutoff;
    vec4 emissive;
    vec4 emissiveScaleParam;
    float xrayAlpha;
  };
}%

CCProgram unlit-vs %{
  precision highp float;
  #include <legacy/input-standard>
  #include <builtin/uniforms/cc-global>
  #include <legacy/local-batch>
  #include <shared-ubos>
  #include <legacy/fog-vs>

  #if USE_VERTEX_COLOR
    in vec4 a_color;
    out lowp vec4 v_color;
  #endif

  out vec3 v_position;
  out vec3 v_normal;
  out vec2 v_uv;

  #if HAS_SECOND_UV
    out mediump vec2 v_uv1;
  #endif

  #if USE_NORMAL_MAP
    out mediump vec4 v_tangent;
  #endif

  #if HAS_SECOND_UV || CC_USE_LIGHTMAP
    in vec2 a_texCoord1;
  #endif

  vec4 vert () {
    StandardVertInput In;
    CCVertInput(In);

    mat4 matWorld, matWorldIT;
    CCGetWorldMatrixFull(matWorld, matWorldIT);

    vec4 pos = matWorld * In.position;
    v_position = pos.xyz;
    v_normal = normalize((matWorldIT * vec4(In.normal, 0.0)).xyz);
    
    v_uv = a_texCoord * tilingOffset.xy + tilingOffset.zw;
    #if SAMPLE_FROM_RT
      CC_HANDLE_RT_SAMPLE_FLIP(v_uv);
    #endif
    #if HAS_SECOND_UV
      v_uv1 = a_texCoord1 * tilingOffset.xy + tilingOffset.zw;
      #if SAMPLE_FROM_RT
        CC_HANDLE_RT_SAMPLE_FLIP(v_uv1);
      #endif
    #endif

    #if USE_VERTEX_COLOR
      v_color = a_color;
    #endif

    #if USE_NORMAL_MAP
      v_tangent.xyz = normalize((matWorld * vec4(In.tangent.xyz, 0.0)).xyz);
      v_tangent.w = In.tangent.w;
    #endif

    CC_TRANSFER_FOG(pos);

    return cc_matProj * (cc_matView * matWorld) * In.position;
  }
}%

CCProgram xray-fs %{
  precision highp float;
  #include <legacy/output>
  #include <legacy/fog-fs>
  #include <shared-ubos>

  in vec3 v_position;
  in vec3 v_normal;
  in vec2 v_uv;

  #if HAS_SECOND_UV
    in mediump vec2 v_uv1;
  #endif

  #if USE_VERTEX_COLOR
    in lowp vec4 v_color;
  #endif

  #if USE_NORMAL_MAP
    in mediump vec4 v_tangent;
    uniform sampler2D normalMap;
    #pragma define-meta NORMAL_UV options([v_uv, v_uv1])
  #endif

  #if USE_ALBEDO_MAP
    uniform sampler2D albedoMap;
    #pragma define-meta ALBEDO_UV options([v_uv, v_uv1])
  #endif

  #if USE_EMISSIVE_MAP
    uniform sampler2D emissiveMap;
    #pragma define-meta EMISSIVE_UV options([v_uv, v_uv1])
  #endif

  #if USE_ALPHA_TEST
    #pragma define-meta ALPHA_TEST_CHANNEL options([a, r])
  #endif

// 获取世界空间中的相机位置
vec3 getCameraPosition() {
    return cc_cameraPos.xyz;
}

vec4 frag () {
    vec4 baseColor = albedo;
    
    #if USE_VERTEX_COLOR
      baseColor.rgb *= SRGBToLinear(v_color.rgb);
      baseColor.a *= v_color.a;
    #endif
    
    #if USE_ALBEDO_MAP
      vec4 texColor = texture(albedoMap, ALBEDO_UV);
      texColor.rgb = SRGBToLinear(texColor.rgb);
      baseColor *= texColor;
    #endif
    
    baseColor.rgb *= albedoScaleAndCutoff.xyz;

    #if USE_ALPHA_TEST
      if (baseColor.ALPHA_TEST_CHANNEL < albedoScaleAndCutoff.w) discard;
    #endif

    vec3 normal = normalize(v_normal);

    vec4 col;

    #if XRAY_PASS
        vec3 cameraPos = getCameraPosition();
        vec3 viewDir = normalize(cameraPos - v_position);
        float edgeStrength = 1.0 - dot(normalize(v_normal), viewDir);
        edgeStrength = smoothstep(0.5, 1.0, edgeStrength); // 调整边缘范围

        col = vec4(baseColor.rgb, xrayAlpha); // 使用原始颜色 + 透明度
        col.rgb = mix(col.rgb, vec3(1.0, 1.0, 1.0), edgeStrength); // 边缘高亮混合
    #else
        col = vec4(baseColor.rgb, baseColor.a);
    #endif

    CC_APPLY_FOG(col, v_position);
    return CCFragOutput(col);
  }

}%