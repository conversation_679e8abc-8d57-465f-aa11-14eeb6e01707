{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/game/managers/UnifiedGameManager.ts"], "names": ["UnifiedGameManager", "Vec3", "oops", "Utils", "simpleLoader", "smc", "GuideView3DItemComp", "config<PERSON><PERSON><PERSON>", "GameState", "constructor", "gameEntity", "currentState", "Loading", "previousGameState", "stateCallbacks", "Map", "currentLevelConfig", "currentLevelIndex", "inputEnabled", "refillStrategy", "adjustByPerformance", "adjustByTime", "minInterval", "maxInterval", "difficultyMultiplier", "initializeStateCallbacks", "set", "SimpleMode", "HardMode", "GameOver", "Win", "Paused", "onStateChange", "state", "callback", "callbacks", "get", "push", "changeState", "newState", "log", "logBusiness", "for<PERSON>ach", "getCurrentState", "isSimpleMode", "isHardMode", "wasPreviouslyHardMode", "wasPreviouslySimpleMode", "isPaused", "GameModel", "pasue", "isGameOver", "isWin", "loadLevel", "levelIndex", "onEssentialLoaded", "getLevelConfig", "Error", "loadEasyModeItems", "loadWallAndScene", "loadGameUI", "beforeStartGame", "error", "logError", "easyItems", "easyModeItems", "length", "itemPrefabPath", "getItemPrefabPath", "uniqueItemNames", "Array", "from", "Set", "map", "item", "name", "prefabPaths", "loadPrefabs", "onUIReady", "Promise", "resolve", "loadGameUIWithCallback", "getCurrentLevelConfig", "getCurrentLevelIndex", "setInputEnabled", "enabled", "isInputEnabled", "setRefillStrategy", "strategy", "checkAndRefillItems", "touchedItem", "currentItems", "allItemsToPick", "size", "shouldRefill", "allItemsToCreate", "refillCount", "Math", "min", "i", "itemName", "pop", "<PERSON><PERSON><PERSON>", "createItemOnPos", "initializeEasyModeItems", "count", "arr<PERSON><PERSON><PERSON><PERSON>", "initializeHardModeItems", "hardModeItems", "createHardModeItemsBatched", "batchSize", "batchDelay", "allItems", "currentBatch", "totalBatches", "ceil", "createBatch", "startIndex", "endIndex", "getRandomBornPos", "setTimeout", "handleInputEnabling", "createInitialItemsInScene", "log<PERSON>arn", "itemCount", "itemIndex", "isNewPlayer", "role", "fixedPositions", "generateRandomPosition", "startNewPlayerGuide", "radius", "angle", "random", "PI", "distance", "cos", "sin", "guideSteps", "guide", "GuideModel", "last", "addGuideComponentToItem", "guideStep", "itemNode", "guideComp", "getComponent", "addComponent", "setStep", "destroy", "clear"], "mappings": ";;;yJAsBaA,kB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAtBEC,MAAAA,I,OAAAA,I;;AACNC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,G,iBAAAA,G;;AAEAC,MAAAA,mB,iBAAAA,mB;;AACAC,MAAAA,a,iBAAAA,a;;;;;;;;;AACT;;AAGA;2BACYC,S,0BAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;eAAAA,S;;AASZ;;;oCACaR,kB,GAAN,MAAMA,kBAAN,CAAyB;AAwB5BS,QAAAA,WAAW,CAACC,UAAD,EAAyB;AAAA,eAvB5BA,UAuB4B;AArBpC;AAqBoC,eApB5BC,YAoB4B,GApBFH,SAAS,CAACI,OAoBR;AAAA,eAnB5BC,iBAmB4B,GAnBGL,SAAS,CAACI,OAmBb;AAnBsB;AAmBtB,eAlB5BE,cAkB4B,GAlBwB,IAAIC,GAAJ,EAkBxB;AAhBpC;AAgBoC,eAf5BC,kBAe4B,GAfa,IAeb;AAAA,eAd5BC,iBAc4B,GAdA,CAcA;AAdG;AAEvC;AAYoC,eAX5BC,YAW4B,GAXJ,KAWI;AATpC;AASoC,eAR5BC,cAQ4B,GARX;AACrBC,YAAAA,mBAAmB,EAAE,KADA;AAErBC,YAAAA,YAAY,EAAE,KAFO;AAGrBC,YAAAA,WAAW,EAAE,IAHQ;AAIrBC,YAAAA,WAAW,EAAE,IAJQ;AAKrBC,YAAAA,oBAAoB,EAAE;AALD,WAQW;AAChC,eAAKd,UAAL,GAAkBA,UAAlB;AACA,eAAKe,wBAAL;AACH,SA3B2B,CA6B5B;;;AAEQA,QAAAA,wBAAwB,GAAS;AACrC,eAAKX,cAAL,CAAoBY,GAApB,CAAwBlB,SAAS,CAACmB,UAAlC,EAA8C,EAA9C;AACA,eAAKb,cAAL,CAAoBY,GAApB,CAAwBlB,SAAS,CAACoB,QAAlC,EAA4C,EAA5C;AACA,eAAKd,cAAL,CAAoBY,GAApB,CAAwBlB,SAAS,CAACI,OAAlC,EAA2C,EAA3C;AACA,eAAKE,cAAL,CAAoBY,GAApB,CAAwBlB,SAAS,CAACqB,QAAlC,EAA4C,EAA5C;AACA,eAAKf,cAAL,CAAoBY,GAApB,CAAwBlB,SAAS,CAACsB,GAAlC,EAAuC,EAAvC;AACA,eAAKhB,cAAL,CAAoBY,GAApB,CAAwBlB,SAAS,CAACuB,MAAlC,EAA0C,EAA1C;AACH;;AAEDC,QAAAA,aAAa,CAACC,KAAD,EAAmBC,QAAnB,EAA+C;AACxD,cAAMC,SAAS,GAAG,KAAKrB,cAAL,CAAoBsB,GAApB,CAAwBH,KAAxB,CAAlB;;AACA,cAAIE,SAAJ,EAAe;AACXA,YAAAA,SAAS,CAACE,IAAV,CAAeH,QAAf;AACH;AACJ;;AAEDI,QAAAA,WAAW,CAACC,QAAD,EAA4B;AACnC,cAAI,KAAK5B,YAAL,KAAsB4B,QAA1B,EAAoC;AAEpC;AAAA;AAAA,4BAAKC,GAAL,CAASC,WAAT,6CAAiC,KAAK9B,YAAtC,gBAAwD4B,QAAxD,EAHmC,CAKnC;;AACA,cACI,KAAK5B,YAAL,KAAsBH,SAAS,CAACmB,UAAhC,IACA,KAAKhB,YAAL,KAAsBH,SAAS,CAACoB,QAFpC,EAGE;AACE,iBAAKf,iBAAL,GAAyB,KAAKF,YAA9B;AACH;;AAED,eAAKA,YAAL,GAAoB4B,QAApB,CAbmC,CAenC;;AACA,cAAMJ,SAAS,GAAG,KAAKrB,cAAL,CAAoBsB,GAApB,CAAwBG,QAAxB,CAAlB;;AACA,cAAIJ,SAAJ,EAAe;AACXA,YAAAA,SAAS,CAACO,OAAV,CAAkBR,QAAQ,IAAIA,QAAQ,EAAtC;AACH;AACJ;;AAEDS,QAAAA,eAAe,GAAc;AACzB,iBAAO,KAAKhC,YAAZ;AACH;;AAEDiC,QAAAA,YAAY,GAAY;AACpB,iBAAO,KAAKjC,YAAL,KAAsBH,SAAS,CAACmB,UAAvC;AACH;;AAEDkB,QAAAA,UAAU,GAAY;AAClB,iBAAO,KAAKlC,YAAL,KAAsBH,SAAS,CAACoB,QAAvC;AACH;AAED;AACJ;AACA;;;AACIkB,QAAAA,qBAAqB,GAAY;AAC7B,iBAAO,KAAKjC,iBAAL,KAA2BL,SAAS,CAACoB,QAA5C;AACH;AAED;AACJ;AACA;;;AACImB,QAAAA,uBAAuB,GAAY;AAC/B,iBAAO,KAAKlC,iBAAL,KAA2BL,SAAS,CAACmB,UAA5C;AACH;;AAEDqB,QAAAA,QAAQ,GAAY;AAChB,iBAAO,KAAKrC,YAAL,KAAsBH,SAAS,CAACuB,MAAhC,IAA0C,KAAKrB,UAAL,CAAgBuC,SAAhB,CAA0BC,KAA3E;AACH;;AAEDC,QAAAA,UAAU,GAAY;AAClB,iBAAO,KAAKxC,YAAL,KAAsBH,SAAS,CAACqB,QAAvC;AACH;;AAEDuB,QAAAA,KAAK,GAAY;AACb,iBAAO,KAAKzC,YAAL,KAAsBH,SAAS,CAACsB,GAAvC;AACH,SAzG2B,CA2G5B;;;AAEMuB,QAAAA,SAAS,CAACC,UAAD,EAAqBC,iBAArB,EAAoE;AAAA;;AAAA;AAC/E,gBAAI;AACA;AAAA;AAAA,gCAAKf,GAAL,CAASC,WAAT,wDAAkCa,UAAlC,EADA,CAGA;;AACA,cAAA,KAAI,CAACrC,iBAAL,GAAyBqC,UAAzB,CAJA,CAMA;;AACA,cAAA,KAAI,CAACtC,kBAAL,SAAgC;AAAA;AAAA,kDAAcwC,cAAd,CAA6BF,UAA7B,CAAhC;;AACA,kBAAI,CAAC,KAAI,CAACtC,kBAAV,EAA8B;AAC1B,sBAAM,IAAIyC,KAAJ,kDAAsBH,UAAtB,CAAN;AACH,eAVD,CAYA;;;AACA,oBAAM,KAAI,CAACI,iBAAL,EAAN,CAbA,CAeA;;AACA;AAAA;AAAA,gCAAKlB,GAAL,CAASC,WAAT,CAAqB,uBAArB;;AACA,cAAA,KAAI,CAACH,WAAL,CAAiB9B,SAAS,CAACmB,UAA3B,EAjBA,CAmBA;;;AACA,oBAAM,KAAI,CAACjB,UAAL,CAAgBiD,gBAAhB,EAAN;AACA,oBAAM,KAAI,CAACC,UAAL,CAAgBL,iBAAhB,CAAN,CArBA,CAuBA;;AACA;AAAA;AAAA,gCAAKf,GAAL,CAASC,WAAT,CAAqB,mCAArB;;AACA,cAAA,KAAI,CAAC/B,UAAL,CAAgBmD,eAAhB,GAzBA,CA2BA;;;AAEA;AAAA;AAAA,gCAAKrB,GAAL,CAASC,WAAT,0BAA6Ba,UAA7B;AACH,aA9BD,CA8BE,OAAOQ,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAKtB,GAAL,CAASuB,QAAT,0BAA0BT,UAA1B,iCAA8CQ,KAA9C;AACA,oBAAMA,KAAN;AACH;AAlC8E;AAmClF;;AAEaJ,QAAAA,iBAAiB,GAAkB;AAAA;;AAAA;AAC7C,gBAAI,CAAC,MAAI,CAAC1C,kBAAV,EAA8B;AAE9B,gBAAMgD,SAAS,GAAG,MAAI,CAAChD,kBAAL,CAAwBiD,aAA1C;AACA,gBAAID,SAAS,CAACE,MAAV,KAAqB,CAAzB,EAA4B,OAJiB,CAM7C;;AACA,gBAAMC,cAAc,GAAG,MAAI,CAACzD,UAAL,CAAgB0D,iBAAhB,EAAvB,CAP6C,CAS7C;;;AACA,gBAAMC,eAAe,GAAGC,KAAK,CAACC,IAAN,CAAW,IAAIC,GAAJ,CAAQR,SAAS,CAACS,GAAV,CAAcC,IAAI,IAAIA,IAAI,CAACC,IAA3B,CAAR,CAAX,CAAxB;AACA,gBAAMC,WAAW,GAAGP,eAAe,CAACI,GAAhB,CAAoBE,IAAI,SAAOR,cAAP,GAAwBQ,IAAhD,CAApB;AAEA,kBAAM;AAAA;AAAA,8CAAaE,WAAb,CAAyBD,WAAzB,CAAN;AACA;AAAA;AAAA,8BAAKpC,GAAL,CAASC,WAAT;AAd6C;AAehD;;AAEamB,QAAAA,UAAU,CAACkB,SAAD,EAAwC;AAAA;;AAAA;AAC5D,mBAAO,IAAIC,OAAJ,CAAkBC,OAAO,IAAI;AAChC,cAAA,MAAI,CAACtE,UAAL,CAAgBuE,sBAAhB,CAAuC,MAAM;AACzC,oBAAIH,SAAJ,EAAe;AACXA,kBAAAA,SAAS;AACZ;;AACDE,gBAAAA,OAAO;AACV,eALD;AAMH,aAPM,CAAP;AAD4D;AAS/D;;AAEDE,QAAAA,qBAAqB,GAAuB;AACxC,iBAAO,KAAKlE,kBAAZ;AACH;;AAEDmE,QAAAA,oBAAoB,GAAW;AAC3B,iBAAO,KAAKlE,iBAAZ;AACH,SApL2B,CAsL5B;;;AAEAmE,QAAAA,eAAe,CAACC,OAAD,EAAyB;AACpC,eAAKnE,YAAL,GAAoBmE,OAApB;AACH;;AAEDC,QAAAA,cAAc,GAAY;AACtB,iBAAO,KAAKpE,YAAZ;AACH,SA9L2B,CAgM5B;;;AAEAqE,QAAAA,iBAAiB,CAACC,QAAD,EAAsB;AACnC,eAAKrE,cAAL,gBAA2B,KAAKA,cAAhC,EAAmDqE,QAAnD;AACH;;AAEDC,QAAAA,mBAAmB,CAACC,WAAD,EAA2B;AAC1C;AACA,cAAI,KAAK9C,YAAL,MAAuB,KAAKO,UAAL,EAAvB,IAA4C,KAAKC,KAAL,EAAhD,EAA8D;AAC1D;AACH,WAJyC,CAM1C;;;AACA,cAAMuC,YAAY,GAAG,KAAKjF,UAAL,CAAgBuC,SAAhB,CAA0B2C,cAA1B,CAAyCC,IAA9D;AACA,cAAMC,YAAY,GAAGH,YAAY,GAAG,CAApC,CAR0C,CAQH;;AAEvC,cAAIG,YAAY,IAAI,KAAKpF,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,CAA2C7B,MAA3C,GAAoD,CAAxE,EAA2E;AACvE,gBAAM8B,WAAW,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,KAAKxF,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,CAA2C7B,MAAvD,CAApB;;AAEA,iBAAK,IAAIiC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,WAApB,EAAiCG,CAAC,EAAlC,EAAsC;AAClC,kBAAMC,QAAQ,GAAG,KAAK1F,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,CAA2CM,GAA3C,EAAjB;;AACA,kBAAID,QAAJ,EAAc;AACV,oBAAME,OAAO,GAAG,IAAIrG,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CAAhB;AACA,qBAAKS,UAAL,CAAgB6F,eAAhB,CAAgCD,OAAhC,EAAyCF,QAAzC,EAAmDD,CAAC,GAAG,CAAvD,EAA0D,IAA1D;AACH;AACJ;AACJ;AACJ,SA3N2B,CA6N5B;;;AAEAK,QAAAA,uBAAuB,GAAS;AAC5B,cAAI,CAAC,KAAKxF,kBAAV,EAA8B;AAE9B,eAAKN,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,GAA6C,EAA7C;AACA,cAAM9B,aAAa,GAAG,KAAKjD,kBAAL,CAAwBiD,aAA9C;;AAEA,eAAK,IAAMS,IAAX,IAAmBT,aAAnB,EAAkC;AAC9B,iBAAK,IAAIkC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGzB,IAAI,CAAC+B,KAAzB,EAAgCN,CAAC,EAAjC,EAAqC;AACjC,mBAAKzF,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,CAA2C1D,IAA3C,CAAgDqC,IAAI,CAACC,IAArD;AACH;AACJ;;AAED,eAAKjE,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,GAA6C;AAAA;AAAA,8BAAMW,WAAN,CACzC,KAAKhG,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBADe,CAA7C;AAGA;AAAA;AAAA,4BAAKvD,GAAL,CAASC,WAAT,uFACuB,KAAK/B,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,CAA2C7B,MADlE;AAGH;;AAEDyC,QAAAA,uBAAuB,GAAS;AAC5B,cAAI,CAAC,KAAK3F,kBAAV,EAA8B;AAE9B,eAAKN,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,GAA6C,EAA7C;AACA,cAAMa,aAAa,GAAG,KAAK5F,kBAAL,CAAwB4F,aAA9C;;AAEA,eAAK,IAAMlC,IAAX,IAAmBkC,aAAnB,EAAkC;AAC9B,iBAAK,IAAIT,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGzB,IAAI,CAAC+B,KAAzB,EAAgCN,CAAC,EAAjC,EAAqC;AACjC,mBAAKzF,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,CAA2C1D,IAA3C,CAAgDqC,IAAI,CAACC,IAArD;AACH;AACJ;;AAED,eAAKjE,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,GAA6C;AAAA;AAAA,8BAAMW,WAAN,CACzC,KAAKhG,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBADe,CAA7C;AAGA;AAAA;AAAA,4BAAKvD,GAAL,CAASC,WAAT,uFACuB,KAAK/B,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,CAA2C7B,MADlE;AAGH;AAED;AACJ;AACA;;;AACI2C,QAAAA,0BAA0B,GAAS;AAC/B,cAAMC,SAAS,GAAG,EAAlB,CAD+B,CACT;;AACtB,cAAMC,UAAU,GAAG,EAAnB,CAF+B,CAER;;AACvB,cAAMC,QAAQ,GAAG,KAAKtG,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA3C;AACA,cAAIkB,YAAY,GAAG,CAAnB;AACA,cAAMC,YAAY,GAAGjB,IAAI,CAACkB,IAAL,CAAUH,QAAQ,CAAC9C,MAAT,GAAkB4C,SAA5B,CAArB;AAEA;AAAA;AAAA,4BAAKtE,GAAL,CAASC,WAAT,wDAAkCuE,QAAQ,CAAC9C,MAA3C,wCAA2DgD,YAA3D;;AAEA,cAAME,WAAW,GAAG,MAAM;AACtB,gBAAMC,UAAU,GAAGJ,YAAY,GAAGH,SAAlC;AACA,gBAAMQ,QAAQ,GAAGrB,IAAI,CAACC,GAAL,CAASmB,UAAU,GAAGP,SAAtB,EAAiCE,QAAQ,CAAC9C,MAA1C,CAAjB,CAFsB,CAItB;;AACA,iBAAK,IAAIiC,CAAC,GAAGkB,UAAb,EAAyBlB,CAAC,GAAGmB,QAA7B,EAAuCnB,CAAC,EAAxC,EAA4C;AACxC,kBAAMC,QAAQ,GAAGY,QAAQ,CAACb,CAAD,CAAzB;AACA,kBAAMG,OAAO,GAAG,KAAK5F,UAAL,CAAgB6G,gBAAhB,EAAhB;AACA,mBAAK7G,UAAL,CAAgB6F,eAAhB,CAAgCD,OAAhC,EAAyCF,QAAzC,EAAmDD,CAAnD;AACH;;AAEDc,YAAAA,YAAY;;AAEZ,gBAAIA,YAAY,GAAGC,YAAnB,EAAiC;AAC7B;AACAM,cAAAA,UAAU,CAACJ,WAAD,EAAcL,UAAd,CAAV;AACH,aAHD,MAGO;AACH;AACA;AAAA;AAAA,gCAAKvE,GAAL,CAASC,WAAT,uFAAwCuE,QAAQ,CAAC9C,MAAjD;AACA,mBAAKxD,UAAL,CAAgB+G,mBAAhB;AACH;AACJ,WArBD,CAT+B,CAgC/B;;;AACAL,UAAAA,WAAW;AACd;;AAEDM,QAAAA,yBAAyB,GAAS;AAAA;;AAC9B,cAAI,KAAKhH,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,CAA2C7B,MAA3C,KAAsD,CAA1D,EAA6D;AACzD;AAAA;AAAA,8BAAK1B,GAAL,CAASmF,OAAT,CAAiB,aAAjB;AACA;AACH;;AAED,cAAMC,SAAS,GAAG,KAAKlH,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,CAA2C7B,MAA7D;AACA;AAAA;AAAA,4BAAK1B,GAAL,CAASC,WAAT,4CAAgCmF,SAAhC,0BAP8B,CAS9B;;AACA,cAAI,KAAK/E,UAAL,MAAqB+E,SAAS,GAAG,EAArC,EAAyC;AACrC;AAAA;AAAA,8BAAKpF,GAAL,CAASC,WAAT,CAAqB,wBAArB;AACA,iBAAKoE,0BAAL;AACA;AACH;;AAED,cAAIgB,SAAS,GAAG,CAAhB;AACA,cAAMC,WAAW,GAAG,KAAKpH,UAAL,CAAgBkC,YAAhB,gBAAkC;AAAA;AAAA,0BAAImF,IAAtC,qBAAkC,MAAUD,WAAV,EAAlC,CAApB;AACA,cAAMlF,YAAY,GAAG,KAAKlC,UAAL,CAAgBkC,YAAhB,EAArB,CAlB8B,CAoB9B;;AACA,cAAIoF,cAAsB,GAAG,EAA7B;;AACA,cAAIF,WAAJ,EAAiB;AACbE,YAAAA,cAAc,GAAG,CACb,IAAI/H,IAAJ,CAAS,CAAC,CAAV,EAAa,CAAb,EAAgB,CAAC,CAAjB,CADa,EACQ;AACrB,gBAAIA,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAC,CAAhB,CAFa,EAEO;AACpB,gBAAIA,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAC,CAAhB,CAHa,EAGO;AACpB,gBAAIA,IAAJ,CAAS,CAAC,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAJa,EAIO;AACpB,gBAAIA,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CALa,EAKM;AACnB,gBAAIA,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CANa,EAMM;AACnB,gBAAIA,IAAJ,CAAS,CAAC,CAAV,EAAa,CAAb,EAAgB,CAAhB,CAPa,EAOO;AACpB,gBAAIA,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CARa,EAQM;AACnB,gBAAIA,IAAJ,CAAS,CAAT,EAAY,CAAZ,EAAe,CAAf,CATa,CASM;AATN,aAAjB;AAWA;AAAA;AAAA,8BAAKuC,GAAL,CAASC,WAAT,CAAqB,uBAArB;AACH;;AAED,iBAAO,KAAK/B,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,CAA2C7B,MAA3C,GAAoD,CAA3D,EAA8D;AAC1D,gBAAMkC,QAAQ,GAAG,KAAK1F,UAAL,CAAgBuC,SAAhB,CAA0B8C,gBAA1B,CAA2CM,GAA3C,EAAjB;;AACA,gBAAID,QAAJ,EAAc;AACV,kBAAIE,OAAa,SAAjB;;AAEA,kBAAIwB,WAAW,IAAID,SAAS,GAAGG,cAAc,CAAC9D,MAA9C,EAAsD;AAClD;AACAoC,gBAAAA,OAAO,GAAG0B,cAAc,CAACH,SAAD,CAAxB;AACH,eAHD,MAGO;AACH;AACAvB,gBAAAA,OAAO,GAAG,KAAK2B,sBAAL,CAA4BrF,YAA5B,CAAV;AACH,eATS,CAWV;;;AACA,mBAAKlC,UAAL,CAAgB6F,eAAhB,CAAgCD,OAAhC,EAAyCF,QAAzC,EAAmDyB,SAAS,EAA5D,EAAgE,CAACC,WAAjE;AACH;AACJ;;AAED;AAAA;AAAA,4BAAKtF,GAAL,CAASC,WAAT,uFAAwCoF,SAAxC,cAvD8B,CAyD9B;;AACA,cAAIC,WAAJ,EAAiB;AACb,iBAAKI,mBAAL,CAAyBL,SAAzB;AACH;AACJ;AAED;AACJ;AACA;;;AACYI,QAAAA,sBAAsB,CAACrF,YAAD,EAA8B;AACxD,cAAIA,YAAJ,EAAkB;AACd;AACA,gBAAMuF,MAAM,GAAG,CAAf;AACA,gBAAMC,KAAK,GAAGnC,IAAI,CAACoC,MAAL,KAAgBpC,IAAI,CAACqC,EAArB,GAA0B,CAAxC;AACA,gBAAMC,QAAQ,GAAGtC,IAAI,CAACoC,MAAL,KAAgBF,MAAjC;AAEA,mBAAO,IAAIlI,IAAJ,CACHgG,IAAI,CAACuC,GAAL,CAASJ,KAAT,IAAkBG,QADf,EAEH,IAAItC,IAAI,CAACoC,MAAL,KAAgB,CAFjB,EAEoB;AACvBpC,YAAAA,IAAI,CAACwC,GAAL,CAASL,KAAT,IAAkBG,QAHf,CAAP;AAKH,WAXD,MAWO;AACH;AACA,mBAAO,IAAItI,IAAJ,CACH,CAACgG,IAAI,CAACoC,MAAL,KAAgB,GAAjB,IAAwB,EADrB,EACyB;AAC5BpC,YAAAA,IAAI,CAACoC,MAAL,KAAgB,CAAhB,GAAoB,CAFjB,EAEoB;AACvB,aAACpC,IAAI,CAACoC,MAAL,KAAgB,GAAjB,IAAwB,EAHrB,CAGwB;AAHxB,aAAP;AAKH;AACJ;AAED;AACJ;AACA;;;AACYH,QAAAA,mBAAmB,CAACN,SAAD,EAA0B;AACjDJ,UAAAA,UAAU,CAAC,MAAM;AAAA;;AACb,gBAAMkB,UAAU,GAAGzC,IAAI,CAACC,GAAL,CAAS0B,SAAT,EAAoB,CAApB,CAAnB,CADa,CAGb;;AACA,0BAAI;AAAA;AAAA,4BAAIe,KAAR,aAAI,OAAWC,UAAf,EAA2B;AACvB;AAAA;AAAA,8BAAID,KAAJ,CAAUC,UAAV,CAAqBC,IAArB,GAA4BH,UAA5B;AACA;AAAA;AAAA,gCAAKlG,GAAL,CAASC,WAAT,qEAAqCiG,UAArC;AACH,aAPY,CASb;;;AACA,iBAAK,IAAIvC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuC,UAApB,EAAgCvC,CAAC,EAAjC,EAAqC;AACjC,mBAAK2C,uBAAL,CAA6B3C,CAA7B,EAAgCA,CAAC,GAAG,CAApC;AACH;AACJ,WAbS,EAaP,GAbO,CAAV,CADiD,CAcxC;AACZ;AAED;AACJ;AACA;;;AACY2C,QAAAA,uBAAuB,CAACjB,SAAD,EAAoBkB,SAApB,EAA6C;AACxE,cAAMC,QAAQ,GAAG,KAAKtI,UAAL,CAAgBuC,SAAhB,CAA0B2C,cAA1B,CAAyCxD,GAAzC,CAA6CyF,SAA7C,CAAjB;;AACA,cAAI,CAACmB,QAAL,EAAe;AACX;AAAA;AAAA,8BAAKxG,GAAL,CAASmF,OAAT,+DAAgCE,SAAhC;AACA;AACH;;AAED,cAAIoB,SAAS,GAAGD,QAAQ,CAACE,YAAT;AAAA;AAAA,yDAAhB;;AACA,cAAI,CAACD,SAAL,EAAgB;AACZA,YAAAA,SAAS,GAAGD,QAAQ,CAACG,YAAT;AAAA;AAAA,2DAAZ;AACH;;AAED,cAAIF,SAAS,IAAKA,SAAD,CAAmBG,OAApC,EAA6C;AACxCH,YAAAA,SAAD,CAAmBG,OAAnB,CAA2BL,SAA3B;AACA;AAAA;AAAA,8BAAKvG,GAAL,CAASC,WAAT,sCAA+BoF,SAA/B,+CAAoDkB,SAApD;AACH;AACJ,SA7a2B,CA+a5B;;;AAEAM,QAAAA,OAAO,GAAS;AACZ,eAAKrI,kBAAL,GAA0B,IAA1B;AACA,eAAKF,cAAL,CAAoBwI,KAApB;AACH;;AApb2B,O", "sourcesContent": ["import { Node, Vec3 } from 'cc';\nimport { oops } from '../../../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { Utils } from '../../utils/Utils';\nimport { simpleLoader } from '../common/loader/SimpleLoadingManager';\nimport { smc } from '../common/SingletonModuleComp';\nimport { LevelConfig } from '../common/table/LevelConfigTypes';\nimport { GuideView3DItemComp } from '../guide/view/GuideView3DItemComp';\nimport { configManager } from './ConfigManager';\n// 引入 GameEntity，但避免循环引用\ntype GameEntity = import('../scenes/Game/GameEntity').GameEntity;\n\n/** 游戏状态枚举 */\nexport enum GameState {\n    Loading = 'Loading',\n    SimpleMode = 'SimpleMode',\n    HardMode = 'HardMode',\n    Paused = 'Paused',\n    GameOver = 'GameOver',\n    Win = 'Win',\n}\n\n/** 统一游戏管理器 - 合并多个管理器功能 */\nexport class UnifiedGameManager {\n    private gameEntity: GameEntity;\n\n    // 状态管理\n    private currentState: GameState = GameState.Loading;\n    private previousGameState: GameState = GameState.Loading; // 🎯 记录前一个游戏状态\n    private stateCallbacks: Map<GameState, Array<() => void>> = new Map();\n\n    // 关卡管理\n    private currentLevelConfig: LevelConfig | null = null;\n    private currentLevelIndex: number = 1; // 🎯 记录当前关卡索引\n\n    // 输入管理\n    private inputEnabled: boolean = false;\n\n    // 物品生成管理\n    private refillStrategy = {\n        adjustByPerformance: false,\n        adjustByTime: false,\n        minInterval: 3000,\n        maxInterval: 4500,\n        difficultyMultiplier: 1.0,\n    };\n\n    constructor(gameEntity: GameEntity) {\n        this.gameEntity = gameEntity;\n        this.initializeStateCallbacks();\n    }\n\n    // =================== 状态管理 ===================\n\n    private initializeStateCallbacks(): void {\n        this.stateCallbacks.set(GameState.SimpleMode, []);\n        this.stateCallbacks.set(GameState.HardMode, []);\n        this.stateCallbacks.set(GameState.Loading, []);\n        this.stateCallbacks.set(GameState.GameOver, []);\n        this.stateCallbacks.set(GameState.Win, []);\n        this.stateCallbacks.set(GameState.Paused, []);\n    }\n\n    onStateChange(state: GameState, callback: () => void): void {\n        const callbacks = this.stateCallbacks.get(state);\n        if (callbacks) {\n            callbacks.push(callback);\n        }\n    }\n\n    changeState(newState: GameState): void {\n        if (this.currentState === newState) return;\n\n        oops.log.logBusiness(`🎮 状态切换: ${this.currentState} → ${newState}`);\n\n        // 🎯 记录前一个状态（仅在游戏进行状态时记录）\n        if (\n            this.currentState === GameState.SimpleMode ||\n            this.currentState === GameState.HardMode\n        ) {\n            this.previousGameState = this.currentState;\n        }\n\n        this.currentState = newState;\n\n        // 执行状态回调\n        const callbacks = this.stateCallbacks.get(newState);\n        if (callbacks) {\n            callbacks.forEach(callback => callback());\n        }\n    }\n\n    getCurrentState(): GameState {\n        return this.currentState;\n    }\n\n    isSimpleMode(): boolean {\n        return this.currentState === GameState.SimpleMode;\n    }\n\n    isHardMode(): boolean {\n        return this.currentState === GameState.HardMode;\n    }\n\n    /**\n     * 🎯 判断之前是否为困难模式（用于Win/GameOver状态时的模式判断）\n     */\n    wasPreviouslyHardMode(): boolean {\n        return this.previousGameState === GameState.HardMode;\n    }\n\n    /**\n     * 🎯 判断之前是否为简单模式（用于Win/GameOver状态时的模式判断）\n     */\n    wasPreviouslySimpleMode(): boolean {\n        return this.previousGameState === GameState.SimpleMode;\n    }\n\n    isPaused(): boolean {\n        return this.currentState === GameState.Paused || this.gameEntity.GameModel.pasue;\n    }\n\n    isGameOver(): boolean {\n        return this.currentState === GameState.GameOver;\n    }\n\n    isWin(): boolean {\n        return this.currentState === GameState.Win;\n    }\n\n    // =================== 关卡加载管理 ===================\n\n    async loadLevel(levelIndex: number, onEssentialLoaded?: () => void): Promise<void> {\n        try {\n            oops.log.logBusiness(`🎮 开始加载关卡 ${levelIndex}`);\n\n            // 🎯 记录当前关卡索引\n            this.currentLevelIndex = levelIndex;\n\n            // 加载关卡配置\n            this.currentLevelConfig = await configManager.getLevelConfig(levelIndex);\n            if (!this.currentLevelConfig) {\n                throw new Error(`关卡配置不存在: ${levelIndex}`);\n            }\n\n            // 加载简单模式物品资源\n            await this.loadEasyModeItems();\n\n            // 🔧 设置游戏模式（但输入仍禁用，等待物品生成完成）\n            oops.log.logBusiness('🎯 设置游戏状态为简单模式，但输入仍禁用');\n            this.changeState(GameState.SimpleMode);\n\n            // 加载场景和UI（wallSceneView现在包含在gameSceneView中）\n            await this.gameEntity.loadWallAndScene();\n            await this.loadGameUI(onEssentialLoaded);\n\n            // 🔧 调用游戏实体的beforeStartGame方法（此时状态已经是SimpleMode）\n            oops.log.logBusiness('🎮 调用GameEntity.beforeStartGame()');\n            this.gameEntity.beforeStartGame();\n\n            // 🎯 准备配置，但不自动创建道具（由startGame调用）\n\n            oops.log.logBusiness(`✅ 关卡 ${levelIndex} 加载完成`);\n        } catch (error) {\n            oops.log.logError(`❌ 关卡 ${levelIndex} 加载失败:`, error);\n            throw error;\n        }\n    }\n\n    private async loadEasyModeItems(): Promise<void> {\n        if (!this.currentLevelConfig) return;\n\n        const easyItems = this.currentLevelConfig.easyModeItems;\n        if (easyItems.length === 0) return;\n\n        // 🎯 获取关卡配置的物品路径\n        const itemPrefabPath = this.gameEntity.getItemPrefabPath();\n\n        // 获取去重的物品名称并加载\n        const uniqueItemNames = Array.from(new Set(easyItems.map(item => item.name)));\n        const prefabPaths = uniqueItemNames.map(name => `${itemPrefabPath}${name}`);\n\n        await simpleLoader.loadPrefabs(prefabPaths);\n        oops.log.logBusiness(`✅ 简单模式物品资源加载完成`);\n    }\n\n    private async loadGameUI(onUIReady?: () => void): Promise<void> {\n        return new Promise<void>(resolve => {\n            this.gameEntity.loadGameUIWithCallback(() => {\n                if (onUIReady) {\n                    onUIReady();\n                }\n                resolve();\n            });\n        });\n    }\n\n    getCurrentLevelConfig(): LevelConfig | null {\n        return this.currentLevelConfig;\n    }\n\n    getCurrentLevelIndex(): number {\n        return this.currentLevelIndex;\n    }\n\n    // =================== 输入管理 ===================\n\n    setInputEnabled(enabled: boolean): void {\n        this.inputEnabled = enabled;\n    }\n\n    isInputEnabled(): boolean {\n        return this.inputEnabled;\n    }\n\n    // =================== 物品生成管理 ===================\n\n    setRefillStrategy(strategy: any): void {\n        this.refillStrategy = { ...this.refillStrategy, ...strategy };\n    }\n\n    checkAndRefillItems(touchedItem?: Node): void {\n        // 🎯 使用统一的状态检查\n        if (this.isSimpleMode() || this.isGameOver() || this.isWin()) {\n            return;\n        }\n\n        // 简化的补充逻辑\n        const currentItems = this.gameEntity.GameModel.allItemsToPick.size;\n        const shouldRefill = currentItems < 5; // 简单阈值\n\n        if (shouldRefill && this.gameEntity.GameModel.allItemsToCreate.length > 0) {\n            const refillCount = Math.min(3, this.gameEntity.GameModel.allItemsToCreate.length);\n\n            for (let i = 0; i < refillCount; i++) {\n                const itemName = this.gameEntity.GameModel.allItemsToCreate.pop();\n                if (itemName) {\n                    const bornPos = new Vec3(0, 5, 0);\n                    this.gameEntity.createItemOnPos(bornPos, itemName, i + 1, true);\n                }\n            }\n        }\n    }\n\n    // =================== 游戏控制 ===================\n\n    initializeEasyModeItems(): void {\n        if (!this.currentLevelConfig) return;\n\n        this.gameEntity.GameModel.allItemsToCreate = [];\n        const easyModeItems = this.currentLevelConfig.easyModeItems;\n\n        for (const item of easyModeItems) {\n            for (let i = 0; i < item.count; i++) {\n                this.gameEntity.GameModel.allItemsToCreate.push(item.name);\n            }\n        }\n\n        this.gameEntity.GameModel.allItemsToCreate = Utils.arrRandomly(\n            this.gameEntity.GameModel.allItemsToCreate\n        );\n        oops.log.logBusiness(\n            `🎮 简单模式物品初始化完成: ${this.gameEntity.GameModel.allItemsToCreate.length} 个物品`\n        );\n    }\n\n    initializeHardModeItems(): void {\n        if (!this.currentLevelConfig) return;\n\n        this.gameEntity.GameModel.allItemsToCreate = [];\n        const hardModeItems = this.currentLevelConfig.hardModeItems;\n\n        for (const item of hardModeItems) {\n            for (let i = 0; i < item.count; i++) {\n                this.gameEntity.GameModel.allItemsToCreate.push(item.name);\n            }\n        }\n\n        this.gameEntity.GameModel.allItemsToCreate = Utils.arrRandomly(\n            this.gameEntity.GameModel.allItemsToCreate\n        );\n        oops.log.logBusiness(\n            `🔥 困难模式物品初始化完成: ${this.gameEntity.GameModel.allItemsToCreate.length} 个物品`\n        );\n    }\n\n    /**\n     * 🚀 分批创建困难模式道具 - 性能优化版本\n     */\n    createHardModeItemsBatched(): void {\n        const batchSize = 20; // 每批创建20个道具\n        const batchDelay = 50; // 每批间隔50ms\n        const allItems = this.gameEntity.GameModel.allItemsToCreate;\n        let currentBatch = 0;\n        const totalBatches = Math.ceil(allItems.length / batchSize);\n\n        oops.log.logBusiness(`🚀 开始分批创建 ${allItems.length} 个道具，共 ${totalBatches} 批`);\n\n        const createBatch = () => {\n            const startIndex = currentBatch * batchSize;\n            const endIndex = Math.min(startIndex + batchSize, allItems.length);\n\n            // 创建当前批次的道具\n            for (let i = startIndex; i < endIndex; i++) {\n                const itemName = allItems[i];\n                const bornPos = this.gameEntity.getRandomBornPos();\n                this.gameEntity.createItemOnPos(bornPos, itemName, i);\n            }\n\n            currentBatch++;\n\n            if (currentBatch < totalBatches) {\n                // 继续下一批\n                setTimeout(createBatch, batchDelay);\n            } else {\n                // 所有批次完成\n                oops.log.logBusiness(`🎉 所有道具创建完成，总计: ${allItems.length} 个`);\n                this.gameEntity.handleInputEnabling();\n            }\n        };\n\n        // 开始第一批\n        createBatch();\n    }\n\n    createInitialItemsInScene(): void {\n        if (this.gameEntity.GameModel.allItemsToCreate.length === 0) {\n            oops.log.logWarn('⚠️ 没有道具需要创建');\n            return;\n        }\n\n        const itemCount = this.gameEntity.GameModel.allItemsToCreate.length;\n        oops.log.logBusiness(`🎯 开始创建 ${itemCount} 个道具`);\n\n        // 🚀 性能优化：困难模式使用分批创建\n        if (this.isHardMode() && itemCount > 50) {\n            oops.log.logBusiness('🚀 困难模式大量道具，使用分批创建优化性能');\n            this.createHardModeItemsBatched();\n            return;\n        }\n\n        let itemIndex = 0;\n        const isNewPlayer = this.gameEntity.isSimpleMode() && smc.role?.isNewPlayer();\n        const isSimpleMode = this.gameEntity.isSimpleMode();\n\n        // 🎯 新手玩家：使用固定九宫格位置\n        let fixedPositions: Vec3[] = [];\n        if (isNewPlayer) {\n            fixedPositions = [\n                new Vec3(-2, 8, -2), // 1. 左上\n                new Vec3(0, 8, -2), // 2. 中上\n                new Vec3(2, 8, -2), // 3. 右上\n                new Vec3(-2, 8, 0), // 4. 左中\n                new Vec3(0, 8, 0), // 5. 中心\n                new Vec3(2, 8, 0), // 6. 右中\n                new Vec3(-2, 8, 2), // 7. 左下\n                new Vec3(0, 8, 2), // 8. 中下\n                new Vec3(2, 8, 2), // 9. 右下\n            ];\n            oops.log.logBusiness('🎓 新手玩家：使用固定九宫格位置创建道具');\n        }\n\n        while (this.gameEntity.GameModel.allItemsToCreate.length > 0) {\n            const itemName = this.gameEntity.GameModel.allItemsToCreate.pop();\n            if (itemName) {\n                let bornPos: Vec3;\n\n                if (isNewPlayer && itemIndex < fixedPositions.length) {\n                    // 🎯 新手玩家简单模式：使用固定位置\n                    bornPos = fixedPositions[itemIndex];\n                } else {\n                    // 🎲 其他情况：使用随机位置（老玩家简单模式 + 所有困难模式）\n                    bornPos = this.generateRandomPosition(isSimpleMode);\n                }\n\n                // 创建道具（新手不随机旋转）\n                this.gameEntity.createItemOnPos(bornPos, itemName, itemIndex++, !isNewPlayer);\n            }\n        }\n\n        oops.log.logBusiness(`🎉 所有道具创建完成，总计: ${itemIndex} 个`);\n\n        // 🎯 新手玩家：启动引导系统\n        if (isNewPlayer) {\n            this.startNewPlayerGuide(itemIndex);\n        }\n    }\n\n    /**\n     * 🎲 生成随机位置\n     */\n    private generateRandomPosition(isSimpleMode: boolean): Vec3 {\n        if (isSimpleMode) {\n            // 简单模式：较小的随机范围\n            const radius = 3;\n            const angle = Math.random() * Math.PI * 2;\n            const distance = Math.random() * radius;\n\n            return new Vec3(\n                Math.cos(angle) * distance,\n                8 + Math.random() * 2, // 高度在8-10之间\n                Math.sin(angle) * distance\n            );\n        } else {\n            // 困难模式：较大的随机范围\n            return new Vec3(\n                (Math.random() - 0.5) * 10, // x: -5 到 5\n                Math.random() * 3 + 2, // y: 2 到 5\n                (Math.random() - 0.5) * 10 // z: -5 到 5\n            );\n        }\n    }\n\n    /**\n     * 🎯 启动新手引导系统\n     */\n    private startNewPlayerGuide(itemCount: number): void {\n        setTimeout(() => {\n            const guideSteps = Math.min(itemCount, 9);\n\n            // 🎯 设置引导的最后一步\n            if (smc.guide?.GuideModel) {\n                smc.guide.GuideModel.last = guideSteps;\n                oops.log.logBusiness(`🎓 设置引导最后一步: ${guideSteps}`);\n            }\n\n            // 为前9个道具添加引导组件\n            for (let i = 0; i < guideSteps; i++) {\n                this.addGuideComponentToItem(i, i + 1);\n            }\n        }, 100); // 延迟确保道具创建完成\n    }\n\n    /**\n     * 🎯 为道具添加引导组件\n     */\n    private addGuideComponentToItem(itemIndex: number, guideStep: number): void {\n        const itemNode = this.gameEntity.GameModel.allItemsToPick.get(itemIndex);\n        if (!itemNode) {\n            oops.log.logWarn(`⚠️ 道具节点未找到: ${itemIndex}`);\n            return;\n        }\n\n        let guideComp = itemNode.getComponent(GuideView3DItemComp);\n        if (!guideComp) {\n            guideComp = itemNode.addComponent(GuideView3DItemComp);\n        }\n\n        if (guideComp && (guideComp as any).setStep) {\n            (guideComp as any).setStep(guideStep);\n            oops.log.logBusiness(`🎓 为道具 ${itemIndex} 添加引导步骤: ${guideStep}`);\n        }\n    }\n\n    // =================== 清理 ===================\n\n    destroy(): void {\n        this.currentLevelConfig = null;\n        this.stateCallbacks.clear();\n    }\n}\n"]}