资源都放到这个bundle，模块化，模块下细分文件夹 越大的文件越要详细划分，方便加载后可以统一释放

1.common 下有audio、prefab、texture等文件夹，公用的资源，不知道放哪里的资源可以放这里.
2.大厅 Hall 下有audio、prefab、texture等文件夹，每个文件夹下放的都是大厅的资源
3.游戏 GameCommon 下有audio、prefab、texture等文件夹，每个文件夹下放的都是游戏关卡公用的资源，进入每个关卡前。都需要先加载这里的部分资源
4.关卡例如Foods 下有audio、prefab、texture等文件夹，每个文件夹下放的都是Foods关卡的资源，进入关卡后，加载这里面的资源
5.后续新增关卡 例如Foods2 也这么来，每个关卡一个文件夹，里面放该关卡的资源
6.boot 文件夹比较特殊，里面放的是进入游戏后，第一个界面需要用到的资源，这部分资源加载后不会释放，所以尽量少放资源

这样设计目的是，部分关卡在关闭时，可较为简单的卸载此模块资源 oops.res.releaseDir("Foods")


