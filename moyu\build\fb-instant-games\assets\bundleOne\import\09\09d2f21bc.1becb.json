[1, ["e5LUoqx3RAr41dA5QrbKMj", "25LzlW3n1PAoWx+6H1sH4s@8a36e"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 1471040202, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 32016, "length": 5946, "count": 2973, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 32016, "count": 667, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.3648976683616638, -1.250653862953186, -0.23272861540317535], "maxPosition", 8, [1, 0.23658335208892822, 0.39899876713752747, 0.06918396800756454]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_14"], [3, "日式寿司_14", [[4, 1, -2, [0, "61ly1mPDdIS6WLz+YB5Shu"], [0], [5, true, true], 1], [6, 4, -3, [0, "39A1sNgXNC24AhmcfqlHXK"]], [7, 0.2850208729505539, 1.393813282251358, -4, [0, "4dylpyQt5LSLAAwu3YyE1Q"], [1, -0.068135, -0.519636, 0.016697999089956284]]], [8, "baaQL4LsBOC6RKby3DRLCh", null, null, null, -1, 0], [1, 3.665, 0, 3.788]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]