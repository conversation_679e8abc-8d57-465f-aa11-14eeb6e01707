[1, ["195386dfc@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [[[{"name": "t标题底", "rect": {"x": 382, "y": 3, "width": 116, "height": 73}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 116, "height": 73}, "rotated": true, "capInsets": [52, 51, 56, 14], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "loading", "rect": {"x": 308, "y": 314, "width": 138, "height": 138}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 138, "height": 138}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "icon_loading_bar", "rect": {"x": 3, "y": 3, "width": 549, "height": 47}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 549, "height": 47}, "rotated": true, "capInsets": [278, 25, 269, 19], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "t底", "rect": {"x": 308, "y": 167, "width": 141, "height": 106}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 141, "height": 106}, "rotated": true, "capInsets": [57, 44, 63, 46], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "0小按钮", "rect": {"x": 308, "y": 3, "width": 158, "height": 68}, "offset": {"x": 0, "y": 0.5}, "originalSize": {"width": 158, "height": 69}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "广告弹窗", "rect": {"x": 3, "y": 558, "width": 460, "height": 461}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 460, "height": 461}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "mask", "rect": {"x": 417, "y": 125, "width": 20, "height": 20}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 20, "height": 20}, "rotated": false, "capInsets": [10, 10, 10, 10], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "small_sprite", "rect": {"x": 417, "y": 151, "width": 2, "height": 2}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 2, "height": 2}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "panel_loading_bottom_frame", "rect": {"x": 420, "y": 255, "width": 63, "height": 40}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 63, "height": 40}, "rotated": false, "capInsets": [28, 19, 28, 19], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "logo", "rect": {"x": 56, "y": 3, "width": 536, "height": 246}, "offset": {"x": -1, "y": 1}, "originalSize": {"width": 538, "height": 248}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "google", "rect": {"x": 382, "y": 125, "width": 29, "height": 29}, "offset": {"x": -0.5, "y": 0.5}, "originalSize": {"width": 32, "height": 32}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "cat", "rect": {"x": 420, "y": 159, "width": 90, "height": 69}, "offset": {"x": 0, "y": 0.5}, "originalSize": {"width": 90, "height": 70}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]]]