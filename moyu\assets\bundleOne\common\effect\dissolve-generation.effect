// Copyright (c) 2017-2022 Xiamen Yaji Software Co., Ltd.

// 🎬 Dissolve生成效果 (基于legacy格式，符合项目现有架构)

CCEffect %{
  techniques:
  - name: opaque
    passes:
    - vert: standard-vs
      frag: standard-fs
      properties: &props
        tilingOffset:                     { value: [1.0, 1.0, 0.0, 0.0] }
        mainColor:                        { value: [1.0, 1.0, 1.0, 1.0], target: albedo, linear: true, editor: { displayName: Albedo, type: color } }
        albedoScale:                      { value: [1.0, 1.0, 1.0], target: albedoScaleAndCutoff.xyz }
        alphaThreshold:                   { value: 0.5, target: albedoScaleAndCutoff.w, editor: { parent: USE_ALPHA_TEST, slide: true, range: [0, 1.0], step: 0.001 } }
        occlusion:                        { value: 0.0, target: pbrParams.x, editor: { slide: true, range: [0, 1.0], step: 0.001 } }
        roughness:                        { value: 0.5, target: pbrParams.y, editor: { slide: true, range: [0, 1.0], step: 0.001 } }
        metallic:                         { value: 0.0, target: pbrParams.z, editor: { slide: true, range: [0, 1.0], step: 0.001 } }
        specularIntensity:                { value: 0.5, target: pbrParams.w, editor: { slide: true, range: [0.0, 1.0], step: 0.001 } }
        emissive:                         { value: [0.0, 0.0, 0.0, 1.0], linear: true, editor: { type: color } }
        emissiveScale:                    { value: [1.0, 1.0, 1.0], target: emissiveScaleParam.xyz }
        normalStrength:                   { value: 1.0, target: emissiveScaleParam.w, editor: { parent: USE_NORMAL_MAP, slide: true, range: [0, 5.0], step: 0.001 } }
        
        # 🎬 Dissolve效果参数
        dissolveProgress:                 { value: 0.0, target: dissolveParams.x, editor: { slide: true, range: [0, 1.0], step: 0.001, displayName: "生成进度" } }
        dissolveEdgeWidth:                { value: 0.1, target: dissolveParams.y, editor: { slide: true, range: [0, 0.5], step: 0.001, displayName: "边缘宽度" } }
        dissolveEdgeColor:                { value: [0.0, 1.0, 0.0, 1.0], target: dissolveColor, linear: true, editor: { type: color, displayName: "边缘颜色" } }
        
        albedoMap:                        { value: grey, editor: { displayName: AlbedoMap } }
        normalMap:                        { value: normal }
        pbrMap:                           { value: grey }
        occlusionMap:                     { value: white }
        emissiveMap:                      { value: grey }
        dissolveNoise:                    { value: white, editor: { displayName: "溶解噪声贴图" } }
    - &forward-add
      vert: standard-vs
      frag: standard-fs
      phase: forward-add
      propertyIndex: 0
      embeddedMacros: { CC_FORWARD_ADD: true }
      depthStencilState:
        depthFunc: equal
        depthTest: true
        depthWrite: false
      blendState:
        targets:
        - blend: true
          blendSrc: one
          blendDst: one
          blendSrcAlpha: zero
          blendDstAlpha: one
    - &shadow-caster
      vert: shadow-caster-vs
      frag: shadow-caster-fs
      phase: shadow-caster
      propertyIndex: 0
      rasterizerState:
        cullMode: front
      properties:
        tilingOffset:   { value: [1.0, 1.0, 0.0, 0.0] }
        mainColor:      { value: [1.0, 1.0, 1.0, 1.0], target: albedo, editor: { displayName: Albedo, type: color } }
        albedoScale:    { value: [1.0, 1.0, 1.0], target: albedoScaleAndCutoff.xyz }
        alphaThreshold: { value: 0.5, target: albedoScaleAndCutoff.w, editor: { parent: USE_ALPHA_TEST } }
        albedoMap:      { value: grey, editor: { displayName: AlbedoMap } }
        dissolveProgress: { value: 0.0, target: dissolveParams.x }
        dissolveNoise:  { value: white }
    - &reflect-map
      vert: standard-vs
      frag: reflect-map-fs
      phase: reflect-map
      propertyIndex: 0
    - &planar-shadow
      vert: planar-shadow-vs
      frag: planar-shadow-fs
      phase: planar-shadow
      propertyIndex: 0
      depthStencilState:
        depthTest: true
        depthWrite: false
        stencilTestFront: true
        stencilFuncFront: not_equal
        stencilPassOpFront: replace
        stencilRef: 0x80 # only use the leftmost bit
        stencilReadMask: 0x80
        stencilWriteMask: 0x80
      blendState:
        targets:
        - blend: true
          blendSrc: src_alpha
          blendDst: one_minus_src_alpha
          blendDstAlpha: one_minus_src_alpha
    - &deferred
      vert: standard-vs
      frag: standard-fs
      pass: gbuffer
      phase: gbuffer
      embeddedMacros: { CC_PIPELINE_TYPE: 1 }
      propertyIndex: 0
  - name: transparent
    passes:
    - vert: standard-vs
      frag: standard-fs
      embeddedMacros: { CC_FORCE_FORWARD_SHADING: true }
      depthStencilState: &d1
        depthTest: true
        depthWrite: false
      blendState: &b1
        targets:
        - blend: true
          blendSrc: src_alpha
          blendDst: one_minus_src_alpha
          blendDstAlpha: one_minus_src_alpha
      properties: *props
    - *forward-add
    - *shadow-caster
    - *planar-shadow
    - &deferred-forward
      vert: standard-vs
      frag: standard-fs
      phase: deferred-forward
      embeddedMacros: { CC_PIPELINE_TYPE: 0 }
      propertyIndex: 0
      depthStencilState: *d1
      blendState: *b1
}%

CCProgram shared-ubos %{
  uniform Constants {
    vec4 tilingOffset;
    vec4 albedo;
    vec4 albedoScaleAndCutoff;
    vec4 pbrParams;
    vec4 emissive;
    vec4 emissiveScaleParam;
    vec4 dissolveParams; // x: progress, y: edgeWidth, z: unused, w: unused
    vec4 dissolveColor;
  };
}%

CCProgram macro-remapping %{
  // ui displayed macros
  #pragma define-meta HAS_SECOND_UV
  #pragma define-meta USE_TWOSIDE
  #pragma define-meta USE_VERTEX_COLOR

  #define CC_SURFACES_USE_SECOND_UV HAS_SECOND_UV
  #define CC_SURFACES_USE_TWO_SIDED USE_TWOSIDE
  #define CC_SURFACES_USE_VERTEX_COLOR USE_VERTEX_COLOR

  // depend on UI macros
#if USE_NORMAL_MAP
  #define CC_SURFACES_USE_TANGENT_SPACE 1
#endif
}%

CCProgram surface-vertex %{
  #define CC_SURFACES_VERTEX_MODIFY_UV
  void SurfacesVertexModifyUV(inout SurfacesStandardVertexIntermediate In)
  {
    In.texCoord = In.texCoord * tilingOffset.xy + tilingOffset.zw;
  #if CC_SURFACES_USE_SECOND_UV
    In.texCoord1 = In.texCoord1 * tilingOffset.xy + tilingOffset.zw;
  #endif
  }
}%

CCProgram surface-fragment %{
  #if USE_ALBEDO_MAP
    uniform sampler2D albedoMap;
    #pragma define-meta ALBEDO_UV options([v_uv, v_uv1])
  #endif
  #if USE_NORMAL_MAP
    uniform sampler2D normalMap;
    #pragma define-meta NORMAL_UV options([v_uv, v_uv1])
  #endif
  #pragma define-meta DEFAULT_UV options([v_uv, v_uv1])
  #if USE_PBR_MAP
    uniform sampler2D pbrMap;
  #endif
  #if USE_OCCLUSION_MAP
    uniform sampler2D occlusionMap;
  #endif
  #if USE_EMISSIVE_MAP
    uniform sampler2D emissiveMap;
    #pragma define-meta EMISSIVE_UV options([v_uv, v_uv1])
  #endif
  
  // 🎬 Dissolve效果贴图
  uniform sampler2D dissolveNoise;

  #pragma define OCCLUSION_CHANNEL          r
  #pragma define ROUGHNESS_CHANNEL          g
  #pragma define METALLIC_CHANNEL           b
  #pragma define SPECULAR_INTENSITY_CHANNEL a

  #if USE_ALPHA_TEST
    #pragma define-meta ALPHA_TEST_CHANNEL options([a, r])
  #endif

  #define CC_SURFACES_FRAGMENT_MODIFY_BASECOLOR_AND_TRANSPARENCY
  vec4 SurfacesFragmentModifyBaseColorAndTransparency()
  {
    vec4 baseColor = albedo;

    #if USE_VERTEX_COLOR
      baseColor.rgb *= SRGBToLinear(FSInput_vertexColor.rgb); // use linear
      baseColor.a *= FSInput_vertexColor.a;
    #endif

    #if USE_ALBEDO_MAP
      vec4 texColor = texture(albedoMap, ALBEDO_UV);
      texColor.rgb = SRGBToLinear(texColor.rgb);
      baseColor *= texColor;
    #endif

    // 🎬 Dissolve效果核心逻辑
    vec4 noiseColor = texture(dissolveNoise, DEFAULT_UV);
    float noiseValue = noiseColor.r; // 使用红色通道作为噪声值
    
    float dissolveProgress = dissolveParams.x;
    float dissolveEdgeWidth = dissolveParams.y;
    
    // 计算溶解阈值
    float dissolveThreshold = 1.0 - dissolveProgress;
    
    // 边缘检测
    float edgeStart = dissolveThreshold - dissolveEdgeWidth;
    float edgeEnd = dissolveThreshold;
    
    // 丢弃像素（实现溶解效果）
    if (noiseValue < dissolveThreshold) {
      discard;
    }
    
    // 边缘发光效果
    if (noiseValue >= edgeStart && noiseValue <= edgeEnd && dissolveEdgeWidth > 0.0) {
      float edgeFactor = 1.0 - smoothstep(edgeStart, edgeEnd, noiseValue);
      baseColor.rgb = mix(baseColor.rgb, dissolveColor.rgb, edgeFactor * dissolveColor.a);
    }

    #if USE_ALPHA_TEST
      if (baseColor.ALPHA_TEST_CHANNEL < albedoScaleAndCutoff.w) discard;
    #endif

    baseColor.rgb *= albedoScaleAndCutoff.xyz;
    return baseColor;
  }

  #define CC_SURFACES_FRAGMENT_ALPHA_CLIP_ONLY
  void SurfacesFragmentAlphaClipOnly()
  {
    #if USE_ALPHA_TEST
      float alpha = albedo.ALPHA_TEST_CHANNEL;
      #if USE_VERTEX_COLOR
        alpha *= FSInput_vertexColor.a;
      #endif
      #if USE_ALBEDO_MAP
        alpha = texture(albedoMap, ALBEDO_UV).ALPHA_TEST_CHANNEL;
      #endif

      if (alpha < albedoScaleAndCutoff.w) discard;
    #endif
    
    // 在阴影渲染中也应用dissolve效果
    vec4 noiseColor = texture(dissolveNoise, DEFAULT_UV);
    float noiseValue = noiseColor.r;
    float dissolveProgress = dissolveParams.x;
    float dissolveThreshold = 1.0 - dissolveProgress;
    
    if (noiseValue < dissolveThreshold) {
      discard;
    }
  }

  #define CC_SURFACES_FRAGMENT_MODIFY_WORLD_NORMAL
  vec3 SurfacesFragmentModifyWorldNormal()
  {
    vec3 normal = FSInput_worldNormal;
    #if USE_NORMAL_MAP
      vec3 nmmp = texture(normalMap, NORMAL_UV).xyz - vec3(0.5);
      normal = CalculateNormalFromTangentSpace(nmmp, emissiveScaleParam.w, normalize(normal.xyz), normalize(FSInput_worldTangent), FSInput_mirrorNormal);
    #endif

    return normalize(normal);
  }

  #define CC_SURFACES_FRAGMENT_MODIFY_EMISSIVE
  vec3 SurfacesFragmentModifyEmissive()
  {
    vec3 emissive = emissive.rgb;
    #if USE_EMISSIVE_MAP
      emissive = SRGBToLinear(texture(emissiveMap, EMISSIVE_UV).rgb);
    #endif
    return emissive * emissiveScaleParam.xyz;
  }

  #define CC_SURFACES_FRAGMENT_MODIFY_PBRPARAMS
  vec4 SurfacesFragmentModifyPBRParams()
  {
    vec4 pbr = pbrParams;
    pbr.x = 1.0;
    #if USE_PBR_MAP
      vec4 res = texture(pbrMap, DEFAULT_UV);
      pbr.x = mix(1.0, res.OCCLUSION_CHANNEL, pbrParams.x);
      pbr.y *= res.ROUGHNESS_CHANNEL;
      pbr.z *= res.METALLIC_CHANNEL;
      pbr.w *= res.SPECULAR_INTENSITY_CHANNEL;
    #endif
    #if USE_OCCLUSION_MAP
      pbr.x = mix(1.0, texture(occlusionMap, DEFAULT_UV).OCCLUSION_CHANNEL, pbrParams.x);
    #endif

    return pbr;
  }
}%

CCProgram standard-vs %{
  precision highp float;

  // 1. surface internal macros, for technique usage or remapping some user (material) macros to surface internal macros
  #include <macro-remapping>
  #include <surfaces/effect-macros/common-macros>

  // 2. common include with corresponding shader stage, include before surface functions
  #include <surfaces/includes/common-vs>

  // 3. user surface functions that can use user (effect) parameters (ubo Constants)
  //    see surfaces/default-functions/xxx.chunk
  #include <shared-ubos>
  #include <surface-vertex>

  // 4. surface include with corresponding shader stage and shading-model (optional)
  #include <surfaces/includes/standard-vs>

  // 5. shader entry with corresponding shader stage and technique usage/type
  #include <shading-entries/main-functions/render-to-scene/vs>
}%

CCProgram shadow-caster-vs %{
  precision highp float;
  #include <macro-remapping>
  #include <surfaces/effect-macros/render-to-shadowmap>
  #include <surfaces/includes/common-vs>
  #include <shared-ubos>
  #include <surface-vertex>
  #include <shading-entries/main-functions/render-to-shadowmap/vs>
}%

CCProgram planar-shadow-vs %{
  precision highp float;
  #include <macro-remapping>
  #include <surfaces/effect-macros/render-planar-shadow>
  #include <surfaces/includes/common-vs>
  #include <shared-ubos>
  #include <surface-vertex>
  #include <shading-entries/main-functions/render-planar-shadow/vs>
}%

CCProgram standard-fs %{
  // shading-model : standard
  // lighting-model : standard (isotropy / anisotropy pbr)
  // shader stage : fs
  // technique usage/type : render-to-scene

  precision highp float;
  // 1. surface internal macros, for technique usage or remapping some user (material) macros to surface internal macros
  #include <macro-remapping>
  #include <surfaces/effect-macros/common-macros>

  // 2. common include with corresponding shader stage, include before surface functions
  #include <surfaces/includes/common-fs>

  // 3. user surface functions that can use user (effect) parameters (ubo Constants)
  //    see surfaces/default-functions/xxx.chunk
  #include <shared-ubos>
  #include <surface-fragment>

  // 4. lighting-model (optional)
  #include <lighting-models/includes/standard>

  // 5. surface include with corresponding shader stage and shading-model (optional)
  #include <surfaces/includes/standard-fs>

  // 6. shader entry with corresponding shader stage and technique usage/type
  #include <shading-entries/main-functions/render-to-scene/fs>
}%

CCProgram shadow-caster-fs %{
  precision highp float;
  #include <macro-remapping>
  #include <surfaces/effect-macros/render-to-shadowmap>
  #include <surfaces/includes/common-fs>
  #include <shared-ubos>
  #include <surface-fragment>
  #include <shading-entries/main-functions/render-to-shadowmap/fs>
}%

CCProgram planar-shadow-fs %{
  precision highp float;
  #include <macro-remapping>
  #include <surfaces/effect-macros/render-planar-shadow>
  #include <surfaces/includes/common-fs>
  #include <shared-ubos>
  #include <surface-fragment>
  #include <shading-entries/main-functions/render-planar-shadow/fs>
}%

CCProgram reflect-map-fs %{
  precision highp float;
  #include <macro-remapping>
  #include <surfaces/effect-macros/common-macros>
  #include <surfaces/includes/common-fs>
  #include <shared-ubos>
  #include <surface-fragment>
  #include <lighting-models/includes/standard>
  #include <surfaces/includes/standard-fs>
  #include <shading-entries/main-functions/render-to-reflectmap/fs>
}% 