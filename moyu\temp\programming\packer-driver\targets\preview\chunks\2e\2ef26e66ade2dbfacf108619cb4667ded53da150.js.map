{"version": 3, "sources": ["file:///F:/moyuproject/moyu/assets/script/Main.ts"], "names": ["_decorator", "Node", "oops", "Root", "ecs", "Initialize", "Environment", "Platform", "ShareConfig", "ccclass", "property", "Main", "type", "tooltip", "iniStart", "log", "start", "isProduction", "environment", "PRODUCTION_FACEBOOK", "initializeCoreModules", "smc", "initialize", "getEntity", "platform", "FACEBOOK", "FBI<PERSON>antManager", "FacebookGameEvents", "Promise", "all", "fbInstantManager", "facebookGameEvents", "loadBusinessModulesLater", "loadCriticalModules", "requestIdleCallback", "loadNonCriticalModules", "Role", "SimpleSceneManager", "role", "sceneMgr", "logBusiness", "error", "logError", "Account", "Guide", "CameraEntity", "profiler", "showStats", "assetManager", "downloader", "maxRequestsPerFrame", "account", "guide", "camera", "initGui", "UIConfigData", "gui", "init", "initEcsSystem", "run", "initializeFacebook", "load", "initial", "toast", "maxRetries", "initSuccess", "i", "fbInit", "onPlayerFirstEntry", "fbData", "getFacebookLoginData", "facebookId", "Error", "log<PERSON>arn", "retry<PERSON><PERSON><PERSON>", "resolve", "setTimeout", "errorMsg"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;;AACZC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,G,iBAAAA,G;;AAOAC,MAAAA,U,iBAAAA,U;;AAGAC,MAAAA,W,iBAAAA,W;AAAaC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,W,iBAAAA,W;;;;;;AAnBhC;AACA;AACA;AACA;AACA;AACA;;;;;OAgBM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;sBAGjBW,I,WADZF,OAAO,CAAC,MAAD,C,UAEHC,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAEX,IADA;AAENY,QAAAA,OAAO,EAAE;AAFH,OAAD,C,2BAFb,MACaF,IADb;AAAA;AAAA,wBAC+B;AAAA;AAAA;;AAAA;AAAA;;AAOjBG,QAAAA,QAAQ,GAAG;AACjB;AACA;AAAA;AAAA,4BAAKC,GAAL,CAASC,KAAT,CAAe,SAAf,EAFiB,CAIjB;AAEA;;AACA,cAAMC,YAAY,GAAG;AAAA;AAAA,0CAAYA,YAAjC;;AAEA,cAAI;AAAA;AAAA,0CAAYC,WAAZ,KAA4B;AAAA;AAAA,0CAAYC,mBAA5C,EAAiE,CAC7D;AACH,WAFD,MAEO,IAAIF,YAAJ,EAAkB,CACrB;AACH,WAFM,MAEA,CACH;AACH,WAfgB,CAiBjB;;;AACA,eAAKG,qBAAL;AACH;AAED;;;AACcA,QAAAA,qBAAqB,GAAG;AAAA;;AAAA;AAClC,gBAAM;AAAEC,cAAAA;AAAF,uDAAN,CADkC,CAGlC;;AACAA,YAAAA,GAAG,CAACC,UAAJ,GAAiB;AAAA;AAAA,4BAAIC,SAAJ;AAAA;AAAA,yCAAjB,CAJkC,CAMlC;;AACA,gBAAI;AAAA;AAAA,4CAAYC,QAAZ,KAAyB;AAAA;AAAA,sCAASC,QAAtC,EAAgD;AAC5C,kBAAM,CAAC;AAAEC,gBAAAA;AAAF,eAAD,EAAuB;AAAEC,gBAAAA;AAAF,eAAvB,UAAuDC,OAAO,CAACC,GAAR,CAAY,sEAAZ,CAA7D;AAKAR,cAAAA,GAAG,CAACS,gBAAJ,GAAuB;AAAA;AAAA,8BAAIP,SAAJ,CAAgCG,gBAAhC,CAAvB;AACAL,cAAAA,GAAG,CAACU,kBAAJ,GAAyB;AAAA;AAAA,8BAAIR,SAAJ,CAAkCI,kBAAlC,CAAzB;AACH,aAfiC,CAiBlC;;;AACA,YAAA,KAAI,CAACK,wBAAL;AAlBkC;AAmBrC;AAED;;;AACcA,QAAAA,wBAAwB,GAAG;AAAA;;AAAA;AACrC;AACA,YAAA,MAAI,CAACC,mBAAL,GAFqC,CAIrC;;;AACAC,YAAAA,mBAAmB,iCAAC,aAAY;AAC5B,oBAAM,MAAI,CAACC,sBAAL,EAAN;AACH,aAFkB,EAAnB;AALqC;AAQxC;AAED;;;AACcF,QAAAA,mBAAmB,GAAG;AAAA;AAChC,gBAAI;AACA,kBAAM,CAAC;AAAEZ,gBAAAA;AAAF,eAAD,EAAU;AAAEe,gBAAAA;AAAF,eAAV,EAAoB;AAAEC,gBAAAA;AAAF,eAApB,UAAoDT,OAAO,CAACC,GAAR,CAAY,2GAAZ,CAA1D,CADA,CAOA;;AACAR,cAAAA,GAAG,CAACiB,IAAJ,GAAW;AAAA;AAAA,8BAAIf,SAAJ,CAAoBa,IAApB,CAAX;AACAf,cAAAA,GAAG,CAACkB,QAAJ,GAAe;AAAA;AAAA,8BAAIhB,SAAJ,CAAkCc,kBAAlC,CAAf;AAEA;AAAA;AAAA,gCAAKtB,GAAL,CAASyB,WAAT,CAAqB,YAArB;AACH,aAZD,CAYE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAK1B,GAAL,CAAS2B,QAAT,CAAkB,aAAlB,EAAiCD,KAAjC;AACH;AAf+B;AAgBnC;AAED;;;AACcN,QAAAA,sBAAsB,GAAG;AAAA;AACnC,gBAAI;AACA,kBAAM,CAAC;AAAEd,gBAAAA;AAAF,eAAD,EAAU;AAAEsB,gBAAAA;AAAF,eAAV,EAAuB;AAAEC,gBAAAA;AAAF,eAAvB,EAAkC;AAAEC,gBAAAA;AAAF,eAAlC,UAA4DjB,OAAO,CAACC,GAAR,CAAY,gJAAZ,CAAlE,CADA,CAQA;AACA;;AACA,kBAAI,CAAC;AAAA;AAAA,8CAAYZ,YAAjB,EAA+B;AAC3B,oBAAM;AAAE6B,kBAAAA;AAAF,0CAA4B,IAA5B,CAAN;AACAA,gBAAAA,QAAQ,CAACC,SAAT;AACH,eAbD,CAeA;;;AACA,kBAAM;AAAEC,gBAAAA;AAAF,wCAAgC,IAAhC,CAAN;AACAA,cAAAA,YAAY,CAACC,UAAb,CAAwBC,mBAAxB,GAA8C,EAA9C,CAjBA,CAiBkD;AAElD;;AACA7B,cAAAA,GAAG,CAAC8B,OAAJ,GAAc;AAAA;AAAA,8BAAI5B,SAAJ,CAAuBoB,OAAvB,CAAd;AACAtB,cAAAA,GAAG,CAAC+B,KAAJ,GAAY;AAAA;AAAA,8BAAI7B,SAAJ,CAAqBqB,KAArB,CAAZ;AACAvB,cAAAA,GAAG,CAACgC,MAAJ,GAAa;AAAA;AAAA,8BAAI9B,SAAJ,CAA4BsB,YAA5B,CAAb;AAEA;AAAA;AAAA,gCAAK9B,GAAL,CAASyB,WAAT,CAAqB,aAArB;AACH,aAzBD,CAyBE,OAAOC,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAK1B,GAAL,CAAS2B,QAAT,CAAkB,cAAlB,EAAkCD,KAAlC;AACH;AA5BkC;AA6BtC;;AAESa,QAAAA,OAAO,GAAG;AAChB;AACApB,UAAAA,mBAAmB,iCAAC,aAAY;AAC5B,gBAAM;AAAEqB,cAAAA;AAAF,wDAAN;AACA;AAAA;AAAA,8BAAKC,GAAL,CAASC,IAAT,CAAcF,YAAd;AACH,WAHkB,EAAnB;AAIH;;AAESG,QAAAA,aAAa,GAAG,CACtB;AACH;;AAEeC,QAAAA,GAAG,GAAG;AAAA;;AAAA;AAClB,gBAAI;AACA;AACA,kBAAI;AAAA;AAAA,8CAAYnC,QAAZ,KAAyB;AAAA;AAAA,wCAASC,QAAtC,EAAgD;AAC5C,sBAAM,MAAI,CAACmC,kBAAL,EAAN;AACH,eAJD,CAMA;;;AACA,kBAAM;AAAEvC,gBAAAA;AAAF,0DAAN;AACAA,cAAAA,GAAG,CAACC,UAAJ,CAAeuC,IAAf,CAAoB,MAAI,CAACC,OAAzB;AACH,aATD,CASE,OAAOrB,KAAP,EAAc;AACZ;AAAA;AAAA,gCAAK1B,GAAL,CAAS2B,QAAT,CAAkB,UAAlB,EAA8BD,KAA9B,EADY,CAGZ;;AACA,kBAAI;AAAA;AAAA,8CAAYvB,WAAZ,KAA4B;AAAA;AAAA,8CAAYC,mBAA5C,EAAiE;AAC7D;AAAA;AAAA,kCAAKqC,GAAL,CAASO,KAAT,CAAe,sBAAf;AACA;AACH;;AACD;AAAA;AAAA,gCAAKP,GAAL,CAASO,KAAT,CAAe,gBAAf;AACH;AAnBiB;AAoBrB;AAED;;;AACcH,QAAAA,kBAAkB,GAAG;AAAA;AAC/B,gBAAM;AAAEvC,cAAAA;AAAF,wDAAN;AACA,gBAAM2C,UAAU,GAAG,CAAnB;AACA,gBAAIC,WAAW,GAAG,KAAlB;;AAH+B,0CAKM;AACjC,kBAAI;AACA;AAAA;AAAA,kCAAKlD,GAAL,CAASyB,WAAT,2DAAyC0B,CAAC,GAAG,CAA7C,UAAkDF,UAAlD;AAEA,sBAAM3C,GAAG,CAACS,gBAAJ,CAAqBqC,MAArB,EAAN;AACA,sBAAM9C,GAAG,CAACU,kBAAJ,CAAuBqC,kBAAvB,EAAN,CAJA,CAMA;;AACA,oBAAMC,MAAM,GAAGhD,GAAG,CAACS,gBAAJ,CAAqBwC,oBAArB,EAAf;;AACA,oBAAI,CAACD,MAAD,IAAW,CAACA,MAAM,CAACE,UAAvB,EAAmC;AAC/B,wBAAM,IAAIC,KAAJ,CAAU,sBAAV,CAAN;AACH;;AAED;AAAA;AAAA,kCAAKzD,GAAL,CAASyB,WAAT,CAAqB,uBAArB,EAA8C6B,MAAM,CAACE,UAArD;AACAN,gBAAAA,WAAW,GAAG,IAAd;AAbA;AAeH,eAfD,CAeE,OAAOxB,KAAP,EAAc;AACZ;AAAA;AAAA,kCAAK1B,GAAL,CAAS0D,OAAT,yEAAyCP,CAAC,GAAG,CAA7C,UAAkDF,UAAlD,SAAkEvB,KAAlE;;AAEA,oBAAIyB,CAAC,GAAGF,UAAU,GAAG,CAArB,EAAwB;AACpB,sBAAMU,UAAU,GAAG,CAACR,CAAC,GAAG,CAAL,IAAU,IAA7B;AACA;AAAA;AAAA,oCAAKnD,GAAL,CAASyB,WAAT,0BAA6BkC,UAA7B;AACA,wBAAM,IAAI9C,OAAJ,CAAY+C,OAAO,IAAIC,UAAU,CAACD,OAAD,EAAUD,UAAV,CAAjC,CAAN;AACH;AACJ;AACJ,aA9B8B;;AAK/B,iBAAK,IAAIR,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,UAApB,EAAgCE,CAAC,EAAjC;AAAA,kCAeQ;AAfR;;AA2BA,gBAAI,CAACD,WAAL,EAAkB;AACd,kBAAMY,QAAQ,GAAG,iBAAjB;AACA;AAAA;AAAA,gCAAK9D,GAAL,CAAS2B,QAAT,CAAkBmC,QAAlB;;AAEA,kBAAI;AAAA;AAAA,8CAAY3D,WAAZ,KAA4B;AAAA;AAAA,8CAAYC,mBAA5C,EAAiE;AAC7D;AAAA;AAAA,kCAAKqC,GAAL,CAASO,KAAT,CAAe,sBAAf;AACA;AACH,eAHD,MAGO;AACH;AAAA;AAAA,kCAAKhD,GAAL,CAAS0D,OAAT,CAAiB,uBAAjB;AACH;AACJ;AA1C8B;AA2ClC;;AA9L0B,O;;;;;iBAKX,I", "sourcesContent": ["/*\n * @Author: dgflash\n * @Date: 2021-07-03 16:13:17\n * @LastEditors: dgflash\n * @LastEditTime: 2022-08-05 18:25:56\n */\nimport { _decorator, Node } from 'cc';\nimport { oops } from '../../extensions/oops-plugin-framework/assets/core/Oops';\nimport { Root } from '../../extensions/oops-plugin-framework/assets/core/Root';\nimport { ecs } from '../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';\nimport { FBInstantManager } from './facebook-instant-games/FBInstantManager';\nimport { FacebookGameEvents } from './facebook-instant-games/FacebookGameEvents';\nimport { CameraEntity } from './game/Camera/CameraEntity';\nimport { Account } from './game/account/Account';\nimport { SimpleSceneManager } from './game/common/scene/SimpleSceneManager';\nimport { Guide } from './game/guide/Guide';\nimport { Initialize } from './game/initialize/Initialize';\nimport { Role } from './game/role/Role';\n\nimport { Environment, Platform, ShareConfig } from './tsrpc/models/ShareConfig';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('Main')\nexport class Main extends Root {\n    @property({\n        type: Node,\n        tooltip: '游戏初始画面',\n    })\n    initial: Node = null!;\n\n    protected iniStart() {\n        // 🔧 开始游戏完整初始化计时\n        oops.log.start('游戏完整初始化');\n\n        // profiler.hideStats();\n\n        // 使用LogControl自动设置日志级别\n        const isProduction = ShareConfig.isProduction;\n\n        if (ShareConfig.environment === Environment.PRODUCTION_FACEBOOK) {\n            // Facebook生产环境：超精简模式\n        } else if (isProduction) {\n            // 其他生产环境：精简模式\n        } else {\n            // 开发环境：完整日志\n        }\n\n        // 🚀 优化：延迟初始化单例模块，减少启动时间\n        this.initializeCoreModules();\n    }\n\n    /** 🔧 优化：分阶段初始化核心模块 */\n    private async initializeCoreModules() {\n        const { smc } = await import('./game/common/SingletonModuleComp');\n\n        // 只初始化最基础的模块\n        smc.initialize = ecs.getEntity<Initialize>(Initialize);\n\n        // 🚀 Facebook模块懒加载\n        if (ShareConfig.platform === Platform.FACEBOOK) {\n            const [{ FBInstantManager }, { FacebookGameEvents }] = await Promise.all([\n                import('./facebook-instant-games/FBInstantManager'),\n                import('./facebook-instant-games/FacebookGameEvents'),\n            ]);\n\n            smc.fbInstantManager = ecs.getEntity<FBInstantManager>(FBInstantManager);\n            smc.facebookGameEvents = ecs.getEntity<FacebookGameEvents>(FacebookGameEvents);\n        }\n\n        // 🚀 其他业务模块延迟加载\n        this.loadBusinessModulesLater();\n    }\n\n    /** 🔧 业务模块延迟加载 - 优化版本 */\n    private async loadBusinessModulesLater() {\n        // 🚀 优化：立即开始加载关键模块，不等待空闲时间\n        this.loadCriticalModules();\n\n        // 在空闲时间加载非关键模块\n        requestIdleCallback(async () => {\n            await this.loadNonCriticalModules();\n        });\n    }\n\n    /** 🚀 立即加载关键模块 */\n    private async loadCriticalModules() {\n        try {\n            const [{ smc }, { Role }, { SimpleSceneManager }] = await Promise.all([\n                import('./game/common/SingletonModuleComp'),\n                import('./game/role/Role'),\n                import('./game/common/scene/SimpleSceneManager'),\n            ]);\n\n            // 🚀 优先初始化关键模块\n            smc.role = ecs.getEntity<Role>(Role);\n            smc.sceneMgr = ecs.getEntity<SimpleSceneManager>(SimpleSceneManager);\n\n            oops.log.logBusiness('✅ 关键模块加载完成');\n        } catch (error) {\n            oops.log.logError('❌ 关键模块加载失败:', error);\n        }\n    }\n\n    /** 🔧 加载非关键模块 */\n    private async loadNonCriticalModules() {\n        try {\n            const [{ smc }, { Account }, { Guide }, { CameraEntity }] = await Promise.all([\n                import('./game/common/SingletonModuleComp'),\n                import('./game/account/Account'),\n                import('./game/guide/Guide'),\n                import('./game/Camera/CameraEntity'),\n            ]);\n\n            // 🚀 性能监控已移除 - 使用Cocos内置profiler即可\n            // 开发环境启用内置性能面板\n            if (!ShareConfig.isProduction) {\n                const { profiler } = await import('cc');\n                profiler.showStats();\n            }\n\n            // 🚀 直接设置资源下载并发数（替代ResourceOptimizer）\n            const { assetManager } = await import('cc');\n            assetManager.downloader.maxRequestsPerFrame = 20; // 🚀 增加并发数\n\n            // 初始化非关键业务模块\n            smc.account = ecs.getEntity<Account>(Account);\n            smc.guide = ecs.getEntity<Guide>(Guide);\n            smc.camera = ecs.getEntity<CameraEntity>(CameraEntity);\n\n            oops.log.logBusiness('✅ 非关键模块加载完成');\n        } catch (error) {\n            oops.log.logError('❌ 非关键模块加载失败:', error);\n        }\n    }\n\n    protected initGui() {\n        // 🚀 延迟加载UI配置\n        requestIdleCallback(async () => {\n            const { UIConfigData } = await import('./game/common/config/GameUIConfig');\n            oops.gui.init(UIConfigData);\n        });\n    }\n\n    protected initEcsSystem() {\n        // ECS系统现在在 loadBusinessModulesLater 中初始化\n    }\n\n    protected async run() {\n        try {\n            // Facebook平台初始化\n            if (ShareConfig.platform === Platform.FACEBOOK) {\n                await this.initializeFacebook();\n            }\n\n            // 开始游戏初始化\n            const { smc } = await import('./game/common/SingletonModuleComp');\n            smc.initialize.load(this.initial);\n        } catch (error) {\n            oops.log.logError('游戏初始化失败:', error);\n\n            // 生产环境显示错误\n            if (ShareConfig.environment === Environment.PRODUCTION_FACEBOOK) {\n                oops.gui.toast('Facebook连接失败，请刷新页面重试');\n                return;\n            }\n            oops.gui.toast('游戏启动失败，请刷新页面重试');\n        }\n    }\n\n    /** 🔧 Facebook初始化优化 */\n    private async initializeFacebook() {\n        const { smc } = await import('./game/common/SingletonModuleComp');\n        const maxRetries = 3;\n        let initSuccess = false;\n\n        for (let i = 0; i < maxRetries; i++) {\n            try {\n                oops.log.logBusiness(`🚀 Facebook初始化尝试 ${i + 1}/${maxRetries}...`);\n\n                await smc.fbInstantManager.fbInit();\n                await smc.facebookGameEvents.onPlayerFirstEntry();\n\n                // 验证Facebook数据\n                const fbData = smc.fbInstantManager.getFacebookLoginData();\n                if (!fbData || !fbData.facebookId) {\n                    throw new Error('Facebook数据不完整或玩家ID为空');\n                }\n\n                oops.log.logBusiness('✅ Facebook初始化成功，玩家ID:', fbData.facebookId);\n                initSuccess = true;\n                break;\n            } catch (error) {\n                oops.log.logWarn(`⚠️ Facebook初始化失败 (尝试 ${i + 1}/${maxRetries}):`, error);\n\n                if (i < maxRetries - 1) {\n                    const retryDelay = (i + 1) * 1000;\n                    oops.log.logBusiness(`⏳ 等待 ${retryDelay}ms 后重试...`);\n                    await new Promise(resolve => setTimeout(resolve, retryDelay));\n                }\n            }\n        }\n\n        if (!initSuccess) {\n            const errorMsg = 'Facebook多次初始化失败';\n            oops.log.logError(errorMsg);\n\n            if (ShareConfig.environment === Environment.PRODUCTION_FACEBOOK) {\n                oops.gui.toast('Facebook连接失败，请刷新页面重试');\n                return;\n            } else {\n                oops.log.logWarn('⚠️ 非生产环境：继续游戏启动（降级模式）');\n            }\n        }\n    }\n}\n"]}