[1, ["1eWFsMVo9G2Iy50BMhdO0J@f9941", "05oWtVrBlJ5IIsP6XvWXgT@f9941", "22uVqlFnJGNpLDdM1Z+uwi@f9941"], ["node", "_spriteFrame", "root", "lab_ok", "lab_content", "lab_title", "_target", "data"], [["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_cacheMode", "_overflow", "_name", "_enableWrapText", "node", "__prefab", "_color"], -4, 1, 4, 5], ["cc.Sprite", ["_type", "_sizeMode", "_name", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Node", ["_name", "_layer", "_children", "_components", "_prefab", "_parent", "_lpos"], 1, 2, 9, 4, 1, 5], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab", "_lpos"], 1, 1, 12, 4, 5], ["cc.UITransform", ["_name", "node", "__prefab", "_contentSize"], 2, 1, 4, 5], ["110c8vEd5NEPL/N9meGQnaX", ["_name", "node", "__prefab"], 2, 1, 4], ["cc.Prefab", ["_name"], 2], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["_name", "_transition", "_zoomScale", "node", "__prefab", "clickEvents", "_normalColor", "_target"], 0, 1, 4, 9, 5, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Widget", ["_name", "_alignFlags", "_left", "_bottom", "node", "__prefab"], -1, 1, 4], ["653bf8VPC5Fn49zFJFqXVgx", ["node", "__prefab", "lab_title", "lab_content", "lab_ok"], 3, 1, 4, 1, 1, 1]], [[7, 0, 2], [8, 0, 1, 2, 3, 4, 5, 5], [4, 1, 2, 3, 1], [2, 0, 1, 5, 2, 3, 4, 6, 3], [3, 0, 1, 2, 3, 4, 5, 3], [4, 0, 1, 2, 3, 2], [5, 0, 1, 2, 2], [1, 0, 1, 3, 4, 5, 3], [6, 0, 2], [2, 0, 1, 2, 3, 4, 3], [3, 0, 1, 2, 3, 4, 3], [0, 0, 1, 2, 4, 3, 7, 8, 9, 6], [0, 0, 1, 2, 3, 7, 8, 5], [0, 5, 0, 1, 2, 4, 6, 3, 7, 8, 8], [5, 1, 2, 1], [1, 2, 0, 3, 4, 5, 3], [1, 1, 3, 4, 2], [9, 0, 1, 2, 3, 4, 5, 6, 7, 4], [10, 0, 1, 2, 3], [11, 0, 1, 2, 3, 4, 5, 5], [12, 0, 1, 2, 3, 4, 1]], [[8, "alert"], [9, "alert", 33554432, [-8, -9, -10], [[2, -2, [0, "37o1ko8nFK1oK3xmkqtwMG"], [5, 478, 320]], [16, 0, -3, [0, "a0vC/W2QdFb7e69t18Gf21"]], [20, -7, [0, "92SJypfohE74p2ebUwinVx"], -6, -5, -4]], [1, "c7ZN3fr+hJoZmUZs2A0Ye0", null, null, null, -1, 0]], [3, "btn_ok", 33554432, 1, [-16], [[5, "btn_ok<UITransform>", -11, [0, "f7xQN8U0NKuYCTfCHfwIz2"], [5, 158, 68]], [15, "btn_ok<Sprite>", 1, -12, [0, "3dr+KXw4lC5oS1lceuHGsr"], 2], [17, "btn_ok<Button>", 3, 0.9, -14, [0, "64+udhPrdI4oLL+6S6rR5C"], [[18, "653bf8VPC5Fn49zFJFqXVgx", "onOk", 1]], [4, 4292269782], -13], [19, "btn_ok<Widget>", 12, 160, 42.92399999999998, -15, [0, "37gJva6bdFs6GgPAU1af2N"]]], [1, "fdFk91GVpJKo4VT/zlGejW", null, null, null, 1, 0], [1, 0, -83.07600000000002, 0]], [3, "cotent", 33554432, 1, [-19], [[2, -17, [0, "beCfdHCpRBk4weVAQUwFf1"], [5, 450, 300]], [7, 1, 0, -18, [0, "c8PrcK41NI05YWC1GMkZLX"], 0]], [1, "bdKboigutDpLyMAJBWAy6C", null, null, null, 1, 0], [1, 0, 12, 0]], [4, "lab_content", 33554432, 3, [[[2, -20, [0, "49SU+NRCVFC7lotcT5Arsj"], [5, 420, 50.4]], [11, "内容", 28, 28, 3, 1, -21, [0, "867mEteZhIVLc5LxzUTUW5"], [4, 4285487306]], -22], 4, 4, 1], [1, "7fk7nP/NJBI7+oI60VkgZO", null, null, null, 1, 0], [1, 0, 12, 0]], [3, "title", 33554432, 1, [-25], [[2, -23, [0, "c4vrzXIsVAn6ogQVqDJEkH"], [5, 450, 73]], [7, 1, 0, -24, [0, "75gSwWhgtLfr03UrqE8dbs"], 1]], [1, "020rdVquhCSZPKTnInr6tQ", null, null, null, 1, 0], [1, 0, 128.163, 0]], [10, "lab_title", 33554432, 5, [[[2, -26, [0, "1c9xohlJtL3bm53voN5DeV"], [5, 60, 50.4]], [12, "标题", 30, 30, 1, -27, [0, "26eZlgc6xJWqYRq4rBoG6R"]], -28], 4, 4, 1], [1, "42TIPOfhhM7ra85tXw74QR", null, null, null, 1, 0]], [4, "lab_ok", 33554432, 2, [[[5, "Label<UITransform>", -29, [0, "44bD80KHZProBBM3AMP1KY"], [5, 100, 40]], [13, "Label<Label>", "确认", 26, 26, 1, false, 1, -30, [0, "83w7Pp8C1M6Kt+xac8Iku4"]], -31], 4, 4, 1], [1, "79ujcxm8RNi7nnpjL7+jNw", null, null, null, 1, 0], [1, 0, 5.933999999999969, 0]], [6, "lab_ok<LanguageLabel>", 4, [0, "fdJ1rAAmlDeqjsBHa5gesk"]], [6, "lab_ok<LanguageLabel>", 6, [0, "12QljFj4dG9LVb4oZYMNmY"]], [14, 7, [0, "a6mROSd8pJA6FVDucSlwDW"]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 3, 10, 0, 4, 8, 0, 5, 9, 0, 0, 1, 0, -1, 3, 0, -2, 5, 0, -3, 2, 0, 0, 2, 0, 0, 2, 0, 6, 2, 0, 0, 2, 0, 0, 2, 0, -1, 7, 0, 0, 3, 0, 0, 3, 0, -1, 4, 0, 0, 4, 0, 0, 4, 0, -3, 8, 0, 0, 5, 0, 0, 5, 0, -1, 6, 0, 0, 6, 0, 0, 6, 0, -3, 9, 0, 0, 7, 0, 0, 7, 0, -3, 10, 0, 7, 1, 31], [0, 0, 0], [1, 1, 1], [0, 1, 2]]