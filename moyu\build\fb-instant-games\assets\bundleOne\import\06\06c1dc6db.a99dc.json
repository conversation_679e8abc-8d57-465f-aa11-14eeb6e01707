[1, ["56oY0PFUVKRJA95WSWzdXD", "e28dJLVXJC3YFObxU5gPQx@f8b97"], ["node", "root", "data", "_mesh"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", [], 3], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["<PERSON>.<PERSON><PERSON>", ["_radius", "_height", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11]], [[3, 0, 2], [7, 0, 1, 2, 3, 4, 3], [0, 0, 2], [1, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 5, 2], [4, 1], [5, 0, 1, 2, 2], [6, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5], [9, 0, 1, 2, 3]], [[[[2, "半个茄子"], [3, "半个茄子", [[4, 1, -2, [0, "3ccO7jQstIILM39x8t7UqZ"], [0], [5], 1], [6, 4, -3, [0, "17adfxA6pNELqDJ6eoUeyD"]], [7, 0.387, 0.873, -4, [0, "5cxlHbRARH8r6g4bUwxBgA"], [1, -0.0016234219074249268, -0.12, -0.005185]], [1, 0.111, 0.826, -5, [0, "5509JfzSZBx63kCq9JwdEG"], [1, -0.101623, 0.315543, -0.005185157060623169]], [1, 0.313, 0.402, -6, [0, "8eOGZCciFMhq1XtuKxXs5k"], [1, -0.05, -0.1, -0.005185157060623169]]], [8, "3bkPyLbchEIo95tzXizSCF", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 6], [0, 0], [-1, 3], [0, 1]], [[[9, ".bin", 1491764657, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 24120, "length": 2544, "count": 1272, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 24120, "count": 335, "stride": 72}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}, {"name": "a_texCoord1", "format": 21, "isNormalized": false}, {"name": "a_texCoord2", "format": 21, "isNormalized": false}, {"name": "a_texCoord3", "format": 21, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.46282026171684265, -0.5844277739524841, -0.4265800714492798], "maxPosition", 8, [1, 0.4595734179019928, 0.8155142068862915, 0.41620975732803345]]], -1], 0, 0, [], [], []]]]