System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, tween, UIOpacity, Vec3, DEBUG, DEV, oops, JsonUtil, ecs, CCComp, Platform, ShareConfig, SceneType, ClientConst, GameStorageConfig, StorageTestUtils, smc, _dec, _dec2, _class, _crd, ccclass, property, InitialViewComp;

  function _reportPossibleCrUseOfoops(extras) {
    _reporterNs.report("oops", "../../../../../extensions/oops-plugin-framework/assets/core/Oops", _context.meta, extras);
  }

  function _reportPossibleCrUseOfJsonUtil(extras) {
    _reporterNs.report("JsonUtil", "../../../../../extensions/oops-plugin-framework/assets/core/utils/JsonUtil", _context.meta, extras);
  }

  function _reportPossibleCrUseOfecs(extras) {
    _reporterNs.report("ecs", "../../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCCComp(extras) {
    _reporterNs.report("CCComp", "../../../../../extensions/oops-plugin-framework/assets/module/common/CCComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlatform(extras) {
    _reporterNs.report("Platform", "../../../tsrpc/models/ShareConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfShareConfig(extras) {
    _reporterNs.report("ShareConfig", "../../../tsrpc/models/ShareConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSceneType(extras) {
    _reporterNs.report("SceneType", "../../../tsrpc/protocols/base", _context.meta, extras);
  }

  function _reportPossibleCrUseOfClientConst(extras) {
    _reporterNs.report("ClientConst", "../../common/ClientConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameStorageConfig(extras) {
    _reporterNs.report("GameStorageConfig", "../../common/config/GameStorageConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStorageTestUtils(extras) {
    _reporterNs.report("StorageTestUtils", "../../common/config/GameStorageConfig", _context.meta, extras);
  }

  function _reportPossibleCrUseOfsmc(extras) {
    _reporterNs.report("smc", "../../common/SingletonModuleComp", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      tween = _cc.tween;
      UIOpacity = _cc.UIOpacity;
      Vec3 = _cc.Vec3;
    }, function (_ccEnv) {
      DEBUG = _ccEnv.DEBUG;
      DEV = _ccEnv.DEV;
    }, function (_unresolved_2) {
      oops = _unresolved_2.oops;
    }, function (_unresolved_3) {
      JsonUtil = _unresolved_3.JsonUtil;
    }, function (_unresolved_4) {
      ecs = _unresolved_4.ecs;
    }, function (_unresolved_5) {
      CCComp = _unresolved_5.CCComp;
    }, function (_unresolved_6) {
      Platform = _unresolved_6.Platform;
      ShareConfig = _unresolved_6.ShareConfig;
    }, function (_unresolved_7) {
      SceneType = _unresolved_7.SceneType;
    }, function (_unresolved_8) {
      ClientConst = _unresolved_8.ClientConst;
    }, function (_unresolved_9) {
      GameStorageConfig = _unresolved_9.GameStorageConfig;
      StorageTestUtils = _unresolved_9.StorageTestUtils;
    }, function (_unresolved_10) {
      smc = _unresolved_10.smc;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "2839dCGakdHw4LrsrSxiinW", "InitialViewComp", undefined);

      __checkObsolete__(['_decorator', 'tween', 'UIOpacity', 'Vec3']);

      // 扩展window类型
      ({
        ccclass,
        property
      } = _decorator);
      /**
       * 游戏完整初始化组件 - 自定义加载版本
       *
       * 职责：
       * - UI动画播放
       * - Bundle验证和加载
       * - 配置数据加载（配置表 + 语言包）
       * - 用户数据加载
       * - 进入游戏流程
       *
       * 优化：
       * - 所有初始化逻辑集中管理
       * - Bundle加载失败直接报错
       * - 配置表和语言包并行加载
       * - 支持自定义加载界面（Facebook + 个人平台）
       */

      _export("InitialViewComp", InitialViewComp = (_dec = ccclass('InitialViewComp'), _dec2 = (_crd && ecs === void 0 ? (_reportPossibleCrUseOfecs({
        error: Error()
      }), ecs) : ecs).register('InitialView', false), _dec(_class = _dec2(_class = class InitialViewComp extends (_crd && CCComp === void 0 ? (_reportPossibleCrUseOfCCComp({
        error: Error()
      }), CCComp) : CCComp) {
        constructor(...args) {
          super(...args);
          this.waitComplete = false;
          this.loadComplete = false;
          this.currentProgress = 0;
          this.startTime = 0;
        }

        start() {
          // 🚀 记录启动开始时间
          this.startTime = Date.now();
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🚀 游戏初始化开始', {
            startTime: this.startTime
          }); // 🚀 统一使用自定义加载界面，跳过Cocos内置动画

          if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).FACEBOOK) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🚀 Facebook环境: 自定义加载模式');
          } else if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).PERSONAL) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🚀 个人环境: 自定义加载模式');
            this.updateLoadingProgress(50, '游戏引擎已启动', '个人环境初始化完成');
          } else {
            // 其他环境：使用传统动画
            this.playWaitAnimation();
          }

          this.startFullInitialization();
        }
        /** 🎬 播放等待动画（传统环境） */


        playWaitAnimation() {
          var _this$node$getChildBy;

          let logoOpacity = (_this$node$getChildBy = this.node.getChildByName('logo')) == null ? void 0 : _this$node$getChildBy.getComponent(UIOpacity);

          if (logoOpacity) {
            logoOpacity.opacity = 50;
            tween(logoOpacity.node).to(1, {
              opacity: 255,
              position: new Vec3(0, 0, 0)
            }).call(() => {
              this.waitComplete = true;

              if (this.loadComplete) {
                this.enterGame();
              }
            }).start();
          } else {
            this.waitComplete = true;
          }
        }
        /** 🎯 更新加载进度 */


        updateLoadingProgress(progress, message, detail) {
          this.currentProgress = progress;

          if (window.updateProgress && typeof window.updateProgress === 'function') {
            window.updateProgress(progress, message, detail);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness(`🎯 加载进度: ${progress}% - ${message}`);
          }
        }
        /** 🚀 完整初始化流程 - 重构版本 */


        async startFullInitialization() {
          try {
            // 🧪 测试开关：控制数据清空行为
            if ((_crd && ClientConst === void 0 ? (_reportPossibleCrUseOfClientConst({
              error: Error()
            }), ClientConst) : ClientConst).alwaysNewPlayerTest && (DEV || DEBUG)) {
              (_crd && StorageTestUtils === void 0 ? (_reportPossibleCrUseOfStorageTestUtils({
                error: Error()
              }), StorageTestUtils) : StorageTestUtils).forceNewPlayerState();
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🎓 [测试模式] 强制设置为新手状态');
            } // 🚀 优化：并行执行可独立的初始化任务


            await Promise.all([// 1️⃣ 验证和加载Bundle + 基础UI资源
            this.loadBundleAndUIResources(), // 2️⃣ 加载配置数据（配置表 + 语言包）
            this.loadConfigurationData()]); // 3️⃣ 尝试快速用户数据初始化

            await this.quickUserDataInitialization(); // 设置窗口打开失败事件

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.setOpenFailure(this.onOpenFailure);
            this.loadComplete = true; // 🚀 自定义加载环境直接进入游戏

            if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
              error: Error()
            }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
              error: Error()
            }), Platform) : Platform).FACEBOOK || (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
              error: Error()
            }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
              error: Error()
            }), Platform) : Platform).PERSONAL) {
              this.enterGame();
            } else if (this.waitComplete) {
              this.enterGame();
            }
          } catch (error) {
            this.handleInitializationError(error);
          }
        }
        /** � 快速用户数据初始化 */


        async quickUserDataInitialization() {
          try {
            // 🔍 首先尝试快速加载现有用户数据
            const isNewPlayer = this.determineNewPlayerStatus();

            if (isNewPlayer) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🚀 启用新手快速启动模式');
              await this.initializeBasicRoleData(); // 🔄 在后台继续完整的用户数据加载

              this.loadFullUserDataInBackground();
            } else {
              // 🔄 传统模式：完整加载用户数据
              await this.ensureUserDataLoaded();
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 快速用户数据初始化失败:', error); // 失败时创建基础数据，确保游戏可以启动

            await this.initializeBasicRoleData();
          }
        }
        /** 🎁 为新手玩家创建默认道具数据 */


        createNewPlayerDefaultProps() {
          const currentTime = new Date(); // 🎯 新手玩家默认道具配置

          const newPlayerProps = {
            // 1. 移出道具 - 新手给3个
            1: {
              amount: 3,
              propType: 1,
              propId: 1,
              desc: '移出道具',
              getTime: currentTime,
              lastResetTime: currentTime,
              lastUpdateTime: currentTime
            },
            // 2. 提示道具 - 新手给3个
            2: {
              amount: 3,
              propType: 2,
              propId: 2,
              desc: '提示道具',
              getTime: currentTime,
              lastResetTime: currentTime,
              lastUpdateTime: currentTime
            },
            // 3. 重新洗牌道具 - 新手给2个
            3: {
              amount: 2,
              propType: 3,
              propId: 3,
              desc: '重新洗牌道具',
              getTime: currentTime,
              lastResetTime: currentTime,
              lastUpdateTime: currentTime
            },
            // 7. 每日挑战剩余次数 - 新手给足够的次数
            7: {
              amount: 9999,
              propType: 7,
              propId: 7,
              desc: '每日挑战剩余次数',
              getTime: currentTime,
              lastResetTime: currentTime,
              lastUpdateTime: currentTime
            }
          };
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎁 新手默认道具配置:', {
            移出道具: newPlayerProps[1].amount,
            提示道具: newPlayerProps[2].amount,
            洗牌道具: newPlayerProps[3].amount,
            挑战次数: newPlayerProps[7].amount
          });
          return newPlayerProps;
        }
        /** 🔄 后台加载完整用户数据 */


        loadFullUserDataInBackground() {
          // 使用setTimeout确保不阻塞当前流程
          setTimeout(async () => {
            try {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('🔄 开始后台加载完整用户数据...'); // 🚀 强制执行完整的登录流程，不跳过

              await this.forceCompleteUserDataLoad();
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ 后台用户数据加载完成'); // 触发数据更新事件，通知游戏其他模块

              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).message.dispatchEvent('UserDataLoaded');
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logWarn('⚠️ 后台用户数据加载失败:', error);
            }
          }, 100);
        }
        /** 🔄 强制执行完整的用户数据加载流程 */


        async forceCompleteUserDataLoad() {
          try {
            var _role$RoleModel;

            // 🔄 临时清除基础数据标记，强制执行完整登录
            const tempUserData = (_role$RoleModel = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).role.RoleModel) == null ? void 0 : _role$RoleModel.userGameData;

            if (tempUserData) {
              // 标记这是临时数据，需要完整登录
              tempUserData.isTemporaryData = true;
            } // 🔄 执行完整的登录流程


            await this.performCompleteLogin();
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 强制完整登录失败:', error);
            throw error;
          }
        }
        /** 🔐 执行完整的登录流程 */


        async performCompleteLogin() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🔐 开始执行完整登录流程...'); // 🔧 根据平台选择不同的登录方式

          if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).FACEBOOK) {
            // Facebook环境：执行Facebook自动登录
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔐 Facebook环境：执行自动登录...');

            try {
              const {
                LoginViewComp
              } = await _context.import("__unresolved_10");
              const loginSuccess = await LoginViewComp.doFacebookLogin();

              if (loginSuccess) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('✅ Facebook自动登录成功');
              } else {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logWarn('⚠️ Facebook自动登录失败，继续尝试加载数据');
              }
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ Facebook自动登录异常:', error);
            }
          } else {
            // 🔐 其他环境：使用现有的游客登录逻辑
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔐 执行游客登录流程...');

            try {
              // 🚀 直接使用LoginViewComp的游客登录方法
              const {
                LoginViewComp
              } = await _context.import("__unresolved_11");
              const loginViewComp = new LoginViewComp();
              const loginSuccess = await loginViewComp.loginGuestButton();

              if (loginSuccess) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('✅ 游客登录成功');
              } else {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logWarn('⚠️ 游客登录失败');
              }
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logWarn('⚠️ 游客登录流程失败:', error); // 不抛出错误，允许游戏继续运行
            }
          }
        }
        /** � 智能判断新手状态 */


        determineNewPlayerStatus() {
          // 1. 优先使用Role模块的判断
          if ((_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).role.RoleModel && (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).role.RoleModel.userGameData) {
            const isNewFromRole = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).role.isNewPlayer();
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness(`🎯 Role模块判断新手状态: ${isNewFromRole}`);
            return isNewFromRole;
          } // 2. 检查本地存储的登录记录


          const hasLoginRecord = !!(_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).storage.get((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
            error: Error()
          }), GameStorageConfig) : GameStorageConfig).SSOToken);

          if (hasLoginRecord) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔍 检测到登录记录，判定为老玩家');
            return false;
          } // 3. 检查是否有其他用户数据痕迹


          const userDumpKey = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).storage.getJson((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
            error: Error()
          }), GameStorageConfig) : GameStorageConfig).UserDumpKey, null);

          if (userDumpKey && String(userDumpKey) !== '0') {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔍 检测到用户数据痕迹，判定为老玩家');
            return false;
          } // 4. 默认判定为新手


          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🆕 无任何用户数据痕迹，判定为新手玩家');
          return true;
        }
        /** �🚀 初始化基础Role数据，确保道具系统可用 */


        async initializeBasicRoleData() {
          try {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🚀 开始初始化基础Role数据...'); // 创建完整的基础用户数据结构

            const currentTime = new Date();
            const basicUserData = {
              // 基础标识
              key: 0,
              guuid: '',
              googleUuid: '',
              facebookId: '',
              userName: '',
              nickName: '',
              sex: 1,
              // SexType.None
              createtime: currentTime,
              openid: '',
              platform: 'web',
              platformType: 'web',
              avatar: '',
              avatarId: 0,
              countryCode: 'Other',
              // 游戏进度
              passTimes: 0,
              index: 0,
              currCountryPassTimes: 0,
              lastChangeCountryTime: currentTime,
              selfCountryRank: 0,
              // 道具和记录数据
              propUseData: this.createNewPlayerDefaultProps(),
              recordData: {},
              // 新手和游客状态
              isNewPlayer: true,
              isGuest: true,
              lastStep: 0
            }; // 🎯 使用Role模块的updateUserData方法来设置数据
            // 这样可以确保数据完整性和触发相关事件

            if ((_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).role && (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).role.updateUserData) {
              (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).role.updateUserData(basicUserData);
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ 基础用户数据已通过updateUserData设置');
            } else {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logWarn('⚠️ Role模块或updateUserData方法不可用');
            }

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🚀 基础Role数据初始化完成', {
              isNewPlayer: basicUserData.isNewPlayer,
              propCount: Object.keys(basicUserData.propUseData).length
            });
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 基础Role数据初始化失败:', error);
          }
        }
        /** 1️⃣ 验证和加载Bundle + 基础UI资源 */


        async loadBundleAndUIResources() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🔍 开始验证Bundle和加载基础UI资源...');

          try {
            // 🚀 直接验证bundleOne是否存在，不存在就报错
            try {
              await (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).res.loadBundle('bundleOne');
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ bundleOne加载成功');
            } catch (error) {
              throw new Error(`❌ 关键Bundle 'bundleOne' 加载失败: ${error}`);
            } // 🚀 延迟导入，减少初始bundle大小


            const {
              simpleLoader
            } = await _context.import("__unresolved_12"); // 🎯 加载基础UI资源

            const tasks = [{
              name: '加载基础UI资源',
              dirs: ['boot'],
              priority: 'high',
              bundle: 'bundleOne'
            }];
            const result = await simpleLoader.loadTasks(tasks);

            if (!result) {
              throw new Error('基础UI资源加载失败');
            }

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('✅ Bundle和基础UI资源加载完成');
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ Bundle和UI资源加载失败:', error);
            throw error;
          }
        }
        /** 2️⃣ 加载配置数据资源（配置表 + 语言包） */


        async loadConfigurationData() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🔄 开始加载配置数据资源...');

          try {
            // 🚀 优化：配置表和语言包可以并行加载
            const [configResult] = await Promise.all([(_crd && JsonUtil === void 0 ? (_reportPossibleCrUseOfJsonUtil({
              error: Error()
            }), JsonUtil) : JsonUtil).loadDirAsync(), this.loadLanguage()]);

            if (!configResult) {
              throw new Error('配置表加载失败');
            }

            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('✅ 配置数据资源加载完成');
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('❌ 配置数据资源加载失败:', error);
            throw error;
          }
        }
        /** 加载语言包 */


        async loadLanguage() {
          let language = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).storage.get((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
            error: Error()
          }), GameStorageConfig) : GameStorageConfig).Language, 'en');
          language = 'en';
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).language.setLanguage(language);
        }
        /** 3️⃣ 确保用户数据已加载 - 完整登录流程 */


        async ensureUserDataLoaded() {
          var _role$RoleModel2;

          // 检查是否已有用户数据，但排除临时数据
          const userData = (_role$RoleModel2 = (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).role.RoleModel) == null ? void 0 : _role$RoleModel2.userGameData;

          if (userData && !userData.isTemporaryData) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('✅ 用户数据已存在，跳过加载');
            return;
          }

          if (userData != null && userData.isTemporaryData) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔄 检测到临时数据，执行完整登录流程...');
          } else {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔄 开始完整用户数据加载流程...');
          } // 🔧 根据平台选择不同的登录方式


          if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).FACEBOOK) {
            // Facebook环境：执行Facebook自动登录
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔐 Facebook环境：执行自动登录...');

            try {
              const {
                LoginViewComp
              } = await _context.import("__unresolved_13");
              const loginSuccess = await LoginViewComp.doFacebookLogin();

              if (loginSuccess) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('✅ Facebook自动登录成功');
              } else {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logWarn('⚠️ Facebook自动登录失败，继续尝试加载数据');
              }
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ Facebook自动登录异常:', error);
            }
          } else if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).PERSONAL) {
            //
            try {
              const {
                LoginViewComp
              } = await _context.import("__unresolved_14"); // 🔍 先检查token过期时间，再尝试加载数据

              const existingToken = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).storage.get((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
                error: Error()
              }), GameStorageConfig) : GameStorageConfig).SSOToken);
              const tokenInfo = this.getTokenInfo();
              let needRelogin = false;

              if (existingToken && tokenInfo) {
                // 检查token是否过期
                if (tokenInfo.expiredTime < Date.now()) {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logWarn('⏰ SSO Token已过期，需要重新登录', {
                    创建时间: new Date(tokenInfo.createdTime).toLocaleString(),
                    过期时间: new Date(tokenInfo.expiredTime).toLocaleString(),
                    当前时间: new Date().toLocaleString()
                  });
                  needRelogin = true;
                } else {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness('🔑 检测到有效SSO Token，尝试加载用户数据...', {
                    剩余时间: Math.round((tokenInfo.expiredTime - Date.now()) / (60 * 60 * 1000)) + '小时'
                  }); // 尝试加载用户数据以验证token有效性

                  try {
                    await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                      error: Error()
                    }), smc) : smc).role.loadData();

                    if ((_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                      error: Error()
                    }), smc) : smc).role.RoleModel && (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                      error: Error()
                    }), smc) : smc).role.RoleModel.userGameData) {
                      (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                        error: Error()
                      }), oops) : oops).log.logBusiness('✅ 用户数据加载成功，token有效');
                      return; // 成功加载，直接返回
                    } else {
                      needRelogin = true;
                    }
                  } catch (error) {
                    (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                      error: Error()
                    }), oops) : oops).log.logWarn('⚠️ token验证失败，需要重新登录:', error);
                    needRelogin = true;
                  }
                }
              } else {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('🔍 未检测到有效token或token信息');
                needRelogin = true;
              } // 如果需要重新登录


              if (needRelogin) {
                (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                  error: Error()
                }), oops) : oops).log.logBusiness('🔄 执行游客登录...'); // 清除旧token和token信息

                if (existingToken) {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).storage.remove((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
                    error: Error()
                  }), GameStorageConfig) : GameStorageConfig).SSOToken);
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).storage.remove((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
                    error: Error()
                  }), GameStorageConfig) : GameStorageConfig).SSOTokenInfo);
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness('🗑️ 已清除无效的SSO Token和相关信息');
                } // 创建临时LoginViewComp实例进行游客登录


                const tempLoginComp = new LoginViewComp();
                const guestLoginSuccess = await tempLoginComp.loginGuestButton();

                if (guestLoginSuccess) {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logBusiness('✅ 游客登录成功'); // 重新加载用户数据

                  await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                    error: Error()
                  }), smc) : smc).role.loadData();

                  if ((_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                    error: Error()
                  }), smc) : smc).role.RoleModel && (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                    error: Error()
                  }), smc) : smc).role.RoleModel.userGameData) {
                    (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                      error: Error()
                    }), oops) : oops).log.logBusiness('✅ 用户数据加载成功');
                  } else {
                    (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                      error: Error()
                    }), oops) : oops).log.logWarn('⚠️ 游客登录后用户数据加载失败');
                  }
                } else {
                  (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                    error: Error()
                  }), oops) : oops).log.logWarn('⚠️ 游客登录失败');
                }
              }
            } catch (error) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logError('❌ 游客登录流程异常:', error);
            }
          } else {
            // 其他平台，直接尝试加载数据
            await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
              error: Error()
            }), smc) : smc).role.loadData();
          } // 最终检查结果


          if ((_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).role.RoleModel && (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
            error: Error()
          }), smc) : smc).role.RoleModel.userGameData) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('✅ 用户数据最终加载成功');
          } else {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 用户数据最终加载失败，但继续进入游戏');
          }
        }
        /** 🎮 进入游戏流程 - 优化版本 */


        async enterGame() {
          try {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🎓 开始进入游戏...'); // 🔍 智能判断新手状态

            let isNewPlayer = this.determineNewPlayerStatus();
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness(`🎯 最终判定新手状态: ${isNewPlayer}`);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness(`🎮 进入${isNewPlayer ? '新手游戏' : '大厅'}场景`);
            let success = false;
            let showLoading = true; // 🎯 使用自定义加载界面的平台不显示Cocos默认加载

            if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
              error: Error()
            }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
              error: Error()
            }), Platform) : Platform).FACEBOOK || (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
              error: Error()
            }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
              error: Error()
            }), Platform) : Platform).PERSONAL) {
              showLoading = false;
            }

            if (isNewPlayer) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness(`🆕 ${(_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
                error: Error()
              }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
                error: Error()
              }), Platform) : Platform).FACEBOOK ? 'Facebook' : (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
                error: Error()
              }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
                error: Error()
              }), Platform) : Platform).PERSONAL ? '个人' : ''}新手玩家：进入游戏场景（Foods）`);
              success = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).sceneMgr.switchToScene((_crd && SceneType === void 0 ? (_reportPossibleCrUseOfSceneType({
                error: Error()
              }), SceneType) : SceneType).Foods, 1, showLoading, this.closeLoadingUI.bind(this));
            } else {
              // 老玩家：进入大厅场景
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness(`👤 ${(_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
                error: Error()
              }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
                error: Error()
              }), Platform) : Platform).FACEBOOK ? 'Facebook' : (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
                error: Error()
              }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
                error: Error()
              }), Platform) : Platform).PERSONAL ? '个人' : ''}老玩家：进入大厅场景（Hall）`);
              success = await (_crd && smc === void 0 ? (_reportPossibleCrUseOfsmc({
                error: Error()
              }), smc) : smc).sceneMgr.switchToScene((_crd && SceneType === void 0 ? (_reportPossibleCrUseOfSceneType({
                error: Error()
              }), SceneType) : SceneType).Hall, undefined, showLoading, this.closeLoadingUI.bind(this));
            }

            if (success) {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness(`🎮 ${isNewPlayer ? '游戏' : '大厅'}场景加载成功`);
            } else {
              throw new Error(`${isNewPlayer ? '游戏' : '大厅'}场景加载失败`);
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logError('🔥 进入游戏失败:', error); // 更新加载界面显示错误

            this.updateLoadingProgress(0, '进入游戏失败', '请刷新页面重试');
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast('进入游戏失败，请刷新页面重试'); // 🎯 失败时也要移除组件，避免内存泄漏

            this.ent.remove(InitialViewComp);
          }
        }
        /** 🚨 错误处理 */


        handleInitializationError(error) {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logError('❌ 游戏初始化失败:', error); // 更新加载界面显示错误

          this.updateLoadingProgress(0, '初始化失败', error.message || '未知错误'); // 🚨 如果是Bundle加载失败，显示更明确的错误信息

          if (error.message.includes('bundleOne')) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).gui.toast('❌ 游戏资源包缺失，请检查资源配置'); // 🎯 严重错误时移除组件，避免内存泄漏

            this.ent.remove(InitialViewComp);
            return; // 不重试，直接失败
          } // 其他错误可以重试


          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).gui.toast('初始化失败，请检查网络后重试');
          this.retryInitialization();
        }
        /** 🔧 初始化重试机制 */


        retryInitialization() {
          setTimeout(() => {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('🔄 尝试重新初始化...');
            this.currentProgress = 0;
            this.updateLoadingProgress(0, '正在重试...', '重新初始化游戏');
            this.startFullInitialization();
          }, 2000);
        }
        /** 通知HTML加载完成 */


        notifyHTMLLoadingComplete() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🚪 通知HTML加载完成，关闭自定义加载界面');

          try {
            // 更新进度到100%
            if (window.updateProgress && typeof window.updateProgress === 'function') {
              window.updateProgress(100, '加载完成');
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ HTML进度更新为100%');
            } else {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('ℹ️ window.updateProgress 方法不存在（正常情况）');
            } // 隐藏加载界面


            if (window.hideLoader && typeof window.hideLoader === 'function') {
              window.hideLoader();
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('✅ 自定义加载界面已隐藏');
            } else {
              (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
                error: Error()
              }), oops) : oops).log.logBusiness('ℹ️ window.hideLoader 方法不存在（正常情况）');
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 通知HTML加载完成时出现异常:', error);
          }
        }
        /** 关闭加载界面 */


        closeLoadingUI() {
          // 🎯 计算启动总时间
          const endTime = Date.now();
          const totalTime = endTime - this.startTime; // 🎯 自定义加载界面的平台都调用HTML通知

          if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).FACEBOOK || (_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).PERSONAL) {
            this.notifyHTMLLoadingComplete();
          } // Facebook生产环境额外通知


          if ((_crd && ShareConfig === void 0 ? (_reportPossibleCrUseOfShareConfig({
            error: Error()
          }), ShareConfig) : ShareConfig).platform === (_crd && Platform === void 0 ? (_reportPossibleCrUseOfPlatform({
            error: Error()
          }), Platform) : Platform).FACEBOOK) {
            this.notifyFacebookComplete();
          }

          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logBusiness('🎉 游戏启动完成！', {
            totalTime: `${totalTime}ms`,
            performance: totalTime < 3000 ? '优秀' : totalTime < 5000 ? '良好' : '需要优化'
          }); // 移除自己

          this.ent.remove(InitialViewComp);
        }
        /** 通知Facebook完成 */


        notifyFacebookComplete() {
          if (window.FBInstant && window.FBInstant.setLoadingProgress) {
            window.FBInstant.setLoadingProgress(100);
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logBusiness('📊 Facebook SDK: 进度已设置为100%');
          }
        }
        /** 窗口打开失败回调 */


        onOpenFailure() {
          (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
            error: Error()
          }), oops) : oops).log.logError('❌ 窗口打开失败');
        }
        /** 获取token信息 */


        getTokenInfo() {
          try {
            const tokenInfoStr = (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).storage.get((_crd && GameStorageConfig === void 0 ? (_reportPossibleCrUseOfGameStorageConfig({
              error: Error()
            }), GameStorageConfig) : GameStorageConfig).SSOTokenInfo);

            if (tokenInfoStr) {
              return JSON.parse(tokenInfoStr);
            }
          } catch (error) {
            (_crd && oops === void 0 ? (_reportPossibleCrUseOfoops({
              error: Error()
            }), oops) : oops).log.logWarn('⚠️ 解析token信息失败:', error);
          }

          return null;
        }

        reset() {
          this.waitComplete = false;
          this.loadComplete = false;
          this.currentProgress = 0;
        }

      }) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=f15f8a5e1033fcb170fe8d91d64a76565474f284.js.map