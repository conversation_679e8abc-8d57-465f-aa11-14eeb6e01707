[1, 0, 0, [["cc.EffectAsset", ["_name", "shaders", "techniques"], 0]], [[0, 0, 1, 2, 4]], [[0, "pipeline/skybox", [{"hash": 4049110380, "name": "pipeline/skybox|sky-vs:vert|sky-fs:frag", "blocks": [], "samplerTextures": [{"name": "environmentMap", "type": 31, "count": 1, "stageFlags": 16, "binding": 0, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": [], "attributes": [{"name": "a_position", "format": 32, "location": 0, "defines": []}, {"name": "a_normal", "format": 32, "location": 1, "defines": []}, {"name": "a_texCoord", "format": 21, "location": 2, "defines": []}, {"name": "a_tangent", "format": 44, "location": 3, "defines": []}, {"name": "a_joints", "location": 4, "defines": ["CC_USE_SKINNING"]}, {"name": "a_weights", "format": 44, "location": 5, "defines": ["CC_USE_SKINNING"]}, {"name": "a_jointAnimInfo", "format": 44, "isInstanced": true, "location": 6, "defines": ["USE_INSTANCING", "CC_USE_BAKED_ANIMATION"]}, {"name": "a_matWorld0", "format": 44, "isInstanced": true, "location": 7, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld1", "format": 44, "isInstanced": true, "location": 8, "defines": ["USE_INSTANCING"]}, {"name": "a_matWorld2", "format": 44, "isInstanced": true, "location": 9, "defines": ["USE_INSTANCING"]}, {"name": "a_lightingMapUVParam", "format": 44, "isInstanced": true, "location": 10, "defines": ["USE_INSTANCING", "CC_USE_LIGHTMAP"]}, {"name": "a_localShadowBiasAndProbeId", "format": 44, "isInstanced": true, "location": 11, "defines": ["USE_INSTANCING"]}, {"name": "a_reflectionProbeData", "format": 44, "isInstanced": true, "location": 12, "defines": ["USE_INSTANCING", "CC_USE_REFLECTION_PROBE"]}, {"name": "a_sh_linear_const_r", "format": 44, "isInstanced": true, "location": 13, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_g", "format": 44, "isInstanced": true, "location": 14, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_sh_linear_const_b", "format": 44, "isInstanced": true, "location": 15, "defines": ["USE_INSTANCING", "CC_USE_LIGHT_PROBE"]}, {"name": "a_vertexId", "format": 11, "location": 16, "defines": ["CC_USE_MORPH"]}], "fragColors": [{"name": "cc_FragColor", "typename": "vec4", "type": 16, "count": 1, "stageFlags": 16, "location": 0, "defines": []}], "descriptors": [{"rate": 0, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 1, "blocks": [], "samplerTextures": [{"name": "environmentMap", "type": 31, "count": 1, "stageFlags": 16, "binding": 0, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 2, "blocks": [], "samplerTextures": [], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}, {"rate": 3, "blocks": [{"name": "CCGlobal", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_time", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_screenSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nativeSize", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_probeInfo", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_debug_view_mode", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}, {"name": "CCCamera", "stageFlags": 17, "tags": {"builtin": "global"}, "members": [{"name": "cc_matView", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProj", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_matViewProjInv", "typename": "mat4", "type": 25, "count": 1, "precision": "highp "}, {"name": "cc_cameraPos", "typename": "vec4", "type": 16, "count": 1, "precision": "highp "}, {"name": "cc_surfaceTransform", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_screenScale", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_exposure", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitDir", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_mainLitColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientSky", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_ambientGround", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogColor", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogBase", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_fogAdd", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_nearFar", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}, {"name": "cc_viewPort", "typename": "vec4", "type": 16, "count": 1, "precision": "mediump "}], "defines": []}], "samplerTextures": [{"name": "cc_environment", "typename": "samplerCube", "type": 31, "count": 1, "stageFlags": 16, "tags": {"builtin": "global"}, "defines": []}], "samplers": [], "textures": [], "buffers": [], "images": [], "subpassInputs": []}], "glsl3": {"vert": "\nprecision highp float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\n#define QUATER_PI         0.78539816340\n#define HALF_PI           1.57079632679\n#define PI                3.14159265359\n#define PI2               6.28318530718\n#define PI4               12.5663706144\n#define INV_QUATER_PI     1.27323954474\n#define INV_HALF_PI       0.63661977237\n#define INV_PI            0.31830988618\n#define INV_PI2           0.15915494309\n#define INV_PI4           0.07957747155\n#define EPSILON           1e-6\n#define EPSILON_LOWP      1e-4\n#define LOG2              1.442695\n#define EXP_VALUE         2.71828183\n#define FP_MAX            65504.0\n#define FP_SCALE          0.0009765625\n#define FP_SCALE_INV      1024.0\n#define GRAY_VECTOR       vec3(0.299, 0.587, 0.114)\n#define LIGHT_MAP_TYPE_DISABLED 0\n#define LIGHT_MAP_TYPE_ALL_IN_ONE 1\n#define LIGHT_MAP_TYPE_INDIRECT_OCCLUSION 2\n#define REFLECTION_PROBE_TYPE_NONE 0\n#define REFLECTION_PROBE_TYPE_CUBE 1\n#define REFLECTION_PROBE_TYPE_PLANAR 2\n#define REFLECTION_PROBE_TYPE_BLEND 3\n#define REFLECTION_PROBE_TYPE_BLEND_AND_SKYBOX 4\n#define LIGHT_TYPE_DIRECTIONAL 0.0\n#define LIGHT_TYPE_SPHERE 1.0\n#define LIGHT_TYPE_SPOT 2.0\n#define LIGHT_TYPE_POINT 3.0\n#define LIGHT_TYPE_RANGED_DIRECTIONAL 4.0\n#define IS_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_DIRECTIONAL)) < EPSILON_LOWP)\n#define IS_SPHERE_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPHERE)) < EPSILON_LOWP)\n#define IS_SPOT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_SPOT)) < EPSILON_LOWP)\n#define IS_POINT_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_POINT)) < EPSILON_LOWP)\n#define IS_RANGED_DIRECTIONAL_LIGHT(light_type) (abs(float(light_type) - float(LIGHT_TYPE_RANGED_DIRECTIONAL)) < EPSILON_LOWP)\n#define TONE_MAPPING_ACES 0\n#define TONE_MAPPING_LINEAR 1\n#define SURFACES_MAX_TRANSMIT_DEPTH_VALUE 999999.0\n#ifndef CC_SURFACES_DEBUG_VIEW_SINGLE\n  #define CC_SURFACES_DEBUG_VIEW_SINGLE 1\n#endif\n#ifndef CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC\n  #define CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC 2\n#endif\nstruct StandardVertInput {\n  highp vec4 position;\n  vec3 normal;\n  vec4 tangent;\n};\nin vec3 a_position;\nin vec3 a_normal;\nin vec2 a_texCoord;\nin vec4 a_tangent;\n#if CC_USE_SKINNING\n    in vec4 a_joints;\n  in vec4 a_weights;\n#endif\n#if USE_INSTANCING\n  #if CC_USE_BAKED_ANIMATION\n    in highp vec4 a_jointAnimInfo;\n  #endif\n  in vec4 a_matWorld0;\n  in vec4 a_matWorld1;\n  in vec4 a_matWorld2;\n  #if CC_USE_LIGHTMAP\n    in vec4 a_lightingMapUVParam;\n  #endif\n  #if CC_USE_REFLECTION_PROBE || CC_RECEIVE_SHADOW\n    #if CC_RECEIVE_SHADOW\n    #endif\n    in vec4 a_localShadowBiasAndProbeId;\n  #endif\n  #if CC_USE_REFLECTION_PROBE\n    in vec4 a_reflectionProbeData;\n  #endif\n  #if CC_USE_LIGHT_PROBE\n    in vec4 a_sh_linear_const_r;\n    in vec4 a_sh_linear_const_g;\n    in vec4 a_sh_linear_const_b;\n  #endif\n#endif\n#if CC_USE_MORPH\n    in float a_vertexId;\n#endif\nout mediump vec4 viewDir;\nvec4 vert () {\n    viewDir = vec4(a_position, 1.0);\n  mat4 matViewRotOnly = mat4(mat3(cc_matView));\n  vec4 pos = matViewRotOnly * viewDir;\n  if (cc_matProj[3].w > 0.0) {\n    mat4 matProj = cc_matProj;\n    matProj[0].x = 5.2;\n    matProj[1].y = 2.6;\n    matProj[2].zw = vec2(-1.0);\n    matProj[3].zw = vec2(0.0);\n    pos = matProj * pos;\n  } else {\n    pos = cc_matProj * pos;\n  }\n  pos.z = 0.99999 * pos.w;\n  return pos;\n}\nvoid main() { gl_Position = vert(); }", "frag": "\nprecision mediump float;\nlayout(std140) uniform CCGlobal {\n  highp   vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_nativeSize;\n  mediump vec4 cc_probeInfo;\n  mediump vec4 cc_debug_view_mode;\n};\nlayout(std140) uniform CCCamera {\n  highp   mat4 cc_matView;\n  highp   mat4 cc_matViewInv;\n  highp   mat4 cc_matProj;\n  highp   mat4 cc_matProjInv;\n  highp   mat4 cc_matViewProj;\n  highp   mat4 cc_matViewProjInv;\n  highp   vec4 cc_cameraPos;\n  mediump vec4 cc_surfaceTransform;\n  mediump vec4 cc_screenScale;\n  mediump vec4 cc_exposure;\n  mediump vec4 cc_mainLitDir;\n  mediump vec4 cc_mainLitColor;\n  mediump vec4 cc_ambientSky;\n  mediump vec4 cc_ambientGround;\n  mediump vec4 cc_fogColor;\n  mediump vec4 cc_fogBase;\n  mediump vec4 cc_fogAdd;\n  mediump vec4 cc_nearFar;\n  mediump vec4 cc_viewPort;\n};\nuniform samplerCube cc_environment;\nvec4 fragTextureLod (sampler2D tex, vec2 coord, float lod) {\n    return textureLod(tex, coord, lod);\n}\nvec4 fragTextureLod (samplerCube tex, vec3 coord, float lod) {\n      return textureLod(tex, coord, lod);\n}\nvec4 packRGBE (vec3 rgb) {\n  highp float maxComp = max(max(rgb.r, rgb.g), rgb.b);\n  highp float e = 128.0;\n  if (maxComp > 0.0001) {\n    e = log(maxComp) / log(1.1);\n    e = ceil(e);\n    e = clamp(e + 128.0, 0.0, 255.0);\n  }\n  highp float sc = 1.0 / pow(1.1, e - 128.0);\n  vec3 encode = clamp(rgb * sc, vec3(0.0), vec3(1.0)) * 255.0;\n  vec3 encode_rounded = floor(encode) + step(encode - floor(encode), vec3(0.5));\n  return vec4(encode_rounded, e) / 255.0;\n}\nvec3 unpackRGBE (vec4 rgbe) {\n  return rgbe.rgb * pow(1.1, rgbe.a * 255.0 - 128.0);\n}\nvec3 SRGBToLinear (vec3 gamma) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return gamma;\n    }\n  #endif\n#endif\n  return gamma * gamma;\n}\nvec3 LinearToSRGB(vec3 linear) {\n#ifdef CC_USE_SURFACE_SHADER\n  #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n    if (!IS_DEBUG_VIEW_COMPOSITE_ENABLE_GAMMA_CORRECTION) {\n      return linear;\n    }\n  #endif\n#endif\n  return sqrt(linear);\n}\nvec3 ACESToneMap (vec3 color) {\n  color = min(color, vec3(8.0));\n  const float A = 2.51;\n  const float B = 0.03;\n  const float C = 2.43;\n  const float D = 0.59;\n  const float E = 0.14;\n  return (color * (A * color + B)) / (color * (C * color + D) + E);\n}\nvec3 HDRToLDR(vec3 color)\n{\n  #if CC_USE_HDR\n    #if CC_USE_DEBUG_VIEW == CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC && CC_SURFACES_ENABLE_DEBUG_VIEW\n      if (IS_DEBUG_VIEW_COMPOSITE_ENABLE_TONE_MAPPING)\n    #endif\n    {\n    #if CC_TONE_MAPPING_TYPE == HDR_TONE_MAPPING_ACES\n      color.rgb = ACESToneMap(color.rgb);\n    #endif\n    }\n  #endif\n  return color;\n}\n#if defined(CC_USE_METAL) || defined(CC_USE_WGPU)\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y) y = -y\n#else\n#define CC_HANDLE_SAMPLE_NDC_FLIP_STATIC(y)\n#endif\nvec3 RotationVecFromAxisY(vec3 v, float cosTheta, float sinTheta)\n{\n    vec3 result;\n    result.x = dot(v, vec3(cosTheta, 0.0, -sinTheta));\n    result.y = v.y;\n    result.z = dot(v, vec3(sinTheta, 0.0,  cosTheta));\n    return result;\n}\nvec3 RotationVecFromAxisY(vec3 v, float rotateAngleArc)\n{\n  return RotationVecFromAxisY(v, cos(rotateAngleArc), sin(rotateAngleArc));\n}\nin mediump vec4 viewDir;\nuniform samplerCube environmentMap;\nvec4 frag () {\n  vec3 rotationDir = RotationVecFromAxisY(viewDir.xyz, cc_surfaceTransform.z, cc_surfaceTransform.w);\n  #if USE_RGBE_CUBEMAP\n    vec3 c = unpackRGBE(fragTextureLod(environmentMap, rotationDir.xyz, 0.0));\n  #else\n    vec3 c = SRGBToLinear(fragTextureLod(environmentMap, rotationDir.xyz, 0.0).rgb);\n  #endif\n  vec4 color = vec4(c * cc_ambientSky.w, 1.0);\n  #if CC_USE_RGBE_OUTPUT\n    color = packRGBE(color.rgb);\n  #else\n    color.rgb = HDRToLDR(color.rgb);\n    color.rgb = LinearToSRGB(color.rgb);\n  #endif\n  return color;\n}\nlayout(location = 0) out vec4 cc_FragColor;\nvoid main() { cc_FragColor = frag(); }"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}, {"name": "CCCamera", "defines": []}], "samplerTextures": [{"name": "cc_environment", "defines": []}], "buffers": [], "images": []}, "locals": {"blocks": [], "samplerTextures": [], "buffers": [], "images": []}, "statistics": {"CC_EFFECT_USED_VERTEX_UNIFORM_VECTORS": 42, "CC_EFFECT_USED_FRAGMENT_UNIFORM_VECTORS": 42}}, "defines": [{"name": "USE_INSTANCING", "type": "boolean"}, {"name": "CC_USE_SKINNING", "type": "boolean"}, {"name": "CC_USE_BAKED_ANIMATION", "type": "boolean"}, {"name": "CC_USE_LIGHTMAP", "type": "boolean"}, {"name": "CC_USE_REFLECTION_PROBE", "type": "boolean"}, {"name": "CC_RECEIVE_SHADOW", "type": "boolean"}, {"name": "CC_USE_LIGHT_PROBE", "type": "boolean"}, {"name": "CC_USE_MORPH", "type": "boolean"}, {"name": "CC_USE_IBL", "type": "number", "range": [0, 2]}, {"name": "CC_USE_DEBUG_VIEW", "type": "number", "range": [0, 3]}, {"name": "CC_SURFACES_DEBUG_VIEW_COMPOSITE_AND_MISC", "type": "boolean"}, {"name": "CC_SURFACES_ENABLE_DEBUG_VIEW", "type": "boolean"}, {"name": "CC_USE_HDR", "type": "boolean"}, {"name": "CC_TONE_MAPPING_TYPE", "type": "number", "range": [0, 3]}, {"name": "HDR_TONE_MAPPING_ACES", "type": "boolean"}, {"name": "USE_RGBE_CUBEMAP", "type": "boolean"}, {"name": "CC_USE_RGBE_OUTPUT", "type": "boolean"}]}], [{"passes": [{"program": "pipeline/skybox|sky-vs:vert|sky-fs:frag", "priority": 245, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": true, "depthWrite": false}, "properties": {"environmentMap": {"value": "grey", "type": 31}}}, {"propertyIndex": 0, "phase": "deferred-forward", "program": "pipeline/skybox|sky-vs:vert|sky-fs:frag", "priority": 245, "rasterizerState": {"cullMode": 0}, "depthStencilState": {"depthTest": true, "depthWrite": false}}]}]]], 0, 0, [], [], []]