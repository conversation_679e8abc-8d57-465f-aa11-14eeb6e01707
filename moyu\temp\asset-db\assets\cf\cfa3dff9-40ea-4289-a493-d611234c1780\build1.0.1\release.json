[1, ["56oY0PFUVKRJA95WSWzdXD", "e28dJLVXJC3YFObxU5gPQx@3434a"], ["node", "_mesh", "root", "data"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lrot", "_lscale", "_euler"], 2, 9, 4, 5, 5, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["3eb5fdhNS5Iu4yEQ+dEujjt", ["node", "__prefab"], 3, 1, 4], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["<PERSON>.<PERSON><PERSON><PERSON>", ["_radius", "_height", "_direction", "node", "__prefab", "_center"], 0, 1, 4, 5], ["cc.S<PERSON>ider", ["_radius", "node", "__prefab", "_center"], 2, 1, 4, 5], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_direction", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[3, 0, 2], [8, 0, 1, 2, 3, 2], [0, 0, 2], [1, 0, 1, 2, 3, 4, 5, 2], [2, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 3], [5, 0, 1, 1], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 5, 4], [9, 0, 1, 2, 3, 4, 3], [10, 0, 1, 2, 3, 4, 5, 5]], [[2, "芦笋"], [3, "芦笋", [[4, 1, -2, [0, "edIYjgxhJLMIZMz8cRvuh9"], [0], [5, true, true], 1], [6, -3, [0, "c3VmWRDt9FZ6SL9b30QHq+"]], [7, 4, -4, [0, "457rsGmjVBX50WPtAbdC4t"]], [8, 0.082, 1.919, 2, -5, [0, "eeWbAY/npAipUEqPbbCKUs"], [1, 0.004921022802591324, 0.011396970599889755, 0.08581548929214478]], [1, 0.088, -6, [0, "9c2kGStGlKWJNArnYROmwS"], [1, 0.004921022802591324, 0.03, 1.03]], [1, 0.088, -7, [0, "10asz1HmdBPqb11vI8VY/x"], [1, 0.004921022802591324, 0.03, 1]], [9, 0.089, 2, -8, [0, "68vqbe58JEDqmLTe7DU8Go"], [1, 0, 0, -0.34]]], [10, "53u7hnLG9APYJ9gjM0AVUQ", null, null, null, -1, 0], [3, 0, -0.25881904510252074, 0, 0.9659258262890683], [1, 3, 3, 3], [1, 0, -30, 0]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 3, 1, 8], [0, 0], [-1, 1], [0, 1]]