# 新手启动API调用失败修复

## 🔍 **问题分析**

从日志中发现的问题：
1. **第120行**：`[ApiReq] #1 UserInfo {}`
2. **第127行**：`[ApiErr] #1 UserInfo TsrpcError {message: '登录后获取访问权限', type: 'ApiError', code: 'NEED_LOGIN'}`

**根本原因**：新手快速启动时只创建了临时数据，没有完成服务器端登录验证，但API调用需要登录状态。

## 🚀 **修复方案**

### 1. **后台登录流程重构**
- 不再直接调用 `smc.role.loadData()`（会立即调用UserInfo API）
- 改为先完成登录验证，再加载用户数据

### 2. **游客登录流程**
```typescript
performGuestLogin() {
    // 1. 检查现有登录凭据
    // 2. 如果没有，注册新游客账号
    // 3. 使用凭据登录
    // 4. 保存登录状态
}
```

### 3. **API调用优化**
- `completeNewPlayerGuide`: 临时状态下延迟服务器同步
- `updateProp`: 临时状态下本地更新，后台同步队列

## 🔧 **修复内容**

### A. **后台登录流程**
```typescript
/** 🔐 执行完整的登录流程 */
private async performCompleteLogin(): Promise<void> {
    if (ShareConfig.platform === Platform.FACEBOOK) {
        // Facebook登录
        await LoginViewComp.doFacebookLogin();
    } else {
        // 游客登录流程
        await this.performGuestLogin();
        await smc.role.loadData(); // 登录成功后再加载数据
    }
}
```

### B. **游客注册和登录**
```typescript
/** 🆔 执行游客注册 */
private async performGuestRegister() {
    const response = await smc.net.hcGate.callApi('Register', {
        platform: 'web',
        platformType: 'web',
        isGuest: true,
        countryCode: 'Other',
    });
    return response.res; // { userName, passWord }
}

/** 🔐 执行登录 */
private async performLogin(userName: string, passWord: string) {
    const response = await smc.net.hcGate.callApi('Login', {
        server: httpUrl,
        userName: userName,
        passWord: passWord,
    });
    // 保存登录凭据
}
```

## 📊 **预期效果**

### ✅ **修复后的流程**
1. **新手启动** → 创建临时数据（快速进入游戏）
2. **后台注册** → 调用Register API创建游客账号
3. **后台登录** → 调用Login API获取登录状态
4. **数据加载** → 登录成功后调用UserInfo API
5. **状态同步** → 同步所有待处理的操作

### 🎯 **解决的问题**
- ❌ `UserInfo TsrpcError: NEED_LOGIN` → ✅ 正常调用
- ❌ `GameUpdateSimpleData TsrpcError` → ✅ 延迟同步
- ❌ `UpdateProp TsrpcError` → ✅ 本地更新+后台同步

## 🧪 **测试要点**

1. **启动速度** - 新手应该快速进入游戏
2. **API调用** - 不应该有NEED_LOGIN错误
3. **道具使用** - 应该能正常使用道具
4. **引导完成** - 应该能正常完成新手引导
5. **数据同步** - 后台登录完成后数据应该同步

## 📝 **关键日志**

成功的日志应该显示：
- `🆔 注册新的游客账号...`
- `✅ 游客注册成功: [用户名]`
- `🔐 使用注册返回的凭据登录`
- `✅ 登录成功`
- `✅ 登录凭据已保存，准备加载用户数据`
- `✅ 标准登录流程完成`
- `✅ 后台用户数据加载完成`
