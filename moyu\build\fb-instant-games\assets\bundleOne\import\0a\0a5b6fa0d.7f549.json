[1, ["56oY0PFUVKRJA95WSWzdXD", "e28dJLVXJC3YFObxU5gPQx@18f32"], ["node", "root", "data", "_mesh"], [["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "_direction", "node", "__prefab", "_center"], 0, 1, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lrot", "_euler"], 2, 9, 4, 5, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", [], 3], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["<PERSON>.<PERSON><PERSON><PERSON>", ["_radius", "_height", "_direction", "node", "__prefab", "_center"], 0, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11]], [[4, 0, 2], [7, 0, 1, 2, 3, 4, 5, 4], [1, 0, 2], [2, 0, 1, 2, 3, 4, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 1], [6, 0, 1, 2, 2], [0, 0, 1, 3, 4, 5, 3], [0, 0, 1, 2, 3, 4, 5, 4], [8, 0, 1, 2, 3, 4, 5, 5], [9, 0, 1, 2, 3]], [[[[2, "甜菜"], [3, "甜菜", [[4, 1, -2, [0, "52c1uoyJVBprrzXpONJYxX"], [0], [5], 1], [6, 4, -3, [0, "a3ll+/L+JIM6EfVmulolyZ"]], [7, 0.385, 0, -4, [0, "0av0dpkiBJbZLvf5NogLDP"], [1, 0, 0, -0.245251]], [8, 0.147, 0.834, 2, -5, [0, "4e4DMnuFhF0LwT3ZCGmYX5"], [1, 0, 0, 0.4]], [1, 0.452, 0.19, 2, -6, [0, "ffVAP1npJIIoZAczT5qOWo"], [1, 0.2, -0.4, 1.2]], [1, 0.743, 0.505, 2, -7, [0, "46q5k5xEdHhZMu3hHah4Jf"], [1, -0.1, 0.4, 0.7]]], [9, "9d4kbvJNNKUZJ/+Jq9QBuh", null, null, null, -1, 0], [3, 1, 0, 0, 6.123233995736766e-17], [1, 180, 0, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 7], [0, 0], [-1, 3], [0, 1]], [[[10, ".bin", 761545318, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 72320, "length": 8472, "count": 4236, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 72320, "count": 1130, "stride": 64}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}, {"name": "a_texCoord1", "format": 21, "isNormalized": false}, {"name": "a_texCoord2", "format": 21, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.8730858564376831, -0.7318735122680664, -0.7036548852920532], "maxPosition", 8, [1, 0.695838451385498, 1.0411202907562256, 1.4131526947021484]]], -1], 0, 0, [], [], []]]]