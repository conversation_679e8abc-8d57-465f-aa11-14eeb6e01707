[1, ["e5LUoqx3RAr41dA5QrbKMj", "e2MN/YHmxMNLScDnRAz99K@7e977"], ["node", "root", "data", "_mesh"], [["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11]], [[3, 0, 2], [0, 0, 2], [1, 0, 1, 2, 2], [2, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 3], [5, 0, 1, 2, 2], [6, 0, 1, 2, 3, 4, 3], [7, 0, 1, 2, 3, 4, 5, 5], [8, 0, 1, 2, 3]], [[[[1, "日式寿司_01"], [2, "日式寿司_01", [[3, 1, -2, [0, "6daE5id91ACYBNKC9t52Ai"], [0], [4, true, true], 1], [5, 4, -3, [0, "6cJM1VZWtDfpiFOjwwl5t6"]], [6, 0.6693820357322693, 0, -4, [0, "5dR7oDfd9Anadi6nrhDr1L"], [1, -0.010988950729370117, 0.09023606032133102, 0.014634042978286743]]], [7, "f6KfjbDARD+ZwZj6TPRdFr", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]], [[[8, ".bin", 2841659500, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 28896, "length": 5178, "count": 2589, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 28896, "count": 602, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.6803709864616394, -0.22329466044902802, -0.279572069644928], "maxPosition", 8, [1, 0.6583930850028992, 0.40376678109169006, 0.30884015560150146]]], -1], 0, 0, [], [], []]]]