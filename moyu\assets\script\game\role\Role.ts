import { oops } from '../../../../extensions/oops-plugin-framework/assets/core/Oops';
import { ecs } from '../../../../extensions/oops-plugin-framework/assets/libs/ecs/ECS';
import { LanguageData } from '../../../../extensions/oops-plugin-framework/assets/libs/gui/language/LanguageData';
import { VM } from '../../../../extensions/oops-plugin-framework/assets/libs/model-view/ViewModel';
import { GameConst } from '../../tsrpc/models/GameConst';
import { Platform, ShareConfig } from '../../tsrpc/models/ShareConfig';
import {
    PropType,
    RecordType,
    RecordTypeData,
    SceneItemType,
    UserGameData,
} from '../../tsrpc/protocols/base';
import { GameStorageConfig } from '../common/config/GameStorageConfig';
import { GameEvent } from '../common/Enum';
import { smc } from '../common/SingletonModuleComp';
import { RoleModelComp } from './model/RoleModelComp';

/**
 * 角色管理器 - 重构版本
 * 职责：
 * 1. 用户数据的加载、更新和管理
 * 2. 道具系统的操作和验证
 * 3. 游戏进度的管理
 * 4. ViewModel的数据绑定
 */
@ecs.register('Role')
export class Role extends ecs.Entity {
    RoleModel!: RoleModelComp;

    // 🔄 待同步的道具更新队列
    private pendingPropUpdates: Array<{
        propType: PropType;
        amount: number;
        reason?: string;
    }> = [];

    // ==================== 初始化 ====================

    protected init() {
        this.add(RoleModelComp);
    }

    destroy(): void {
        this.removeFromViewModel();
        this.remove(RoleModelComp);
    }

    // ==================== 数据加载 ====================

    /**
     * 🚀 快速初始化用户数据 - 统一入口
     */
    async quickInitialize(): Promise<void> {
        try {
            // 🔍 检查本地登录痕迹，决定启动策略
            const hasLocalTrace = this.hasLocalLoginTrace();

            if (hasLocalTrace) {
                // 有本地痕迹：尝试完整登录，失败则降级为新手模式
                oops.log.logBusiness('🔄 检测到本地痕迹，尝试完整登录...');

                const loginSuccess = await this.loadData();
                if (loginSuccess) {
                    oops.log.logBusiness('✅ 老玩家登录成功');
                    return;
                } else {
                    oops.log.logWarn('⚠️ 老玩家登录失败，降级为新手快速启动');
                    // 清理可能损坏的本地数据
                    this.clearCorruptedLocalData();
                }
            }

            // 新手模式或登录失败降级：快速启动
            oops.log.logBusiness('🚀 启用新手快速启动模式');
            await this.initializeNewPlayerData();

            // 🔄 在后台继续完整的用户数据加载
            this.loadDataInBackground();
        } catch (error) {
            oops.log.logWarn('⚠️ 快速用户数据初始化失败:', error);
            // 失败时创建基础数据，确保游戏可以启动
            await this.initializeNewPlayerData();
        }
    }

    /**
     * 加载用户数据
     * @returns 是否加载成功
     */
    async loadData(): Promise<boolean> {
        oops.log.logBusiness('🔄 开始加载用户数据...');

        if (!this.validateNetworkConnection()) {
            return false;
        }

        try {
            const response = await smc.net.hcGame.callApi('UserInfo', {});

            if (response.isSucc) {
                // UserInfo API成功后，数据应该已经通过DataManager更新了
                oops.log.logBusiness('✅ 用户数据加载成功');
                return true;
            } else {
                this.handleAPIError('UserInfo', response.err);
                return false;
            }
        } catch (error) {
            oops.log.logError('❌ 用户数据加载异常:', error);
            return false;
        }
    }

    /**
     * 更新用户数据（由DataManager调用）
     */
    updateUserData(newData: UserGameData): void {
        const isFirstInitialization = !this.hasUserData();

        // 确保数据结构完整
        this.ensureDataIntegrity(newData);

        // 合并数据
        if (this.RoleModel.userGameData) {
            this.mergeUserData(this.RoleModel.userGameData, newData);
        } else {
            this.RoleModel.userGameData = newData;
        }

        // 更新ViewModel
        this.updateViewModel();

        // 首次初始化触发事件
        if (isFirstInitialization) {
            oops.log.logBusiness('🎉 用户数据首次初始化完成');
            oops.message.dispatchEvent(GameEvent.UserDataInitialized, this.RoleModel.userGameData);
        }
    }

    // ==================== 道具系统 ====================

    /**
     * 获取道具数据
     */
    getPropData(propType: PropType): any {
        if (!this.hasUserData()) {
            return this.createDefaultPropData(propType);
        }

        const propData = this.RoleModel.userGameData.propUseData?.[propType];
        if (!propData) {
            // 创建默认道具数据
            const defaultData = this.createDefaultPropData(propType);
            this.RoleModel.userGameData.propUseData = this.RoleModel.userGameData.propUseData || {};
            this.RoleModel.userGameData.propUseData[propType] = defaultData;
            return defaultData;
        }

        return propData;
    }

    /**
     * 获取道具数据（别名方法，兼容现有代码）
     */
    getPropsDataByType(propType: PropType): any {
        return this.getPropData(propType);
    }

    /**
     * 尝试消耗道具（检查数量并显示提示）
     * @param args 道具参数（包含类型和数量）
     * @param showToast 是否显示提示
     * @param failureMessageKey 失败时的消息键
     * @returns 是否可以消耗
     */
    tryCostProp(
        args: { propType: PropType; amount: number },
        showToast: boolean = true,
        failureMessageKey?: string
    ): boolean {
        if (!this.hasUserData()) {
            if (showToast) {
                oops.gui.toast('用户数据未初始化');
            }
            return false;
        }

        const absAmount = Math.abs(args.amount);
        const canUse = this.canUseProp(args.propType, absAmount);

        if (!canUse && showToast && failureMessageKey) {
            oops.gui.toast(LanguageData.getLangByID(failureMessageKey) || failureMessageKey);
        }

        return canUse;
    }

    /**
     * 更新道具数据（别名方法，兼容现有代码）
     */
    async updatePropData(args: {
        propType: PropType;
        amount: number;
        reason?: string;
    }): Promise<boolean> {
        return await this.updateProp(args.propType, args.amount, args.reason);
    }

    /**
     * 检查道具是否足够
     */
    canUseProp(propType: PropType, amount: number): boolean {
        const propData = this.getPropData(propType);
        return propData.amount >= Math.abs(amount);
    }

    /**
     * 更新道具数量
     */
    async updateProp(propType: PropType, amount: number, reason?: string): Promise<boolean> {
        // 消耗操作前检查数量
        if (amount < 0 && !this.canUseProp(propType, Math.abs(amount))) {
            oops.gui.toast(LanguageData.getLangByID('UseLimitsDaily'));
            return false;
        }

        // 🔍 检查是否为临时数据状态
        const userData = this.RoleModel?.userGameData;
        if ((userData as any)?.isTemporaryData) {
            oops.log.logBusiness('🔄 检测到临时数据状态，本地更新道具数量');

            // 在临时状态下，直接更新本地数据
            const propData = this.getPropsDataByType(propType);
            if (propData) {
                propData.amount += amount;
                propData.lastUpdateTime = new Date();

                oops.log.logBusiness(
                    `✅ 道具本地更新: ${propType} ${amount > 0 ? '+' : ''}${amount} (临时)`
                );

                // 触发道具使用事件
                if (amount < 0) {
                    oops.message.dispatchEvent(GameEvent.UseProp, { propType, amount });
                }

                // 等后台登录完成后同步到服务器
                this.queuePropUpdateForSync(propType, amount, reason);
                return true;
            }
        }

        try {
            const response = await smc.net.hcGame.callApi('UpdateProp', {
                propType,
                amount,
                reason: reason || 'player_action',
            });

            if (response.isSucc) {
                oops.log.logBusiness(
                    `✅ 道具更新成功: ${propType} ${amount > 0 ? '+' : ''}${amount}`
                );

                // 触发道具使用事件
                if (amount < 0) {
                    oops.message.dispatchEvent(GameEvent.UseProp, { propType, amount });
                }

                return true;
            } else {
                this.handleAPIError('UpdateProp', response.err);
                return false;
            }
        } catch (error) {
            oops.log.logError('❌ 道具更新异常:', error);
            oops.gui.toast('网络错误，请重试');
            return false;
        }
    }

    /**
     * 批量更新道具
     */
    async batchUpdateProps(
        updates: Array<{ propType: PropType; amount: number; reason?: string }>
    ): Promise<boolean> {
        for (const update of updates) {
            const success = await this.updateProp(update.propType, update.amount, update.reason);
            if (!success) {
                return false;
            }
        }
        return true;
    }

    // ==================== 游戏进度 ====================

    /**
     * 获取当前通关进度
     */
    getGameProgress(): number {
        if (!this.hasUserData()) {
            return 0;
        }
        return this.RoleModel.userGameData.index || 0;
    }

    /**
     * 获取已通过的关卡索引（别名方法，兼容现有代码）
     */
    getPassIndex(): number {
        return this.getGameProgress();
    }

    /**
     * 获取下一关卡索引（循环通关）
     */
    getNextLevelIndex(currentProgress?: number): number {
        const progress = currentProgress ?? this.getGameProgress();
        const nextLevel = (progress + 1) % SceneItemType.Max;
        return nextLevel === 0 ? 1 : nextLevel;
    }

    /**
     * 更新游戏通关进度
     */
    async updateGameProgress(newIndex?: number, isGM: boolean = false): Promise<boolean> {
        const targetIndex = newIndex ?? this.getGameProgress() + 1;

        try {
            const response = await smc.net.hcGame.callApi('UpdateProgress', {
                index: targetIndex,
                isGm: isGM,
            });

            if (response.isSucc) {
                oops.log.logBusiness(`✅ 游戏进度更新成功: ${targetIndex}`);

                // 触发通关事件
                oops.message.dispatchEvent(GameEvent.GamePass, {
                    oldIndex: this.getGameProgress(),
                    newIndex: targetIndex,
                    isGm: isGM,
                });

                return true;
            } else {
                this.handleAPIError('UpdateProgress', response.err);
                return false;
            }
        } catch (error) {
            oops.log.logError('❌ 游戏进度更新异常:', error);
            oops.gui.toast('网络错误，请重试');
            return false;
        }
    }

    // ==================== 新手引导 ====================

    /**
     * 检查是否为新玩家 - 统一判断逻辑
     */
    isNewPlayer(): boolean {
        // 1. 优先使用服务端数据（最可靠）
        if (this.hasUserData()) {
            const isNew = this.RoleModel.userGameData.isNewPlayer ?? true;
            oops.log.logBusiness(`🎯 服务端数据判定新手状态: ${isNew}`);
            return isNew;
        }

        // 2. 如果没有服务端数据，保守判定为新手
        // 这样可以触发新手快速启动流程，避免API调用失败
        oops.log.logBusiness('🆕 无服务端数据，保守判定为新手玩家');
        return true;
    }

    /**
     * 🔍 检查本地是否有登录痕迹（用于优化启动流程）
     */
    hasLocalLoginTrace(): boolean {
        // 检查本地存储的登录记录
        const hasLoginRecord = !!oops.storage.get(GameStorageConfig.SSOToken);
        if (hasLoginRecord) {
            oops.log.logBusiness('🔍 检测到登录记录');
            return true;
        }

        // 检查是否有其他用户数据痕迹
        const userDumpKey = oops.storage.getJson(GameStorageConfig.UserDumpKey, null);
        if (userDumpKey && String(userDumpKey) !== '0') {
            oops.log.logBusiness('🔍 检测到用户数据痕迹');
            return true;
        }

        oops.log.logBusiness('🆕 无任何本地登录痕迹');
        return false;
    }

    /**
     * 🧹 清理损坏的本地数据
     */
    private clearCorruptedLocalData(): void {
        try {
            oops.log.logBusiness('🧹 清理损坏的本地数据...');

            // 清理可能损坏的登录相关数据
            oops.storage.remove(GameStorageConfig.SSOToken);
            oops.storage.remove(GameStorageConfig.SSOTokenInfo);

            // 保留用户偏好设置，只清理登录状态
            oops.log.logBusiness('✅ 损坏数据清理完成');
        } catch (error) {
            oops.log.logWarn('⚠️ 清理本地数据失败:', error);
        }
    }

    /**
     * 完成新手引导
     */
    async completeNewPlayerGuide(): Promise<boolean> {
        if (!this.isNewPlayer()) {
            oops.log.logBusiness('✅ 用户已完成新手引导');
            return true;
        }

        // 🔍 检查是否为临时数据状态
        const userData = this.RoleModel?.userGameData;
        if ((userData as any)?.isTemporaryData) {
            oops.log.logBusiness('🔄 检测到临时数据状态，延迟新手引导完成');
            // 先更新本地状态，等后台登录完成后再同步到服务器
            if (this.RoleModel && this.RoleModel.userGameData) {
                this.RoleModel.userGameData.isNewPlayer = false;
                oops.log.logBusiness('🔄 本地新手状态已更新为false（临时）');
            }

            // 监听后台登录完成事件，然后同步状态
            oops.message.once(
                'UserDataLoaded',
                async () => {
                    await this.syncNewPlayerStatusToServer();
                },
                this
            );

            return true;
        }

        return await this.syncNewPlayerStatusToServer();
    }

    /**
     * 🔄 同步新手状态到服务器
     */
    private async syncNewPlayerStatusToServer(): Promise<boolean> {
        try {
            const response = await smc.net.hcGame.callApi('GameUpdateSimpleData', {
                isNewPlayer: false,
            });

            if (response.isSucc) {
                // 🎯 立即更新本地数据，确保状态同步
                if (this.RoleModel && this.RoleModel.userGameData) {
                    this.RoleModel.userGameData.isNewPlayer = false;
                    oops.log.logBusiness('🔄 本地新手状态已更新为false');
                }

                oops.log.logBusiness('✅ 新手引导完成');
                oops.message.dispatchEvent(GameEvent.BasicInfoUpdate, { isNewPlayer: false });
                return true;
            } else {
                this.handleAPIError('GameUpdateSimpleData', response.err);
                return false;
            }
        } catch (error) {
            oops.log.logError('❌ 新手状态更新异常:', error);
            oops.gui.toast('网络错误，请重试');
            return false;
        }
    }

    /**
     * 🔄 将道具更新加入同步队列
     */
    private queuePropUpdateForSync(propType: PropType, amount: number, reason?: string): void {
        this.pendingPropUpdates.push({ propType, amount, reason });
        oops.log.logBusiness(`📝 道具更新已加入同步队列: ${propType} ${amount}`);

        // 监听后台登录完成事件
        oops.message.once(
            'UserDataLoaded',
            async () => {
                await this.syncPendingPropUpdates();
            },
            this
        );
    }

    /**
     * 🔄 同步待处理的道具更新到服务器
     */
    private async syncPendingPropUpdates(): Promise<void> {
        if (this.pendingPropUpdates.length === 0) {
            return;
        }

        oops.log.logBusiness(`🔄 开始同步 ${this.pendingPropUpdates.length} 个道具更新`);

        // 复制队列并清空原队列
        const updates = [...this.pendingPropUpdates];
        this.pendingPropUpdates = [];

        for (const update of updates) {
            try {
                const response = await smc.net.hcGame.callApi('UpdateProp', {
                    propType: update.propType,
                    amount: update.amount,
                    reason: update.reason || 'delayed_sync',
                });

                if (response.isSucc) {
                    oops.log.logBusiness(
                        `✅ 道具同步成功: ${update.propType} ${update.amount > 0 ? '+' : ''}${update.amount}`
                    );
                } else {
                    oops.log.logWarn(`⚠️ 道具同步失败: ${update.propType}`, response.err);
                }
            } catch (error) {
                oops.log.logError(`❌ 道具同步异常: ${update.propType}`, error);
            }
        }

        oops.log.logBusiness('✅ 道具更新同步完成');
    }

    // ==================== 新手数据初始化 ====================

    /**
     * 🚀 初始化新手玩家数据
     */
    private async initializeNewPlayerData(): Promise<void> {
        try {
            oops.log.logBusiness('🚀 开始创建默认的新手玩家数据...');

            // 创建完整的基础用户数据结构
            const currentTime = new Date();
            const basicUserData = {
                // 基础标识
                key: 0,
                guuid: '',
                googleUuid: '',
                facebookId: '',
                userName: '',
                nickName: '',
                sex: 1, // SexType.None
                createtime: currentTime,
                openid: '',
                platform: 'web',
                platformType: 'web',
                avatar: '',
                avatarId: 0,
                countryCode: 'Other',

                // 游戏进度
                passTimes: 0,
                index: 0,
                currCountryPassTimes: 0,
                lastChangeCountryTime: currentTime,
                selfCountryRank: 0,

                // 道具和记录数据
                propUseData: this.createDefaultProps(),
                recordData: {},

                // 新手和游客状态
                isNewPlayer: true,
                isGuest: true,
                lastStep: 0,

                // 🔄 标记为临时数据
                isTemporaryData: true,
            };

            // 🎯 设置用户数据
            this.updateUserData(basicUserData as any);

            // 🎯 确保ViewModel数据可用
            this.updateViewModel();

            oops.log.logBusiness('✅ 新手玩家数据初始化完成，ViewModel已更新');
        } catch (error) {
            oops.log.logWarn('⚠️ 新手玩家数据初始化失败:', error);
        }
    }

    /**
     * 🎁 创建新手玩家默认道具
     */
    private createDefaultProps(): { [key: number]: any } {
        const currentTime = new Date();
        const newPlayerProps: { [key: number]: any } = {};

        // 🎯 为每种道具类型创建默认数据
        const propTypes = [
            PropType.PropsMoveOut,
            PropType.PropsTips,
            PropType.PropsReShuffle,
            PropType.PropsDayLeftCount,
            PropType.PropsRevive,
            PropType.PropsExp,
            PropType.PropsCoin,
        ];

        propTypes.forEach(propType => {
            newPlayerProps[propType] = {
                propType: propType,
                amount: this.getNewPlayerDefaultAmount(propType),
                lastUpdateTime: currentTime,
            };
        });

        oops.log.logBusiness('🎁 新手默认道具创建完成', {
            propCount: Object.keys(newPlayerProps).length,
        });

        return newPlayerProps;
    }

    /**
     * 🔄 后台加载完整用户数据
     */
    private loadDataInBackground(): void {
        // 使用setTimeout确保不阻塞当前流程
        setTimeout(async () => {
            try {
                oops.log.logBusiness('🔄 开始后台加载完整用户数据...');

                // 🚀 强制执行完整的登录流程，不跳过
                await this.forceCompleteUserDataLoad();

                oops.log.logBusiness('✅ 后台用户数据加载完成');

                // 触发数据更新事件，通知游戏其他模块
                oops.message.dispatchEvent('UserDataLoaded');
            } catch (error) {
                oops.log.logWarn('⚠️ 后台用户数据加载失败:', error);
            }
        }, 100);
    }

    /**
     * 🔄 强制执行完整的用户数据加载流程
     */
    private async forceCompleteUserDataLoad(): Promise<void> {
        try {
            // 🔄 临时清除基础数据标记，强制执行完整登录
            const tempUserData = this.RoleModel?.userGameData;
            if (tempUserData) {
                // 标记这是临时数据，需要完整登录
                (tempUserData as any).isTemporaryData = true;
            }

            // 🔄 执行完整的登录流程
            await this.performCompleteLogin();
        } catch (error) {
            oops.log.logWarn('⚠️ 强制完整登录失败:', error);
            throw error;
        }
    }

    /**
     * 🔐 执行完整的登录流程
     */
    private async performCompleteLogin(): Promise<void> {
        oops.log.logBusiness('🔐 开始执行完整登录流程...');

        try {
            const { LoginViewComp } = await import('../initialize/view/LoginViewComp');

            // 🔧 根据平台选择不同的登录方式
            if (ShareConfig.platform === Platform.FACEBOOK) {
                // Facebook环境：执行Facebook自动登录
                oops.log.logBusiness('🔐 Facebook环境：执行自动登录...');
                const loginSuccess = await LoginViewComp.doFacebookLogin();

                if (loginSuccess) {
                    oops.log.logBusiness('✅ Facebook自动登录成功');
                } else {
                    oops.log.logWarn('⚠️ Facebook自动登录失败');
                }
            } else {
                // 🔐 其他环境：使用现有的游客登录逻辑
                oops.log.logBusiness('🔐 执行游客登录流程...');

                // 🚀 直接使用LoginViewComp的游客登录方法
                const loginViewComp = new LoginViewComp();
                const loginSuccess = await loginViewComp.loginGuestButton();

                if (loginSuccess) {
                    oops.log.logBusiness('✅ 游客登录成功');
                } else {
                    oops.log.logWarn('⚠️ 游客登录失败');
                }
            }

            oops.log.logBusiness('✅ 登录流程完成');
        } catch (error) {
            oops.log.logWarn('⚠️ 登录流程失败:', error);
            // 不抛出错误，允许游戏继续运行
        }
    }

    // ==================== 记录数据 ====================

    /**
     * 获取指定日期的记录数据
     */
    getRecordData(recordType: RecordType, dateString?: string): RecordTypeData | null {
        if (!this.hasUserData()) {
            return null;
        }

        const targetDate = dateString || new Date().toDateString();
        const recordData = this.RoleModel.userGameData.recordData?.[targetDate];

        return recordData?.[recordType] || null;
    }

    /**
     * 🔧 新增：获取指定关卡的今日挑战次数
     * @param levelId 关卡ID
     * @param dateString 可选的日期字符串，默认为今日
     * @returns 该关卡的挑战次数
     */
    getLevelChallengeCount(levelId: number, dateString?: string): number {
        const recordData = this.getRecordData(RecordType.Level, dateString);
        if (!recordData?.levelDetails) {
            return 0;
        }

        const levelKey = `level_${levelId}`;
        return recordData.levelDetails[levelKey]?.attempts || 0;
    }

    /**
     * 🔧 新增：获取当前关卡的今日挑战次数
     * @param dateString 可选的日期字符串，默认为今日
     * @returns 当前关卡的挑战次数
     */
    getCurrentLevelChallengeCount(dateString?: string): number {
        const currentLevelId = this.getNextLevelIndex(); // 获取当前要挑战的关卡
        return this.getLevelChallengeCount(currentLevelId, dateString);
    }

    // ==================== 工具方法 ====================

    /**
     * 获取完整的用户游戏数据
     */
    getUserGameData(): UserGameData {
        if (!this.hasUserData()) {
            throw new Error('用户数据尚未初始化，请等待登录完成');
        }
        return this.RoleModel.userGameData;
    }

    // ==================== 私有方法 ====================

    /**
     * 检查是否有用户数据
     */
    private hasUserData(): boolean {
        return !!this.RoleModel?.userGameData;
    }

    /**
     * 验证网络连接
     */
    private validateNetworkConnection(): boolean {
        if (!smc.net?.hcGame) {
            oops.log.logError('❌ 网络连接未初始化');
            return false;
        }
        return true;
    }

    /**
     * 确保数据结构完整性
     */
    private ensureDataIntegrity(data: UserGameData): void {
        data.key = data.key || 0;
        data.userName = data.userName || 'Player';
        data.index = data.index || 0;
        data.isNewPlayer = data.isNewPlayer ?? true;
        data.propUseData = data.propUseData || {};
        data.recordData = data.recordData || {};
    }

    /**
     * 深度合并用户数据
     */
    private mergeUserData(target: UserGameData, source: UserGameData): void {
        Object.keys(source).forEach(key => {
            const sourceValue = source[key as keyof UserGameData];
            const targetValue = target[key as keyof UserGameData];

            if (sourceValue && typeof sourceValue === 'object' && !Array.isArray(sourceValue)) {
                if (targetValue && typeof targetValue === 'object') {
                    this.mergeUserData(targetValue as any, sourceValue as any);
                } else {
                    (target as any)[key] = sourceValue;
                }
            } else {
                (target as any)[key] = sourceValue;
            }
        });
    }

    /**
     * 创建默认道具数据 - 优化版本
     */
    private createDefaultPropData(propType: PropType): any {
        // 🎁 为新手玩家提供默认道具数量
        const defaultAmount = this.getNewPlayerDefaultAmount(propType);

        return {
            amount: defaultAmount,
            propType,
            propId: propType,
            desc: `道具${propType}`,
            getTime: new Date(),
            lastResetTime: new Date(),
            lastUpdateTime: new Date(),
        };
    }

    /**
     * 🎁 获取新手玩家的默认道具数量
     */
    private getNewPlayerDefaultAmount(propType: PropType): number {
        if (!this.isNewPlayer()) {
            return 0;
        }

        // 🎯 新手玩家默认道具配置（使用配置常量）
        switch (propType) {
            case PropType.PropsMoveOut: // 移出道具
                return GameConst.newPlayerDefaultProps.moveOut;
            case PropType.PropsTips: // 提示道具
                return GameConst.newPlayerDefaultProps.tips;
            case PropType.PropsReShuffle: // 重新洗牌道具
                return GameConst.newPlayerDefaultProps.reShuffle;
            case PropType.PropsDayLeftCount: // 每日挑战剩余次数
                return GameConst.dayFreeLimts; // 使用配置的默认值
            case PropType.PropsRevive: // 复活道具
                return GameConst.newPlayerDefaultProps.revive;
            case PropType.PropsExp: // 游戏经验
                return 0;
            case PropType.PropsCoin: // 玩家金币
                return 0;
            default:
                return 0;
        }
    }

    /**
     * 更新ViewModel
     */
    private updateViewModel(): void {
        if (!this.hasUserData()) {
            return;
        }

        this.removeFromViewModel();

        const viewModelData = {
            userId: this.RoleModel.userGameData.key,
            userName: this.RoleModel.userGameData.userName,
            level: this.getGameProgress(),
            isNewPlayer: this.RoleModel.userGameData.isNewPlayer,
            index: this.RoleModel.userGameData.index,
            propUseData: this.RoleModel.userGameData.propUseData,
            // 扩展其他需要的数据
            ...this.RoleModel.userGameData,
        };

        VM.add(viewModelData, 'role');
        oops.log.logBusiness('🎯 ViewModel已更新');
    }

    /**
     * 从ViewModel移除数据
     */
    private removeFromViewModel(): void {
        VM.remove('role');
    }

    /**
     * 统一的API错误处理
     */
    private handleAPIError(apiName: string, error: any): void {
        oops.log.logError(`❌ ${apiName} API失败:`, error);

        const errorMessage = error?.message || error?.code?.toString() || '操作失败';
        oops.gui.toast(errorMessage);
    }
}
