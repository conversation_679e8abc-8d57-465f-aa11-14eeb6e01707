[1, ["e5LUoqx3RAr41dA5QrbKMj", "d055twaSxDFrq9UhF90DgW@f7dd4"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab", "_lpos"], 2, 9, 4, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.<PERSON><PERSON><PERSON>", ["_radius", "_cylinderHeight", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 3, 2], [3, 0, 1, 2, 3, 4, 5, 2], [5, 0, 1, 3], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 2118624528, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 16704, "length": 2532, "count": 1266, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 16704, "count": 348, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.26220136880874634, -1.1719989776611328, -0.26586928963661194], "maxPosition", 8, [1, 0.2754984200000763, 1.3583519458770752, 0.25571849942207336]]], -1], 0, 0, [], [], []], [[[2, "日式寿司_11"], [3, "日式寿司_11", [[4, 1, -2, [0, "38p4XzQ+JJlZIWgWws4Spv"], [0], [5, true, true], 1], [6, 4, -3, [0, "255P27AaxKCYFZY6LtzMnB"]], [7, 0.2688498944044113, 1.9926511347293854, -4, [0, "7esDk8WsBDV61AKeQdKmrI"], [1, 0.006648525595664978, 0.09317648410797119, -0.005075395107269287]]], [8, "c3FFngNDVN1IKkufxmLCMN", null, null, null, -1, 0], [1, 0.846, 0, 3.712]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]