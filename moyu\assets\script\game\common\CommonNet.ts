/*
 * @Author: dgflash
 * @Date: 2022-06-28 19:10:14
 * @LastEditors: dgflash
 * @LastEditTime: 2022-09-20 10:38:39
 */

import { WECHAT } from 'cc/env';
import { HttpClient as HttpClient_Browser, WsClient as WsClient_Browser } from 'tsrpc-browser';
import { HttpClient as HttpClient_Miniapp, WsClient as WsClient_Miniapp } from 'tsrpc-miniapp';
import { oops } from '../../../../extensions/oops-plugin-framework/assets/core/Oops';
import { Security } from '../../tsrpc/models/Security';
import { ShareConfig } from '../../tsrpc/models/ShareConfig';
import { BaseResponse, DataUpdateType, OptimizedDataResponse } from '../../tsrpc/protocols/base';
import {
    serviceProto as ServiceProtoGame,
    ServiceType as ServiceTypeGame,
} from '../../tsrpc/protocols/ServiceProtoGame';
import {
    serviceProto as ServiceProtoGate,
    ServiceType as ServiceTypeGate,
} from '../../tsrpc/protocols/ServiceProtoGate';
import { ClientConfig } from './ClientConfig';

import { GameServerConfig } from './config/GameServerConfig';
import { LocalConfig } from './config/LocalConfig';
import { DataManager } from './DataManager';
import { smc } from './SingletonModuleComp';

/** TSRPC网络模块 */
export class CommonNet {
    /** 连接网关服务器 Http 客户端 */
    hcGate: HttpClient_Miniapp<ServiceTypeGate> | HttpClient_Browser<ServiceTypeGate> = null!;

    /** 连接数据服务器 http 客户端 ，不用了*/
    hcGame: HttpClient_Miniapp<ServiceTypeGame> | HttpClient_Browser<ServiceTypeGame> = null!;

    /** 连接数据服务器 WebSocket 客户端 */
    wcGame: WsClient_Miniapp<ServiceTypeGame> | WsClient_Browser<ServiceTypeGame> = null!;

    /** 连接数据服务器 WebSocket 客户端 */
    wcGate: WsClient_Miniapp<ServiceTypeGate> | WsClient_Browser<ServiceTypeGate> = null!;

    constructor() {
        /** 创建连接网关服务器 Http 客户端 */
        this.createHcGate();

        /** 🎯 纯HTTP架构：同时初始化游戏HTTP客户端 */
        this.createHcGame();
    }

    createHcGate() {
        // 🚪 网关客户端：连接端口5000，处理登录注册
        const serverUrl = ClientConfig.gateUrl;
        console.log('🚪 网关HTTP客户端初始化:', serverUrl);

        this.hcGate = new (WECHAT ? HttpClient_Miniapp : HttpClient_Browser)(ServiceProtoGate, {
            server: serverUrl,
            json: ShareConfig.json,
            logger: console,
            // 🚀 优化：减少超时时间，加快失败响应
            timeout: 5000, // 从默认10秒减少到5秒
        });
        this.flowClientApi(this.hcGate);
        this.flowAuth(this.hcGate);
    }
    createWcGate(serverUrl: string) {
        if (this.wcGate && this.wcGate.isConnected) {
            this.wcGate.disconnect();
            return this.wcGate;
        }
        // 创建客户端与游戏服务器的 WebSocket 连接
        let wsc = new (WECHAT ? WsClient_Miniapp : WsClient_Browser)(ServiceProtoGate, {
            server: serverUrl,
            heartbeat: {
                interval: LocalConfig.heartbeat_interval,
                timeout: LocalConfig.heartbeat_timeout,
            },
            json: ShareConfig.json,
            // logger: console,
            // logMsg: true,
        });
        smc.net.wcGate = wsc;
        this.flowClientMsg(wsc);
        this.flowAuth(wsc);
        this.flowUserGameData(wsc);
        return wsc;
    }

    /** 创建连游戏服务器 Http 客户端 */
    createHcGame() {
        // 🎮 游戏客户端：连接端口5001，处理游戏逻辑
        const serverUrl = ClientConfig.gameUrl;

        console.log('🎮 游戏HTTP客户端初始化:', serverUrl);

        this.hcGame = new (WECHAT ? HttpClient_Miniapp : HttpClient_Browser)(ServiceProtoGame, {
            server: serverUrl,
            json: ShareConfig.json,
            logger: console,
            // 🚀 优化：减少超时时间，加快失败响应
            timeout: 5000, // 从默认10秒减少到5秒
        });

        // 🔧 同步更新GameServerConfig，保持一致性
        GameServerConfig.httpUrl = serverUrl;

        this.flowClientApi(this.hcGame);
        this.flowAuth(this.hcGame);
        this.flowUserGameData(this.hcGame);
    }
    /**
     *  创建连接游戏服务器 Websocket 客户端
     *  🎯 纯HTTP架构：此方法已弃用，保留用于向后兼容
     */
    createWscGame() {
        // 🎯 纯HTTP架构：跳过WebSocket客户端创建
        oops.log.logNet('🎯 纯HTTP架构：跳过WebSocket游戏客户端创建');

        // 不设置wcGame，避免混淆
        // smc.net.wcGame = null;

        oops.log.logNet('✅ 纯HTTP架构：WebSocket游戏客户端已跳过');
    }

    private flowUserGameData(client: any) {
        // 将 callApi 的结果返回给调用方之后，如果有携带用户数据，直接覆盖
        client.flows.postApiReturnFlow.push(v => {
            if (v.return.isSucc && v.return.res) {
                const res = v.return.res;

                // 使用新的数据管理器处理优化响应
                if (res.updateType && Object.values(DataUpdateType).includes(res.updateType)) {
                    const dataManager = DataManager.getInstance();
                    dataManager.processOptimizedResponse(res as OptimizedDataResponse);
                }
                // 🗑️ 旧的全量数据响应已弃用，所有API已迁移到新格式
                // 如果遇到没有updateType的响应，记录警告
                else if (res.userGameData) {
                    console.warn('[CommonNet] 检测到旧格式API响应，请升级API到新格式:', res);
                }
            }
            return v;
        });
    }

    /** HTTP 客户端协议数据加密、解密 */
    private flowClientApi(hc: any) {
        if (!ShareConfig.security) return;

        hc.flows.preSendDataFlow.push(v => {
            if (v.data instanceof Uint8Array) {
                v.data = Security.encrypt(v.data);
            }
            return v;
        });

        // 在处理接收到的数据之前，通常要进行加密/解密
        hc.flows.preRecvDataFlow.push(v => {
            if (v.data instanceof Uint8Array) {
                v.data = Security.decrypt(v.data);
            }
            return v;
        });
    }

    /** WebSocket 客户端协议数据加密、解密 */
    private flowClientMsg(wsc: any) {
        if (!ShareConfig.security) return;

        // 发送 Message 之前
        wsc.flows.preSendMsgFlow.push(v => {
            if (v.data instanceof Uint8Array) {
                v.data = Security.encrypt(v.data);
            }
            return v;
        });

        // 触发 Message 监听事件之前
        wsc.flows.preRecvMsgFlow.push(v => {
            if (v.data instanceof Uint8Array) {
                v.data = Security.decrypt(v.data);
            }
            return v;
        });
    }

    /** 帐号登录令牌验证是否逻辑（帐号中加入登录令牌，服务器通过令牌解析玩家数据，如果存在就是已登录） */
    private flowAuth(client: any) {
        // HttpClient WsClient
        // 执行 callApi 之前协议中插入登录令牌
        client.flows.preCallApiFlow.push(v => {
            // 请求前插入登录令牌
            const ssoToken = oops.storage.get('SSO_TOKEN');
            if (ssoToken) {
                v.req.__ssoToken = ssoToken;
            }
            return v;
        });

        // 将 callApi 的结果返回给调用方之后将登录令牌存到本地（收到协议时将登录令牌存到本地）
        client.flows.postApiReturnFlow.push(v => {
            if (v.return.isSucc) {
                const res = v.return.res as BaseResponse;

                // 请求成功后刷新登录令牌
                if (res.__ssoToken !== undefined) {
                    // 存储token和预估过期时间（7天，但提前1小时过期以确保安全）
                    const tokenData = {
                        token: res.__ssoToken,
                        expiredTime: Date.now() + 86400000 * 7 - 60 * 60 * 1000, // 7天 - 1小时
                        createdTime: Date.now(),
                    };
                    oops.storage.set('SSO_TOKEN', res.__ssoToken);
                    oops.storage.set('SSO_TOKEN_INFO', JSON.stringify(tokenData));
                }
            }
            // 登录令牌过期时删除客户端登录令牌（可跳转到登录界面）
            else if (v.return.err.code === 'NEED_LOGIN') {
                oops.storage.remove('SSO_TOKEN');
                oops.storage.remove('SSO_TOKEN_INFO');
            }
            return v;
        });
    }
}
