[1, ["786QW1LRpNOasfEX/o0Nvb", "22uVqlFnJGNpLDdM1Z+uwi@f9941", "05oWtVrBlJ5IIsP6XvWXgT@f9941", "1eWFsMVo9G2Iy50BMhdO0J@f9941"], ["node", "_font", "_spriteFrame", "_target", "root", "lab_cancel", "lab_ok", "lab_content", "lab_title", "data"], [["cc.Node", ["_name", "_layer", "_children", "_components", "_prefab", "_parent", "_lpos"], 1, 2, 9, 4, 1, 5], ["cc.UITransform", ["_name", "node", "__prefab", "_contentSize"], 2, 1, 4, 5], ["cc.Label", ["_string", "_actualFontSize", "_fontSize", "_isSystemFontUsed", "_cacheMode", "_enableOutline", "_overflow", "_enableWrapText", "_name", "node", "__prefab", "_font"], -6, 1, 4, 6], ["110c8vEd5NEPL/N9meGQnaX", ["_dataID", "_name", "node", "__prefab"], 1, 1, 4], ["cc.Sprite", ["_type", "_sizeMode", "_name", "node", "__prefab", "_spriteFrame"], 0, 1, 4, 6], ["cc.Widget", ["_alignFlags", "_bottom", "_name", "_left", "_right", "node", "__prefab"], -2, 1, 4], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_layer", "_parent", "_components", "_prefab"], 1, 1, 12, 4], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.<PERSON><PERSON>", ["_name", "_transition", "_zoomScale", "node", "__prefab", "clickEvents", "_normalColor", "_target"], 0, 1, 4, 9, 5, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["653bf8VPC5Fn49zFJFqXVgx", ["node", "__prefab", "lab_title", "lab_content", "lab_ok", "lab_cancel"], 3, 1, 4, 1, 1, 1, 1]], [[8, 0, 2], [9, 0, 1, 2, 3, 4, 5, 5], [1, 1, 2, 3, 1], [7, 0, 1, 2, 3, 4, 3], [0, 0, 1, 5, 2, 3, 4, 6, 3], [1, 0, 1, 2, 3, 2], [2, 0, 1, 2, 3, 4, 5, 9, 10, 11, 7], [3, 1, 2, 3, 2], [4, 2, 0, 1, 3, 4, 5, 4], [4, 0, 1, 3, 4, 5, 3], [10, 0, 1, 2, 3, 4, 5, 6, 7, 4], [11, 0, 1, 2, 3], [6, 0, 2], [0, 0, 1, 2, 3, 4, 3], [0, 0, 1, 5, 2, 3, 4, 3], [1, 1, 2, 1], [2, 8, 0, 1, 2, 6, 7, 3, 4, 5, 9, 10, 11, 10], [2, 0, 1, 2, 6, 7, 3, 4, 5, 9, 10, 11, 9], [3, 0, 2, 3, 2], [3, 1, 0, 2, 3, 3], [4, 3, 4, 1], [5, 2, 0, 3, 1, 5, 6, 5], [5, 0, 4, 1, 5, 6, 4], [12, 0, 1, 2, 3, 4, 5, 1]], [[12, "confirm"], [13, "confirm", 33554432, [-9, -10, -11, -12], [[2, -2, [0, "37o1ko8nFK1oK3xmkqtwMG"], [5, 450, 320]], [9, 1, 0, -3, [0, "a0vC/W2QdFb7e69t18Gf21"], 7], [23, -8, [0, "92SJypfohE74p2ebUwinVx"], -7, -6, -5, -4]], [1, "c7ZN3fr+hJoZmUZs2A0Ye0", null, null, null, -1, 0]], [4, "btn_ok", 33554432, 1, [-18], [[5, "btn_ok<UITransform>", -13, [0, "f7xQN8U0NKuYCTfCHfwIz2"], [5, 100, 40]], [8, "btn_ok<Sprite>", 1, 0, -14, [0, "3dr+KXw4lC5oS1lceuHGsr"], 1], [10, "btn_ok<Button>", 3, 0.9, -16, [0, "64+udhPrdI4oLL+6S6rR5C"], [[11, "653bf8VPC5Fn49zFJFqXVgx", "onOk", 1]], [4, 4292269782], -15], [21, "btn_ok<Widget>", 12, 100, 40, -17, [0, "37gJva6bdFs6GgPAU1af2N"]]], [1, "fdFk91GVpJKo4VT/zlGejW", null, null, null, 1, 0], [1, -75, -100, 0]], [4, "btn_cancel", 33554432, 1, [-24], [[5, "btn_cancel<UITransform>", -19, [0, "79W4eqKFxMapzMreg0cRB3"], [5, 100, 40]], [8, "btn_cancel<Sprite>", 1, 0, -20, [0, "08v+UeZXZH+a5Tu0BOuubs"], 3], [10, "btn_cancel<Button>", 3, 0.9, -22, [0, "23mGTQh9JPcaNniCjfDZ/c"], [[11, "653bf8VPC5Fn49zFJFqXVgx", "onCancel", 1]], [4, 4292269782], -21], [22, 36, 100, 40, -23, [0, "ffeyp+r3tEe67JwpieOoNw"]]], [1, "3aIfIm6bVFaK62tZkb2Iic", null, null, null, 1, 0], [1, 75, -100, 0]], [3, "lab_ok", 33554432, 2, [[[5, "Label<UITransform>", -25, [0, "44bD80KHZProBBM3AMP1KY"], [5, 100, 40]], [16, "Label<Label>", "确认", 20, 20, 1, false, false, 1, true, -26, [0, "83w7Pp8C1M6Kt+xac8Iku4"], 0], -27], 4, 4, 1], [1, "79ujcxm8RNi7nnpjL7+jNw", null, null, null, 1, 0]], [3, "lab_cancal", 33554432, 3, [[[2, -28, [0, "73nkyWjZ9I/oxF+6QfBLqr"], [5, 100, 40]], [17, "取消", 20, 20, 1, false, false, 1, true, -29, [0, "0eYsv4mJlIa7/bSpPQvt+7"], 2], -30], 4, 4, 1], [1, "38cZMAuzVCOJPotiPg+6BM", null, null, null, 1, 0]], [14, "contenNode", 33554432, 1, [-33], [[15, -31, [0, "44e0aiCRREULKcmsm+5odc"]], [20, -32, [0, "cbpugtBqxBlpaCzue7b9Xb"]]], [1, "63y2N3IBxL8a6t6AyHPeFD", null, null, null, 1, 0]], [3, "lab_content", 33554432, 6, [[[2, -34, [0, "49SU+NRCVFC7lotcT5Arsj"], [5, 56, 54.4]], [6, "内容", 26, 26, false, 1, true, -35, [0, "867mEteZhIVLc5LxzUTUW5"], 4], -36], 4, 4, 1], [1, "7fk7nP/NJBI7+oI60VkgZO", null, null, null, 1, 0]], [4, "titleNode", 33554432, 1, [-39], [[2, -37, [0, "ecLRVXoINGi5Vdj/JTWSRz"], [5, 450, 73]], [9, 1, 0, -38, [0, "b7idNI7spOa7IMrACYTwWo"], 6]], [1, "70cWOi+/JHLpAnLU8qkbna", null, null, null, 1, 0], [1, 0, 124.22900000000004, 0]], [3, "lab_title", 33554432, 8, [[[2, -40, [0, "1c9xohlJtL3bm53voN5DeV"], [5, 64, 54.4]], [6, "标题", 30, 30, false, 1, true, -41, [0, "26eZlgc6xJWqYRq4rBoG6R"], 5], -42], 4, 4, 1], [1, "42TIPOfhhM7ra85tXw74QR", null, null, null, 1, 0]], [18, "common_prompt_ok", 4, [0, "a6mROSd8pJA6FVDucSlwDW"]], [19, "lab_ok<LanguageLabel>", "common_prompt_cancal", 5, [0, "c5vY7CEaJOBbvqXXwmsDAU"]], [7, "lab_ok<LanguageLabel>", 7, [0, "fdJ1rAAmlDeqjsBHa5gesk"]], [7, "lab_ok<LanguageLabel>", 9, [0, "12QljFj4dG9LVb4oZYMNmY"]]], 0, [0, 4, 1, 0, 0, 1, 0, 0, 1, 0, 5, 11, 0, 6, 10, 0, 7, 12, 0, 8, 13, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 6, 0, -4, 8, 0, 0, 2, 0, 0, 2, 0, 3, 2, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, 0, 3, 0, 0, 3, 0, 3, 3, 0, 0, 3, 0, 0, 3, 0, -1, 5, 0, 0, 4, 0, 0, 4, 0, -3, 10, 0, 0, 5, 0, 0, 5, 0, -3, 11, 0, 0, 6, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 7, 0, -3, 12, 0, 0, 8, 0, 0, 8, 0, -1, 9, 0, 0, 9, 0, 0, 9, 0, -3, 13, 0, 9, 1, 42], [0, 0, 0, 0, 0, 0, 0, 0], [1, 2, 1, 2, 1, 1, 2, 2], [0, 1, 0, 1, 0, 0, 2, 3]]