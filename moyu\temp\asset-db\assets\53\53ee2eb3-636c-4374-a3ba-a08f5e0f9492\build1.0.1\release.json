[1, ["12Y9dMgWdJKJGmTiZyQR9H@fc873", "f4lKg1ONJFU7hlZjYSCIig", "a6iFXpDltKfaUGXHppm7Pu", "d0d4/KsHJLtYv+rViUieoQ", "12Y9dMgWdJKJGmTiZyQR9H@2e76e", "34Z3vdUAhEaaHyiyuOwZ96", "2b0fXKMHhMqZQ46sQgJahJ"], ["node", "_mesh", "root", "wallCell", "wallSquareCell", "_parent", "data"], [["cc.Node", ["_name", "_layer", "_prefab", "_components", "_parent", "_lpos", "_lscale", "_children", "_lrot", "_euler"], 1, 4, 9, 1, 5, 5, 12, 5, 5], ["cc.Node", ["_name", "_layer", "_parent", "_children", "_prefab", "_components", "_lpos"], 1, 1, 2, 4, 9, 5], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_name", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.ModelBakeSettings", ["_lightmapSize"], 2], ["cc.UITransform", ["node", "__prefab", "_contentSize"], 3, 1, 4, 5], ["cc.Prefab", ["_name"], 2], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1], ["cc.CompPrefabInfo", ["fileId"], 2], ["0e8633xFZhJ95SzXDsUT6mn", ["node", "__prefab"], 3, 1, 4], ["b02f6WMX79Ch6MUTYWUS80F", ["node", "__prefab", "wallCell", "wallSquareCell"], 3, 1, 4, 6, 6], ["852camx6VNFna9C5eJDlw3B", ["node", "__prefab"], 3, 1, 4]], [[7, 0, 2], [6, 0, 1, 2, 3, 4, 5, 5], [0, 0, 4, 3, 2, 5, 8, 6, 9, 2], [2, 0, 1, 2, 3, 4, 5, 2], [3, 0, 2], [8, 0, 1, 1], [4, 0, 1, 1], [1, 0, 2, 3, 5, 4, 2], [3, 1], [5, 0, 2], [0, 0, 7, 3, 2, 2], [0, 0, 4, 2, 5, 2], [0, 0, 1, 4, 3, 2, 6, 3], [1, 0, 2, 3, 5, 4, 6, 2], [1, 0, 1, 2, 3, 4, 6, 3], [2, 1, 2, 3, 4, 5, 1], [4, 0, 1, 2, 1], [9, 0, 1, 2, 3, 1], [10, 0, 1, 1]], [[9, "gameSceneView"], [10, "gameSceneView", [[[11, "centerPos", -5, [1, "ebVUufOShF5bQjUmiUEdQa", null, null, null, -4, 0], [1, 0, 2.034, 0]], -6, -7], 4, 1, 1], [[6, -2, [0, "88neK5z3dFj7mM7GTHfoLG"]], [18, -3, [0, "3awWx9E/hHBLAA0WofEdkh"]]], [1, "c46/YsCPVOJYA4mWEpNYRx", null, null, null, -1, 0]], [13, "pickBoxPanel", 1, [-9, -10, -11, -12, -13, -14, -15, -16, -17], [[16, -8, [0, "21KOlJkt1JY4Uzb258DO2Y"], [5, 100.00000000000011, 99.99999999999952]]], [1, "6d4cFiI6xEDZ4BK9/KL+y6", null, null, null, 1, 0], [1, 0, 40, 6.775]], [7, "extraNode", 2, [-19, -20, -21], [[6, -18, [0, "08G5BDHQpDcoG1pwUJjCX2"]]], [1, "07K3qZNBlBDq+Ouc3sx81o", null, null, null, 1, 0]], [2, "0", 2, [[3, "Cube<ModelComponent>", -22, [0, "45i8FyRqZLLZIDd1W7CT0n"], [0], [4, 67], 1], [5, -23, [0, "fdXDp2JXNHcLOHGcrcI5qT"]]], [1, "878LR5rLNHE62zQmeqZBeD", null, null, null, 1, 0], [1, -4.805000000000007, 0, 0], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, 1.5, 1.5, 1.5], [1, -90, 0, 0]], [2, "1", 2, [[3, "Cube<ModelComponent>", -24, [0, "7eGXGkb1lIMZXqfIjn7HT4"], [2], [4, 67], 3], [5, -25, [0, "2dM2qXuUpK8qAFk7ySuhlz"]]], [1, "44eTbMwFxJD4y5dEmeNP6Q", null, null, null, 1, 0], [1, -3.204999999999984, 0, 0], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, 1.5, 1.5, 1.5], [1, -90, 0, 0]], [2, "2", 2, [[3, "Cube<ModelComponent>", -26, [0, "08mEosuwdD85AJQDDJkIzv"], [4], [4, 67], 5], [5, -27, [0, "42lDKWm+VEma5aiSq9VNwl"]]], [1, "9f1bTYXKlEa5jGQG0YY+US", null, null, null, 1, 0], [1, -1.6050000000000182, 0, 0], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, 1.5, 1.5, 1.5], [1, -90, 0, 0]], [2, "3", 2, [[3, "Cube<ModelComponent>", -28, [0, "3bDDWCN65DdYA4fR7gEE3I"], [6], [4, 67], 7], [5, -29, [0, "c1KjmgkFVAU5I3ME6SXUc6"]]], [1, "abrQ5gcnpKu53AbfMZoYPD", null, null, null, 1, 0], [1, -0.0049999999999954525, 0, 0], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, 1.5, 1.5, 1.5], [1, -90, 0, 0]], [2, "4", 2, [[3, "Cube<ModelComponent>", -30, [0, "79ZZrqO+xDXobIkXVeoeCz"], [8], [4, 67], 9], [5, -31, [0, "a9+dBDltlE6bT7HYYY991Z"]]], [1, "2aBT2A9/tGNrdJG3rmd8QP", null, null, null, 1, 0], [1, 1.5950000000000273, 0, 0], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, 1.5, 1.5, 1.5], [1, -90, 0, 0]], [2, "5", 2, [[3, "Cube<ModelComponent>", -32, [0, "bef3u4JkFLfrwEQW0CfyX4"], [10], [4, 67], 11], [5, -33, [0, "44CjrpKOpFIZxpQMpA6jOd"]]], [1, "69U26CANFAfb6fbJ9VMNEl", null, null, null, 1, 0], [1, 3.194999999999993, 0, 0], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, 1.5, 1.5, 1.5], [1, -90, 0, 0]], [2, "6", 2, [[3, "Cube<ModelComponent>", -34, [0, "8fT697IRZC+Y1e/sC/52Ib"], [12], [4, 67], 13], [5, -35, [0, "b0HwS5zxVE5LzeKt5YLis6"]]], [1, "6cbB9KWUBHopjJ8qpozqBe", null, null, null, 1, 0], [1, 4.795000000000016, 0, 0], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, 1.5, 1.5, 1.5], [1, -90, 0, 0]], [2, "7", 3, [[3, "Cube<ModelComponent>", -36, [0, "b1ctQhzFFKIaq1UgH+YGrS"], [14], [4, 67], 15], [5, -37, [0, "34njvIMbhF/5vvYqEIu63V"]]], [1, "d437sFefpHL4Il5R6iVHk2", null, null, null, 1, 0], [1, -1.6, 0, -1.819], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, 1.5, 1.5, 1.5], [1, -90, 0, 0]], [2, "8", 3, [[3, "Cube<ModelComponent>", -38, [0, "f6P1j2J0NAlaYMoxsofLz9"], [16], [4, 67], 17], [5, -39, [0, "500ImYn9xANa4ijZE86J1s"]]], [1, "5dFam+ksJNzbrpO6hdYVqB", null, null, null, 1, 0], [1, 0, 0, -1.819], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, 1.5, 1.5, 1.5], [1, -90, 0, 0]], [2, "9", 3, [[3, "Cube<ModelComponent>", -40, [0, "959Em3aWJMgoGzTmgOLK+/"], [18], [4, 67], 19], [5, -41, [0, "4caJJ6kh1KTqlA7JLE4/y2"]]], [1, "32JFnF6+dCxrpCbiK8wMiD", null, null, null, 1, 0], [1, 1.6, 0, -1.819], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, 1.5, 1.5, 1.5], [1, -90, 0, 0]], [2, "pickBoxBg", 2, [[6, -42, [0, "d6nkTD/HtKQ4t+hSr1QqTP"]], [15, -43, [0, "bfZ9w8GXRJQbBtOQw1m7QG"], [20], [8], 21]], [1, "b3R00LvnREnoRMfZ6uwvwz", null, null, null, 1, 0], [1, 0, -0.1, 0.892], [3, -0.7071067811865475, 0, 0, 0.7071067811865476], [1, 11, 3.6000000000000005, 2.0000000000000004], [1, -89.99999999999999, 0, 0]], [7, "wallSceneView", 1, [-45], [[17, -44, [0, "39nfc07xpHi63L1WAddR8A"], 24, 25]], [1, "474GPoWQFMwpeB2r1RAWPy", null, null, null, 1, 0]], [14, "ground", 131072, 15, [-46], [1, "1adnMq9W5Ofbv9KzZjOiWb", null, null, null, 1, 0], [1, 0, -0.20000000000004547, 0]], [12, "groundBg", 131072, 16, [[3, "Cube<ModelComponent>", -47, [0, "4bmuwg9kdB27+KFZwdTfGe"], [22], [8], 23]], [1, "c7xnrmW5xLL4rnK0GnKIbT", null, null, null, 1, 0], [1, 1.1, 2, 2]]], 0, [0, 2, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 0, 5, 1, 0, -2, 2, 0, -3, 15, 0, 0, 2, 0, -1, 4, 0, -2, 5, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, -6, 9, 0, -7, 10, 0, -8, 3, 0, -9, 14, 0, 0, 3, 0, -1, 11, 0, -2, 12, 0, -3, 13, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, -1, 16, 0, -1, 17, 0, 0, 17, 0, 6, 1, 47], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 3, 4], [1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 2, 0, 3, 4, 5, 6]]