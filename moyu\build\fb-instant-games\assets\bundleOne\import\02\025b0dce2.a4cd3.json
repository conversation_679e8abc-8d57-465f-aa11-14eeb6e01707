[1, ["125b0dce2@6c48a"], ["_textureSource"], ["cc.SpriteFrame"], 0, [[[{"name": "t3排行榜", "rect": {"x": 3, "y": 264, "width": 78, "height": 80}, "offset": {"x": 1.5, "y": 5}, "originalSize": {"width": 101, "height": 90}, "rotated": true, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "t2图标底", "rect": {"x": 3, "y": 3, "width": 101, "height": 81}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 101, "height": 81}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "t1福利", "rect": {"x": 66, "y": 408, "width": 56, "height": 49}, "offset": {"x": 0, "y": 1.5}, "originalSize": {"width": 72, "height": 72}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "t3图标底", "rect": {"x": 3, "y": 90, "width": 101, "height": 81}, "offset": {"x": 0, "y": -4.5}, "originalSize": {"width": 101, "height": 90}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "t1探险家", "rect": {"x": 3, "y": 414, "width": 47, "height": 53}, "offset": {"x": -1.5, "y": 2.5}, "originalSize": {"width": 72, "height": 72}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "t3图鉴", "rect": {"x": 3, "y": 177, "width": 81, "height": 81}, "offset": {"x": 0, "y": 3.5}, "originalSize": {"width": 101, "height": 90}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "黑底", "rect": {"x": 90, "y": 177, "width": 33, "height": 38}, "offset": {"x": 0, "y": 0}, "originalSize": {"width": 33, "height": 38}, "rotated": false, "capInsets": [14, 15, 15, 17], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "t2分享", "rect": {"x": 66, "y": 348, "width": 57, "height": 54}, "offset": {"x": 2, "y": 4.5}, "originalSize": {"width": 101, "height": 81}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]], [[{"name": "t2团队赛", "rect": {"x": 3, "y": 348, "width": 57, "height": 60}, "offset": {"x": 0, "y": 3.5}, "originalSize": {"width": 101, "height": 81}, "rotated": false, "capInsets": [0, 0, 0, 0], "packable": true, "pixelsToUnit": 100, "pivot": {"x": 0.5, "y": 0.5}, "meshType": 0}], [0], 0, [0], [0], [0]]]]