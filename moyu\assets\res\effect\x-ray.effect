CCEffect %{
  techniques:
  - name: transparent-xray
    passes:
    - vert: unlit-vs:vert
      frag: xray-fs:frag
      embeddedMacros: { CC_FORWARD_ADD: true }
      depthStencilState:
        depthTest: true
        depthWrite: true
        depthFunc: less_equal
      blendState:
        targets:
        - blend: false
      properties: &props
        tilingOffset:         { value: [1.0, 1.0, 0.0, 0.0] }
        albedoMap:            { value: grey, editor: { displayName: AlbedoMap } }
        mainColor:            { value: [1.0, 1.0, 1.0, 1.0], target: albedo, linear: true, editor: { displayName: Albedo, type: color } }
        albedoScale:          { value: [1.0, 1.0, 1.0], target: albedoScaleAndCutoff.xyz }
        alphaThreshold:       { value: 0.5, target: albedoScaleAndCutoff.w, editor: { parent: USE_ALPHA_TEST, slide: true, range: [0, 1.0], step: 0.001 } }

        metallicMap:          { value: black, editor: { displayName: MetallicMap } }
        metallicFactor:       { value: 0.0, editor: { displayName: "Metallic Factor", slide: true, range: [0.0, 1.0], step: 0.01 } }

        roughnessMap:         { value: white, editor: { displayName: RoughnessMap } }
        roughnessFactor:      { value: 1.0, editor: { displayName: "Roughness Factor", slide: true, range: [0.0, 1.0], step: 0.01 } }

        emissive:             { value: [0.0, 0.0, 0.0, 1.0], linear: true, editor: { type: color } }
        emissiveScale:        { value: [1.0, 1.0, 1.0], target: emissiveScaleParam.xyz }
        normalMap:            { value: normal }
        emissiveMap:          { value: grey }
        xrayAlpha:            { value: 0.5, editor: { slide: true, range: [0.0, 1.0], step: 0.01, displayName: "X-Ray Alpha" } }

    - vert: unlit-vs:vert
      frag: xray-fs:frag
      embeddedMacros: { CC_FORWARD_ADD: true, XRAY_PASS: true }
      depthStencilState:
        depthTest: true
        depthWrite: false
        depthFunc: greater
      blendState:
        targets:
        - blend: true
          blendSrc: src_alpha
          blendDst: one_minus_src_alpha
          blendDstAlpha: one_minus_src_alpha
      properties: *props
}%
CCProgram shared-ubos %{
  uniform Constants {
    vec4 tilingOffset;
    vec4 albedo;
    vec4 albedoScaleAndCutoff;
    vec4 emissive;
    vec4 emissiveScaleParam;

    float metallicFactor;
    float roughnessFactor;

    float xrayAlpha;
  };
}%
CCProgram unlit-vs %{
  precision highp float;
  #include <legacy/input-standard>
  #include <builtin/uniforms/cc-global>
  #include <legacy/local-batch>
  #include <shared-ubos>
  #include <legacy/fog-vs>

  #if USE_VERTEX_COLOR
    in vec4 a_color;
    out lowp vec4 v_color;
  #endif

  out vec3 v_position;
  out vec3 v_normal;
  out vec2 v_uv;

  #if HAS_SECOND_UV
    out mediump vec2 v_uv1;
  #endif

  #if USE_NORMAL_MAP
    out mediump vec4 v_tangent;
  #endif

  #if HAS_SECOND_UV || CC_USE_LIGHTMAP
    in vec2 a_texCoord1;
  #endif

  vec4 vert () {
    StandardVertInput In;
    CCVertInput(In);

    mat4 matWorld, matWorldIT;
    CCGetWorldMatrixFull(matWorld, matWorldIT);

    vec4 pos = matWorld * In.position;
    v_position = pos.xyz;
    v_normal = normalize((matWorldIT * vec4(In.normal, 0.0)).xyz);
    
    v_uv = a_texCoord * tilingOffset.xy + tilingOffset.zw;
    #if SAMPLE_FROM_RT
      CC_HANDLE_RT_SAMPLE_FLIP(v_uv);
    #endif
    #if HAS_SECOND_UV
      v_uv1 = a_texCoord1 * tilingOffset.xy + tilingOffset.zw;
      #if SAMPLE_FROM_RT
        CC_HANDLE_RT_SAMPLE_FLIP(v_uv1);
      #endif
    #endif

    #if USE_VERTEX_COLOR
      v_color = a_color;
    #endif

    #if USE_NORMAL_MAP
      v_tangent.xyz = normalize((matWorld * vec4(In.tangent.xyz, 0.0)).xyz);
      v_tangent.w = In.tangent.w;
    #endif

    CC_TRANSFER_FOG(pos);

    return cc_matProj * (cc_matView * matWorld) * In.position;
  }
}%

CCProgram xray-fs %{
  precision highp float;
  #define PI 3.14159265358979323846

  #include <legacy/output>
  #include <legacy/fog-fs>
  #include <shared-ubos>

  in vec3 v_position;
  in vec3 v_normal;
  in vec2 v_uv;

  #if HAS_SECOND_UV
    in mediump vec2 v_uv1;
  #endif

  #if USE_VERTEX_COLOR
    in lowp vec4 v_color;
  #endif

  #if USE_NORMAL_MAP
    in mediump vec4 v_tangent;
    uniform sampler2D normalMap;
    #pragma define-meta NORMAL_UV options([v_uv, v_uv1])
  #endif

  #if USE_ALBEDO_MAP
    uniform sampler2D albedoMap;
    #pragma define-meta ALBEDO_UV options([v_uv, v_uv1])
  #endif

  #if USE_METALLIC_MAP
    uniform sampler2D metallicMap;
  #endif

  #if USE_ROUGHNESS_MAP
    uniform sampler2D roughnessMap;
  #endif

  #if USE_EMISSIVE_MAP
    uniform sampler2D emissiveMap;
    #pragma define-meta EMISSIVE_UV options([v_uv, v_uv1])
  #endif

  #if USE_ALPHA_TEST
    #pragma define-meta ALPHA_TEST_CHANNEL options([a, r])
  #endif

vec3 getCameraPosition() {
    return cc_cameraPos.xyz;
}

// 简化的 GGX NDF
float DistributionGGX(vec3 N, vec3 H, float roughness)
{
    float a = roughness * roughness;
    float a2 = a * a;
    float NdotH = max(dot(N, H), 0.0);
    float NdotH2 = NdotH*NdotH;

    float num = a2;
    float denom = (NdotH2 * (a2 - 1.0) + 1.0);
    denom = PI * denom * denom;

    return num / denom;
}

// Schlick近似 Fresnel
vec3 fresnelSchlick(float cosTheta, vec3 F0)
{
    return F0 + (1.0 - F0) * pow(clamp(1.0 - cosTheta, 0.0, 1.0), 5.0);
}

// Smith 几何函数
float GeometrySchlickGGX(float NdotV, float roughness)
{
    float r = (roughness + 1.0);
    float k = (r*r) / 8.0;
    float num = NdotV;
    float denom = NdotV * (1.0 - k) + k;
    return num / denom;
}

float GeometrySmith(vec3 N, vec3 V, vec3 L, float roughness)
{
    float NdotV = max(dot(N, V), 0.0);
    float NdotL = max(dot(N, L), 0.0);
    float ggx1 = GeometrySchlickGGX(NdotV, roughness);
    float ggx2 = GeometrySchlickGGX(NdotL, roughness);
    return ggx1 * ggx2;
}

vec4 frag () {
    vec4 baseColor = albedo;

    #if USE_VERTEX_COLOR
        baseColor.rgb *= SRGBToLinear(v_color.rgb);
        baseColor.a *= v_color.a;
    #endif

    #if USE_ALBEDO_MAP
        vec4 texColor = texture(albedoMap, ALBEDO_UV);
        texColor.rgb = SRGBToLinear(texColor.rgb);
        baseColor *= texColor;
    #endif

    // 读取 PBR 参数
    float metallic = metallicFactor;
    float roughness = roughnessFactor;

    #if USE_METALLIC_MAP
        metallic *= texture(metallicMap, ALBEDO_UV).r;
    #endif

    #if USE_ROUGHNESS_MAP
        roughness *= texture(roughnessMap, ALBEDO_UV).g;
    #endif

    vec3 N = normalize(v_normal);
    vec3 V = normalize(getCameraPosition() - v_position);
    vec3 L = normalize(vec3(1.0, 1.0, 1.0)); // 光源方向
    vec3 H = normalize(L + V);

    // Fresnel
    vec3 F0 = mix(vec3(0.04), baseColor.rgb, metallic);
    vec3 F = fresnelSchlick(max(dot(H, V), 0.0), F0);

    // Normal Distribution Function
    float NDF = DistributionGGX(N, H, roughness);

    // Geometry Function
    float G = GeometrySmith(N, V, L, roughness);

    // 分离漫反射和镜面反射
    vec3 kS = F;
    vec3 kD = vec3(1.0) - kS;
    kD *= 1.0 - metallic;

    // 镜面项
    float NdotL = max(dot(N, L), 0.0);
    vec3 numerator = NDF * G * F;
    float denominator = 4.0 * max(dot(N, V), 0.0) * NdotL;
    vec3 specular = numerator / max(denominator, 0.001);

    // 漫反射项
    vec3 diffuse = baseColor.rgb / PI;

    vec3 color = (kD * diffuse + specular) * NdotL;

    vec4 col;

    #if XRAY_PASS
        vec3 cameraPos = getCameraPosition();
        vec3 viewDir = normalize(cameraPos - v_position);
        float edgeStrength = 1.0 - dot(normalize(v_normal), viewDir);
        edgeStrength = smoothstep(0.5, 1.0, edgeStrength); // 调整边缘范围

        col = vec4(color, xrayAlpha); // 使用原始颜色 + 透明度
        col.rgb = mix(col.rgb, vec3(1.0, 1.0, 1.0), edgeStrength); // 边缘高亮混合
    #else
        col = vec4(color, baseColor.a);
    #endif

    CC_APPLY_FOG(col, v_position);
    return CCFragOutput(col);
}
}%