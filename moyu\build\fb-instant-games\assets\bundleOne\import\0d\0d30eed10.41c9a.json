[1, ["d2lw3KtrZG9aD626X+kq2a", "bdc/VAaXlMH57gR5XTjf+v@8b05d"], ["node", "root", "data", "_mesh"], [["cc.Mesh", ["_native", "_hash", "_struct"], 1, 11], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_components", "_prefab"], 2, 9, 4], ["cc.RigidBody", ["_group", "node", "__prefab"], 2, 1, 4], ["cc.CompPrefabInfo", ["fileId"], 2], ["cc.<PERSON><PERSON><PERSON><PERSON>", ["_shadowCastingMode", "node", "__prefab", "_materials", "bakeSettings", "_mesh"], 2, 1, 4, 3, 4, 6], ["cc.ModelBakeSettings", ["_castShadow", "_receiveShadow"], 1], ["<PERSON>.<PERSON><PERSON><PERSON>", ["_radius", "_height", "node", "__prefab", "_center"], 1, 1, 4, 5], ["cc.PrefabInfo", ["fileId", "instance", "targetOverrides", "nestedPrefabInstanceRoots", "root", "asset"], -1, 1, 1]], [[4, 0, 2], [0, 0, 1, 2, 3], [1, 0, 2], [2, 0, 1, 2, 2], [3, 0, 1, 2, 2], [5, 0, 1, 2, 3, 4, 5, 2], [6, 0, 1, 3], [7, 0, 1, 2, 3, 4, 3], [8, 0, 1, 2, 3, 4, 5, 5]], [[[[1, ".bin", 311744526, [{"primitives": [{"primitiveMode": 7, "vertexBundelIndices": [0], "indexView": {"offset": 21216, "length": 4800, "count": 2400, "stride": 2}}], "vertexBundles": [{"view": {"offset": 0, "length": 21216, "count": 442, "stride": 48}, "attributes": [{"name": "a_position", "format": 32, "isNormalized": false}, {"name": "a_normal", "format": 32, "isNormalized": false}, {"name": "a_texCoord", "format": 21, "isNormalized": false}, {"name": "a_tangent", "format": 44, "isNormalized": false}]}]}, "minPosition", 8, [1, -0.29153168201446533, -0.0036326467525213957, -0.2915317118167877], "maxPosition", 8, [1, 0.29153168201446533, 0.3828025460243225, 0.29153165221214294]]], -1], 0, 0, [], [], []], [[[2, "面包_4"], [3, "面包_4", [[4, 4, -2, [0, "f0sWrfXqJCMqHSOOD1NOzk"]], [5, 1, -3, [0, "f7E23Txd9BLYC0PDf9X++t"], [0], [6, true, true], 1], [7, 0.281, 0.383, -4, [0, "bczHV+pARE+7UZED1lqQs/"], [1, 0, 0.2, 0]]], [8, "45y+FsuoJA6JYiXrlwbEtY", null, null, null, -1, 0]]], 0, [0, 1, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 2, 1, 4], [0, 0], [-1, 3], [0, 1]]]]